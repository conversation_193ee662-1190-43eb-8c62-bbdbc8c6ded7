import {createUseStyles} from "react-jss";
import Color from "./Color";

/** 创建或者详情时使用 */
const useDetailStyle = createUseStyles({
    box: {
        backgroundColor: "white",
        paddingTop: 28,
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
    },
    boxAbsolute: {
        position: "absolute",
        top: 0,
        left: 0,
        backgroundColor: "white",
        zIndex: 9,
        paddingTop: 28,
        display: "flex",
        flexDirection: "column",
        width: "100%",
        height: "100%",
    },
    head: {
        flexShrink: 0,
        flexBasis: 32,
        padding: "0 24px"
    },
    headTitle: {
        fontWeight: 700,
        fontSize: 18,
        color: Color["text-1"],
    },
    title14: {
        fontWeight: 700,
        fontSize: "14px",
        color: Color["text-1"],
    },
    boxContent: {
        overflowY: "auto",
        marginTop: 24,
        padding: "0 24px",
        flexGrow: 1,
    },
    footer: {
        display: "flex",
        padding: "0 24px",
        borderTop: "1px solid #DDE2EE",
        flexBasis: 52,
        alignItems: "center",
        flexShrink: 0,
        justifyContent: "flex-end",
    },
});

export default useDetailStyle;
