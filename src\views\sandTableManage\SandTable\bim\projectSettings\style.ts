import {createUseStyles} from "react-jss";
import ProjSettingsAutoFetchPng from "../../../../../assets/images/projSettings_auto_fetch.png";
import ProjSettingsBgPng from "../../../../../assets/images/projSettings_bg.png";

const useStyle = createUseStyles({
    autoFetch: {
        background: `url(${ProjSettingsAutoFetchPng}) no-repeat 0 0`,
        height: 42,
        width: 42,
    },
    bg: {
        background: `url(${ProjSettingsBgPng}) no-repeat 0 0`,
        height: "100%",
        width: "100%"
    },
    completeSettings: {
        "& .ant-radio-group .ant-radio-button-wrapper": {
            marginRight: 2,
        },
    },
});

export default useStyle;
