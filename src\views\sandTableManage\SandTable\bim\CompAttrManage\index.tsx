import React, {useCallback, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {Drawer, Row, Typography} from "antd";
import {CloseOutlined} from "@ant-design/icons";
import ModelCompAttr from "../../../../../components/ModelCompAttr";
import {isDefined} from "../../../../../assets/ts/utils";
import {MotorContext, PickInfo, useMouseEventListener} from "../../../../../reMotor";
import {RootState} from "../../../../../store/rootReducer";
import {getCompAttr} from "../../../../../api/comAttr";
import {CompAttrResult} from "../../../../../api/comAttr/type";
import {setSelectedComps} from "../../../../../store/process/action";

const {Title} = Typography;

interface CompAttrManageProps {
    /** 模型是否处在定义工序的状态 */
    isInDefineProcessStatus?: boolean;
}

const CompAttrManage = (props: CompAttrManageProps) => {
    const {isInDefineProcessStatus} = props;
    const dispatch = useDispatch();
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {selectType, selectedComps} = useSelector((state: RootState) => state.processData);
    const [compAttrInfo, setCompAttrInfo] = useState<CompAttrResult>();
    const [compAttrVisible, setCompAttrVisible] = useState<boolean>(false);
    const [, setSelectedCompId] = useState<string>(); // 右键单击选中的构件id

    const handleOpenCompAttrDrawer = useCallback(
        (ppid: number, path: string[], handle: string) => {
            getCompAttr({
                ppid,
                floor: path[0],
                compClass: path[1],
                subClass: path[2],
                handle
            }).then((res) => {
                setCompAttrInfo(res?.data);
                setCompAttrVisible(true);
            });
        },
        []
    );

    // 模型未进入定义工序的状态的时候，右键单击事件是独立的
    // 进入定义工序状态的时候，右键单击事件会与模型的单选、多选、全选产生交互
    // 简单来说，此时的右键单击事件会遵循左键单击事件（即单选、多选、全选）的逻辑
    const handleModelLeftClick = useCallback(
        (info: PickInfo) => {
            const curProj = MotorContext.getCurBIMProject();
            const pickInfo = info.pickObj;
            if (curProj === null || selectType === undefined || pickInfo === undefined) {
                return;
            }
            if (selectType === "single") {
                curProj.deselect();
                curProj.select(pickInfo.id ?? "");
                dispatch(setSelectedComps([pickInfo]));
            }
            if (selectType === "multiple" || selectType === "all") {
                let isSelected = false;
                for (let i = 0; i < selectedComps.length; i++) {
                    const element = selectedComps[i];
                    if (element.id === pickInfo.id) {
                        isSelected = true;
                        break;
                    }
                }
                if (isSelected) {
                    curProj.deselect(pickInfo.id ?? "");
                    dispatch(setSelectedComps(selectedComps.filter((v) => v.id !== pickInfo.id)));
                } else {
                    curProj.select(pickInfo.id ?? "");
                    dispatch(setSelectedComps([...selectedComps, pickInfo]));
                }
            }
        },
        [dispatch, selectType, selectedComps]
    );

    const handleModelRightClick = useCallback(
        (info: PickInfo) => {
            const {pickObj} = info;
            const curProj = MotorContext.getCurBIMProject();
            if (!isDefined(curSandTable) || !isDefined(pickObj) || !isDefined(curProj)) {
                return;
            }
            const {dir = [], id = "", bimId = ""} = pickObj;
            if (isInDefineProcessStatus === true) {
                // 模型在定义工序的状态下，右键的逻辑同左键，唯一的区别是，右键会打开构件属性的drawer
                handleModelLeftClick(info);
                setSelectedCompId(undefined);
            } else {
                setSelectedCompId((prevId) => {
                    if (prevId !== id) {
                        if (typeof prevId === "string") {
                            curProj.deselect(prevId);
                        }
                        curProj.select(id);
                    }
                    return id;
                });
            }
            handleOpenCompAttrDrawer(curSandTable.ppid, dir, bimId);
        },
        [curSandTable, handleModelLeftClick, handleOpenCompAttrDrawer, isInDefineProcessStatus]
    );

    useMouseEventListener(handleModelRightClick, "rightClick");

    const handleCloseCompAttrDrawer = useCallback(
        () => {
            setCompAttrVisible(false);
        },
        []
    );

    const handleCloseDrawer = useCallback(
        () => {
            setCompAttrVisible(false);
        },
        []
    );

    return (
        <Drawer
            title={(
                <Row justify="space-between">
                    <Title level={5}>构件信息</Title>
                    <CloseOutlined onClick={handleCloseDrawer} />
                </Row>
            )}
            visible={compAttrVisible}
            onClose={handleCloseCompAttrDrawer}
            width={480}
            bodyStyle={{padding: 16}}
            closable={false}
        >
            <ModelCompAttr deptId={orgInfo.orgId} attrInfo={compAttrInfo} />
        </Drawer>
    );
};

export default CompAttrManage;
