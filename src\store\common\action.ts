import {createSFAPayloadAction} from "../utils";
import ActionTypes from "./actionTypes";
import {StateType} from "./reducer";
import {getState} from "..";

export const setToken = createSFAPayloadAction(ActionTypes.SAVE_TOKEN, (payload: StateType["token"]) => payload);
export const setFrom = createSFAPayloadAction(ActionTypes.SET_FROM, (payload: StateType["from"]) => payload);
export const setMenuId = createSFAPayloadAction(ActionTypes.SET_MENU_ID, (payload: StateType["menuId"]) => payload);
export const setAuthCodeList = createSFAPayloadAction(ActionTypes.SET_AUTH_CODE_LIST, (payload: StateType["authCodeList"]) => payload);
export const setOriginAuthCodeList = createSFAPayloadAction(ActionTypes.SET_ORIGIN_AUTH_CODE_LIST, (payload: StateType["originAuthCodeList"]) => payload);
export const setPreviewUrl = createSFAPayloadAction(ActionTypes.SAVE_PREVIEW_URL, (payload: StateType["previewInfo"]) => payload);
export const setUserInfo = createSFAPayloadAction(ActionTypes.SET_USER_INFO, (payload: StateType["userInfo"]) => payload);
export const setOrgInfo = createSFAPayloadAction(ActionTypes.SAVE_ORG_INFO, (payload: StateType["orgInfo"]) => payload);
export const setMenuList = createSFAPayloadAction(ActionTypes.SET_MENU_LIST, (payload: StateType["menuList"]) => payload);
export const setLastSelectedMenu = createSFAPayloadAction(ActionTypes.SET_SELECTED_MENU, (payload: StateType["lastSelectedMenu"]) => payload);
export const setServerInfo = createSFAPayloadAction(ActionTypes.SET_SERVER_INFO, (payload: StateType["serverInfo"]) => payload);
export const setOrgList = createSFAPayloadAction(ActionTypes.SET_ORG_LIST, (payload: StateType["orgList"]) => payload);

export const setHideProjectTreeStatus = createSFAPayloadAction(ActionTypes.HIDE_PROJECT_TREE_STATUS, (payload: StateType["hideProjectTreeStatus"]) => payload);
export const resetCommon = createSFAPayloadAction(ActionTypes.RESET, () => ({}));
export const saveChild = createSFAPayloadAction(ActionTypes.SAVE_CHILD_API, (payload: StateType["child"]) => payload);
export const setSectionList = createSFAPayloadAction(ActionTypes.SECTION_LIST, (payload: StateType["sectionList"]) => payload);
export const setCurSectionInfo = createSFAPayloadAction(ActionTypes.CUR_SECTION_INFO, (payload: StateType["curSectionInfo"]) => payload);
export const emit = createSFAPayloadAction(ActionTypes.SET_EMIT, (payload: StateType["on"]) => payload);

export const setLoading = createSFAPayloadAction(ActionTypes.SET_LODING, (payload: StateType["loading"]) => payload);

const updateIframeEditStatus = (val: boolean) => {
    const {child} = getState().commonData;
    return child?.emit("changeIframeStatus", val);
};
export const setIframeEditStatus = createSFAPayloadAction(ActionTypes.SET_IFRAME_EDIT_STATUS, (payload: StateType["iframeEditStatus"]) => {
    updateIframeEditStatus(payload);
    return payload;
});

export const setLeafMenuId = createSFAPayloadAction(ActionTypes.SET_LEAF_MENU_ID, (payload: string) => payload);
