import React, {useState, useEffect, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {Space, Tree, TreeProps} from "antd";
import {ProjectTreeNodeData, EBSNodeType, getWBSEBSType} from "./data";
import {OrgProjNodeVo, ProjNameVo} from "../../api/common.type";
import ProjectSearch, {ProjectSearchProps} from "./ProjecSearch";
import {toArr} from "../../assets/ts/utils";
import MyIcon from "../MyIcon";
import renderText from "../renderTableText/renderText";

const useStyle = createUseStyles({
    box: {},
});

interface RelatedProjectProps {
    projectTreeData: ProjectTreeNodeData[];
    value?: EBSNodeType[];
    onChange?: (val: EBSNodeType[]) => void;
}

const RelatedProject = (props: RelatedProjectProps) => {
    const {projectTreeData, value, onChange} = props;
    const cls = useStyle();
    const [treeData, setTreeData] = useState<ProjectTreeNodeData[]>([]);
    // 选中的项目
    const [checkedKeys, setCheckedKeys] = useState<number[]>([]);
    const tempType = getWBSEBSType(value);

    useEffect(() => {
        if (tempType === 1) {
            setCheckedKeys(toArr(value ?? []).map((el) => el.ppid));
        } else {
            setCheckedKeys([]);
        }
    }, [tempType, value]);

    const handleProjectTreeCheck: TreeProps["onCheck"] = (_checkedKeys, _info) => {
        const checkedNodes = _info.checkedNodes as unknown as {originData: ProjNameVo}[];
        const projectNodes = checkedNodes
            .filter((el) => el.originData.ppid !== undefined)
            .map((el) => ({ppid: el.originData.ppid, projName: el.originData.projName})) as unknown as EBSNodeType[];
        setCheckedKeys(projectNodes.map((el) => el.ppid));
        if (onChange !== undefined) {
            onChange(projectNodes);
        }
    };

    const handleProjectTreeChange: ProjectSearchProps["onProjectTreeChange"] = useCallback(
        (val) => {
            setTreeData(val);
        },
        []
    );

    const renderTreeNode = useCallback((node: ProjectTreeNodeData) => {
        const nodeData = node.originData as OrgProjNodeVo;
        if (nodeData.type === 0) {
            return (
                <Space>
                    <div><MyIcon type="icon-gaosuxiangmu" fontSize={16} color="#000" style={{position: "relative", top: 2}} /></div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        if (nodeData.type === 1) {
            return (
                <Space>
                    <div style={{backgroundColor: "#2DA641", padding: "2px", color: "#fff", lineHeight: 1}}>标</div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        return (
            <div>{renderText(node.title as string)}</div>
        );
    }, []);

    return (
        <div className={cls.box} style={{display: "flex", flexDirection: "column", height: "100%"}}>
            <ProjectSearch projectTreeValue={value} projectTree={projectTreeData} onProjectTreeChange={handleProjectTreeChange} />
            <div style={{flexGrow: 1, height: 0, marginTop: 16, overflowY: "auto"}}>
                {
                    treeData.length > 0 && (
                        <Tree
                            defaultExpandAll
                            treeData={treeData}
                            checkable
                            checkedKeys={checkedKeys}
                            onCheck={handleProjectTreeCheck}
                            titleRender={renderTreeNode}
                        />
                    )
                }
            </div>
        </div>
    );
};

export default RelatedProject;
