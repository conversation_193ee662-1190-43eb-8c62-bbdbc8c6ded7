import React, {useEffect, useState} from "react";
import {Button, InputNumber, Table, Modal, message, Progress} from "antd";
import {ColumnsType} from "antd/lib/table";
import moment from "moment";
import {getRules, updateRule} from "../../../api/statistic";
import * as TYPE from "../../../api/statistic/type";
import ComTable from "../../../components/ComTable";
import PermissionCode from "../../../components/PermissionCode";

const {confirm} = Modal;
export const ruleColor = {
    红色: {strokeColor: "#D40000", trailColor: "#D40000"},
    黄色: {strokeColor: "#FAAB0C", trailColor: "#fef2dc"},
    蓝色: {strokeColor: "#1F54C5", trailColor: "#dde5f5"},
    绿色: {strokeColor: "#2DA641", trailColor: "#2DA641"},
};

// 判断value值是否在区间range内
export const includesRange = (range: TYPE.Rule["rules"], value: number): boolean => {
    if (range === undefined || range.length === 0) {
        return false;
    }
    if (range.length === 1) {
        const [equal] = range;
        return equal.val === value;
    }
    const [min, max] = range;
    // includes: true 表示 包含val
    const isPassMinValue = min.includes ? min.val <= value : min.val < value;
    const isPassMaxValue = max.includes ? value <= max.val : value < max.val;

    if (isPassMinValue && isPassMaxValue) {
        return true;
    }

    return false;
};

export type Color = typeof ruleColor;

const RuleSetting = () => {
    const [tableData, setTableData] = useState<TYPE.Rule[]>([]);

    const handleChangeRate = (value: number) => {
        let nextVal = value;
        if (value <= 0) {
            nextVal = 1;
        }
        if (value >= 100) {
            nextVal = 99;
        }
        const newData = tableData.map((item, i) => {
            if (i === 0) {
                return {
                    ...item,
                    rules: [
                        item.rules[0],
                        {...item.rules[1], val: nextVal},
                    ]
                };
            }
            if (i === 1) {
                return {
                    ...item,
                    rules: [
                        {...item.rules[0], val: nextVal},
                        item.rules[1]
                    ]
                };
            }

            return item;
        });
        setTableData(newData);
    };

    const handleSave = () => {
        confirm({
            title: "提示",
            content: "是否确认保存？",
            onOk: async () => {
                try {
                    await Promise.all([
                        updateRule(tableData[0]),
                        updateRule(tableData[1])
                    ]);
                    const {data} = await getRules();
                    const sortData = data.sort((a, b) => a.sort - b.sort);
                    setTableData(sortData);
                    message.success("保存成功！");
                } catch (error) {
                    //
                }
            }
        });
    };

    const columns: ColumnsType<TYPE.Rule> = [
        {
            title: "序号",
            align: "left",
            width: "5%",
            dataIndex: "index",
            render: (_text, _record, index: number) => index + 1,
        },
        {
            title: "颜色",
            align: "center",
            width: "15%",
            dataIndex: "color",
            render: (text: keyof Color) => {
                const color = ruleColor[text];

                return (
                    <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>
                        <div style={{width: 16, height: 16, background: color.strokeColor, marginRight: 10}} />
                        {text}
                    </div>
                );
            }
        },
        {
            title: "规则说明",
            align: "left",
            width: "30%",
            dataIndex: "description",
            render(text, record, index) {
                if (index >= 2) {
                    return text;
                }
                const [minItem, maxItem] = record.rules;
                return (
                    <>
                        {minItem !== undefined || record.rules.length < 2
                            ? (
                                <InputNumber
                                    value={minItem.val}
                                    onChange={handleChangeRate}
                                    min={0}
                                    max={100}
                                    disabled={index === 0}
                                    formatter={(value) => `${value}%`}
                                    parser={(value) => Number(value!.replace("%", ""))}
                                />
                            )
                            : null}
                        {minItem.includes ? " <=" : " < "}
                        {text}
                        {maxItem.includes ? " <=" : " < "}
                        {maxItem !== undefined || record.rules.length < 2
                            ? (
                                <InputNumber
                                    value={maxItem.val}
                                    onChange={handleChangeRate}
                                    min={0}
                                    max={100}
                                    disabled={index === 1}
                                    formatter={(value) => `${value}%`}
                                    parser={(value) => Number(value!.replace("%", ""))}
                                />
                            )
                            : null}
                    </>
                );
            },
        },
        {
            title: "更新时间",
            align: "left",
            width: "15%",
            dataIndex: "updateAt",
            render: (text, _record, i: number) => {
                if (i >= 2) {
                    return null;
                }
                return moment(text).format("YYYY.MM.DD HH:mm:ss");
            }
        },
        {
            title: "操作人",
            align: "left",
            width: "15%",
            dataIndex: "updateBy",
            render: (text, _record, i: number) => {
                if (i >= 2) {
                    return null;
                }
                return text;
            }
        },
    ];

    useEffect(() => {
        getRules()
            .then(({data}) => {
                const sortData = data.sort((a, b) => a.sort - b.sort);
                setTableData(sortData);
            });
    }, []);

    return (
        <>
            <div style={{display: "flex", justifyContent: "end", padding: 24, paddingBottom: 0}}>
                <PermissionCode authcode="192:ProjectPlatform-Plan:Setting:Rules:save">
                    <Button type="primary" onClick={() => handleSave()}>
                        保存
                    </Button>
                </PermissionCode>
            </div>
            <div className="safety-quality-table" style={{paddingTop: 24}}>
                <ComTable
                    key="id"
                    columns={columns}
                    dataSource={tableData}
                    pagination={false}
                />
            </div>
        </>
    );
};

export default RuleSetting;
