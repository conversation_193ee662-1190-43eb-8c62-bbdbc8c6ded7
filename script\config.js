const path = require("path");
const {name, version} = require("../package.json");
const config = {
    rootPath: path.resolve(__dirname, "../"), // 根路径
    // distZipReg: new RegExp(name + "-([0-9]|-|[a-z])+.zip"), // 压缩文件名的正则 luban-plan-web-2021-9-8-172101.zip
    distZipReg: new RegExp(`${name}-*`), // 压缩文件名的正则 luban-plan-web-2021-9-8-172101.zip
    directory: "dist/", // 压缩路径
};

exports.config = config;
exports.robot = null;
