import {AnyAction} from "redux";
import {ChildAPI} from "postmate";
import {MenuListProps, OrgNode} from "../../api/common.type";
import {IworksProjectType} from "../../assets/ts/globalType";
import ActionTypes, {AuthCodeList, UserInfoType, OrgInfoType, SectionListType} from "./actionTypes";

interface CompleteExtraInfo {
    code: string;
    resId: string;
    sectionId: string;
}

interface WorkGuideInfo {
    businessType: string;
    completeExtraInfo: CompleteExtraInfo;
    id: string;
    source: string;
}
// 我其实common 只想放一些全局的东西，业务的东西其实可以放在其它文件夹
export interface StateType {
    child: ChildAPI | undefined;
    iframeEditStatus: boolean;
    previewInfo: {
        name: string;
        url: string;
    };
    token: string;
    from: string;
    menuId: number;
    userInfo: UserInfoType;
    orgInfo: OrgInfoType;
    authCodeList: string[];
    originAuthCodeList: AuthCodeList[];
    menuList: MenuListProps[];
    lastSelectedMenu?: MenuListProps;
    iworksProjectType: IworksProjectType;
    hideProjectTreeStatus: boolean; // 是否 隐藏顶部树
    beamSiteId: string;
    serverInfo: {[key: string]: string};
    orgList: OrgNode[];
    /* 标段列表 */
    sectionList: SectionListType[];
    /* 当前标段信息 */
    curSectionInfo: SectionListType | null;
    on: {type: string; data?: never};
    loading: boolean;
    workGuideInfo: WorkGuideInfo | null;
    leafMenuId: string;
}

export const initState: StateType = {
    token: "", // 暂时没用上,用localStorage
    from: "", // 来源，固定为newStandard，说明来自新平台
    menuId: 2691,
    lastSelectedMenu: undefined,
    previewInfo: {
        name: "img",
        url: ""
    },
    userInfo: {
        realName: "",
        username: "",
        roleId: "",
        roleName: "",
        epid: "",
        enterpriseName: "",
    },
    orgInfo: {
        orgId: "",
        orgName: "",
        orgType: 1,
        deptDataType: 2
    },
    authCodeList: [],
    originAuthCodeList: [],
    menuList: [],
    iworksProjectType: "enterprise", // 企业身份
    hideProjectTreeStatus: false,
    beamSiteId: "",
    serverInfo: {},
    orgList: [],
    child: undefined,
    iframeEditStatus: false,
    sectionList: [],
    loading: false,
    curSectionInfo: null,
    // TODO: mock
    // curSectionInfo: {
    //     id: "0d107552b02e49cbb83066ea94278dc8",
    //     nodeId: "0d107552b02e49cbb83066ea94278dc8",
    //     nodeName: "施工标段2",
    //     classification: 0,
    //     supervisorOrg: "监理标段2-3",
    //     parentId: "0458d42dd8ac4e3c901a23eac2a41302",
    //     totalPerson: 0,
    //     authFlag: true,
    //     name: "施工标段2",
    //     nodeType: 3
    // },
    on: {type: ""},
    workGuideInfo: null,
    leafMenuId: ""
};

const reducer = (state = initState, action: AnyAction): StateType => {
    switch (action.type) {
        case ActionTypes.SAVE_TOKEN:
            return {
                ...state,
                token: action.payload
            };
        case ActionTypes.SET_FROM:
            return {
                ...state,
                from: action.payload
            };
        case ActionTypes.SET_MENU_ID:
            return {
                ...state,
                menuId: action.payload
            };
        case ActionTypes.SET_USER_INFO:
            return {
                ...state,
                userInfo: action.payload
            };
        case ActionTypes.SAVE_ORG_INFO:
            return {
                ...state,
                orgInfo: action.payload
            };
        case ActionTypes.SET_SELECTED_MENU:
            return {
                ...state,
                lastSelectedMenu: action.payload
            };
        case ActionTypes.SET_AUTH_CODE_LIST:
            return {
                ...state,
                authCodeList: action.payload
            };
        case ActionTypes.SET_ORIGIN_AUTH_CODE_LIST:
            return {
                ...state,
                originAuthCodeList: action.payload
            };
        case ActionTypes.SET_MENU_LIST:
            return {
                ...state,
                menuList: action.payload
            };
        case ActionTypes.HIDE_PROJECT_TREE_STATUS:
            return {
                ...state,
                hideProjectTreeStatus: action.payload
            };
        case ActionTypes.SET_SERVER_INFO:
            return {
                ...state,
                serverInfo: action.payload
            };
        case ActionTypes.SET_ORG_LIST:
            return {
                ...state,
                orgList: action.payload
            };
        case ActionTypes.SAVE_CHILD_API:
            return {
                ...state,
                child: action.payload
            };
        case ActionTypes.SET_IFRAME_EDIT_STATUS:
            return {
                ...state,
                iframeEditStatus: action.payload
            };
        case ActionTypes.RESET:
            return {
                ...initState
            };
        case ActionTypes.SECTION_LIST:
            return {
                ...state,
                sectionList: action.payload
            };
        case ActionTypes.CUR_SECTION_INFO:
            return {
                ...state,
                curSectionInfo: action.payload
            };
        case ActionTypes.SET_EMIT:
            return {
                ...state,
                on: action.payload
            };
        case ActionTypes.SET_LODING:
            return {
                ...state,
                loading: action.payload
            };
        case ActionTypes.SAVE_PREVIEW_URL:
            return {
                ...state,
                previewInfo: action.payload
            };
        case ActionTypes.SET_LEAF_MENU_ID:
            return {
                ...state,
                leafMenuId: action.payload,
            };
        default:
            return state;
    }
};

export default reducer;
