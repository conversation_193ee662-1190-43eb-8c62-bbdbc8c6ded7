import {cloneDeep} from "lodash-es";
import {DataNode} from "antd/lib/tree";
import {isNotNullOrUndefined} from "../../assets/ts/utils";
import {WBSBusinessType, WBSNodeType} from "../../api/center/type";
import {SectionListType} from "../../store/common/actionTypes";


export interface WBSTypeWithParent {
    wbsNodeId: string;
    wbsNodeName: string;
    parentId: string;
}

export const WBSBusinessTypeList: WBSBusinessType[] = [
    {value: "单位工程", key: 1},
    {value: "子单位工程", key: 2},
    {value: "分部工程", key: 3},
    {value: "子分部工程", key: 4},
    {value: "分项工程", key: 5},
    {value: "子分项工程", key: 6},
    {value: "构件", key: 7},
    {value: "工序", key: 8},
    {value: "检验批", key: 9},
];

export interface DataNodeEx extends DataNode {
    id: string;
    parentId: string;
    name: string;
    sort: number;
    businessType: number;
}

class FilterWBSTreeHandler {
    private curSection: SectionListType | null = null;

    private sectionList: SectionListType[] = [];

    private sectionMap: Map<string, WBSNodeType[]> = new Map();

    constructor(curSection: SectionListType | null, sectionList: SectionListType[], sectionMap: Map<string, WBSNodeType[]>) {
        this.curSection = curSection;
        this.sectionList = sectionList;
        this.sectionMap = sectionMap;
    }

    filterWBSTypeTree(wbsTypeList: number[]): DataNode[] {
        const {curSection, sectionList, sectionMap} = this;
        let resultItems: DataNode[] = [];
        if (isNotNullOrUndefined(curSection)) {
            for (let i = 0; i < sectionList.length; ++i) {
                const sectionItem = sectionList[i];
                if (sectionItem.isAll !== true) {
                    const find = sectionMap.get(sectionItem.id);
                    if (typeof find !== "undefined") {
                        if (curSection.nodeType === 2
                            || (curSection.nodeType === 3 && sectionItem.id === curSection.id)) {
                            resultItems = resultItems.concat(this.filterWBSTypeTreeForSingleSection(sectionItem, wbsTypeList, find));
                        }
                    }
                }
            }
        }


        return resultItems;
    }

    private findParentId(parentId: string, parentMap: Map<string, string>, idSet: Set<string>): string | undefined {
        const findParent = idSet.has(parentId);
        if (findParent) {
            return parentId;
        }
        const pId = parentMap.get(parentId);
        if (typeof pId !== "undefined") {
            if (pId === parentId) {
                return undefined;
            }

            return this.findParentId(pId, parentMap, idSet);
        }
        return undefined;
    }

    private listToTree(list: DataNodeEx[], topLevelWBSType: number): DataNodeEx[] {
        const treeMap = new Map<string, DataNodeEx>();
        for (let i = 0; i < list.length; ++i) {
            const listElement = list[i];
            listElement.children = [];
            treeMap.set(listElement.id, listElement);
        }

        for (let i = 0; i < list.length; ++i) {
            const listElement = list[i];
            if (listElement.parentId !== "" && listElement.parentId !== listElement.id) {
                const parent = treeMap.get(listElement.parentId);
                if (typeof parent !== "undefined") {
                    if (typeof parent.children !== "undefined") {
                        parent.children.push(listElement);
                    } else {
                        parent.children = [listElement];
                    }
                }
            }
        }
        // 只返回没有pid的根元素
        return list.filter((item) => item.parentId === "" || item.businessType === topLevelWBSType || item.parentId === item.id);
    }

    private filterWBSTypeTreeForSingleSection(sectionItem: SectionListType, wbsTypeList: number[], wbsList: WBSNodeType[]): DataNode[] {
        const wbsTypeSet = new Set(wbsTypeList);
        const topLevelWBSType = Math.min(...wbsTypeList);

        const parentMap = new Map(wbsList.map((el) => [el.id, el.parentId]));
        const filterWBSList: DataNodeEx[] = cloneDeep(wbsList).filter((el) => wbsTypeSet.has(el.businessType))
            .map((el) => ({...el, key: el.id, title: el.name}));
        const filterWBSSet = new Set(filterWBSList.map((el) => el.id));
        for (let i = 0; i < filterWBSList.length; ++i) {
            const filterItem = filterWBSList[i];
            if (filterItem.businessType > topLevelWBSType) {
                const {parentId} = filterItem;
                const findParentId = this.findParentId(parentId, parentMap, filterWBSSet);
                if (typeof findParentId !== "undefined") {
                    filterItem.parentId = findParentId;
                }
            }
        }

        const childItems = this.listToTree(filterWBSList, topLevelWBSType);
        if (childItems.length > 0) {
            const resultItem: DataNodeEx = {
                id: sectionItem.id,
                name: sectionItem.name,
                key: sectionItem.id,
                title: sectionItem.name,
                children: childItems,
                parentId: "",
                sort: 0,
                businessType: 0,
            };

            for (let i = 0; i < childItems.length; ++i) {
                childItems[i].parentId = sectionItem.id;
            }

            return [resultItem];
        }

        return [];
    }
}

export default FilterWBSTreeHandler;
