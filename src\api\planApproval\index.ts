/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import FileFetch from "../../service/FileFetch";
import {WebRes} from "../common.type";
import {GetPlanApprovalPageParam, GetPlanApprovalPageReturn, PlanApprovalExportParam} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = window.__IWorksConfig__ as any;
export const getPlanApprovalPage = async (data: GetPlanApprovalPageParam): Promise<WebRes<GetPlanApprovalPageReturn>> => Fetch({
    url: `${baseUrl}/sphere/plan/approval/page`,
    methods: "put",
    data,
});

export const planApprovalExport = (data: PlanApprovalExportParam) => FileFetch({
    url: `${baseUrl}/sphere/plan/approval/export`,
    methods: "put",
    data,
    fileName: "计划审批.xlsx"
});
