import {createUseStyles} from "react-jss";

const useStyle = createUseStyles({
    toolBarBox: {
        zIndex: 1,
        background: "rgba(0,0,0,0.85)",
        height: 48,
        position: "absolute",
        bottom: 24,
        left: "50%",
        transform: "translateX(-50%)",
        "& .ant-btn": {
            color: "rgba(255,255,255,0.8)",
            background: "transparent !important",
            border: "none"
        },
        "& .ant-btn:hover": {
            borderColor: "transparent",
            background: "transparent"
        },
        "& .ant-btn:focus": {
            borderColor: "transparent",
            background: "transparent"
        },
        "& .ant-btn:active": {
            borderColor: "transparent",
            background: "transparent"
        }
    },
    toolBarBtn: {
        width: "48px !important",
        height: "100% !important",
        background: "transparent",
        border: "none"
    },
    btnTopRight: {
        position: "absolute",
        right: 24,
        top: 24,
        zIndex: 1
    }
});

export default useStyle;
