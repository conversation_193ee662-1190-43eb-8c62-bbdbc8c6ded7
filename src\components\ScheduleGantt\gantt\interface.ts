import {EbsNodeItem} from "../api/plan/type";
import {DutyPerson} from "../components/SelectPersonDrawer/data";

export interface ExportToExcelProps {
    deptName?: string;
    sectionName?: string;
    planName?: string;
    excelName?: string;
}

export interface ImportExcel {
    [k: string]: string;
}

export interface ExportToMppProps {
    name?: string;
}

export type stringNumberArray = (string | number)[];

export interface GanttColumn {
    name: string;
    label: string;
    min_width?: string | number;
    width?: string | number;
    align?: string;
    editor?: unknown;
    resize?: boolean;
    tree?: boolean;
    hide?: boolean;
    template?: (task: GanttTask) => string | undefined | null;
    type?: string;
    custom?: boolean;
}

export interface GanttTask {
    id: string;
    parentTaskId?: string | number;
    parent?: string | number;
    text: string;
    progress?: number;
    duration: number;
    start_date: Date;
    end_date: Date;
    request_duration?: number;
    request_start_date?: Date;
    request_end_date?: Date;
    taskStatus?: number;
    /** 实际时间是否为手动录入(位运算)
     * 第1位：实际开始时间为手动录入(0b001)
     * 第2位：实际结束时间为手动录入(0b010)
     * 第3位：实际时间为手动录入(0b100)
     */
    actual_syn: number;
    actual_duration?: number;
    actual_start?: Date;
    actual_end?: Date;
    lagDeviation?: number;
    // progress_record_date?: Date;
    // estimated_completion_date?: Date;
    // is_typical_offset?: boolean;
    // actual_forecast_offset?: number;
    predecessors?: string;// 前置任务字符串
    changeStatus?: "Unchanged" | "Changed"; // 变更状态
    dutyUnit?: string;
    dutyPerson?: DutyPerson;
    /** 模型绑定类型：-1-绑定wbs、0-未绑定、 1-工程、 2-构件、 3-类别 */
    bindType: number;
    wbsNodeLevel?: number;
    wbsNodeIds?: string[];
    ebsNodes?: EbsNodeItem[];
    hasEbs?: boolean;
    hasPhoto?: boolean;
    $wbs?: string;
    $new?: boolean;
    $source?: (number | string)[];
    $target?: (number | string)[];
    type?: string;
    order?: number;
    constraint_type?: string;
    constraint_date?: Date;
    calendar_id?: string;
    unscheduled?: boolean;
    $no_end?: boolean;
    $rendered_type?: string;
    readonly?: boolean;
    planId?: string;
    actualPlan?: string|number;
}

export type ExportGanttTask = Omit<GanttTask, "request_duration" | "request_start_date" | "request_end_date">;

export interface GanttLink {
    id: string;
    source: string;
    target: string;
    type: string;
    lag: number;
}

export interface GanttState {
    columnName: string;
    id: string;
    oldValue: unknown;
    newValue: Date | unknown;
}

export interface DurationDateValue {
    duration?: number; // 实际的工期数值，比如标准日期，工期1d，则需要传8
    startDate?: Date;
    endDate?: Date;
}

export interface DurationFormatter {
    format: (t: string | number | undefined) => string;
    canParse: (value: string) => boolean;
    parse: (value: string) => number;
    _config: {
        hoursPerDay: number;
    };
}

export interface LinksFormatter {
    format: (link: GanttLink) => string;
    canParse: (item: string) => boolean;
    parse: (item: string) => GanttLink;
}

export interface GanttSection {
    name: string;
    height?: number;
    map_to: unknown;
    type: string;
    focus?: boolean;
    time_format?: string[];
    formatter?: DurationFormatter;
    options?: unknown;
    readonly?: boolean;
    single_date?: boolean;
    button?: boolean;
    filter?: (type: string) => boolean;
}

export interface GanttDataset {
    data: GanttTask[];
    links: GanttLink[];
}

export interface GanttScale {
    unit: string;
    trace_indexes: Map<number, number>;
}

export interface GanttPerson {
    key: string;
    label: string;
    userName: string;
    realName: string;
    email: string;
}
