import {Key} from "react";
import moment from "moment";
import Fetch from "../../service/Fetch";
import FileFetch from "../../service/FileFetch";
import {WebRes, TableRes} from "../common.type";
import {DangerListType, DangerVersionType, DangerControlDetails} from "./type";

// 风险评估
// 批量导入风险评估
export const batchImportAssessment = async (params: unknown[]) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerAssessment/batchImportAssessment",
    methods: "post",
    data: {
        batchAssessmentVos: params
    },
});
// 检查这个风险是否允许编辑删除
export const checkAssessment = async (id: string) => Fetch<WebRes<boolean>>({
    url: `/sphere/rest/dangerAssessment/checkAssessment/${id}`,
});
// 导出excel文件
export const exportDangerAssessExcel = (params: {}) => FileFetch({
    url: "/sphere/rest/dangerAssessment/exportExcel",
    methods: "post",
    data: params,
    fileName: "风险评估台账.xlsx"
});
// 获取二维码[单个]，后端返回文件流
export const getDangerControlQrcode = async (id: string): Promise<WebRes<string>> => Fetch({
    url: `/sphere/rest/dangerControl/loadQrcodeFlow/${id}`,
    methods: "get",
});

// 删除
export const deleteDangerAssess = async (ids: Key[]) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerAssessment/simpleDel",
    methods: "post",
    data: {ids},
});

// 查询详情
export const getDangerAssessDetails = async (id: string) => Fetch<WebRes<DangerListType>>({
    url: `/sphere/rest/dangerAssessment/simpleDetail/${id}`,
});

// 查询列表
export const getDangerAssessList = async (params: {}) => Fetch<TableRes<DangerListType[]>>({
    url: "/sphere/rest/dangerAssessment/simpleList",
    methods: "post",
    data: params
});

// 新增风险评估
export const addDangerAssess = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerAssessment/simpleSave",
    methods: "post",
    data: params
});

// 更新风险
export const updateDangerAssess = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerAssessment/simpleUpdate",
    methods: "post",
    data: params
});


// 风险管控
// 创建风险管控
export const createDangerControl = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerControl/createDanger",
    methods: "post",
    data: params
});
// 导出excel
export const exportDangerControlExcel = (params: {}) => FileFetch({
    url: "/sphere/rest/dangerControl/exportExcel",
    methods: "post",
    data: params,
    fileName: "风险管控台账.xlsx"
});
// 批量导出二维码
export const exportDangerControlQR = (params: {}) => {
    const time = moment().format("YYYY.MM.DD");
    return FileFetch({
        url: "/sphere/rest/dangerControl/syncExportQrcode",
        methods: "post",
        data: params,
        fileName: `风险二维码-${time}.zip`
    });
};
// 根据风险id查询风险管控版本
export const getDangerControlVersion = async (id: string) => Fetch<WebRes<DangerVersionType[]>>({
    url: `/sphere/rest/dangerControl/loadDangerVersion/${id}`,
});
// 删除
export const deleteDangerControl = async (ids: Key[]) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerControl/simpleDel",
    methods: "post",
    data: {ids},
});

// 查询详情
export const getDangerControlDetails = async (id: string) => Fetch<WebRes<DangerControlDetails>>({
    url: `/sphere/rest/dangerControl/simpleDetail/${id}`,
});

// 查询列表
export const getDangerControlList = async (params: {}) => Fetch<TableRes<DangerListType[]>>({
    url: "/sphere/rest/dangerControl/simpleList",
    methods: "post",
    data: params
});

// 新增风险管控
export const addDangerControl = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerControl/simpleSave",
    methods: "post",
    data: params
});

// 更新风险管控
export const updateDangerControl = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/rest/dangerControl/simpleUpdate",
    methods: "post",
    data: params
});
