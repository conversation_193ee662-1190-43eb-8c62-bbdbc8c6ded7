import Motor from "@motor/core";
import {useEffect, useRef} from "react";
import MotorContext from "./context";
import {MouseEventListener} from "./interface";

type EventType = "leftClick" | "rightClick" | "leftDown" | "leftUp" | "mouseMove";
const transformEventType = (ev: EventType): Motor.InputType => {
    let res = Motor.InputType.LEFT_CLICK;
    switch (ev) {
        case "leftClick":
            res = Motor.InputType.LEFT_CLICK;
            break;
        case "rightClick":
            res = Motor.InputType.RIGHT_CLICK;
            break;
        case "leftDown":
            res = Motor.InputType.LEFT_DOWN;
            break;
        case "leftUp":
            res = Motor.InputType.LEFT_UP;
            break;
        case "mouseMove":
            res = Motor.InputType.MOUSE_MOVE;
            break;
        default:
            break;
    }
    return res;
};

const useMouseEventListener = (handler: MouseEventListener["notifyMouseEvent"], ev: EventType) => {
    const handlerRef = useRef<MouseEventListener>();

    useEffect(
        () => {
            handlerRef.current = {
                notifyMouseEvent: handler,
                inputType: transformEventType(ev)
            };
            const listener = handlerRef.current;
            MotorContext.addMouseEventListener(listener);
            return () => {
                MotorContext.removeMouseEventListener(listener);
            };
        },
        [ev, handler]
    );
};

export default useMouseEventListener;
