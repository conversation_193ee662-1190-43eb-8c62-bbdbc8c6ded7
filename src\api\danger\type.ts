import {WBSListType} from "../center/type";
import {FileType} from "../common.type";


export interface LeaderType {
    personId: string;
    personName: string;
}
export interface DangerListType {
    area: string;
    behavior: string;
    code: string;
    degree: string;
    id: string;
    level: 1 | 2 | 3 | 4 | 5;
    major: string;
    name: string;
    prevention: string;
    result: string;
    leaders: LeaderType[];
    leaderNames: string;
    controlFile: FileType[];
    wbsList: WBSListType[];
    wbsNames?: string;
    createAt: string;
    createBy: string;
    updateAt: string;
    updateBy: string;
    distinguishDate: number;
}

export interface DangerVersionType {
    createAt: number | string;
    id: string;
}

export interface DangerControlDetails extends DangerListType {
    assessmentId: string;
}
