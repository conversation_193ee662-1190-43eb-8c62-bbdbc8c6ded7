import {Col, ColProps, InputProps, Input, Select, SelectProps, Radio, RadioGroupProps, DatePicker, DatePickerProps, FormItemProps, Cascader, CascaderProps, TreeSelect, TreeSelectProps, Typography} from "antd";
import {RangePickerProps} from "antd/lib/date-picker";
import {TextAreaProps} from "antd/lib/input";
import React, {memo, ReactNode, useCallback} from "react";
import {ColConfig, formColCss, formColStyle} from "../../assets/ts/form";
import CurDeptTreeSelect, {CurDeptTreeSelectProps} from "../curDeptTreeSelect";
import ComItem from "./Item";
import FormSearch, {FormSearchProps} from "./FormSearch";

const {Text} = Typography;
const {TextArea} = Input;
const {RangePicker} = DatePicker;

export interface ComFormItemProps {
    key: string;
    itemConfig: FormItemProps;
    colConfig?: ColProps;
    type: "input" | "select" | "radio"
    | "datePicker" | "textArea" | "rangePicker" | "cascader"
    | "treeSelect" | "fileBox" | "orgTreeSelect" | "custom"
    | "curDeptTreeSelect" | "search";
    typeConfig: {};
    isDisplay?: boolean;
    customNode?: ReactNode;
    labelMaxWidth?: number;
    nativeLabel?: boolean;
    className?: string;
}

const ComFormItem = (props: ComFormItemProps) => {
    const cls = formColCss();
    const {
        itemConfig,
        colConfig = ColConfig,
        type,
        typeConfig,
        isDisplay = true,
        customNode,
        labelMaxWidth = 140,
        // true的话将不会处理label,使用props传入的
        nativeLabel = false,
        className,
    } = props;

    const getPopupContainer = useCallback(
        (triggerNode: HTMLElement) => (triggerNode.parentNode ?? document.documentElement) as HTMLElement,
        []
    );

    const renderType = () => {
        switch (type) {
            case "input":
                return <Input maxLength={50} allowClear {...typeConfig as InputProps} />;
            case "select":
                return <Select allowClear {...typeConfig as SelectProps<string | number>} />;
            case "radio":
                return <Radio.Group {...typeConfig as RadioGroupProps} />;
            case "datePicker":
                return <DatePicker allowClear style={{width: "100%"}} {...typeConfig as DatePickerProps} format="YYYY.MM.DD" />;
            case "textArea":
                return <TextArea maxLength={200} showCount allowClear {...typeConfig as TextAreaProps} />;
            case "rangePicker":
                return <RangePicker allowClear {...typeConfig as RangePickerProps} format="YYYY.MM.DD" />;
            case "cascader":
                return <Cascader allowClear {...typeConfig as CascaderProps<never>} getPopupContainer={getPopupContainer} />;
            case "treeSelect":
                return <TreeSelect allowClear {...typeConfig as TreeSelectProps<string>} />;
            case "curDeptTreeSelect":
                return <CurDeptTreeSelect allowClear {...typeConfig as CurDeptTreeSelectProps} />;
            case "custom":
                return customNode;
            default:
                return null;
        }
    };

    const {label, ...otherItemCol} = itemConfig;

    const renderLabel = () => <Text style={{maxWidth: labelMaxWidth - 23, whiteSpace: "pre-wrap"}}>{label}</Text>;

    if (type === "search") {
        return (
            <Col {...colConfig}>
                <ComItem wrapperCol={{span: 24}} className={`${className}`} {...itemConfig}>
                    <FormSearch {...typeConfig as FormSearchProps} />
                </ComItem>
            </Col>
        );
    }

    if (isDisplay) {
        if (nativeLabel) {
            return (
                <Col {...colConfig}>
                    <ComItem className={`${cls.item} ${className}`} {...itemConfig}>
                        {renderType()}
                    </ComItem>
                </Col>
            );
        }
        return (
            <Col {...colConfig}>
                <ComItem
                    className={`${cls.item} ${className}`}
                    {...otherItemCol}
                    label={nativeLabel ? label : renderLabel()}
                    {...formColStyle(labelMaxWidth)}
                >
                    {renderType()}
                </ComItem>
            </Col>
        );
    }
    return null;
};

export default memo(ComFormItem);
