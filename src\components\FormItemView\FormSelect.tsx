import React from "react";
import {createUseStyles} from "react-jss";

const useStyle = createUseStyles({
    box: {},
});

export interface FormSelectProps {
    value?: string | number;
    options?: {label: string; value: number|string}[];
}

const FormSelect = (props: FormSelectProps) => {
    const {value, options = []} = props;
    const cls = useStyle();

    const selected = options.filter((item) => item.value === value);

    return (
        <div className={cls.box}>
            {selected.length > 0 ? selected[0].label : value}
        </div>
    );
};

export default FormSelect;
