// 任务状态
export const TaskStatus = {
    NOT_START: 1,
    IN_PROGRESS: 2,
    COMPLETED: 3,
};

// 典型偏差
export const DeviationType = {
    TYPICAL: 1,
    NOT_TYPICAL: 2,
};

// 自定义列数据，列取值类型1=文本/2=日期/3=数值
export enum CustomValueType {
    text = 1,
    date = 2,
    number = 3,
}

export const taskStatusOptions = [
    {key: TaskStatus.NOT_START, label: "未开始"},
    {key: TaskStatus.IN_PROGRESS, label: "进行中"},
    {key: TaskStatus.COMPLETED, label: "已完成"}
];
