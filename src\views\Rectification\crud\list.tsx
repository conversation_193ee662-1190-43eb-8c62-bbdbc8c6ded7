import React, {useCallback, useEffect, useMemo, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import moment from "moment";
import {But<PERSON>, Pagination, Row, Tabs} from "antd";
import {useDebounceEffect} from "ahooks";
import {getReformList} from "../../../api/rectification/index";
import {ProcessInstanceBusinessQueryParam, ReformFormItemVo} from "../../../api/rectification/models/flowLine";
import {queryFormInit, RectifyQueryFormModel, unitTypeList, TabPaneListType, checkTypeListEasy} from "../interface";
import {ComColumnsProps} from "../../../components/TableColumnsControl/ColumnsControl";
import TableColumnsControl from "../../../components/TableColumnsControl";
import {RootState} from "../../../store/rootReducer";
import RectifyQueryForm from "../components/QueryForm";
import ComTable from "../../../components/ComTable";
import TextEllipsis from "../../../components/TextEllipsis";
import useComStyle from "../../../assets/css/useComStyle";
import {momentText} from "../../../assets/ts/utils";
// import {newSelectMenuTabs} from "../../../../store/select/newIndex";
// import {dealTableData, renderWbsEbs} from "../../../components/FormWbsEbs/data";
// import selectQueryWbsEbsParams from "../../../../store/select/wbsEbsQuery";
import {setCheckedKeys, setQueryCheckedNode} from "../../../components/WBS/store/action";
import useStyle from "../style";
import selectQueryWbsEbsParams from "../../../store/select/wbsEbsQuery";
import {setCurrentCheckInfo} from "../../../store/rectification/detail/actions";
import {setCurrentReformId, setRectifyQueryOptions} from "../../../store/rectification/template/actions";
import TableLayout from "../../../components/TableLayout";
import {dealTableData, renderWbsEbs} from "../../../components/FormWbsEbs/data";
import {tableDebounceEffectOptions} from "../../../../uikit/ts/util";
import {getState} from "../../../store";
import ComTabs from "../../../components/ComTabs";

const {TabPane} = Tabs;
const renderSerialNum = (name: string, status?: string) => {
    if (status === "canceled") {
        const dom = <span style={{color: "red"}}>【撤】</span>;
        return (
            <>
                {dom}
                {name}
            </>
        );
    }
    if (status === "backed") {
        const dom = <span style={{color: "red"}}>【退】</span>;
        return (
            <>
                {dom}
                {name}
            </>
        );
    }
    return name;
};

/**
 * 是否逾期的判断逻辑
 * 如果有endTime，说明任务已完成，将整改期限与任务完成时间进行比较
 * 如果没有endTime，与当前时间进行比较
 */
const isNotOverdue = (reformExpireDate: number, reformStatus?: string, endTime?: number) => {
    if (reformStatus === "passed" || typeof endTime === "number") {
        return true;
    }
    // if (typeof endTime === "number") {
    //     return moment(reformExpireDate).isSameOrAfter(moment(endTime), "day");
    // }
    return moment(reformExpireDate).isSameOrAfter(moment(), "day");
};

// 判断有没标段信息 只返回boolean
export const isHasSectionInfoStatus = () => {
    if (getState().commonData.curSectionInfo === null) {
        return false;
    }
    return true;
};

const rectifyColumns: ComColumnsProps<ReformFormItemVo>[] = [
    {
        title: "序号",
        dataIndex: "order",
        mustShow: true,
        show: true,
        align: "center",
        fixed: "left",
        width: 80,
        render: (_t, _r, i) => i + 1
    },
    {
        key: "wbsEbs",
        title: "关联WBS/EBS",
        dataIndex: "wbsEbs",
        align: "left",
        mustShow: false,
        show: true,
        // width: 120,
        width: 180,
        fixed: "left",
        render: renderWbsEbs
    },
    {
        title: "整改编号",
        dataIndex: "name",
        align: "left",
        mustShow: false,
        show: true,
        width: 250,
        render: (t, r) => <TextEllipsis text={renderSerialNum(t, r.reformStatus)} />
    },
    {
        title: "整改期限",
        dataIndex: "reformExpireDate",
        mustShow: false,
        align: "left",
        show: true,
        width: 120,
        render: (text: number) => (text !== null ? momentText(text, "YYYY.MM.DD") : "")
    },
    {
        title: "检查单位",
        dataIndex: "buildType",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (n: number) => unitTypeList.find((v) => v.value === n)?.label ?? "--"
    },
    {
        title: "检查形式",
        dataIndex: "checkType",
        mustShow: false,
        show: true,
        align: "left",
        width: 120,
        render: (n: number) => checkTypeListEasy.find((v) => v.value === n)?.label ?? "--"
    },
    {
        title: "检查分项",
        dataIndex: "checkOptions",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (_t, r) => {
            const businessInfos = r.businessInfos ?? [];
            const wbsInfos = businessInfos.filter((v) => v.businessType === "CHECK_SUB_OPTION");
            const wbsStr = wbsInfos.map((v) => v.businessName).join(", ");
            return <TextEllipsis text={wbsStr} />;
        }
    },
    {
        title: "整改要求",
        dataIndex: "reformComment",
        align: "left",
        mustShow: false,
        show: true,
        width: 280,
        render: (t) => <TextEllipsis text={t} />
    },
    {
        title: "是否逾期",
        dataIndex: "reformExpireDate",
        align: "center",
        mustShow: false,
        show: true,
        width: 120,
        render: (_n, r) => (isNotOverdue(r.reformExpireDate, r.reformStatus, r.endTime) ? "未逾期" : <span style={{color: "red"}}>已逾期</span>)
    },
    {
        title: "当前处理人",
        dataIndex: "users",
        align: "left",
        mustShow: false,
        show: true,
        width: 250,
        render: (_t, r) => <TextEllipsis text={r.approvalUsers?.map((v) => v?.realName ?? "").join(", ")} />
    },
    {
        title: "整改状态",
        dataIndex: "reformStatus",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (t) => (t === "passed" ? "已完成" : "整改中")
    }
];

interface RectifyListProps {
    moduleType: string;
    // back: (val: RectifyTaskStatus) => void;
    onRecordClick?: () => void;
}

const RectifyList: React.FC<RectifyListProps> = (props) => {
    const {moduleType, onRecordClick} = props;
    const comCls = useComStyle();
    const cls = useStyle();
    const dispatch = useDispatch();
    // const curMenuList = useSelector(newSelectMenuTabs);
    const {orgInfo, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const wbsQueryParams = useSelector(selectQueryWbsEbsParams);
    const {rectifyQueryOptions} = useSelector((state: RootState) => state.rectificationTemp);
    const [queryForm, setQueryForm] = useState<RectifyQueryFormModel>({});
    const [tableData, setTableData] = useState<ReformFormItemVo[]>([]);
    const [page, setPage] = useState({current: 1, pageSize: 10});
    const [total, setTotal] = useState<number>(0);
    const [columns, setColumns] = useState<ComColumnsProps<ReformFormItemVo>[]>(rectifyColumns);
    const [tabPaneList, setTabPaneList] = useState<TabPaneListType[]>([]);

    const init = useCallback(
        () => {
            if (wbsQueryParams?.noSelect === true) {
                setTableData([]);
                setTotal(0);
                return;
            }
            if (Boolean(orgInfo) === true && curSectionInfo !== null) {
                const {time, checkNodeIds, checkSubOption, ...rest} = queryForm;
                const nodeItems: ProcessInstanceBusinessQueryParam = {
                    businessType: "CHECKED_UNIT",
                    businessIds: curSectionInfo?.nodeId === "" || curSectionInfo?.nodeId === undefined ? [] : [curSectionInfo?.nodeId],
                };
                const checkSubOptionItems: ProcessInstanceBusinessQueryParam = {
                    businessType: "CHECK_SUB_OPTION",
                    businessIds: checkSubOption !== undefined ? [checkSubOption] : []
                };
                let businessQueryList: ProcessInstanceBusinessQueryParam[] = [];
                if (orgInfo.orgId === curSectionInfo.nodeId) {
                    businessQueryList = [checkSubOptionItems].filter((v) => v.businessIds.length > 0);
                } else {
                    businessQueryList = [nodeItems, checkSubOptionItems].filter((v) => v.businessIds.length > 0);
                }
                getReformList({
                    ...rest,
                    ...wbsQueryParams,
                    approvalDeptId: orgInfo.orgId,
                    deadlineStart: time?.[0]?.startOf("day")?.valueOf(),
                    deadlineEnd: time?.[1]?.endOf("day")?.valueOf(),
                    pageParam: {
                        orders: [],
                        page: page.current,
                        size: page.pageSize
                    },
                    nodeId: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.nodeId,
                    nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo?.nodeType,
                    processType: rectifyQueryOptions?.processType ?? 2,
                    businessQueryParam: businessQueryList.length > 0 ? businessQueryList : undefined,
                }, moduleType).then(async ({result}) => {
                    const tempTableData = result?.content ?? [];
                    setTableData(await dealTableData({tableData: tempTableData, idKey: "checkId"}));
                    // setTableData(tempTableData);
                    setTotal(result?.totalElements ?? 0);
                }).catch(() => {
                    setTableData([]);
                    setTotal(0);
                });
            }
        },
        [curSectionInfo, moduleType, orgInfo, page, queryForm, rectifyQueryOptions, wbsQueryParams]
    );

    useDebounceEffect(
        () => {
            if (!isHasSectionInfoStatus()) {
                return;
            }
            init();
        },
        [init],
        tableDebounceEffectOptions
    );

    const handleFormFinished = useCallback(
        (values: RectifyQueryFormModel) => {
            setQueryForm(values);
            setPage((prev) => ({...prev, current: 1}));
        },
        []
    );

    const handleReset = () => {
        dispatch(setCheckedKeys(["all"]));
        dispatch(setQueryCheckedNode([{key: "all", title: "全部"}]));
    };

    useEffect(() => {
        const newTabPaneList: TabPaneListType[] = [
            {
                label: "待处理的",
                value: 2,
                key: "",
            },
            {
                label: "我发起的",
                value: 1,
                key: "",
            },
            {
                label: "已处理",
                value: 3,
                key: "",
            },
            {
                label: "抄送我的",
                value: 4,
                key: "",
            },
            {label: "全部", value: 0, key: ""},
        ];
        // if (curMenuList.filter((item) => item.menuName.includes("全部")).length <= 0) {
        //     setTabPaneList(newTabPaneList);
        //     return;
        // }
        // curMenuList.forEach((item) => {
        //     newTabPaneList.push(
        //         {label: "全部", value: 0, key: item.perms},
        //     );
        // });
        setTabPaneList(newTabPaneList);
    }, []);

    const handleProcessTypeChange = useCallback(
        (key: string) => {
            dispatch(setRectifyQueryOptions({processType: Number(key)}));
        },
        [dispatch]
    );

    const handleEdit = useCallback(
        (record) => {
            dispatch(setCurrentReformId(record.serialNum));
            dispatch(setCurrentCheckInfo({id: record.checkId, type: record.checkType, buildType: record.buildType ?? 0}));
            // dispatch(setRectifyStatus("edit"));
            // back("detail");
            if (onRecordClick !== undefined) {
                onRecordClick();
            }
        },
        [dispatch, onRecordClick]
    );

    const actionColumns: ComColumnsProps<ReformFormItemVo>[] = useMemo(() => [
        {
            title: "操作",
            dataIndex: "operate",
            mustShow: true,
            align: "right",
            show: true,
            fixed: "right",
            width: 120,
            render: (_t, record) => (
                <>
                    <Button type="link" onClick={() => handleEdit(record)} style={{paddingRight: 0}}>编辑</Button>
                </>
            )
        }
    ], [handleEdit]);

    return (
        <TableLayout businessType="plan.check.info.wbs" processType={String(rectifyQueryOptions?.processType ?? 2)}>
            <div className={cls.mainBox}>
                {/* <Row justify="space-between" style={{marginBottom: 16}}>
                    <span className="title">整改记录</span>
                    <ThreeAndSection />
                </Row> */}
                <ComTabs
                    onChange={handleProcessTypeChange}
                    activeKey={(rectifyQueryOptions?.processType ?? 2).toString()}
                >
                    {tabPaneList.map((v) => <TabPane key={v.value} tab={v.label} />)}
                </ComTabs>
                <RectifyQueryForm
                    moduleType={moduleType}
                    defaultFormData={queryFormInit}
                    onSubmit={handleFormFinished}
                    onReset={handleReset}
                />
                <Row justify="end" style={{marginBottom: 10}}>
                    <TableColumnsControl
                        setColumnsList={setColumns}
                        columnsList={columns}
                    />
                </Row>
                <ComTable
                    className={comCls.table}
                    columns={columns.filter((el) => el.show || el.mustShow).concat(actionColumns)}
                    dataSource={tableData}
                    pagination={false}
                    rowKey="serialNum"
                />
                <Row justify="end" style={{marginTop: 16}}>
                    <Pagination
                        total={total}
                        showSizeChanger
                        showQuickJumper
                        current={page.current}
                        pageSize={page.pageSize}
                        onChange={(curPage, pageSize) => setPage((prev) => ({...prev, current: curPage, pageSize: pageSize ?? 10}))}
                        showTotal={(t) => `共 ${t} 条`}
                    />
                </Row>
            </div>
        </TableLayout>
    );
};

export default RectifyList;
