import {TreeProps} from "antd";
import {DataNode} from "antd/lib/tree";
// import {uniqBy} from "lodash-es";
import React, {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {RootState} from "../../store/rootReducer";
import ComDrawer from "../ComDrawer";
import {setDrawerCheckedNodes, setIsShowDrawer} from "./store/action";
import WBSTree from "./WBSTree";

// const getLeafNode = (nodeData: DataNode) => {
//     const leafNodes: DataNode[] = [];
//     const traverTree = (val: DataNode) => {
//         if (Array.isArray(val.children) && val.children.length > 0) {
//             val.children.forEach((item) => traverTree(item));
//         } else {
//             leafNodes.push(val);
//         }
//     };
//     traverTree(nodeData);
//     return leafNodes;
// };

const WBSDrawer = () => {
    const dispatch = useDispatch();
    const [tempDrawCheckedNodes, setTempDrawerCheckedNodes] = useState<DataNode[]>([]);
    const {isShowDrawer, drawerCheckedNodes} = useSelector((state: RootState) => state.wbsTree);
    useEffect(() => {
        if (isShowDrawer) {
            setTempDrawerCheckedNodes(drawerCheckedNodes);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isShowDrawer]);

    const handleCheck: TreeProps["onCheck"] = (_val, info) => {
        let checkNodes: DataNode[] = [];
        checkNodes = info.checkedNodes.filter((el) => (el.children ?? []).length === 0);
        dispatch(setDrawerCheckedNodes(checkNodes));
    };

    const handleClose = () => {
        dispatch(setIsShowDrawer(false));
    };

    const handleCancel = () => {
        dispatch(setDrawerCheckedNodes(tempDrawCheckedNodes));
        dispatch(setIsShowDrawer(false));
    };

    return (
        <ComDrawer
            title="WBS分部分项"
            visible={isShowDrawer}
            width={520}
            onOk={handleClose}
            onCancel={handleCancel}
        >

            <div style={{padding: "24px", height: "100%", paddingBottom: 0}}>
                <WBSTree
                    checkable
                    checkedKeys={drawerCheckedNodes.map((el) => el.key)}
                    onCheck={handleCheck}
                />
            </div>


        </ComDrawer>
    );
};

export default WBSDrawer;
