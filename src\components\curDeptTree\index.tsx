import {But<PERSON>, Drawer, Input, Space, Tree, Typography} from "antd";
import React, {memo, useCallback, useEffect, useState} from "react";
import {DataNode} from "antd/lib/tree";
import {CurDeptTreeType, filterTree, generateList} from "./typeAndData";
import xiangmubu from "../../assets/images/xiangmubu.png";
import biaoduan from "../../assets/images/biaoduan.png";
import danxiang from "../../assets/images/danxiang.png";
import danwei from "../../assets/images/danwei.png";



interface Props {
    treeData: CurDeptTreeType[];
    isVisible: boolean; // 控制是否显示
    onClose: (closeType: string, item?: CurDeptTreeType) => void;
    selectedKeyProps?: string;
}

const CurDeptTree = (props: Props) => {
    const {isVisible, onClose, selectedKeyProps, treeData} = props;
    const [selectedKey, setSelectedKey] = useState<string>("");
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [copyTreeData, setCopyTreeData] = useState<CurDeptTreeType[]>([]); // 备份一份数据
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

    useEffect(() => {
        if (isVisible) {
            setCopyTreeData(treeData);
            // 默认展开全部
            const newExpandedKeys = generateList(treeData, [])
                .map((item: DataNode) => item.key);
            setExpandedKeys(newExpandedKeys);
        }
    }, [isVisible, treeData]);
    useEffect(() => {
        if (selectedKeyProps !== undefined) {
            setSelectedKey(selectedKeyProps);
        }
    }, [selectedKeyProps]);

    const nodeClick = useCallback(
        (item: CurDeptTreeType) => {
            onClose("ok", item);
        },
        [onClose],
    );

    const renderNodeTitle = useCallback(
        (item: CurDeptTreeType) => {
            const renderIcon = () => {
                if (item.type === 0) {
                    // 项目部
                    return <img src={xiangmubu} style={{marginBottom: 4}} alt="项目部图片" />;
                }
                if (item.type === 1) {
                    // 标段
                    return <img src={biaoduan} style={{marginBottom: 4}} alt="标段图片" />;
                }
                if (item.type === 2) {
                    // 单项
                    return <img src={danxiang} style={{marginBottom: 4}} alt="单项图片" />;
                }

                if (item.type === 3) {
                    // 单位
                    return <img src={danwei} style={{marginBottom: 4}} alt="单位图片" />;
                }
                return <></>;
            };
            return (
                <div onClick={() => nodeClick(item)}>
                    <Space>
                        {renderIcon()}
                        <Typography.Text
                            ellipsis
                            style={{wordBreak: "break-all", width: 350}}
                        >
                            {item.title ?? ""}
                        </Typography.Text>
                    </Space>
                </div>
            );
        },
        [nodeClick],
    );

    const renderTreeNode = (list: CurDeptTreeType[]) => list.map((item) => {
        if (Array.isArray(item.children) && item.children.length > 0) {
            return (
                <Tree.TreeNode
                    selected={item.id === selectedKey}
                    key={item.id}
                    title={() => renderNodeTitle(item)}
                >
                    {renderTreeNode(item.children)}
                </Tree.TreeNode>
            );
        }
        return (
            <Tree.TreeNode
                selected={item.id === selectedKey}
                key={item.id}
                title={() => renderNodeTitle(item)}
            />
        );
    });

    const defaultMatcher = (filterText: string, node: CurDeptTreeType | DataNode) => {
        if (typeof node?.title === "string") {
            return node?.title?.includes(filterText);
        }
        return false;
    };

    const onSearch = (value: string) => {
        const newTreeData = JSON.parse(JSON.stringify(treeData));
        if (value === "") {
            setAutoExpandParent(false);
            setExpandedKeys([]);
            setCopyTreeData(treeData);
            return;
        }
        const filtered = newTreeData.map((item: CurDeptTreeType) => filterTree(item, value, defaultMatcher))
            .filter((item: CurDeptTreeType) => Array.isArray(item.children) && item.children.length > 0);
        setCopyTreeData(filtered);
        const newExpandedKeys = generateList(filtered, [])
            .map((item: DataNode) => item.key);
        setAutoExpandParent(true);
        setExpandedKeys(newExpandedKeys);
    };

    const onExpand = (newExpandedKeys: React.Key[]) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
    };

    const renderBox = () => (
        <Drawer
            onClose={() => onClose("cancel")}
            visible={isVisible}
            title="选择组织"
            width={520}
            bodyStyle={{padding: 15}}
            maskClosable={false}
            closable={false}
            keyboard={false}
            footer={(
                <Space style={{float: "right"}}>
                    <Button onClick={() => onClose("cancel")}>取消</Button>
                </Space>
            )}
        >
            <Input.Search onSearch={onSearch} placeholder="请输入字段" style={{marginBottom: 10}} />
            <Tree
                defaultExpandAll
                autoExpandParent={autoExpandParent}
                expandedKeys={expandedKeys}
                onExpand={onExpand}
                selectedKeys={[selectedKey]}
            >
                {renderTreeNode(copyTreeData)}
            </Tree>
        </Drawer>
    );
    return <>{renderBox()}</>;
};

export default memo(CurDeptTree);
