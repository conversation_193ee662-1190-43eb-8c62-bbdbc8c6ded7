import {GetDeptOrgListReturn} from "../../api/common.type";

export default {
    SAVE_TOKEN: "SAVE_TOKEN",
    SET_FROM: "SET_FROM",
    SET_USER_INFO: "SET_USER_INFO",
    SET_AUTH_CODE_LIST: "SET_AUTH_CODE_LIST",
    RESET: "RESET",
    SET_MENU_ID: "SET_MENU_ID",
    SET_ORIGIN_AUTH_CODE_LIST: "SET_ORIGIN_AUTH_CODE_LIST",
    SET_MENU_LIST: "SET_MENU_LIST",
    SAVE_ORG_INFO: "SAVE_ORG_INFO",
    SET_SELECTED_MENU: "SET_SELECTED_MENU",
    HIDE_PROJECT_TREE_STATUS: "HIDE_PROJECT_TREE_STATUS",
    SET_SERVER_INFO: "SET_SERVER_INFO",
    SET_ORG_LIST: "SET_ORG_LIST",
    SAVE_CHILD_API: "SAVE_CHILD_API",
    SET_IFRAME_EDIT_STATUS: "SET_IFRAME_EDIT_STATUS",
    SAVE_PREVIEW_URL: "SAVE_PREVIEW_URL",
    // 标段列表
    SECTION_LIST: "SECTION_LIST",
    // 当前标段信息
    CUR_SECTION_INFO: "CUR_SECTION_INFO",
    SET_EMIT: "SET_EMIT",
    SET_LODING: "SET_LODING",
    SET_LEAF_MENU_ID: "SET_LEAF_MENU_ID",
} as const;

export interface SectionListType extends GetDeptOrgListReturn {
    /**
     * 1: 公司,分公司; 2: 项目; 3: 子项目,标段
     */
    nodeType: number;
    /** 是否是全部 */
    isAll?: boolean;
}

export enum NodeTypeEnum {
    "公司" = 1,
    "项目" = 2,
    "子项目" = 3
}

export interface AuthCodeResultList {
    authCode: string;
    authName: string;
    code?: string;
}

export interface AuthCodeList {
    groupName: string;
    id: string;
    parentId: string;
    authCodeResultList: AuthCodeResultList[];
}
export interface OrgInfoType {
    orgId: string;
    orgType: number;
    orgName: string;
    /** 1-房建项目 2-基建项目 */
    deptDataType: number;
}
export interface UserInfoType {
    realName: string;
    roleId: string;
    roleName: string;
    username: string;
    epid: string;
    enterpriseName: string;
}
