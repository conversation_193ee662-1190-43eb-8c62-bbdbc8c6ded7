import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Pagination} from "antd";
import React, {memo, useState, useCallback, useEffect, useMemo} from "react";
import {useSelector, useDispatch} from "react-redux";
import useComStyle from "../../assets/css/useComStyle";
import ComTabs from "../../components/ComTabs";
import TableLayout from "../../components/TableLayout";
import {planTabList} from "./dataAndType";
import {PlanPreparationTabNumReturn} from "../../api/Preparation/type";
import {getApprovalTabNum} from "../../api/Preparation";
import {RootState} from "../../store/rootReducer";
import useComState from "../../assets/hooks/useComState";
import {queryItemList, columnsInit, QueryFormType} from "./ApprovalBox/dataAndType";
import {PlanApprovalPageList} from "../../api/planApproval/type";
// import TableColumnsControl, {ComColumnsProps} from "../../components/TableColumnsControl";
import {ComColumnsProps} from "../../components/TableColumnsControl";
// import {getPlanApprovalPage, planApprovalExport} from "../../api/planApproval";
import {getPlanApprovalPage} from "../../api/planApproval";
import {popPage, addPage} from "../../store/no-persist/action";
import {BackType} from "../../assets/ts/globalType";
import OperationApproval from "./ApprovalBox/OperationApproval";
// import {isHasSectionInfo} from "../../assets/ts/globalUtils";
import ComTable from "../../components/ComTable";
// import ComModalExport from "../../components/ComModal/ComModalExport";
import QueryFormSingle from "../../components/QueryFormSingle";

const PlanApprovalManage = () => {
    const comCls = useComStyle();
    const {orgInfo, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const dispatch = useDispatch();
    const [state, setState] = useComState({queryFormInit: queryItemList});
    const [queryFormData, setQueryFormData] = useState<QueryFormType | undefined>(); // 搜索条件
    const [columns, setColumns] = useState<ComColumnsProps<PlanApprovalPageList>[]>(columnsInit);
    const [tableData, setTableData] = useState<PlanApprovalPageList[]>([]);
    // const [curListItem, setCurListItem] = useState<PlanApprovalPageList | null>(null);
    const [tableLodding, setTableLodding] = useState(false);
    const [currentTab, setCurrentTab] = useState("pendingApproval");
    const [tabNumObj, setTabNumObj] = useState<PlanPreparationTabNumReturn>();

    React.useEffect(() => {
        if (curSectionInfo === null) {
            return;
        }
        getApprovalTabNum({
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
        }).then((res) => {
            setTabNumObj(res.data);
        }).catch((err) => {
            console.log(err);
        });
    }, [curSectionInfo, orgInfo.orgId, tableData]);

    const getTableListData = useCallback((params = {}) => {
        if (curSectionInfo === undefined || curSectionInfo === null || curSectionInfo.id.length === 0) {
            return;
        }
        setTableLodding(true);
        const planTabListItem = planTabList.find((item) => item.key === currentTab);
        const query = {
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
            pageNum: state.curPage,
            pageSize: state.pageSize,
            // changeStatus: routerType,
            nameKey: queryFormData?.nameKey,
            type: queryFormData?.type === null ? undefined : queryFormData?.type,
            status: queryFormData?.status,
            processType: planTabListItem?.processType ?? 0,
            ...params,
        };
        getPlanApprovalPage(query)
            .then(
                (res) => {
                    if (res.success && res.data !== null && Array.isArray(res.data.items)) {
                        setTableData(res.data.items ?? []);
                        setState.setTotal(res.data.total);
                    }
                }
            )
            .finally(() => {
                setTableLodding(false);
            });
    }, [curSectionInfo, orgInfo.orgId, queryFormData, setState, state.curPage, state.pageSize, currentTab]);

    useEffect(() => {
        setState.setCurPage(1);
        getTableListData({pageNum: 1});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orgInfo.orgId, curSectionInfo, currentTab]);

    useEffect(() => {
        state.queryForm.setFieldsValue({type: null});
    }, [state.queryForm]);

    useEffect(() => {
        // 切换项目部，回到列表页
        dispatch(popPage());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orgInfo.orgId]);

    const handleBackPlan = useCallback((type: BackType = "cancel") => {
        dispatch(popPage());
        if (type === "ok") {
            // 确认 返回
            getTableListData();
        } else if (type === "cancel") {
            // 取消 返回
        }
    }, [dispatch, getTableListData]);

    const handleView = useCallback((item: PlanApprovalPageList) => {
        dispatch(addPage(
            <OperationApproval detailId={item?.id ?? ""} back={handleBackPlan} />
        ));
    }, [dispatch, handleBackPlan]);

    useEffect(() => {
        setColumns((s) => s.map((item) => {
            if (item.title === "操作") {
                return {
                    ...item,
                    render(_text: string, _record: PlanApprovalPageList, _index: number) {
                        return (
                            <Button type="link" onClick={() => handleView(_record)} style={{paddingRight: 0}}>详情</Button>
                        );
                    }
                };
            }
            return item;
        }));
    }, [handleView]);

    const handleFormFinished = useCallback((values: QueryFormType) => {
        setState.setCurPage(1);
        setQueryFormData(values);
        getTableListData({pageNum: 1, ...values, type: values.type === null ? undefined : values.type});
    }, [getTableListData, setState]);

    const handleFormClear = useCallback(() => {
        state.queryForm.setFieldsValue({type: null});
        setState.setCurPage(1);
        setQueryFormData(undefined);
        getTableListData({pageNum: 1, nameKey: undefined, status: undefined, type: undefined});
    }, [getTableListData, setState, state.queryForm]);

    const handleFormValuesChange = useCallback((values) => {
        if (values.type !== undefined) {
            setState.setCurPage(1);
            setQueryFormData(values);
            getTableListData({pageNum: 1, ...values, type: values.type === null ? undefined : values.type});
        }
    }, [getTableListData, setState]);

    const rowSelection = useMemo(() => (
        {
            selectedRowKeys: state.selectIds,
            onChange: setState.setSelectIds,
            columnWidth: 60
        }
    ), [setState.setSelectIds, state.selectIds]);

    const paginationChange = useCallback((curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(Number(pageSizeVal));
        getTableListData({pageNum: curPageVal, pageSize: pageSizeVal});
    }, [getTableListData, setState]);

    // const handleExport = useCallback(() => {
    //     if (!isHasSectionInfo()) {
    //         return;
    //     }
    //     if (curSectionInfo === null) {
    //         return;
    //     }
    //     const planTabListItem = planTabList.find((item) => item.key === currentTab);
    //     let params: {} = {
    //         deptId: orgInfo.orgId,
    //         processType: planTabListItem?.processType ?? 0,
    //         nodeId: curSectionInfo?.isAll !== true ? curSectionInfo?.id : undefined,
    //         nodeType: curSectionInfo?.isAll !== true ? curSectionInfo?.nodeType : undefined,
    //         // pageNum: state.curPage,
    //         // pageSize: state.pageSize,
    //     };
    //     if (state.selectIds.length > 0) {
    //         params = {
    //             ...params,
    //             ids: state.selectIds
    //         };
    //     } else {
    //         params = {
    //             ...params,
    //             ...queryFormData,
    //         };
    //     }
    //     planApprovalExport(params);
    // }, [curSectionInfo, orgInfo.orgId, queryFormData, state.selectIds, currentTab]);
    return (
        <TableLayout>
            <div className={comCls.rootBox}>
                <div>
                    <div>
                        <ComTabs onChange={(activeKey) => setCurrentTab(activeKey)} activeKey={currentTab}>
                            {planTabList.map((tab) => <Tabs.TabPane key={tab.key} tab={`${tab.label}(${tabNumObj !== undefined ? tabNumObj[tab.processType] ?? "0" : "0"})`} />)}
                        </ComTabs>
                    </div>
                    <div>
                        {currentTab !== "" && (
                            <div>
                                <Row>
                                    {/* <Col span={3}>
                                        <ComModalExport
                                            totalNum={state.total}
                                            selectedNum={state.selectIds.length}
                                            onOk={handleExport}
                                        />
                                    </Col> */}
                                    <Col span={24}>
                                        <QueryFormSingle<QueryFormType>
                                            form={state.queryForm}
                                            queryItemList={state.queryFormList}
                                            onFormFinish={handleFormFinished}
                                            onFormClear={handleFormClear}
                                            onFormValuesChange={handleFormValuesChange}
                                            isOperationBtn={false}
                                            // formRow={{justify: "end"}}
                                        />
                                    </Col>
                                    {/* <Col span={1}>
                                        <Row justify="center">
                                            <Col>
                                                <TableColumnsControl
                                                    tableKey={currentTab}
                                                    setColumnsList={setColumns}
                                                    columnsList={columns}
                                                />
                                            </Col>
                                        </Row>
                                    </Col> */}
                                </Row>
                                <ComTable
                                    columns={columns.filter((el: ComColumnsProps<PlanApprovalPageList>) => el.show || el.mustShow)}
                                    dataSource={tableData}
                                    pagination={false}
                                    rowKey="id"
                                    rowSelection={rowSelection}
                                    loading={tableLodding}
                                />
                                <Row justify="space-between" style={{marginTop: 16}}>
                                    <Col>{`已选 ${state.selectIds.length} 项`}</Col>
                                    <Col>
                                        <Pagination
                                            total={state.total}
                                            showSizeChanger
                                            showQuickJumper
                                            current={state.curPage}
                                            pageSize={state.pageSize}
                                            onChange={paginationChange}
                                            showTotal={(totalCount: number) => `共 ${totalCount} 条`}
                                        />
                                    </Col>
                                </Row>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </TableLayout>
    );
};

export default memo(PlanApprovalManage);
