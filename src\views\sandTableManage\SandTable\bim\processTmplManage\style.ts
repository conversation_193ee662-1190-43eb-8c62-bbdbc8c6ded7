import {createUseStyles} from "react-jss";

const useStyles = createUseStyles({
    box: {
        display: "flex",
        flexDirection: "column",
        height: "100%",
        "& .ant-tree": {
            flexGrow: 1,
            overflow: "auto"
        }
    },
    rect: {
        display: "inline-block",
        width: 16,
        height: 16,
        borderRadius: 2,
        border: "1px solid #061127",
        marginRight: 4
    },
    colorPickerBox: {
        position: "fixed",
        width: 264,
        right: 745,
        top: "50%",
        transform: "translateY(-50%)",
        zIndex: 5,
        background: "#ffffff",
        "& .sketch-picker": {
            boxShadow: "none !important",
            width: "100% !important",
            boxSizing: "border-box !important"
        }
    },
    colorPickerTitle: {
        padding: "10px 10px 0 10px"
    },
    drawerBox: {
        "& .ant-drawer-body": {
            paddingBottom: 0
        },
        "& .ant-drawer-content": {
            overflow: "hidden"
        }
    },
    drawerFooter: {
        margin: "16px -16px -16px -17px !important",
        "& .ant-btn": {
            borderRadius: 0,
            height: 48,
            width: "100%",
            color: "#fff"
        }
    }
});

export default useStyles;
