import React, {useCallback, useMemo} from "react";
import {CustomFieldProps} from "../../models/custom-field";
import Select<PERSON>erson from "../../../SelectPerson";
import {ValueType} from "../../../SelectPerson/data";
// import {TreeType} from "../../../../api/center.type";
// import {getLoadPersonTree} from "../../../../api/center";
// import {bfsTree} from "../../../../assets/ts/utils";

// const getPersonList = (data?: string[]): string[] => {
//     if (data === undefined) {
//         return [];
//     }
//     if (Array.isArray(data)) {
//         return data.filter((v) => typeof v === "string");
//     }
//     return [];
// };


const RenderPerson = (props: CustomFieldProps) => {
    // const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {data, currentValue, onChange, nodeId, nodeType} = props;
    // const [originPersonList, setOriginPersonList] = useState<TreeType[]>([]);
    // const [checkedPersonIds, setCheckPersonIds] = useState<string[]>([]);

    // useEffect(() => {
    //     getLoadPersonTree(0, orgInfo.orgId, 1).then((res) => setOriginPersonList(
    //         bfsTree(res.data as unknown as TreeType).filter((v) => v.type === 5)
    //     ));
    // }, [orgInfo]);

    // const handlePersonChange = useCallback(
    //     (val: TreeType[]) => {
    //         setCheckPersonIds(val.map((el) => el.id));
    //         onChange(val.map((v) => v.centerUserName).filter((v) => typeof v === "string") as string[]);
    //     },
    //     [onChange]
    // );

    // const isPersonData = Array.isArray(currentValue) || typeof currentValue === "undefined";
    // console.log("99999", props);
    // if (!Array.isArray(currentValue)) {
    //     return null;
    // }

    const handleChange = useCallback((values: ValueType[]) => {
        onChange(values.map((item) => item.userName));
    }, [onChange]);

    // eslint-disable-next-line max-len
    const newCurrentValue = useMemo(() => (Array.isArray(currentValue) ? currentValue.map((item) => ({userName: item})) : []), [currentValue]);

    if (typeof data.data === "string") {
        return null;
    }
    return (
        <>
            {/* <ConnectedPersonSelector
                widthMatchSelect={false}
                maxPersons={maxLength}
                defaultCheckedKeys={data.defaultValue as string[]}
                selectedKeys={currentValue as string[]}
                mustCheckedPersonIds={data.data.map((d) => d.id)}
                disabled={disabled === true || data.mode !== CustomFieldMode.Writable}
                onChange={onChange}
                onPopup={() => null}
            /> */}
            <SelectPerson
                value={newCurrentValue}
                onChange={handleChange}
                nodeId={nodeId}
                nodeType={nodeType}
            />
        </>
    );
};

export default RenderPerson;
