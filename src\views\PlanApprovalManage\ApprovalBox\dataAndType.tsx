import React from "react";
import {Col, Row} from "antd";
import {ComFormItemProps} from "../../../components/FormItem";
import renderTableText from "../../../components/renderTableText";
import {ComColumnsProps} from "../../../components/TableColumnsControl";
import {momentText, getCycleMomentText} from "../../../assets/ts/utils";
import {PlanApprovalPageList, planApproveStatusList, planTypeList, planTypeListRadio} from "../../../api/planApproval/type";
import RenderApprovalStatus from "../components/RenderApprovalStatus";
import ComTag from "../../../components/ComTag";

export interface QueryFormType {
    nameKey?: string;
    type?: string | null;
    status?: number;
}

// 列表 筛选 条件
export const queryItemList: ComFormItemProps[] = [
    {
        key: "type",
        type: "radio",
        typeConfig: {options: planTypeListRadio, optionType: "button", className: "planSearchRadioButton"},
        itemConfig: {name: "type", label: "计划类型", labelCol: {span: 0}, wrapperCol: {span: 24}, style: {width: 310}},
        colConfig: {span: 6},
        nativeLabel: true,
    },
    {
        key: "nameKey",
        type: "search",
        typeConfig: {},
        itemConfig: {name: "nameKey"},
        colConfig: {span: 6, offset: 12}
    }
    // {
    //     key: "status",
    //     type: "select",
    //     typeConfig: {
    //         options: planApproveStatusList,
    //         allowClear: true
    //     },
    //     itemConfig: {name: "status", label: "审批状态"},
    //     colConfig: {span: 6}
    // },
    // {
    //     key: "nameKey",
    //     type: "input",
    //     typeConfig: {placeholder: "请输入名称"},
    //     itemConfig: {name: "nameKey", label: "计划名称"},
    //     colConfig: {span: 6},
    // },
];

export const columnsInit: ComColumnsProps<PlanApprovalPageList>[] = [
    {
        title: "序号",
        align: "center",
        mustShow: true,
        show: true,
        fixed: "left",
        width: 80,
        render: (_text: unknown, _record: PlanApprovalPageList, index: number) => index + 1
    },
    {
        key: "name",
        title: "计划名称",
        dataIndex: "name",
        align: "left",
        mustShow: false,
        show: true,
        width: 180,
        fixed: "left",
        // filters: [
        //     {
        //         text: "新增计划",
        //         value: "a",
        //     },
        //     {
        //         text: "变更计划",
        //         value: "b",
        //     },
        // ],
        //     filterDropdown:
        // <div style={{display: "flex", flexDirection: "column", width: 120, fontSize: 14}}>
        //     <Checkbox style={{height: 40, padding: 12}}>新增计划</Checkbox>
        //     <Checkbox style={{height: 40, margin: 0, padding: 12}}>变更计划</Checkbox>
        // </div>,
        render: (name: string, record: PlanApprovalPageList) => (
            <Row align="middle" style={{flexWrap: "nowrap"}}>
                {record.processStatus === "backed" && <ComTag color="#D40000">退回</ComTag>}
                {record.changeStatus === "Changed" && record.status !== 3 && <ComTag color="#FAAB0C">变更</ComTag>}
                <Col flex="1" style={{width: "0"}}>
                    {renderTableText(name)}
                </Col>
            </Row>
        )
    },
    {
        key: "nodeName",
        title: "所属组织",
        dataIndex: "nodeName",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: renderTableText
    },
    {
        key: "type",
        title: "计划类型",
        dataIndex: "type",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (_text: unknown, _record: unknown) => {
            const planTypeListFilter = planTypeList.find((item) => item.value === _text);
            const text = planTypeListFilter === undefined ? "" : planTypeListFilter.label;
            return renderTableText(text);
        }
    },
    {
        key: "cycle",
        title: "计划周期",
        dataIndex: "cycle",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number, record: PlanApprovalPageList) => getCycleMomentText(text, record.type)
    },
    {
        key: "startDate",
        title: "计划开始日期",
        dataIndex: "startDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentText(Number(text))
    },
    {
        key: "endDate",
        title: "计划完成日期",
        dataIndex: "endDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentText(Number(text))
    },
    {
        key: "updateBy",
        title: "编制人",
        dataIndex: "updateBy",
        align: "center",
        mustShow: false,
        show: true,
        width: 120,
        render: renderTableText
    },
    {
        key: "updateAt",
        title: "编制时间",
        dataIndex: "updateAt",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentText(text, "YYYY.MM.DD HH:mm:ss")
    },
    {
        key: "status",
        title: "审批状态",
        dataIndex: "status",
        align: "left",
        mustShow: false,
        show: true,
        width: 140,
        render: (_text: unknown, _record: unknown) => {
            const planStatusListFilter = planApproveStatusList.find((item) => item.value === _text);
            return <RenderApprovalStatus text={planStatusListFilter?.label ?? ""} value={planStatusListFilter?.value ?? 0} />;
        }
    },
    {
        title: "操作",
        align: "right",
        mustShow: true,
        show: true,
        fixed: "right",
        width: "100px",
        // render(_text: string, _record: PlanApprovalPageList, _index: number) {
        //     return (
        //         <Space>
        //             <Button type="link" onClick={() => handleView(_record)}>查看</Button>
        //         </Space>
        //     );
        // }
    }
];
