/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/commondata/region/provice-city-county": {
    /** 获取省市区的信息 */
    get: operations["getProviceCityCountyInfo"];
  };
  "/commondata/weather": {
    get: operations["getWeatherInfoByIP"];
  };
  "/commondata/weather/city/{cityname}": {
    /** 获取天气 */
    get: operations["getWeatherInfo"];
  };
  "/comp/attr": {
    post: operations["getCompAttr"];
  };
  "/comp/attr/project_attr_control": {
    put: operations["setProjectAttrControl"];
    post: operations["getProjectAttrControl"];
  };
  "/comp/pic/info": {
    post: operations["getCompPicInfo"];
  };
  "/comp/pic/save": {
    post: operations["saveCompPicInfo"];
  };
  "/compgroup/pageCompGroup": {
    post: operations["pageCompGroup"];
  };
  "/compnameSearch/componentHierarchy": {
    post: operations["getComponentHierarchy"];
  };
  "/compnameSearch/componentName": {
    post: operations["getComponentName"];
  };
  "/component/locations/{ppid}/{projType}": {
    get: operations["locations"];
  };
  "/dwg/getMotor3dInfo/{ppid}": {
    /** 获取motor3dId */
    get: operations["getMotor3dId"];
  };
  "/dwg/getMotorProjInfoByFileUuid/{uuid}": {
    /** 根据uuid获取motor3dId */
    get: operations["motorProjInfoByFileUuid"];
  };
  "/fileaddress/applyUploadUrlEx": {
    post: operations["applyUploadUrlEx"];
  };
  "/fileaddress/auth/downloadURLs": {
    post: operations["getFileAddressInfo"];
  };
  "/fileaddress/common/qrCodeScan/viewUrl": {
    post: operations["getTrendsFileViewUrlWithoutLogin"];
  };
  "/fileaddress/downloadURLs": {
    post: operations["getDownloadURLs"];
  };
  "/fileaddress/downloadURLs/v2": {
    post: operations["getDownloadURLsWithName"];
  };
  "/fileaddress/fileUploadURLs": {
    post: operations["getFileUploadURLs"];
  };
  "/fileaddress/longLineDownloadURLs": {
    post: operations["getLongLineDownloadURLs"];
  };
  "/fileaddress/viewUrl": {
    post: operations["getTrendsFileViewUrl"];
  };
  "/image/infolist": {
    get: operations["getImageInfoList"];
  };
  "/infradept/deptId/{deptId}": {
    get: operations["getInfraDeptResult"];
  };
  "/infradept/list/coordinate": {
    get: operations["listInfraDeptCoordinate"];
  };
  "/inner/log/addNewPDSOperLog": {
    post: operations["addNewPDSOperLog"];
  };
  "/inner/log/addOperLog": {
    post: operations["addOperLog"];
  };
  "/inner/project/extract/download/fileUuid/{fileUuid}": {
    get: operations["getDownLoadUrlByFileUuid"];
  };
  "/inner/project/extract/download/url/{projId}": {
    get: operations["getProjectDownLoadUrl"];
  };
  "/inner/project/extract/motor3d/status": {
    post: operations["saveOrUpdateMotor3dStatus"];
  };
  "/iot/getControlPointInfosByCameraId": {
    post: operations["getControlPointInfosByCameraId"];
  };
  "/iot/getVideoUrl": {
    post: operations["getVideoUrl"];
  };
  "/iot/listIotManufacturerByDeptIds": {
    post: operations["listIotManufacturerByDeptIds"];
  };
  "/iot/listIotVideoByDeptIds": {
    post: operations["listIotVideoByDeptIds"];
  };
  "/jump": {
    get: operations["jump"];
  };
  "/log/addNewPDSOperLog": {
    post: operations["addNewPDSOperLog"];
  };
  "/log/addOperLog": {
    post: operations["addOperLog"];
  };
  "/motor/v2/model/inner/callback/status": {
    post: operations["postMotorModelStatus"];
  };
  "/motor/v2/model/trans-progress": {
    post: operations["listMotorModelTransStatus"];
  };
  "/motor/v2/model/trigger-motor-model-transform": {
    post: operations["triggerMotorModelTransform"];
  };
  "/newqrcode/createQRCode": {
    post: operations["createQRCode"];
  };
  "/newqrcode/getCommonQRCodeControlProperty": {
    post: operations["getCommonQRCodeControlProperty"];
  };
  "/newqrcode/setCommonQRCodeControlProperty": {
    post: operations["setCommonQRCodeControlProperty"];
  };
  "/newqrcode/switch": {
    post: operations["setQRCodeSwitch"];
  };
  "/newqrcode/switch/ppid/{ppid}": {
    get: operations["getQRCodeSwitch"];
  };
  "/noncas/orgcommon/user-auth-enterprise-org-list": {
    /** 获取用户有权限的企业组织列表 */
    post: operations["getUserAuthEnterpriseOrgList"];
  };
  "/noncas/orgcommon/user-auth-enterprise-user-list": {
    /** 获取有项目部权限的用户信息 */
    post: operations["getEnterpriseUser"];
  };
  "/org/city/dept_num/provinceId/{provinceId}/orgId/{orgId}": {
    get: operations["getCityDeptNumInfos"];
  };
  "/org/dept/detail/edit": {
    /** 编辑项目部（iworks） */
    post: operations["editDeptInfo"];
  };
  "/org/dept/detail/{deptId}": {
    /** 获取项目部概况（iworks） */
    get: operations["getDeptDetailInfo"];
  };
  "/org/dept/summary/org_id/{orgId}/type/{type}": {
    get: operations["getDeptSummaryInfo"];
  };
  "/org/depteffect/check-name": {
    /** 检查效果图名称(返回重名的效果图名称) */
    post: operations["checkDeptEffectImageNames"];
  };
  "/org/depteffect/delete": {
    /** 删除效果图 */
    post: operations["deleteDeptEffectImages"];
  };
  "/org/depteffect/save": {
    /** 上传效果图 */
    post: operations["saveDeptEffectImages"];
  };
  "/org/depteffect/{orgId}": {
    /** 获取指定组织结构效果图 */
    get: operations["getDeptEffectImages"];
  };
  "/org/depteffect/{orgId}/{pictureId}/{orgType}": {
    put: operations["setOrgEffectCover"];
  };
  "/org/detailByDeptId/{deptId}": {
    get: operations["detailByDeptId"];
  };
  "/org/getOrgAndProjectTree": {
    get: operations["getOrgAndProjectTree"];
  };
  "/org/getOrgList": {
    get: operations["getOrgList"];
  };
  "/org/list": {
    get: operations["getOrgResultList"];
  };
  "/org/listExcellentProj/orgId/{orgId}": {
    post: operations["getExcellentProjects"];
  };
  "/org/listNewestDept/{orgId}": {
    get: operations["listNewestDept"];
  };
  "/org/listOrgNode": {
    post: operations["listOrgNode"];
  };
  "/org/listProjNodeByDeptId/{deptId}": {
    /** 获取项目部下的工程组织树列表（标段，单项工程，单位工程，工程）（iworks&iworksApp） */
    get: operations["listProjNodeByDeptId"];
  };
  "/org/project/summary/info": {
    put: operations["setSummaryInfos"];
    post: operations["getSummaryInfos"];
  };
  "/org/province/dept_num/{orgId}": {
    get: operations["getProvinceDeptNumInfos"];
  };
  "/proj/detail-project-item/{deptId}/{motor3did}": {
    get: operations["detailProjectItem"];
  };
  "/proj/detail/ppid/{ppid}": {
    get: operations["getProjectDetailInfo"];
  };
  "/proj/getProjList": {
    post: operations["getProjInfoByDept"];
  };
  "/proj/listProj/nodeType/{nodeType}/nodeId/{nodeId}": {
    get: operations["listProjInfoByNodeId"];
  };
  "/proj/listProj/{deptId}": {
    get: operations["listBIMProj"];
  };
  "/proj/listProjFileInfo": {
    post: operations["listProjFileInfo"];
  };
  "/proj/listProjInfoByDepts": {
    post: operations["listProjInfoByDepts"];
  };
  "/proj/page": {
    post: operations["pageProjectInfo"];
  };
  "/proj/projList4Iworksf": {
    post: operations["listProjInfoByDept4Iworksf"];
  };
  "/proj/proj_type_desc": {
    post: operations["getProjTypeDesc"];
  };
  "/proj/recent_opened_project/ppid/{ppid}": {
    get: operations["recordRecentOpenedProject"];
  };
  "/proj/recent_opened_projects/top/{top}": {
    get: operations["getRecentOpenedProject"];
  };
  "/project/extract/isCanLightWeightExtract": {
    get: operations["isCanLightWeightExtract"];
  };
  "/project/extract/motor3d/fileUuid/status": {
    post: operations["listMotor3dStatusByFileUuid"];
  };
  "/project/extract/motor3d/status": {
    post: operations["upsertMotor3dStatus"];
  };
  "/project/extract/motor3d/status/{ppid}": {
    get: operations["getMotor3dStatusResult"];
  };
  "/project/extract/motor3d/strategy/{projectSize}": {
    get: operations["getExtractStrategy"];
  };
  "/project/extract/trigger/extract": {
    put: operations["triggerMotor3dExtract"];
  };
  "/project/tree/compHandle/ppid/{ppid}": {
    post: operations["getProjectCompForAll"];
  };
  "/project/tree/component_detail/ppid/{ppid}": {
    post: operations["getComponentDetail"];
  };
  "/project/tree/projectcomp": {
    post: operations["getProjectComp"];
  };
  "/project/tree/projecttree": {
    post: operations["getProjectTree"];
  };
  "/project/tree/projecttree/ppid/{ppid}": {
    get: operations["getProjectTreeForAll"];
  };
  "/unit/deptId/{deptId}": {
    get: operations["listNodeCode"];
  };
  "/unit/list": {
    post: operations["listOrgNode"];
  };
  "/unit/nodeId/{nodeId}": {
    get: operations["getUnitInfo"];
  };
  "/userInfo/batchGetUserPortraitInfo/{epid}": {
    post: operations["batchGetUserPortraitInfo"];
  };
  "/userInfo/listDeptUser/deptId/{deptId}": {
    get: operations["getDeptUserInfo"];
  };
  "/userInfo/listUsersOfFilterOrg": {
    post: operations["listUsersOfFilterOrg"];
  };
  "/userInfo/listUsersOfOrg": {
    get: operations["listUsersOfOrg"];
  };
  "/userInfo/listUsersOfRole": {
    get: operations["listUsersOfRole"];
  };
  "/userInfo/listUsersOfRoleFilterOrg": {
    post: operations["listUsersOfRoleFilterOrg"];
  };
  "/userInfo/portraitInfo": {
    post: operations["savePortraitInfo"];
  };
  "/userInfo/pushPlatform/{pushPlatform}": {
    get: operations["setPushPlatform"];
  };
  "/userInfo/searchUsersOfOrg": {
    post: operations["searchUsersOfOrg"];
  };
  "/userInfo/searchUsersOfRole": {
    post: operations["searchUsersOfRole"];
  };
  "/userInfo/userPortraitInfo/{username}": {
    get: operations["downloadPortrait"];
  };
}

export interface definitions {
  UnitPageParam: {
    /** Format: int32 */
    page: number;
    /** Format: int32 */
    size: number;
    orders: definitions["SortParam"][];
    /** Format: int32 */
    nodeType?: number;
    nodeCode?: string;
    /** Format: int32 */
    classification?: number;
    deptId?: string;
    pageRequest?: definitions["PageRequest"];
  };
  /** @description 不同维度统计项目数量返回值 */
  DeptSummaryResult: {
    /**
     * Format: int32
     * @description 统计类型0:状态 1:项目类型 2:项目属性 3:近两年在建项目数量
     */
    type?: number;
    /** @description 汇总信息 */
    deptSummaryItems?: definitions["DeptSummaryItemResult"][];
  };
  UnitInfoVo: {
    /** @description 节点id */
    nodeId?: string;
    /**
     * Format: int32
     * @description 节点类型，1标段，2单项，3单位
     */
    nodeType?: number;
    /** @description 项目部id */
    deptId?: string;
    /** @description 项目名称 */
    deptName?: string;
    /** @description 父节点id，空字符串表示跟节点 */
    parId?: string;
    /** @description 节点名称 */
    nodeName?: string;
    /**
     * Format: int32
     * @description 排序字段
     */
    sortOrder?: number;
    /** @description 节点编号 */
    nodeCode?: string;
    /** @description 合同编号 */
    contractCode?: string;
    /**
     * Format: int32
     * @description 类别
     */
    classification?: number;
    /** @description 合同金额(万元) */
    contractInvertment?: number;
    /** @description 施工单位 */
    constructionOrg?: string;
    /** @description 监理单位 */
    supervisorOrg?: string;
    /** @description 起始桩号 */
    startStation?: string;
    /** @description 终止桩号 */
    endStation?: string;
    /** @description 起点坐标 */
    startCoordinate?: string;
    /** @description 终点坐标 */
    endCoordinate?: string;
    /**
     * Format: date-time
     * @description 起始时间
     */
    startDate?: string;
    /**
     * Format: date-time
     * @description 结束时间
     */
    endDate?: string;
    /** @description 附件 */
    attachmentResults?: definitions["AttachmentResult"][];
  };
  CityInfo: {
    /**
     * Format: int32
     * @description 市ID
     */
    cityId?: number;
    /** @description 市名称 */
    city?: string;
    /**
     * Format: int32
     * @description 省ID
     */
    proviceId?: number;
  };
  /** @description 企业用户头像信息 */
  EpUserPortraitVo: {
    /**
     * Format: int32
     * @description 企业id
     */
    epid?: number;
    /** @description 企业名称 */
    enterpriseName?: string;
    /** @description 用户名 */
    userName?: string;
    /** @description 真实姓名 */
    realName?: string;
    /** @description 头像uuid */
    portraitUuid?: string;
    /**
     * Format: int32
     * @description 用户的来源，0:内部 ;1：外部人员
     */
    sourceType?: number;
  };
  /** @description 设备厂商信息 */
  IotManufacturerInfo: {
    /** @description 名称 */
    name?: string;
    /** @description code */
    code?: string;
    /** @description 扩展属性 */
    extAttribute?: string;
    /**
     * Format: int32
     * @description 排序号(1,2,3...)
     */
    sort?: number;
    /** @description 配置id */
    configId?: string;
  };
  ResponseResultListIotManufacturerInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["IotManufacturerInfo"][];
  };
  /** @description 设备请求参数 */
  IotDeviceRequest: {
    /** @description 项目部id */
    deptId?: string;
  };
  SortParam: {
    /** Format: int32 */
    direction: number;
    property: string;
    order?: definitions["Order"];
    directionStr?: string;
  };
  /** @description 摄像头信息 */
  IotVideoInfo: {
    /** @description 基础地址 */
    baseUrl?: string;
    /** @description 访问token */
    accessToken?: string;
    /** @description deptId */
    deptId?: string;
    /** @description 平台id */
    manufacturerId?: string;
    /** @description 摄像头类型：智慧工地、通用平台、萤石云 */
    type?: string;
    /** @description 摄像头id */
    id?: string;
    /** @description 名称 */
    name?: string;
    /** @description code */
    code?: string;
    /** @description assetId */
    assetId?: string;
    assetName?: string;
    /** @description schemaId */
    schemaId?: string;
    /** @description schemaName */
    schemaName?: string;
    /** @description templateSchemaName */
    templateSchemaName?: string;
    /**
     * Format: int32
     * @description isShow:1=在线，0=不在线
     */
    isShow?: number;
    /**
     * Format: int32
     * @description online:1=在线，0=不在线
     */
    online?: number;
    /** @description onLineName */
    onLineName?: string;
  };
  ResponseResultListEnterpriseUserResponse: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["EnterpriseUserResponse"][];
  };
  NewLogBean: {
    key?: string;
    value?: string;
  };
  ProjectNodeInfo: {
    nodeId?: string;
    /** Format: int32 */
    nodeType?: number;
    deptId?: string;
    parId?: string;
    nodeName?: string;
    /** Format: int32 */
    sortOrder?: number;
    ppids?: number[];
  };
  Motor3dStatusResult: {
    /**
     * Format: int32
     * @description 工程id
     */
    projid?: number;
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /**
     * Format: int32
     * @description motor模型转换状态-1: 待处理， 0：处理失败， 1：处理成功  2：处理中
     */
    status?: number;
    /** @description 转换结果 */
    handleInfo?: string;
    /** @description 转换类型 1:分级加载；2：静态模型 */
    type?: string;
    /** @description 转换操作 1：效果优先，3：性能优先 */
    option?: string;
    /** @description 文件uuid */
    fileUuid?: string;
    /**
     * Format: int32
     * @description 文件来源： 1-工程模型，2-dwg图纸
     */
    fileSource?: number;
    /** @description motor转换成功后此字段才有用 */
    motor3dRelationResult?: definitions["Motor3dRelationResult"];
  };
  ResponseResult天气基本信息: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["天气基本信息"];
  };
  ProjectComp: {
    /**
     * Format: int32
     * @description 企业id
     */
    epid?: number;
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 路径信息 */
    pathList?: string[];
    /** @description 名称列表 */
    compNamesList?: definitions["CompName"][];
  };
  /** @description motor转换进度 */
  ModelTransStatusResponse: {
    /** @description motor模型guid */
    motorModelGuid?: string;
    /**
     * Format: int32
     * @description 1 待转换；2 转换中；3 转换成功；4 转换失败；5 取消转换；
     */
    status?: number;
    /** @description 状态信息（转换成功或取消转换时，无状态信息） */
    statusInfo?: definitions["TransStatusInfoResponse"];
  };
  /** @description 工程属性控制设置传参 */
  ProjectAttrControlSetParam: {
    /**
     * Format: int32
     * @description 代理工程ID
     */
    ppid: number;
    /**
     * Format: int32
     * @description 工作集id
     */
    wsid?: number;
    /**
     * @description 是否显示空白属性(true:显示，false：不显示)
     * @default false
     */
    showBlank: boolean;
    /** @description 用户不勾选的属性ID,全勾选则不传 */
    ids?: string[];
  };
  ResponseResultListControlPointInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["ControlPointInfo"][];
  };
  EnterpriseUserResponse: {
    /** @description 通行证 */
    username?: string;
    /** @description 姓名 */
    realname?: string;
    /** @description 手机号 */
    mobile?: string;
    /** @description 邮箱 */
    mail?: string;
    /** @description 身份证 */
    identityCard?: string;
    /** @description 工作单位 */
    workUnit?: string;
    /** @description 单位类型 */
    unitType?: string;
    /** @description 通讯地址 */
    address?: string;
    /** @description 角色 */
    rolename?: string;
    /** @description 岗位 */
    posiotionList?: string[];
  };
  DeptEditParam: {
    /** @description 项目部名称 */
    name?: string;
    /** @description 项目经理 */
    managerName?: string;
    /** @description 手机号码 */
    mobile?: string;
    /** @description 开工时间 YYYY-MM-DD */
    startDateStr?: string;
    /** @description 项目竣工日期 YYYY-MM-DD */
    endDateStr?: string;
    /**
     * Format: date-time
     * @description 开工时间
     */
    startDate?: string;
    /**
     * Format: date-time
     * @description 竣工时间
     */
    endDate?: string;
    /**
     * Format: int32
     * @description 区县id
     */
    countyId?: number;
    /**
     * Format: int32
     * @description 城市id
     */
    cityId?: number;
    /**
     * Format: int32
     * @description 省份id
     */
    provinceId?: number;
    /**
     * Format: double
     * @description 建筑面积
     */
    area?: number;
    /** @description 里程 */
    mileage?: string;
    /**
     * Format: int32
     * @description 合同类型(1单价合同、2总价合同、3成本加酬金合同)
     */
    contractType?: number;
    /**
     * Format: int32
     * @description 状态(0未开工、1在建、2竣工)
     */
    status?: number;
    /** @description 建设单位 */
    buildOrg?: string;
    /** @description 代建单位 */
    proxyOrg?: string;
    /** @description 勘察单位 */
    surveyOrg?: string;
    /** @description 设计单位 */
    designOrg?: string;
    /** @description 监理单位 */
    supervisorOrg?: string;
    /** @description 施工单位 */
    constructionOrg?: string;
    /** @description 造价咨询 */
    costConsultation?: string;
    /** @description BIM咨询 */
    bimConsultation?: string;
    /**
     * Format: int32
     * @description 项目类型(1房建项目、2基建项目)
     */
    deptType?: number;
    /**
     * Format: int32
     * @description 项目属性(1一般项目(默认)、2重大项目、3高危项目)
     */
    deptProperties?: number;
    /** @description 备注 */
    remarks?: string;
    /** @description 立项批准文号 */
    approvalNum?: string;
    /** @description 建设项目编号 */
    projectNum?: string;
    /**
     * Format: double
     * @description 总投资(万元)
     */
    totalInvestment?: number;
    /**
     * Format: int32
     * @description 项目税金收费类别(市区、乡镇、其他。分别对应1,2,3)
     */
    taxesChargeType?: number;
    /** @description 项目部ID */
    deptId?: string;
  };
  TransStatusInfoResponse: {
    /** @description 转换失败原因 */
    error?: string;
    /**
     * Format: int32
     * @description 转换中时，转换进度信息
     */
    progress?: number;
    /** @description 待转换时，当前的排队信息 */
    queue?: string;
  };
  NodeResult: {
    /** @description 展现的节点名字 */
    name?: string;
    /** @description 节点的id */
    id?: string;
    /**
     * Format: int32
     * @description 节点类型 type = 1:公司/分公司 type = 2:项目部
     */
    type?: number;
    /** @description 父节点id */
    parentId?: string;
    /** @description 项目部编码 */
    deptCode?: string;
    /** @description 项目部所在地省对应的经纬度,中间以逗号分隔，(经度,纬度) */
    proviceCoordinate?: string;
    /** @description 项目部所在地市对应的经纬度,中间以逗号分隔，(经度,纬度) */
    cityCoordinate?: string;
    /** @description 项目部所在地区对应的经纬度,中间以逗号分隔，(经度,纬度) */
    countyCoordinate?: string;
    /**
     * Format: int32
     * @description 所在地
     */
    countyId?: number;
    /**
     * Format: int32
     * @description 城市id
     */
    cityId?: number;
    /** @description 城市名称 */
    cityName?: string;
    /**
     * Format: int32
     * @description 省份id
     */
    provinceId?: number;
    /** @description 经度 */
    longitude?: string;
    /** @description 纬度 */
    latitude?: string;
    /**
     * Format: int32
     * @description 排序
     */
    sortOrder?: number;
    /**
     * Format: int32
     * @description 项目数据类型(默认值为0,   1 房建项目、2 基建项目)
     */
    deptDataType?: number;
    /**
     * Format: int32
     * @description 状态(0拟建、1在建、2建成)
     */
    status?: number;
  };
  ResponseEntityCommonQRCodeControlPropertiesVo: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: definitions["CommonQRCodeControlPropertiesVo"];
  };
  /** @description 二维码控制属性 设置参数 */
  CommonQRCodeControlPropertySetParam: {
    /** @description 控制key */
    key?: string;
    /**
     * Format: int32
     * @description 控制开关属性: 0-关，1-开
     */
    switchControl?: number;
    /**
     * Format: int32
     * @description 二维码类型:1表构件二维码,2表巡检二维码,3材料二维码,4危险源二维码
     */
    type?: number;
  };
  /** @description 分组属性列表 */
  RevitGroupAttrInfo: {
    /** @description 属性类型名称 */
    attrTypeName?: string;
    /** @description 属性列表 */
    revitAttrs?: definitions["RevitAttr"][];
  };
  /** @description PDS新操作日志Rest接口 */
  PDSNewOperLogInfo: {
    /** @description 操作功能组 */
    operGroup?: string;
    /** @description 操作功能 */
    operFunction?: string;
    /** @description 操作功能 */
    operProcess?: string[];
    /**
     * Format: int32
     * @description 完成操作流程数，默认值0
     */
    processOperCount?: number;
    /**
     * Format: int32
     * @description 操作流程总数目，默认值0
     */
    processTotalCount?: number;
    /** @description 操作扩展信息 */
    extendInfos?: definitions["NewLogBean"][];
    /** @description 操作项目部及项目部以上级别时传项目部id */
    operDeptIds?: string[];
    /**
     * @description 日志是否需要记录到pds管理端日志中，true记录，false不记录，默认false
     * @default false
     */
    saveToPds?: boolean;
    /** @description 操作对象 */
    operObject?: string;
  };
  /** @description 工程详情 */
  ProjectDetailInfo: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 项目部id */
    deptId?: string;
    /** @description 工程名称 */
    projName?: string;
    /** @description 工程类型 */
    projType?: string;
    /** @description 文件uuid */
    fileuuid?: string;
    /**
     * Format: date-time
     * @description 上传时间
     */
    createDate?: string;
    /** @description 类型（取值：SLYS/SLSG） */
    projModel?: string;
    /**
     * Format: int32
     * @description 工程类型
     */
    projTypeInt?: number;
    /**
     * Format: int32
     * @description 节点类型 0：项目部 1：标段 2：单项 3：单位
     */
    nodeType?: number;
    /** @description 节点id */
    nodeId?: string;
    /**
     * Format: int32
     * @description 是否抽取部位树（0：没有 1：抽取）
     */
    isExtractLocationTree?: number;
  };
  ImageInfoVO: {
    /** @description logo图片列表 */
    logoList?: definitions["LogoInfo"][];
    /** @description 轮播图图片列表 */
    pptList?: definitions["PPTInfo"][];
  };
  ResponseResultString: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: string;
  };
  UploadInfo: {
    fileMd5?: string;
    /** Format: int64 */
    fileSize?: number;
    /** Format: int32 */
    fileFlag?: number;
    fileName?: string;
  };
  BVAttrTmplInfo: {
    attrGroupName?: string;
    subAttrInfos?: definitions["BVSubAttrTemplateInfo"][];
  };
  ResponseEntityListMotor3dStatusResult: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: definitions["Motor3dStatusResult"][];
  };
  /** @description 房建获取项目部下工程参数 */
  QryProjInfo4Iworksf: {
    /** @description 项目部id */
    deptList?: string[];
    /** @description 分页参数 */
    pgInfo?: definitions["BasicPage"];
  };
  web端下载pds文件: {
    /** @description 文件uuid */
    fileUuid?: string;
    /** @description 文件名称 */
    fileName?: string;
  };
  /** @description 项目数量统计返回值 */
  DeptSummaryItemResult: {
    /** @description 类型, 按项目状态:0未开工、1在建、2竣工、3待建、4代建,按项目类型:1 房建项目、2 基建项目,按项目属性:1一般项目 2重大项目、3高危项目,最近两年在建项目分布月份 */
    type?: string;
    /**
     * Format: int32
     * @description 数量
     */
    count?: number;
  };
  /** @description 构件缩略图信息保存参数 */
  CompPictureSaveParam: {
    /**
     * Format: int32
     * @description 代理工程ID
     */
    ppid: number;
    /** @description 楼层 */
    floor: string;
    /** @description handle */
    handle: string;
    /** @description 图片uuid */
    uuid: string;
    /**
     * Format: int64
     * @description 图片大小
     */
    fileSize: number;
    /** @description 图片MD5 */
    fileMd5: string;
  };
  /** @description UserPortraitVo */
  UserPortraitVo: {
    /** @description 用户名 */
    userName?: string;
    /** @description 真实姓名 */
    realName?: string;
    /** @description 头像uuid */
    portraitUuid?: string;
    /**
     * Format: int32
     * @description 用户的来源，0:内部 ;1：外部人员
     */
    sourceType?: number;
    /** @description 人员组织节点包含的所有项目部ids */
    deptIds?: string[];
  };
  Node: {
    name?: string;
    value?: string;
    /** Format: int32 */
    type?: number;
    parId?: string;
    children?: definitions["Node"][];
    deptCode?: string;
  };
  ResponseEntityBoolean: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /**
     * @description 响应数据
     * @default false
     */
    result?: boolean;
  };
  TrendsViewInfo: {
    fileName?: string;
    uuid?: string;
  };
  /** @description 工程信息 */
  ProjectItemResult: {
    /**
     * Format: int32
     * @description 工程id
     */
    projId?: number;
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 工程名称 */
    projName?: string;
    /**
     * Format: int32
     * @description 专业（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Remiz(班筑客户端上传)/8:班筑家装（PDS客户端）/9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:IFC/14:CATIA）
     */
    projType?: number;
    /** @description 更新人用户名 */
    updateUserName?: string;
    /** @description 更新人真实姓名 */
    updateRealName?: string;
    /**
     * Format: date-time
     * @description 更新时间
     */
    updateDate?: string;
    /**
     * Format: double
     * @description 工程文件大小
     */
    projSize?: number;
    /**
     * Format: int32
     * @description 轻量化抽取状态【-2:未处理，-1: 待处理， 0：处理失败， 1：处理成功  2：处理中】
     */
    extractStatus?: number;
    /** @description 转换操作 1：效果优先，3：性能优先 */
    priorityOption?: string;
    /** @description 转换类型 1:分级加载；2：静态模型 */
    motor3dType?: string;
    /** @description motor模型关联id */
    motor3dId?: string;
    /** @description 备注信息 */
    projMemo?: string;
    /**
     * Format: int32
     * @description 工程分类（2:pdf图纸工程；3:3D模型工程）
     */
    projClassify?: number;
    /** @description 工程属性（SLYS:算量预算/SLSG:算量施工/ZJMX:造价模型/-） */
    projModel?: string;
    /**
     * Format: int32
     * @description 工程归属节点类型 0：项目部 1：标段 2：单项 3：单位
     */
    nodeType?: number;
    /** @description 工程归属节点id */
    nodeId?: string;
    /** @description 工程归属节点名称 */
    nodeName?: string;
    /** @description 文件uuid */
    fileuuid?: string;
    /**
     * Format: int32
     * @description 是否抽取部位树（0：没有 1：抽取）
     */
    extractLocationTree?: number;
  };
  /** @description 用户工程属性设置信息 */
  RevitUserAttrRemote: {
    /**
     * @description 用户是否设置了revit属性显示控制(true:已设置，false：未设置)
     * @default false
     */
    isUserSetAttr?: boolean;
    /** @description 用户未设置的属性ID */
    noSelectIds?: string[];
    /**
     * @description 是否显示空白属性(true:显示，false：不显示)
     * @default false
     */
    showBlank?: boolean;
    /** @description 属性信息列表 */
    attrList?: definitions["RevitGroupAttrInfo"][];
  };
  ExcellentProjectVo: {
    deptId?: string;
    deptName?: string;
    uuid?: string;
  };
  下载请求参数: {
    /** @description 模型id和对应文件名的集合 */
    downloadProjParams?: definitions["web端下载pds文件"][];
  };
  /** @description 属性信息 */
  RevitAttr: {
    /** @description 属性ID */
    id?: string;
    /** @description 属性名称 */
    name?: string;
  };
  Motor3dRelationResult: {
    /** @description motor模型关联id */
    motor3dId?: string;
    /** @description motor模型版本号 */
    motor3d_version?: string;
    /**
     * Format: int32
     * @description 模型文件大小
     */
    motor3dFileSize?: number;
    /** @description motor转换工具版本号 */
    motorVersion?: string;
  };
  DeptEffectImageResult: {
    /** @description 文件名称 */
    fileName?: string;
    /** @description 文件uuid */
    fileUuid?: string;
    /** @description 文件md5 */
    fileMd5?: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize?: number;
    /**
     * @description 是否为封面:true/false
     * @default false
     */
    cover?: boolean;
    /** @description 效果图id */
    id?: string;
    /** @description 创建人 */
    createUser?: string;
    /**
     * Format: int64
     * @description 创建时间,时间戳毫秒值
     */
    createTime?: number;
    /** @description 修改人 */
    updateUser?: string;
    /**
     * Format: int64
     * @description 修改时间,时间戳毫秒值
     */
    updateTime?: number;
  };
  /** @description 构件属性组 */
  CompAttrGroupResult: {
    /** @description 组名称 */
    groupName?: string;
    /** @description 属性信息列表 */
    attrItems?: definitions["CompAttrItemResult"][];
    /** @description 组key（判断安装信息组件与基本信息的标识，安装可能返回多个组件信息，土建/钢筋只有一个） */
    groupKey?: string;
  };
  CompName: {
    /** @description 构件名称 */
    name?: string;
    /**
     * Format: int32
     * @description 构件个数
     */
    hlnum?: number;
    /** @description 构件handle列表 */
    handles?: string[];
  };
  /** @description 二维码控制属性 设置参数 */
  CommonQRCodeControlPropertyQueryParam: {
    /** @description 控制key */
    key?: string;
    /**
     * Format: int32
     * @description 二维码类型:1表构件二维码,2表巡检二维码,3材料二维码,4危险源二维码
     */
    type?: number;
  };
  ProjectInfo: {
    /** Format: int32 */
    treeProjectFlag?: number;
    /** Format: int32 */
    projId?: number;
    /** Format: int32 */
    ppid?: number;
    deptId?: string;
    projName?: string;
    projmd5?: string;
    projType?: string;
    projTypeStr?: string;
    fileuuid?: string;
    /** Format: date-time */
    createDate?: string;
    createUser?: string;
    /** Format: int32 */
    projclassify?: number;
    projModel?: string;
    /** Format: int32 */
    status?: number;
    projVersion?: string;
    projGuid?: string;
    /** Format: int32 */
    productId?: number;
    /** Format: double */
    projSize?: number;
    /** Format: int32 */
    extractStatus?: number;
    /** Format: int32 */
    projStatus?: number;
    remark?: string;
    /** Format: date-time */
    uploadDate?: string;
    /** Format: date-time */
    updateDate?: string;
    updateUser?: string;
    /** Format: int32 */
    packageType?: number;
    /** Format: int32 */
    projTypeInt?: number;
    /** Format: int32 */
    nodeType?: number;
    nodeId?: string;
    /** Format: int32 */
    isExtractLocationTree?: number;
  };
  ResponseResultIotVideoUrlResponse: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["IotVideoUrlResponse"];
  };
  /** @description 构件属性信息 */
  CompAttrResult: {
    /** @description 属性组列表 */
    attrGroups?: definitions["CompAttrGroupResult"][];
    /** @description 自定义构件属性 */
    attr?: definitions["AttrNode1_1"];
    /** @description 属性模板值列表 */
    attrTmplList?: definitions["BVAttrTmplInfo"][];
    /**
     * Format: int32
     * @description 产品ID
     */
    productId?: number;
    /** @description 楼层别名 */
    floorAlias?: string;
    /** @description 大类别名 */
    classAlias?: string;
    /** @description 构件图片uuid */
    uuid?: string;
  };
  /** @description 城市在建项目数量信息 */
  MapCityDeptNumResult: {
    /**
     * Format: int32
     * @description 城市id
     */
    cityId?: number;
    /** @description 城市名称 */
    cityName?: string;
    /** @description 经度 */
    longitude?: string;
    /** @description 纬度 */
    latitude?: string;
    /**
     * Format: int32
     * @description 项目部数量
     */
    deptNum?: number;
    /** @description 项目部id */
    deptIds?: string[];
  };
  FileDownloadInfo: {
    downloadUrls?: string[];
    fileName?: string;
    fileMD5?: string;
    /** Format: int64 */
    fileSize?: number;
    fileUUID?: string;
    /** Format: int32 */
    fileType?: number;
  };
  BasicPage: {
    /** Format: int32 */
    currentPage?: number;
    /** Format: int32 */
    pageSize?: number;
  };
  /** @description 搜索用户信息查询参数 */
  UsersOfRoleQueryParam: {
    /** @description 项目部ID,必传 */
    deptId?: string;
    /** @description 搜索抄送人关键字,不传则不限制 */
    searchKey?: string;
  };
  /** @description 工程信息 */
  ProjCommInfo: {
    /**
     * Format: int32
     * @description 代理工程Id
     */
    ppid?: number;
    /** @description 工程名字 */
    projName?: string;
    /** @description 项目部ID */
    deptId?: string;
  };
  /** @description 二维码开关设置传参 */
  QRCodeSwitchSetParam: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /**
     * @description 是否支持第三方扫码：true只支持App扫码,false支持第三方扫码
     * @default false
     */
    onlyBV: boolean;
    /**
     * @description 是否显示自定义属性: true显示, false不显示
     * @default false
     */
    showCustom: boolean;
    /**
     * @description 第三方扫码是否可以预览构件属性资料: true显示, false不显示
     * @default false
     */
    showDoc: boolean;
    /**
     * @description 是否生成构件缩略图: true生成, false不生成
     * @default false
     */
    generatePicture: boolean;
  };
  LogoInfo: {
    /** @description 图片uuid */
    uuid?: string;
    /** @description 图片md5 */
    md5?: string;
    /**
     * Format: int32
     * @description logo类型(位置): 1:登录页 | 2:加载页 | 3:加载页标语 | 4:首页 | 5:操作页
     */
    type?: number;
  };
  ProjNameVo: {
    /**
     * Format: int32
     * @description 代理工程Id
     */
    ppid?: number;
    /** @description 工程名称 */
    projName?: string;
    /**
     * Format: int32
     * @description 工程类型
     */
    projType?: number;
    /**
     * Format: int32
     * @description 是否抽取部位树（0：没有 1：抽取）
     */
    extractLocationTree?: number;
  };
  /** @description 用户信息 */
  UserMailVo: {
    /** @description 用户名 */
    userName?: string;
    /** @description 真实名 */
    realName?: string;
    /** @description 邮箱 */
    mail?: string;
  };
  ResponseEntity: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: { [key: string]: unknown };
  };
  UserPortraitParam: {
    username?: string;
    fileUUID?: string;
    fileName?: string;
    /** Format: int32 */
    fileSize?: number;
    md5?: string;
  };
  UserAccountRequest: {
    /** @description 用户名 */
    username: string;
    /** @description 密码 */
    password: string;
    /** @description 部门id */
    deptId: string;
  };
  /** @description 用户角色信息（角色+头像uuid） */
  UsersOfRoleVo: {
    /** @description 角色名称 */
    role?: string;
    /** @description 该角色下用户列表 */
    users?: definitions["UserPortraitVo"][];
  };
  TreeNode: {
    /** @description 节点名称 */
    name?: string;
    /** @description 关联构件树字段，楼层、专业、大类、小类、属性 */
    rel?: string;
    /** @description 扩展属性 */
    ext?: string;
    /**
     * Format: int32
     * @description 节点层级
     */
    level?: number;
    /** @description 子节点List */
    treeNodeList?: definitions["TreeNode"][];
  };
  WeatherResult: {
    /** @description 空气质量指数 */
    aqi?: string;
    /** @description 空气质量指数类别，有“优、良、轻度污染、中度污染、重度污染、严重污染”6类 */
    quality?: string;
    /** @description 空气湿度 */
    sd?: string;
    /** @description 当前气温 */
    temperature?: string;
    /** @description 天气文字标识 */
    weather?: string;
    /** @description 天气图片地址 */
    weatherPic?: string;
    /** @description 风向名称 */
    windDirection?: string;
    /** @description 风力 */
    windPower?: string;
  };
  InfraDeptResult: {
    /** @description 项目部id */
    deptId?: string;
    /** @description 项目名称 */
    name?: string;
    /** @description 项目编号 */
    projectCode?: string;
    /** @description 项目简称 */
    shortName?: string;
    /**
     * Format: int32
     * @description 状态(0拟建、1在建、2建成)
     */
    status?: number;
    /**
     * Format: int32
     * @description 项目类型(0 默认、1 高速公路、2 一级公路 3 二级公路 4 三级公路 5 四级公路 6 市政公路)
     */
    deptType?: number;
    /** @description 起点坐标 */
    startCoordinate?: string;
    /** @description 中点坐标 */
    middleCoordinate?: string;
    /** @description 终点坐标 */
    endCoordinate?: string;
    /**
     * Format: date-time
     * @description 起始时间
     */
    startDate?: string;
    /**
     * Format: date-time
     * @description 结束时间
     */
    endDate?: string;
    /**
     * Format: int32
     * @description 工期，单位为月
     */
    duration?: number;
    /** @description 概算金额(万元) */
    estimateInvestment?: number;
    /** @description 建安金额(万元) */
    jiananInvertment?: number;
    /** @description 决算金额(万元) */
    totalInvertment?: number;
    /** @description 建设单位 */
    buildOrg?: string;
    /** @description 设计单位 */
    designOrg?: string;
    /** @description 质量监督单位 */
    supervisorOrg?: string;
    /** @description 项目负责人 */
    chargePerson?: string;
    /** @description 项目联系人 */
    contactPerson?: string;
    /** @description 电话号码 */
    mobile?: string;
    /** @description 联系地址 */
    contactAddress?: string;
    /** @description 邮编 */
    zipCode?: string;
    /** @description 创建人 */
    createUser?: string;
    /**
     * Format: date-time
     * @description 创建时间
     */
    createTime?: string;
    /** @description 最后修改人 */
    modifyUser?: string;
    /**
     * Format: date-time
     * @description 最后修改时间
     */
    modifyTime?: string;
    /** @description 项目概况 */
    remarks?: string;
    /** @description 附件 */
    attachments?: definitions["AttachmentResult"][];
    /** @description 进度 */
    progress?: string;
    /**
     * Format: int32
     * @description 标段数量
     */
    sectionNum?: number;
    /** @description 项目里程 */
    projectMilestone?: string;
  };
  /** @description 根据摄像头信息获取视频流url返回参数 */
  IotVideoUrlResponse: {
    /** @description rtmp地址 */
    rtmp?: string;
    /** @description flv地址 */
    flvAddress?: string;
    /** @description hlsUrl地址 */
    hlsUrl?: string;
    /** @description rtmpHd地址 */
    rtmpHd?: string;
    /** @description hdFlvAddress地址 */
    hdFlvAddress?: string;
    /** @description 基础地址 */
    baseUrl?: string;
    /** @description 访问token */
    accessToken?: string;
    /** @description assetId */
    assetId?: string;
  };
  DeptEffectImageInfo: {
    /** @description 文件名称 */
    fileName?: string;
    /** @description 文件uuid */
    fileUuid?: string;
    /** @description 文件md5 */
    fileMd5?: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize?: number;
    /**
     * @description 是否为封面:true/false
     * @default false
     */
    cover?: boolean;
  };
  PageResultUnitInfoVo: {
    /** Format: int64 */
    total?: number;
    content?: definitions["UnitInfoVo"][];
  };
  DeptDetailInfo: {
    /** @description 项目部名称 */
    name?: string;
    /** @description 项目经理 */
    managerName?: string;
    /** @description 手机号码 */
    mobile?: string;
    /** @description 开工时间 YYYY-MM-DD */
    startDateStr?: string;
    /** @description 项目竣工日期 YYYY-MM-DD */
    endDateStr?: string;
    /**
     * Format: date-time
     * @description 开工时间
     */
    startDate?: string;
    /**
     * Format: date-time
     * @description 竣工时间
     */
    endDate?: string;
    /**
     * Format: int32
     * @description 区县id
     */
    countyId?: number;
    /**
     * Format: int32
     * @description 城市id
     */
    cityId?: number;
    /**
     * Format: int32
     * @description 省份id
     */
    provinceId?: number;
    /**
     * Format: double
     * @description 建筑面积
     */
    area?: number;
    /** @description 里程 */
    mileage?: string;
    /**
     * Format: int32
     * @description 合同类型(1单价合同、2总价合同、3成本加酬金合同)
     */
    contractType?: number;
    /**
     * Format: int32
     * @description 状态(0未开工、1在建、2竣工)
     */
    status?: number;
    /** @description 建设单位 */
    buildOrg?: string;
    /** @description 代建单位 */
    proxyOrg?: string;
    /** @description 勘察单位 */
    surveyOrg?: string;
    /** @description 设计单位 */
    designOrg?: string;
    /** @description 监理单位 */
    supervisorOrg?: string;
    /** @description 施工单位 */
    constructionOrg?: string;
    /** @description 造价咨询 */
    costConsultation?: string;
    /** @description BIM咨询 */
    bimConsultation?: string;
    /**
     * Format: int32
     * @description 项目类型(1房建项目、2基建项目)
     */
    deptType?: number;
    /**
     * Format: int32
     * @description 项目属性(1一般项目(默认)、2重大项目、3高危项目)
     */
    deptProperties?: number;
    /** @description 备注 */
    remarks?: string;
    /** @description 立项批准文号 */
    approvalNum?: string;
    /** @description 建设项目编号 */
    projectNum?: string;
    /**
     * Format: double
     * @description 总投资(万元)
     */
    totalInvestment?: number;
    /**
     * Format: int32
     * @description 项目税金收费类别(市区、乡镇、其他。分别对应1,2,3)
     */
    taxesChargeType?: number;
  };
  ResponseResultListString: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: string[];
  };
  LBPageInfo: {
    /**
     * Format: int32
     * @description 每页显示多少条， 必传
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 当前页数，必传，默认1
     */
    currentPage?: number;
    /**
     * Format: int32
     * @description 总共有多少页，不传
     */
    totalPage?: number;
    /**
     * Format: int32
     * @description 总数，不传
     */
    totalNumber?: number;
  };
  ResponseEntityString: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: string;
  };
  /** @description 用户组织信息（角色+头像uuid） */
  UsersOfOrgItem: {
    /** @description 当前节点的ID */
    id?: string;
    /** @description 父节点id */
    parentId?: string;
    /** @description 组织节点名称（项目部或分公司的名称） */
    name?: string;
    /**
     * Format: int32
     * @description 节点类型（0表示总/分公司，1表示项目部）
     */
    type?: number;
    /**
     * Format: int32
     * @description 数据类型：1、组织数据类型2、项目部门数据类型
     */
    dataType?: number;
    /**
     * Format: int32
     * @description 同级下节点顺序
     */
    sortOrder?: number;
    /** @description 组织人员信息 */
    users?: definitions["UserPortraitVo"][];
  };
  /** @description 根据摄像头信息获取视频流url请求参数 */
  IotVideoBindPpidRequest: {
    /** @description 平台id（可为空，但是不能和cameraIds同时为空） */
    manufacturerId?: string;
    /** @description 摄像头ids（可为空，但是不能和平台id同时为空） */
    cameraIds?: string[];
  };
  ResponseResultListEnterpriseOrgInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["EnterpriseOrgInfo"][];
  };
  /** @description 省份在建项目数量信息 */
  MapProvinceDeptNumResult: {
    /**
     * Format: int32
     * @description 省ID
     */
    provinceId?: number;
    /** @description 省名称 */
    provinceName?: string;
    /** @description 经度 */
    longitude?: string;
    /** @description 纬度 */
    latitude?: string;
    /**
     * Format: int32
     * @description 项目部数量
     */
    deptNum?: number;
    /** @description 项目部id */
    deptIds?: string[];
  };
  /** @description 工程描述信息 */
  ProjDescInfo: {
    /**
     * Format: int32
     * @description 代理主键id
     */
    ppid?: number;
    /** @description 工程描述 */
    desc?: string;
  };
  /** @description 获取小类下构件名称层级查询条件 */
  ComponentNameParam: {
    /**
     * Format: int32
     * @description 代理工程Id
     */
    ppid?: number;
    /** @description 楼层 */
    floor?: string;
    /** @description 大类 */
    compClass?: string;
    /** @description 小类 */
    subClass?: string;
  };
  DownloadURLParam: {
    fileUUIDList?: string[];
    /** Format: int32 */
    fileType?: number;
    /** Format: int32 */
    channelType?: number;
  };
  /** @description 工程信息 */
  ProjWithUserAuthResult: {
    /**
     * Format: int32
     * @description 工程id
     */
    ppid?: number;
    /** @description 工程名称 */
    projName?: string;
    /**
     * @description 是否有权限，true为有，false为无
     * @default false
     */
    isAuth?: boolean;
    /** @description 项目部id */
    deptId?: string;
  };
  UserAccountParam: {
    /** @description 用户名 */
    username: string;
    /** @description 密码 */
    password: string;
  };
  ProjSummaryInfo: {
    /** @description 唯一key */
    uuidKey?: string;
    /** @description 父节点key，空字符串表示跟节点 */
    parentKey?: string;
    /**
     * Format: int32
     * @description 0父节点，1字符串，2日期类型，3字符串不可修改，4Double类型，5下拉列表框
     */
    type?: number;
    /** @description 名字 */
    name?: string;
    /** @description 值，仅子节点值有效 */
    value?: string;
  };
  ResponseEntityMotor3dStatusResult: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: definitions["Motor3dStatusResult"];
  };
  MotorModelStatusCallbackParam: {
    id?: string;
    name?: string;
    appId?: string;
    /** Format: int32 */
    status?: number;
    /** Format: int32 */
    progress?: number;
    metaType?: string;
    type?: string;
    fileId?: string;
    fileName?: string;
    transId?: string;
    /** Format: int32 */
    version?: number;
    updateBy?: string;
    /** Format: date-time */
    updateDate?: string;
    createBy?: string;
    /** Format: date-time */
    createDate?: string;
    /** Format: int32 */
    delFlag?: number;
    result?: string;
    /** Format: int64 */
    size?: number;
  };
  /** @description 最近打开工程信息 */
  ProjectInfoOfRecentOpenedResult: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 工程名称 */
    projName?: string;
    /** @description 工程图片uuid */
    projPicUuid?: string;
    /**
     * Format: int32
     * @description 专业（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Remiz(班筑客户端上传)/8:班筑家装（PDS客户端）/9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:IFC/14:CATIA）
     */
    projType?: number;
    /**
     * Format: double
     * @description 工程文件大小
     */
    projSize?: number;
    /**
     * Format: int32
     * @description 工程分类（2:pdf图纸工程；3:3D模型工程）
     */
    projClassify?: number;
    /** @description 工程属性（SLYS:算量预算/SLSG:算量施工/ZJMX:造价模型/-） */
    projModel?: string;
    /**
     * Format: int32
     * @description 工程归属节点类型 0：项目部 1：标段 2：单项 3：单位
     */
    nodeType?: number;
    /** @description 工程归属节点id */
    nodeId?: string;
    /** @description 工程归属节点名称 */
    nodeName?: string;
    /**
     * Format: int32
     * @description 轻量工程处理状态:(-2:未处理， -1: 待处理， 0：处理失败， 1：处理成功  2：处理中)
     */
    extractStatus?: number;
    /** @description motor模型关联id */
    motor3dId?: string;
    /**
     * Format: int32
     * @description pds模型抽取状态：-1:失败  1:成功 2:处理中(C++抽取) 3:处理中(java导入和聚合) 0未处理/待处理/处理中 【工程未删除，才有值】
     */
    pdsStatus?: number;
    /**
     * @description pds模型是否变更了
     * @default false
     */
    hashUpdated?: boolean;
    /**
     * @description 是否有工程权限
     * @default false
     */
    hashAuth?: boolean;
    /**
     * @description 工程是否删除了
     * @default false
     */
    hashDeleted?: boolean;
    /**
     * Format: int32
     * @description 是否抽取部位树（0：没有 1：抽取）
     */
    extractLocationTree?: number;
  };
  /** @description 构件唯一参数 */
  CompUniqueParam: {
    /**
     * Format: int32
     * @description 代理工程ID
     */
    ppid: number;
    /** @description 楼层 */
    floor: string;
    /** @description handle */
    handle: string;
  };
  UploadInfoEx: {
    fileMd5?: string;
    fileName?: string;
    /** Format: int64 */
    fileSize?: number;
    suportModes?: number[];
    /** @default false */
    isBPUpload?: boolean;
    /** @default false */
    isCheckFastUpload?: boolean;
    extraData?: definitions["LbExtraData"];
  };
  RelationBimProjectApiInfo: {
    /** Format: int32 */
    projId?: number;
    /** Format: int32 */
    ppid?: number;
    motor3dId?: string;
    motor3dVersion?: string;
    /** Format: int32 */
    motor3dFileSize?: number;
    motorVersion?: string;
    fileUuid?: string;
  };
  /** @description 工程构件层级查询条件 */
  ComponentHierarchyQueryParam: {
    /**
     * Format: int32
     * @description 代理工程Id
     */
    ppid?: number;
    /**
     * Format: int32
     * @description 工程类型
     */
    projType?: number;
  };
  LbExtraData: {
    key?: string;
    values?: definitions["KVPair"][];
  };
  DeptEffectImageNameCheckParam: {
    /** @description 当前组织机构Id */
    orgId?: string;
    /** @description 项目部效果图名称列表 */
    effectImageNames?: string[];
  };
  CompGroupItem: {
    compGroupId?: string;
    name?: string;
    /** Format: date-time */
    updateTime?: string;
    /** Format: date-time */
    finishTime?: string;
    attrValue?: string;
  };
  InfraDeptCoordinateResult: {
    /** @description 项目部id */
    deptId?: string;
    /** @description 项目名称 */
    name?: string;
    /**
     * Format: int32
     * @description 状态(0拟建、1在建、2建成)
     */
    status?: number;
    /** @description 起点坐标 */
    startCoordinate?: string;
    /** @description 中点坐标 */
    middleCoordinate?: string;
    /** @description 终点坐标 */
    endCoordinate?: string;
    /** @description 项目里程 */
    projectMilestone?: string;
  };
  ProviceCityCounty: {
    /** @description 省列表 */
    provices?: definitions["ProviceInfo"][];
    /** @description 市列表 */
    citys?: definitions["CityInfo"][];
    /** @description 区县列表 */
    countys?: definitions["CountyInfo"][];
  };
  /** @description 工程列表分页查询返回值 */
  ProjectPageResult: {
    /**
     * Format: int64
     * @description 总数
     */
    total?: number;
    /** @description 列表 */
    items?: definitions["ProjectItemResult"][];
  };
  ProjSummaryParam: {
    /** @description 唯一key */
    uuidKey?: string;
    /** @description 值，仅子节点值有效 */
    value?: string;
  };
  Sort: { [key: string]: unknown };
  AttrNode1_1: {
    ik?: string;
    iv?: string;
    /** Format: int32 */
    it?: number;
    pv?: string;
    cpk?: string;
    subList?: definitions["AttrNode1_1"][];
    docInfo?: definitions["DocInfo"];
  };
  ResponseResultListIotVideoInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["IotVideoInfo"][];
  };
  ComponentNode: {
    path?: string;
    parentPath?: string;
    /** @description 部位名称 */
    nd?: string;
    /** @description 部位别名 */
    al?: string;
    /** @description 子部位 */
    ch?: definitions["ComponentNode"][];
  };
  ResponseResultListDeptEffectImageResult: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["DeptEffectImageResult"][];
  };
  ResponseEntityListBIMProjectItemVo: {
    /**
     * Format: int32
     * @description 响应状态码
     */
    code?: number;
    /**
     * @description 成功/失败（true/false）
     * @default false
     */
    success?: boolean;
    /** @description 响应消息 */
    msg?: string;
    /** @description 响应数据 */
    result?: definitions["BIMProjectItemVo"][];
  };
  /** @description 二维码控制属性 */
  CommonQRCodeControlPropertiesVo: {
    /** @description 控制key */
    key?: string;
    /**
     * Format: int32
     * @description 控制开关属性: 0-关，1-开（默认开）
     */
    switchControl?: number;
    /**
     * Format: int32
     * @description 二维码类型:1表构件二维码,2表巡检二维码,3材料二维码,4危险源二维码
     */
    type?: number;
  };
  PPTInfo: {
    /** @description 图片uuid */
    uuid?: string;
    /** @description 图片md5 */
    md5?: string;
    /**
     * Format: int32
     * @description 顺序
     */
    sort?: number;
    /** @description 图片链接地址 */
    linkUrl?: string;
  };
  ProjectCompParam: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 部位树path */
    path?: string[];
    /**
     * Format: int32
     * @description 页条数
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 当前页码
     */
    pageNo?: number;
  };
  UserOperLog: {
    functionGroup?: string;
    function?: string;
    extendInfos?: definitions["NewLogBean"][];
    ppids?: number[];
    operdeptIds?: string[];
  };
  /** @description 分页查询构建组返回值 */
  CompGroupPageResult: {
    pageInfo?: definitions["LBPageInfo"];
    compGroupShorts?: definitions["CompGroupItem"][];
  };
  /** @description 获取工程图片信息参数 */
  ListProjectFileInfoParam: {
    /** @description 代理工程id */
    ppids?: number[];
    /** @description 类型 hsfpic缩略图 hsfpic_big 大图 */
    type?: string;
  };
  DeptEffectImageSaveParam: {
    /** @description 当前组织机构所属的项目部Id */
    deptId?: string;
    /** @description 当前组织机构Id */
    orgId?: string;
    /**
     * Format: int32
     * @description 当前组织机构类型（1:集团，2：分公司，3：项目部，4：标段，5：单项，6：单位，7：模型）
     */
    orgType?: number;
    /** @description 项目部效果图信息 */
    deptEffectImageInfos?: definitions["DeptEffectImageInfo"][];
  };
  /** @description 工程列表分页查询参数 */
  ProjectPageParam: {
    /**
     * Format: int32
     * @description 页大小，默认15
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 页码，默认1
     */
    pageNum?: number;
    /**
     * Format: int32
     * @description 轻量化抽取状态【-2: 未处理，-1: 待处理， 0：处理失败， 1：处理成功  2：处理中】，不传不过滤
     */
    extractStatus?: number;
    /**
     * Format: date-time
     * @description 更新时间-起始，不传不过滤
     */
    updateDateStart?: string;
    /**
     * Format: date-time
     * @description 更新时间-截止，不传不过滤
     */
    updateDateEnd?: string;
    /** @description 组织节点id，不传不过滤 */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型:分公司1、项目部 2，不传不过滤
     */
    nodeType?: number;
    /** @description 需要过滤的节点类型：项目部、标段、单项、单位 */
    filterNodeIds?: string[];
    /** @description 搜索关键字（工程名称/更新人），不传不过滤 */
    searchKey?: string;
  };
  ProviceInfo: {
    /**
     * Format: int32
     * @description 省ID
     */
    provinceId?: number;
    /** @description 省名称 */
    province?: string;
  };
  /** @description 字段集键值对 */
  QRCodeKV: {
    /** @description 键 */
    key?: string;
    /** @description 值 */
    value?: string;
  };
  CountyInfo: {
    /**
     * Format: int32
     * @description 区县ID
     */
    countyId?: number;
    /**
     * Format: int32
     * @description 市ID
     */
    cityId?: number;
    /** @description 区县名称 */
    county?: string;
  };
  /** @description 根据摄像头信息获取视频流url请求参数 */
  IotVideoUrlRequest: {
    /** @description 平台id */
    manufacturerId?: string;
    /** @description 摄像头id */
    cameraId?: string;
    /** @description 项目部id */
    deptId?: string;
    /** @description 摄像头类型：智慧工地、通用平台、萤石云 */
    type?: string;
  };
  /** @description iworks1.7.0获取下载地址参数 */
  DownloadURL4IworksParam: {
    /**
     * Format: int32
     * @description 资料所在文件夹所属组织节点类型：1项目部、2单位、3单项、4标段、5工程(目前文件夹仅关联到项目部，资料都会关联)
     */
    nodeType?: number;
    /** @description 资料所在文件夹所属组织节点id(当前仅指项目部) */
    nodeId?: string;
    /** @description uuid集合 */
    uuidList?: string[];
    /** @description 资料目录id集合 */
    pathIdList?: string[];
    /**
     * Format: int32
     * @description 1、当前企业文件，3、鲁班云端公共的文件
     */
    type?: number;
  };
  TriggerMotor3dParam: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /**
     * Format: int32
     * @description 工程id,fileSource=1时才传
     */
    projid?: number;
    /** @description 转换类型 1:分级加载；2：静态模型；3：DWG */
    type?: string;
    /** @description 转换操作 1：效果优先，3：性能优先 */
    option?: string;
    /** @description 需抽取的文件uuid,fileSource=2时才传 */
    fileUuid?: string;
    /**
     * Format: int32
     * @description 文件来源 1-模型， 2-dwg图纸
     */
    fileSource: number;
  };
  ProjectTree: {
    /**
     * Format: int32
     * @description 企业id
     */
    epid?: number;
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 部位树，后续会扩展构件树 */
    type?: string;
    /** @description 树节点信息 */
    treeNodeList?: definitions["TreeNode"][];
    /**
     * Format: int32
     * @description 是否抽取部位树（0：没有 1：抽取）
     */
    isExtractLocationTree?: number;
    /**
     * Format: int32
     * @description 工程类型
     */
    projType?: number;
  };
  ResponseResultProviceCityCounty: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["ProviceCityCounty"];
  };
  ResponseResultImageInfoVO: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["ImageInfoVO"];
  };
  Order: {
    /** @enum {string} */
    direction?: "ASC" | "DESC";
    property?: string;
    /** @default false */
    ignoreCase?: boolean;
    /** @enum {string} */
    nullHandling?: "NATIVE" | "NULLS_FIRST" | "NULLS_LAST";
    /** @default false */
    ascending?: boolean;
    /** @default false */
    descending?: boolean;
  };
  /** @description BIM工程item信息 */
  BIMProjectItemVo: {
    /**
     * Format: int32
     * @description 代理工程Id
     */
    ppid?: number;
    /**
     * Format: int32
     * @description 工程类型（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Remiz(班筑客户端上传)/8:班筑家装（PDS客户端）/9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:IFC）
     */
    projType?: number;
    /** @description 工程名称 */
    projName?: string;
    /**
     * Format: int32
     * @description 工程分类  2:代表2D 图纸工程； 3:代表3D BIM模型工程
     */
    projclassify?: number;
    /**
     * Format: int32
     * @description 是否抽取部位树:0-否，1-是
     */
    isExtractLocationTree?: number;
    /** @description 类型（取值：SLYS/SLSG） */
    projModel?: string;
  };
  SummaryInfoEditParam: {
    /** @description id参数 */
    keyParam?: definitions["OrgIdKeyParam"];
    /** @description 概况信息参数 */
    summaryInfos?: definitions["ProjSummaryParam"][];
  };
  /** @description motor模型转换触发传参 */
  MotorModelTransParam: {
    /** @description 业务资源id，用来与motor模型id绑定关系(比如projId, dwg图纸id) */
    sourceId: string;
    /** @description 资源名称(比如：工程名称，dwg图纸) */
    sourceName: string;
    /**
     * @description 资源类型：PDS,
     * RVT,
     * IFC,
     * IMGTILES,  卫片DOM
     * 3DTILES,   倾斜摄影
     * TERTILES,  地形DEM
     * FBX,
     * LBG,
     * DWG,
     */
    motorSubType: string;
    /** @description 业务资源文件uuid,用于抽取时下载资源（模型文件抽取时必传） */
    fileUuid?: string;
    /** @description 链入GIS地址（链入GIS时必传） */
    linkUrl?: string;
  };
  /** @description 分页查询构件组-查询参数 */
  CompGroupPageParam: {
    /**
     * Format: int32
     * @description 工程id
     */
    ppid?: number;
    /**
     * Format: int32
     * @description 当前页
     */
    page?: number;
    /**
     * Format: int32
     * @description 页大小
     */
    pageSize?: number;
    /** @description 搜索关键字 */
    searchKey?: string;
  };
  CompPictureInfo: {
    uuid?: string;
    /** Format: int64 */
    fileSize?: number;
    fileMd5?: string;
    projMd5?: string;
  };
  /** @description 构件属性查询条件 */
  CompAtrrQueryParam: {
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid?: number;
    /** @description 楼层 */
    floor?: string;
    /** @description 大类 */
    compClass?: string;
    /** @description 小类 */
    subClass?: string;
    /** @description 构件handle */
    handle?: string;
  };
  /** @description 工程基础信息 */
  ProjectCommonInfo: {
    /** Format: int32 */
    projId?: number;
    /** Format: int32 */
    ppid?: number;
    deptId?: string;
    projName?: string;
    projmd5?: string;
    projType?: string;
    projTypeStr?: string;
    fileuuid?: string;
    /** Format: date-time */
    createDate?: string;
    createUser?: string;
    /** Format: int32 */
    projclassify?: number;
    projModel?: string;
    /** Format: int32 */
    status?: number;
    projVersion?: string;
    projGuid?: string;
    /** Format: int32 */
    productId?: number;
    /** Format: double */
    projSize?: number;
    /** Format: int32 */
    lightWeightExtractStatus?: number;
    /** Format: int32 */
    projStatus?: number;
    remark?: string;
    /** Format: date-time */
    uploadDate?: string;
    /** Format: date-time */
    updateDate?: string;
    updateUser?: string;
    /** Format: int32 */
    packageType?: number;
    /** Format: int32 */
    projTypeInt?: number;
    /** Format: int32 */
    nodeType?: number;
    nodeId?: string;
    /** Format: int32 */
    isExtractLocationTree?: number;
  };
  /** @description 项目部下工程组织树 */
  OrgProjNodeVo: {
    /** @description 节点id */
    id?: string;
    /**
     * Format: int32
     * @description 节点类型，0项目部，1标段，2单项工程，3单位工程
     */
    type?: number;
    /** @description 父节点id，为null表示根节点 */
    parentId?: string;
    /** @description 节点名称 */
    name?: string;
    /**
     * Format: int32
     * @description 排序字段
     */
    sortOrder?: number;
    /** @description 工程列表 */
    projects?: definitions["ProjNameVo"][];
  };
  天气基本信息: {
    /** @description 所在省 */
    province?: string;
    /** @description 所在市 */
    city?: string;
    /** @description 当前温度 */
    temperature?: string;
    /** @description 天气文字标识 */
    weather?: string;
  };
  ResponseResultWeatherResult: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["WeatherResult"];
  };
  DocInfo: {
    docId?: string;
    /** Format: int32 */
    ppid?: number;
    /** Format: int32 */
    enterpriseId?: number;
    fileuuid?: string;
    filename?: string;
    extension?: string;
    /** Format: int64 */
    filesize?: number;
    fileMD5?: string;
    /** Format: int32 */
    fileType?: number;
    /** Format: int32 */
    docType?: number;
    createUser?: string;
    createTime?: string;
    modifyUser?: string;
    modifyTime?: string;
    /** Format: int32 */
    relType?: number;
    tags?: definitions["DocTag"][];
    weaveTime?: string;
    /** @default false */
    isPreview?: boolean;
    thumbnailUuid?: string;
    /**
     * Format: int64
     * @description 资料修改时间戳（mylubanApp5.10.0新增）
     */
    modifyTimeStamp?: number;
    /** @description 工程名称（iworksApp1.0.0新增） */
    projName?: string;
    /**
     * Format: int32
     * @description 工程模型（iworksApp1.0.0新增）
     */
    projModel?: number;
  };
  DocTag: {
    tagId?: string;
    tagName?: string;
  };
  OrgDeptInfo: {
    /** @description 节点id */
    orgId?: string;
    /** @description 节点name */
    orgName?: string;
    /**
     * Format: int32
     * @description 组织类型 1公司分公司，2项目部
     */
    orgType?: number;
    /** @description 子节点信息列表 */
    childList?: definitions["OrgDeptInfo"][];
  };
  ControlPointInfo: {
    id?: string;
    controlName?: string;
    /** Format: int32 */
    cameraType?: number;
    floor?: string;
    /** Format: double */
    xaxis?: number;
    /** Format: double */
    yaxis?: number;
    /** Format: int32 */
    level?: number;
    /** Format: int32 */
    ppid?: number;
    /** Format: int32 */
    epid?: number;
    memo?: string;
    cameraKeys?: string[];
  };
  KVPair: {
    key?: string;
    value?: string;
  };
  EnterpriseOrgInfo: {
    /**
     * Format: int32
     * @description 企业id
     */
    epid?: number;
    /** @description 企业名称 */
    epname?: string;
    /** @description 组织信息 */
    orgInfo?: definitions["OrgDeptInfo"];
    /** @description 联系人 */
    contract?: string;
    /** @description 手机号 */
    phone?: string;
    /** @description 企业组织机构代码 */
    creditCode?: string;
  };
  /** @description 构件handle详情 */
  ComponentDetailResult: {
    /** @description 楼层 */
    fl?: string;
    /** @description 专业 */
    pf?: string;
    /** @description 大类 */
    cc?: string;
    /** @description 小类 */
    scc?: string;
    /** @description 构件名称 */
    cn?: string;
    /** @description handle */
    hl?: string;
  };
  AttachmentResult: {
    uuid?: string;
    fileName?: string;
    /** Format: int64 */
    fileSize?: number;
  };
  UploadUrlEx: {
    fileMd5?: string;
    /** @default false */
    finished?: boolean;
    fileUUID?: string;
    uploadUrls?: string[];
    /** Format: int32 */
    uploadMode?: number;
    /** Format: int64 */
    fileSize?: number;
    /** Format: int64 */
    offset?: number;
    headers?: definitions["KVPair"][];
    moreParams?: definitions["KVPair"][];
  };
  UploadFileAddressResult: {
    md5?: string;
    uploadUrl?: string;
  };
  /** @description 二维码扫描权限开关控制信息 */
  SwitchInfo: {
    /**
     * @description 是否仅支持App扫描: true仅App支持,false支持第三方 （默认支持第三方）
     * @default false
     */
    onlyBV?: boolean;
    /**
     * @description 是否显示自定义属性: true显示, false不显示 （默认显示自定义属性）
     * @default false
     */
    showCustom?: boolean;
    /**
     * @description 第三方扫码是否可以预览构件属性资料: true显示, false不显示 （默认不可以预览资料）
     * @default false
     */
    showDoc?: boolean;
    /**
     * @description 是否生成构件缩略图: true生成, false不生成
     * @default false
     */
    generatePicture: boolean;
  };
  ProjectTreeParam: {
    /** @description 代理工程ids */
    ppids?: number[];
  };
  /** @description 工程图片信息 */
  ProjectFileInfo: {
    /** Format: int32 */
    ppid?: number;
    md5?: string;
    /** Format: int64 */
    fileSize?: number;
    fileUUID?: string;
    fileName?: string;
  };
  ResponseResultDeptDetailInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["DeptDetailInfo"];
  };
  Motor3dRelationParam: {
    /** @description motor模型关联id */
    motor3dId?: string;
    /** @description motor模型版本号 */
    motor3dVersion?: string;
    /**
     * Format: int32
     * @description 模型文件大小
     */
    motor3dFileSize?: number;
    /** @description motor转换工具版本号 */
    motorVersion?: string;
  };
  Motor3dStatusParam: {
    /**
     * Format: int32
     * @description 工程id
     */
    projid?: number;
    /**
     * Format: int32
     * @description motor模型转换状态-1: 待处理， 0：处理失败， 1：处理成功  2：处理中
     */
    status?: number;
    /** @description 转换结果 */
    handleInfo?: string;
    /** @description 转换类型 1:分级加载；2：静态模型 */
    type?: string;
    /** @description 转换操作 1：效果优先，3：性能优先 */
    option?: string;
    /** @description 需要抽取的文件uuid */
    fileUuid?: string;
    /** @description motor转换成功后此字段才有用 */
    motor3dRelationParam?: definitions["Motor3dRelationParam"];
  };
  /** @description 文件下载信息 */
  DownloadFileInfo: {
    /** @description 文件uuid */
    uuid?: string;
    /** @description 文件MD5 */
    filemd5?: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    filesize?: number;
    /** @description 文件下载地址列表 */
    urlList?: string[];
    /** @description 文件分块信息 */
    blockInfo?: string;
    /** @description p2p（UDPServer）服务器地址 */
    p2pServerURL?: string;
  };
  PageRequest: {
    sort?: definitions["Sort"];
    /** Format: int32 */
    pageSize?: number;
    /** Format: int32 */
    pageNumber?: number;
    /** Format: int32 */
    offset?: number;
  };
  /** @description 工程属性控制查询参数 */
  ProjectAttrControlQueryParam: {
    /** @description 代理工程Id列表 */
    ppids: number[];
    /**
     * Format: int32
     * @description 工作集id(查工作集时必传，查询单工程时不传)
     */
    wsid?: number;
    /**
     * Format: int32
     * @description 工程类型
     */
    projType: number;
  };
  OrgIdKeyParam: {
    /** @description 单位工程id */
    unid?: string;
    /** @description 项目部id */
    deptId?: string;
    /**
     * Format: int32
     * @description 1单位工程id有效，2项目部id有效
     */
    type?: number;
  };
  /** @description 构件属性信息 */
  CompAttrItemResult: {
    /** @description 属性名称 */
    name?: string;
    /** @description 属性值 */
    value?: string;
  };
  ResponseResultListRelationBimProjectApiInfo: {
    /** Format: int32 */
    code?: number;
    msg?: string;
    /** @default false */
    success?: boolean;
    data?: definitions["RelationBimProjectApiInfo"][];
  };
  BVSubAttrTemplateInfo: {
    attrName?: string;
    attrValues?: string[];
  };
  /** @description 二维码创建 参数 */
  QrCodeCreateParam: {
    /** @description 确定唯一的字段集 */
    uniqInfos?: definitions["QRCodeKV"][];
    /** @description 其他字段集 */
    otherInfos?: definitions["QRCodeKV"][];
    /**
     * Format: int32
     * @description 二维码类型:1表构件二维码,2表巡检二维码,3材料二维码,4危险源二维码
     */
    type?: number;
  };
}

export interface operations {
  /** 获取省市区的信息 */
  getProviceCityCountyInfo: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultProviceCityCounty"];
      };
    };
  };
  getWeatherInfoByIP: {
    parameters: {
      query: {
        ip?: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResult天气基本信息"];
      };
    };
  };
  /** 获取天气 */
  getWeatherInfo: {
    parameters: {
      path: {
        cityname: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultWeatherResult"];
      };
    };
  };
  getCompAttr: {
    parameters: {
      body: {
        body?: definitions["CompAtrrQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["CompAttrResult"];
      };
    };
  };
  setProjectAttrControl: {
    parameters: {
      body: {
        body?: definitions["ProjectAttrControlSetParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  getProjectAttrControl: {
    parameters: {
      body: {
        body?: definitions["ProjectAttrControlQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["RevitUserAttrRemote"];
      };
    };
  };
  getCompPicInfo: {
    parameters: {
      body: {
        body?: definitions["CompUniqueParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["CompPictureInfo"];
      };
    };
  };
  saveCompPicInfo: {
    parameters: {
      body: {
        body?: definitions["CompPictureSaveParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  pageCompGroup: {
    parameters: {
      body: {
        body?: definitions["CompGroupPageParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["CompGroupPageResult"];
      };
    };
  };
  getComponentHierarchy: {
    parameters: {
      body: {
        body?: definitions["ComponentHierarchyQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["Node"][];
      };
    };
  };
  getComponentName: {
    parameters: {
      body: {
        body?: definitions["ComponentNameParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["Node"][];
      };
    };
  };
  locations: {
    parameters: {
      path: {
        /** 代理工程ID */
        ppid: number;
        /** 工程类型（1:土建预算；2:安装预算；3:钢筋预算；4:Revit；5:Tekla；6:造价工程；7:Remiz(班筑客户端上传)；8:班筑家装（PDS客户端）；9:场布预算；10:Civil3D；11:Bentley；12:Rhino） */
        projType: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ComponentNode"][];
      };
    };
  };
  /** 获取motor3dId */
  getMotor3dId: {
    parameters: {
      path: {
        /** 项目部Id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListRelationBimProjectApiInfo"];
      };
    };
  };
  /** 根据uuid获取motor3dId */
  motorProjInfoByFileUuid: {
    parameters: {
      path: {
        /** uuid */
        uuid: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListRelationBimProjectApiInfo"];
      };
    };
  };
  applyUploadUrlEx: {
    parameters: {
      body: {
        /** 上传地址参数 */
        lstFileInfo: definitions["UploadInfoEx"][];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UploadUrlEx"][];
      };
    };
  };
  getFileAddressInfo: {
    parameters: {
      body: {
        body?: definitions["DownloadURL4IworksParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["DownloadFileInfo"][];
      };
    };
  };
  getTrendsFileViewUrlWithoutLogin: {
    parameters: {
      body: {
        body?: definitions["TrendsViewInfo"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: string;
      };
    };
  };
  getDownloadURLs: {
    parameters: {
      body: {
        body?: definitions["DownloadURLParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["FileDownloadInfo"][];
      };
    };
  };
  getDownloadURLsWithName: {
    parameters: {
      body: {
        body?: definitions["下载请求参数"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: string[];
      };
    };
  };
  getFileUploadURLs: {
    parameters: {
      body: {
        body?: definitions["UploadInfo"][];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UploadFileAddressResult"][];
      };
    };
  };
  getLongLineDownloadURLs: {
    parameters: {
      body: {
        body?: definitions["DownloadURLParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["FileDownloadInfo"][];
      };
    };
  };
  getTrendsFileViewUrl: {
    parameters: {
      body: {
        body?: definitions["TrendsViewInfo"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: string;
      };
    };
  };
  getImageInfoList: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultImageInfoVO"];
      };
    };
  };
  getInfraDeptResult: {
    parameters: {
      path: {
        /** 项目部ID */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["InfraDeptResult"];
      };
    };
  };
  listInfraDeptCoordinate: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["InfraDeptCoordinateResult"][];
      };
    };
  };
  addNewPDSOperLog: {
    parameters: {
      body: {
        body?: definitions["PDSNewOperLogInfo"][];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: boolean;
      };
    };
  };
  addOperLog: {
    parameters: {
      body: {
        body?: definitions["UserOperLog"][];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  getDownLoadUrlByFileUuid: {
    parameters: {
      path: {
        /** 文件uuid */
        fileUuid: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityString"];
      };
    };
  };
  getProjectDownLoadUrl: {
    parameters: {
      path: {
        /** 工程id */
        projId: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityString"];
      };
    };
  };
  saveOrUpdateMotor3dStatus: {
    parameters: {
      body: {
        body?: definitions["Motor3dStatusParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityBoolean"];
      };
    };
  };
  getControlPointInfosByCameraId: {
    parameters: {
      body: {
        /** 摄像头绑定信息请求参数 */
        body?: definitions["IotVideoBindPpidRequest"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListControlPointInfo"];
      };
    };
  };
  getVideoUrl: {
    parameters: {
      body: {
        /** 摄像头信息请求参数 */
        body?: definitions["IotVideoUrlRequest"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultIotVideoUrlResponse"];
      };
    };
  };
  listIotManufacturerByDeptIds: {
    parameters: {
      body: {
        /** 配置信息请求参数 */
        body?: definitions["IotDeviceRequest"][];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListIotManufacturerInfo"];
      };
    };
  };
  listIotVideoByDeptIds: {
    parameters: {
      body: {
        /** 配置信息请求参数 */
        body?: definitions["IotDeviceRequest"][];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListIotVideoInfo"];
      };
    };
  };
  jump: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: boolean;
      };
    };
  };
  postMotorModelStatus: {
    parameters: {
      body: {
        body?: definitions["MotorModelStatusCallbackParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  listMotorModelTransStatus: {
    parameters: {
      body: {
        /** motor模型guid列表 */
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ModelTransStatusResponse"][];
      };
    };
  };
  triggerMotorModelTransform: {
    parameters: {
      body: {
        body?: definitions["MotorModelTransParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  createQRCode: {
    parameters: {
      body: {
        body?: definitions["QrCodeCreateParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityString"];
      };
    };
  };
  getCommonQRCodeControlProperty: {
    parameters: {
      body: {
        body?: definitions["CommonQRCodeControlPropertyQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityCommonQRCodeControlPropertiesVo"];
      };
    };
  };
  setCommonQRCodeControlProperty: {
    parameters: {
      body: {
        body?: definitions["CommonQRCodeControlPropertySetParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntity"];
      };
    };
  };
  setQRCodeSwitch: {
    parameters: {
      body: {
        body?: definitions["QRCodeSwitchSetParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  getQRCodeSwitch: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["SwitchInfo"];
      };
    };
  };
  /** 获取用户有权限的企业组织列表 */
  getUserAuthEnterpriseOrgList: {
    parameters: {
      body: {
        /** 查询参数 */
        userAccountParam: definitions["UserAccountParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListEnterpriseOrgInfo"];
      };
    };
  };
  /** 获取有项目部权限的用户信息 */
  getEnterpriseUser: {
    parameters: {
      body: {
        /** 查询参数 */
        userAccount: definitions["UserAccountRequest"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListEnterpriseUserResponse"];
      };
    };
  };
  getCityDeptNumInfos: {
    parameters: {
      path: {
        /** 省ID */
        provinceId: number;
        /** 分公司ID */
        orgId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["MapCityDeptNumResult"][];
      };
    };
  };
  /** 编辑项目部（iworks） */
  editDeptInfo: {
    parameters: {
      body: {
        /** 项目部编辑参数 */
        DeptEditParam: definitions["DeptEditParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultString"];
      };
    };
  };
  /** 获取项目部概况（iworks） */
  getDeptDetailInfo: {
    parameters: {
      path: {
        /** 项目部ID */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultDeptDetailInfo"];
      };
    };
  };
  getDeptSummaryInfo: {
    parameters: {
      path: {
        /** 分公司节点id */
        orgId: string;
        /** 统计类型 1:按项目状态  2:按项目类型  4:按项目属性 8:最近两年在建项目分布 ,如果需要某几种类型的统计，将各自type相加即可 */
        type: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["DeptSummaryResult"][];
      };
    };
  };
  /** 检查效果图名称(返回重名的效果图名称) */
  checkDeptEffectImageNames: {
    parameters: {
      body: {
        /** 效果图名称检查参数 */
        deptEffectImageSaveParam: definitions["DeptEffectImageNameCheckParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListString"];
      };
    };
  };
  /** 删除效果图 */
  deleteDeptEffectImages: {
    parameters: {
      body: {
        /** 效果图ID列表 */
        ids: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultString"];
      };
    };
  };
  /** 上传效果图 */
  saveDeptEffectImages: {
    parameters: {
      body: {
        /** 效果图保存参数 */
        deptEffectImageSaveParam: definitions["DeptEffectImageSaveParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultString"];
      };
    };
  };
  /** 获取指定组织结构效果图 */
  getDeptEffectImages: {
    parameters: {
      path: {
        /** 组织机构ID */
        orgId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseResultListDeptEffectImageResult"];
      };
    };
  };
  setOrgEffectCover: {
    parameters: {
      path: {
        /** 组织机构ID */
        orgId: string;
        /** 效果图ID */
        pictureId: string;
        /** 当前组织机构类型（1:集团，2：分公司，3：项目部，4：标段，5：单项，6：单位，7：模型） */
        orgType: number;
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  detailByDeptId: {
    parameters: {
      path: {
        /** 基建或者房建的id */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["NodeResult"];
      };
    };
  };
  getOrgAndProjectTree: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["Node"][];
      };
    };
  };
  getOrgList: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["Node"][];
      };
    };
  };
  getOrgResultList: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["NodeResult"][];
      };
    };
  };
  getExcellentProjects: {
    parameters: {
      path: {
        /** 分公司ID */
        orgId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ExcellentProjectVo"][];
      };
    };
  };
  listNewestDept: {
    parameters: {
      path: {
        /** 分公司ID */
        orgId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["DeptDetailInfo"][];
      };
    };
  };
  listOrgNode: {
    parameters: {
      body: {
        body?: definitions["UnitPageParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["PageResultUnitInfoVo"];
      };
    };
  };
  /** 获取项目部下的工程组织树列表（标段，单项工程，单位工程，工程）（iworks&iworksApp） */
  listProjNodeByDeptId: {
    parameters: {
      path: {
        /** 项目部ID */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["OrgProjNodeVo"][];
      };
    };
  };
  setSummaryInfos: {
    parameters: {
      body: {
        /** 概况修改参数 */
        summaryInfoEditParam: definitions["SummaryInfoEditParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  getSummaryInfos: {
    parameters: {
      body: {
        /** id参数 */
        keyParam: definitions["OrgIdKeyParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjSummaryInfo"][];
      };
    };
  };
  getProvinceDeptNumInfos: {
    parameters: {
      path: {
        /** 分公司ID */
        orgId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["MapProvinceDeptNumResult"][];
      };
    };
  };
  detailProjectItem: {
    parameters: {
      path: {
        motor3did: string;
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectItemResult"];
      };
    };
  };
  getProjectDetailInfo: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectDetailInfo"];
      };
    };
  };
  getProjInfoByDept: {
    parameters: {
      body: {
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjCommInfo"][];
      };
    };
  };
  listProjInfoByNodeId: {
    parameters: {
      path: {
        /** 节点类型 0:分公司 1:项目部 */
        nodeType: number;
        /** 节点id */
        nodeId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjWithUserAuthResult"][];
      };
    };
  };
  listBIMProj: {
    parameters: {
      path: {
        /** 项目部Id */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityListBIMProjectItemVo"];
      };
    };
  };
  listProjFileInfo: {
    parameters: {
      body: {
        body?: definitions["ListProjectFileInfoParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectFileInfo"][];
      };
    };
  };
  listProjInfoByDepts: {
    parameters: {
      body: {
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectCommonInfo"][];
      };
    };
  };
  pageProjectInfo: {
    parameters: {
      body: {
        body?: definitions["ProjectPageParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectPageResult"];
      };
    };
  };
  listProjInfoByDept4Iworksf: {
    parameters: {
      body: {
        body?: definitions["QryProjInfo4Iworksf"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectInfo"][];
      };
    };
  };
  getProjTypeDesc: {
    parameters: {
      body: {
        /** 代理工程id列表 */
        body?: number[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjDescInfo"][];
      };
    };
  };
  recordRecentOpenedProject: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  getRecentOpenedProject: {
    parameters: {
      path: {
        /** 最近几个 */
        top: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectInfoOfRecentOpenedResult"][];
      };
    };
  };
  isCanLightWeightExtract: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: boolean;
      };
    };
  };
  listMotor3dStatusByFileUuid: {
    parameters: {
      body: {
        /** 文件uuid列表 */
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityListMotor3dStatusResult"];
      };
    };
  };
  upsertMotor3dStatus: {
    parameters: {
      body: {
        body?: definitions["Motor3dStatusParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityBoolean"];
      };
    };
  };
  getMotor3dStatusResult: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityMotor3dStatusResult"];
      };
    };
  };
  getExtractStrategy: {
    parameters: {
      path: {
        /** 工程大小,单位为byte */
        projectSize: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityBoolean"];
      };
    };
  };
  triggerMotor3dExtract: {
    parameters: {
      body: {
        body?: definitions["TriggerMotor3dParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ResponseEntityBoolean"];
      };
    };
  };
  getProjectCompForAll: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
      body: {
        /** 工程树path */
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectComp"][];
      };
    };
  };
  getComponentDetail: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
      body: {
        /** handle列表 */
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ComponentDetailResult"][];
      };
    };
  };
  getProjectComp: {
    parameters: {
      body: {
        body?: definitions["ProjectCompParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectComp"][];
      };
    };
  };
  getProjectTree: {
    parameters: {
      body: {
        body?: definitions["ProjectTreeParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectTree"][];
      };
    };
  };
  getProjectTreeForAll: {
    parameters: {
      path: {
        /** 代理工程id */
        ppid: number;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["ProjectTree"];
      };
    };
  };
  listNodeCode: {
    parameters: {
      path: {
        /** 项目id */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: string[];
      };
    };
  };
  getUnitInfo: {
    parameters: {
      path: {
        /** 节点id */
        nodeId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UnitInfoVo"];
      };
    };
  };
  batchGetUserPortraitInfo: {
    parameters: {
      path: {
        /** 企业id */
        epid: number;
      };
      body: {
        /** 用户账号列表 */
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["EpUserPortraitVo"][];
      };
    };
  };
  getDeptUserInfo: {
    parameters: {
      path: {
        /** 项目部id */
        deptId: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UserMailVo"][];
      };
    };
  };
  listUsersOfFilterOrg: {
    parameters: {
      body: {
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfOrgItem"][];
      };
    };
  };
  listUsersOfOrg: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfOrgItem"][];
      };
    };
  };
  listUsersOfRole: {
    parameters: {};
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfRoleVo"][];
      };
    };
  };
  listUsersOfRoleFilterOrg: {
    parameters: {
      body: {
        body?: string[];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfRoleVo"][];
      };
    };
  };
  savePortraitInfo: {
    parameters: {
      body: {
        body?: definitions["UserPortraitParam"];
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  setPushPlatform: {
    parameters: {
      path: {
        pushPlatform: number;
      };
    };
    responses: {
      /** successful operation */
      default: unknown;
    };
  };
  searchUsersOfOrg: {
    parameters: {
      body: {
        body?: definitions["UsersOfRoleQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfOrgItem"][];
      };
    };
  };
  searchUsersOfRole: {
    parameters: {
      body: {
        body?: definitions["UsersOfRoleQueryParam"];
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["UsersOfRoleVo"][];
      };
    };
  };
  downloadPortrait: {
    parameters: {
      path: {
        username: string;
      };
    };
    responses: {
      /** successful operation */
      200: {
        schema: definitions["FileDownloadInfo"];
      };
    };
  };
}

export interface external {}
