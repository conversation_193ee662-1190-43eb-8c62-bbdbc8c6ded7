import React, {useContext} from "react";
import {Button, Dropdown, Menu} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import {createTask, createSubtask, createMilepost} from "../../../gantt/taskUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const InsertButton = () => {
    const cls = useStyles();
    const {isWbs, checkoutStatus} = useContext(EditorContext);

    const insertMenus = (
        <Menu>
            <Menu.Item key="task" onClick={createTask}>插入任务</Menu.Item>
            <Menu.Item key="childTask" onClick={createSubtask}>插入子任务</Menu.Item>
            <Menu.Item key="milestone" onClick={createMilepost}>插入里程碑</Menu.Item>
        </Menu>
    );

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Dropdown overlay={insertMenus} placement="bottomCenter" disabled={isWbs}>
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-charuhang" fontSize={18} />}
            >
                插入
            </Button>
        </Dropdown>
    );
};

export default InsertButton;
