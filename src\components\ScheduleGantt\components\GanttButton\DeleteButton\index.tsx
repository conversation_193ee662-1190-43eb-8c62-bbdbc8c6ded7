import React, {useContext} from "react";
import {But<PERSON>} from "antd";
import MyIconFont from "../../../../MyIconFont";
import {deleteTask} from "../../../gantt/taskUtils";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";

const DeleteButton = () => {
    const cls = useStyles();
    const {checkoutStatus} = useContext(EditorContext);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-shanchuhang" fontSize={18} />}
            onClick={deleteTask}
        >
            删除
        </Button>
    );
};

export default DeleteButton;
