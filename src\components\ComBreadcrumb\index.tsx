import React, {useState, useEffect, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {useLocation} from "react-router-dom";
import {cloneDeep} from "lodash-es";
import {RootState} from "../../store/rootReducer";
import {useAppSelector} from "../../store";
// import AnchorSkip from "../Anchor";

const useStyle = createUseStyles({
    box: {
        height: 52,
        display: "flex",
        borderBottom: "1px solid #E1E2E5",
        alignItems: "center",
        paddingLeft: 24,
    },
    anchorList: {
        position: "absolute",
        left: 843,
    }
});

type MenuListType = RootState["noRegister"]["menuList"][number];

interface ListType extends MenuListType {
    nameList?: string[];
}

const ComBreadcrumb = () => {
    const cls = useStyle();
    const {menuList, anchor} = useAppSelector((state) => state.noRegister);
    const location = useLocation();
    const [nameList, setNameList] = useState<string[]>([]);
    const dealMenuList = useCallback(
        () => {
            if (location === undefined) {
                return;
            }
            const menuFlatArr: ListType[] = [];
            const dealData = (list: ListType[], parentName: string[]) => {
                list.forEach((el) => {
                    el.nameList = [...parentName, el.menuName];
                    menuFlatArr.push(el);
                    if (Array.isArray(el.children)) {
                        dealData(el.children, el.nameList);
                    }
                });
            };
            dealData(cloneDeep(menuList), []);
            const findedMenu = menuFlatArr.find((el) => el.path === location.pathname);
            if (findedMenu !== undefined) {
                const tempNameList = cloneDeep(findedMenu.nameList ?? []);
                setNameList(tempNameList);
            }
        },
        [location, menuList]
    );

    useEffect(() => {
        dealMenuList();
    }, [dealMenuList]);

    if (nameList.length === 0) {
        return null;
    }

    return (
        <div className={cls.box}>
            {nameList.map((name, index) => {
                if (index === (nameList.length - 1)) {
                    return (
                        <span className="text14" style={{color: "#717784"}} key={name}>
                            <span style={{color: "#061127"}}>{name}</span>
                        </span>
                    );
                }
                return (
                    <span className="text14" style={{color: "#717784"}} key={name}>
                        <span>{name}</span>
                        <span style={{margin: "0 8px"}}>/</span>
                    </span>
                );
            })}
            <div className={cls.anchorList}>
                {/* <AnchorSkip list={[{id: "default", name: "任务信息"}, {id: "test", name: "任务列表"}]} /> */}
                {anchor}
            </div>
        </div>
    );
};
export default ComBreadcrumb;
