import {createUseStyles} from "react-jss";

const useModelBimStyle = createUseStyles({
    box: {

    },
    tabsBox: {
        marginTop: 24,
    },
    tabs: {
        padding: "0 24px !important",
        color: "#33394D",
        "& div.ant-tabs-nav": {
            margin: 0
        },
        "& .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn": {
            color: "#061127",
            fontWeight: "bold"
        },
        "& .ant-tabs-ink-bar": {
            background: "#061127"
        },
        "& .ant-tabs-tab:hover": {
            color: "#061127"
        }
    },
    tabsItem: {
        // width: 128,
        // height: 40,
        // lineHeight: "40px",
        // textAlign: "center",
        // backgroundColor: "#E1E2E5",
        // color: "#33394D",
        // cursor: "pointer",
        // marginRight: 2,
        // "&.active": {
        //     backgroundColor: "white",
        //     color: "#061127",
        //     fontWeight: 700,
        //     borderTop: "2px solid #1F54C5",
        //     lineHeight: "38px",
        // }
    }
});

export default useModelBimStyle;
