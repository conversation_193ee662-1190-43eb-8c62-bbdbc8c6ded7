/* eslint-disable */
import {message} from "antd";
import { ScheduleWindowType } from "./planModel";

interface LooseObj {
    [k: string]: any
}
let authorization: string = localStorage.getItem("token") ?? "";
export const setToken = (val: string) => {
    authorization = val;
}

export const getToken = () => authorization

export type HTTPMethod = "GET" | "POST" | "DELETE" | "PUT" | "OPTIONS" | "HEAD";
export const toLogin = () => {
    console.log(window);
    if ((window as unknown as ScheduleWindowType).embedPlatform === true) {
        window.location.href = (window as any)._PLATFORM_;
    } else {
        window.location.href = `${window.location.href.split("#")[0]}#/login`;
    }
}
function obj2String(obj: LooseObj = {}) {
    let arr: any[] = [];
    let idx: number = 0;
    for (let item in obj) {
        arr[idx++] = [item, obj[item]];
    }
    return new URLSearchParams(arr).toString();
}

let isFirst = true;
export async function IFetch(path: string, opts: LooseObj = {}, method: HTTPMethod = "GET", bRetry: boolean = true): Promise<any> {
    if (authorization === undefined || authorization.length === 0) {
        return Promise.reject(new Error("Token不存在"));
    }
    let initObj: RequestInit = {};
    if (method === "GET") {
        const queryStr = obj2String(opts);
        if (queryStr) {
            path += "?" + queryStr;
        }
        initObj = {
            method,
            headers: {
                "access-token": authorization,
            },
            credentials: 'include'
        }
    } else {
        initObj = {
            method,
            credentials: 'include',
            headers: {
                'content-Type': 'application/json',
                "access-token": authorization,
            },
            body: JSON.stringify(opts)
        }
    }

    try {
        const res: Response = await fetch(path, initObj);
        const {url, status, redirected, ok, statusText} = res;
        if (
            url.includes("login?service")
            || (url.includes("casValidUserException/validUserException?type") && redirected)
        ) {
            let msg = "";
            try {
                const json = await res.json();
                if (json) {
                    msg = json.message || json.msg;
                }
            } catch (error) {
                console.log(error);
            }
            if (isFirst) {
                message.error(msg || "身份认证已过期，请重新登录！");
                isFirst = false;
                setTimeout(() => {
                    isFirst = true;
                }, 1000);
            }
            setTimeout(() => {
                toLogin();
                // window.location.replace(window.origin);
            }, 1000);
            return;
        }

        if (bRetry && status === 405 && method === "POST") {
            return IFetch(path, opts, method, false);
        }

        if (!ok) {
            message.error(`错误的状态码${status}`);
            return Promise.reject(new Error(statusText));
        }

        const json = await res.json();
        if (!json) {
            message.error("请求成功，接口无响应!");
            return Promise.reject(new Error("no response!"));
        }
        if (json && json.code && json.code !== 200) {
            if ([1007, 1002].includes(json.code)) {
                if (isFirst) {
                    message.error(json.msg || "身份认证已过期，请重新登录！");
                    isFirst = false;
                    setTimeout(() => {
                        isFirst = true;
                    }, 2000);
                }
                setTimeout(() => {
                    toLogin();
                }, 1000);
                return;
            }
            else {
                message.error(json.msg || "请求出错!");
                return Promise.reject(json);
            }
        }

        return json;
    } catch (error) {
        console.log(error);
    }
}

export const IGET = (path: string, opts: LooseObj = {}) => IFetch(path, opts, "GET");
export const IPOST = (path: string, opts: LooseObj = {}) => IFetch(path, opts, "POST");
export const IPUT = (path: string, opts: LooseObj = {}) => IFetch(path, opts, "PUT");
export const IDELETE = (path: string, opts: LooseObj = {}) => IFetch(path, opts, "DELETE");
