import {useCallback, useEffect, useState} from "react";

/**
 * 监听指定dom节点是否处于全屏状态
 * @param dom 指定监听的dom节点，不传默认监听document
 */
const useFullScreenChange = (dom?: string | HTMLElement) => {
    const [isTargetFullScreen, setIsTargetFullScreen] = useState(false);

    const handleFullScreenChange = useCallback(
        () => {
            const target = document.fullscreenElement;
            if (dom !== undefined) {
                let curDom: HTMLElement | null = null;
                if (typeof dom === "string") {
                    curDom = document.getElementById(dom);
                } else {
                    curDom = dom;
                }
                setIsTargetFullScreen(curDom === target);
            } else {
                setIsTargetFullScreen(target !== null);
            }
        },
        [dom]
    );

    useEffect(
        () => {
            document.addEventListener("fullscreenchange", handleFullScreenChange);
            return () => {
                document.removeEventListener("fullscreenchange", handleFullScreenChange);
            };
        },
        [handleFullScreenChange]
    );
    return {isTargetFullScreen};
};

export default useFullScreenChange;
