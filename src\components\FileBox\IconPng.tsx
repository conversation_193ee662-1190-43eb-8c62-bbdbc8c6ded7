import React, {CSSProperties, memo, useEffect, useState} from "react";
import {getFileUrl} from "../../api/common.api";

interface Porps {
    thumbUuid: string;
    width?: number | string;
    height?: number | string;
    style?: CSSProperties;
}
const IconPng = (props: Porps) => {
    const {
        thumbUuid,
        width = 45,
        height = 45,
        style
    } = props;
    const [fileUrl, setFileUrl] = useState<string>("");

    useEffect(() => {
        getFileUrl([thumbUuid])
            .then((res) => {
                if (res.length > 0 && res[0].downloadUrls.length > 0) {
                    setFileUrl(res[0].downloadUrls[0]);
                }
            })
            .catch(() => {
                setFileUrl("");
            });
    }, [thumbUuid]);

    return <><img src={fileUrl} alt="缩略图" width={width} height={height} style={style} /></>;
};

export default memo(IconPng);
