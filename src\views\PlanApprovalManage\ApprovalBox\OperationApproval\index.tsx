import React, {use<PERSON><PERSON>back, useEffect, useMemo, useState} from "react";
import {Button, Row, Space, Input, message, Form, Spin, Col, Modal} from "antd";
import {ExclamationCircleFilled} from "@ant-design/icons";
import {ApprovalCommentVo, ReformDetailVo, ReformInstSubmitParam} from "../../../../api/rectification/models/process";
import {ApprovalRole, ApprovalType, Flow, FlowState, RectificationActionType} from "../../../../components/Rectification/models/rectification";
import {DownFileListType, FileType} from "../../../../api/common.type";
import FileBox from "../../../../components/FileBox";
import FlowChart from "../../../../components/Rectification/FlowChart";
import {ConditionFlowChartData} from "../../../../components/Rectification/models/flow-chart";
import CommentComp from "../../../../components/Rectification/comment";
import BackIcon from "../../../../components/MyIcon/BackIcon";
import useStyles from "./style";
import {RectifySubmitFormModel} from "../../../Rectification/interface";
import {commentReform, deleteReform, getReformDetail, getReformFlowChart, submitReform} from "../../../../api/rectification/index";
import RectifyButtons from "../../../Rectification/components/RectifyButtons";
import {getButtonVisiblePolicy, loadCurrentFlowAsync, rules, setIsReturnBackFn, transerFileToAttachment, transformCustomFieldMap, transformJsonValues} from "../../../../components/Rectification/rectification-helpers";
import useDetailStyle from "../../../../assets/css/useDetailStyle";
import ComDrawer from "../../../../components/ComDrawer";
import ListItem from "../../../../components/FileBox/ListItem";
import BtnIcon from "../../../../components/MyIcon/BtnIcon";
import {getPlanInfoDetail, changePreDate} from "../../../../api/Preparation";
import {PlanInfoDetailType} from "../../../../api/Preparation/type";
import ScheduleGantt from "../../../../components/ScheduleGantt";
import {BackType} from "../../../../assets/ts/globalType";
import ChangeInfoModal from "./ChangeInfoModal";
import {getChangeInfluence} from "../../../../assets/ts/utils";
import PlanChangeProject from "../../../../components/PlanChangeProject";
import {packageDownload} from "../../../../api/common.api";
import AnchorSkip from "../../../../components/Anchor";
import {dispatch} from "../../../../store";
import {setAnchorSkip} from "../../../../store/no-persist/action";

const {TextArea} = Input;
interface RectifyDetailProps {
    detailId: string;
    back: (val: BackType) => void;
}
/* 审批类型 */
const moduleType = "PLAN_APPROVAL";
// eslint-disable-next-line max-lines-per-function
const OperationApproval: React.FC<RectifyDetailProps> = (props) => {
    const {back, detailId} = props;
    const cls = useStyles();
    const clsDetail = useDetailStyle();
    const [reformDetail, setReormDetail] = useState<ReformDetailVo>();
    const [commentForm] = Form.useForm<RectifySubmitFormModel>();
    const [visibleButtons, setVisibleButtons] = useState<RectificationActionType[]>([]);
    /* 审批记录列表 */
    const [commentList, setCommentList] = useState<ApprovalCommentVo[]>([]);
    const [fileList, setFileList] = useState<FileType[]>([]);
    const [flowChartVisible, setFlowChartVisible] = useState(false);
    const [flowChartData, setFlowChartData] = useState<ConditionFlowChartData>();
    const [loading, setLoading] = useState(false);
    /* 撤回和退回的需要特殊处理 */
    const [isReturnBack, setIsReturnBack] = useState(false);
    const [nodeUsers, setNodeUsers] = useState<Flow[]>();
    const [submitLoading, setSubmitLoading] = useState(false);
    const [approvalInfoVisible, setApprovalInfoVisible] = useState(false);
    const [planDetail, setPlanDetail] = useState<PlanInfoDetailType | null>(null); // 计划详情
    const [changeInfoModalVisible, setChangeInfoModalVisible] = useState(false); // 变更信息弹窗

    const init = useCallback(
        async () => {
            setLoading(true);
            try {
                const {data: planDetailData} = await getPlanInfoDetail(detailId);
                const {result} = await getReformDetail(planDetailData.approvalId, moduleType);
                if (result !== undefined && setIsReturnBackFn(result.processStatus)) {
                    // 撤销或者退回状态
                    const tempData = transformJsonValues(result.componentJsons);
                    const tempFieldData = transformCustomFieldMap(result.componentJsons);
                    const flowRes = await loadCurrentFlowAsync(
                        tempFieldData,
                        tempData,
                        result.formTemplId!,
                        result.approvalTypeId,
                        result.serialNum,
                    );
                    setNodeUsers(flowRes);
                }
                setReormDetail(result);
                const chartRes = await getReformFlowChart(result.serialNum, result.approvalTypeId);
                const policy = getButtonVisiblePolicy(result.processStatus as FlowState, result.approvalRoles, result.startFlowNode);
                const showButtons = rules.get(policy) ?? [];
                setCommentList(result.comments ?? []);
                setVisibleButtons(showButtons);
                setFlowChartData(chartRes.result);
                setIsReturnBack(setIsReturnBackFn(result.processStatus));
                setPlanDetail(planDetailData);
            } finally {
                setLoading(false);
            }
        },
        [detailId]
    );

    useEffect(() => {
        init();
    }, [init]);

    const handleSubmit = useCallback(
        async () => {
            setSubmitLoading(true);
            try {
                const values = await commentForm.validateFields();
                const roles = reformDetail?.approvalRoles ?? [];
                /** 判断是否是抄送行为 */
                const isCopyTo = roles.includes(ApprovalRole.CopyPerson) && !roles.includes(ApprovalRole.ApprovalPerson);
                /** 附件 */
                const attachments = transerFileToAttachment(fileList);
                if (isCopyTo) {
                    await commentReform({
                        formTaskId: reformDetail!.formTaskId,
                        message: values.message,
                        attachments
                    });
                } else {
                    let cancelExtra: Partial<ReformInstSubmitParam> = {};
                    if (isReturnBack) {
                        cancelExtra = {
                            nodeUsers: nodeUsers?.filter((f) => f.type === ApprovalType.InitiatorSpecify).map((f) => ({
                                approvalNodeId: f.id,
                                approvalUsers: f.persons.map((v) => v.name)
                            })),
                        };
                    }
                    // 提交
                    await submitReform({
                        serialNum: reformDetail!.serialNum,
                        formTaskId: reformDetail!.formTaskId,
                        checkFormId: planDetail?.id,
                        message: values.message,
                        deptId: reformDetail?.deptId,
                        nodeId: reformDetail?.nodeId,
                        nodeType: reformDetail?.nodeType,
                        attachments,
                        ...cancelExtra,
                    }, moduleType);
                    if (planDetail !== undefined && planDetail !== null && reformDetail !== undefined) {
                        const lastNode = flowChartData?.flowNodeList.slice(-2)[0];
                        if (lastNode?.flowNodeId === reformDetail.currentFlowNodeId) {
                            await changePreDate(planDetail.id);
                        }
                    }
                }
                message.success("操作成功");
                setSubmitLoading(false);
                back("ok");
            } catch (err) {
                setSubmitLoading(false);
            }
        },
        [commentForm, reformDetail, fileList, back, isReturnBack, planDetail, nodeUsers, flowChartData]
    );

    const handleExportFiles = useCallback(
        (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            const files: DownFileListType[] = [
                {
                    dir: true,
                    name: `${planDetail?.name}变更资料_附件`,
                    fileList: planDetail?.changeFileList.map((i) => ({fileUuid: i.fileUuid, name: i.fileName, dir: false}))
                }
            ];
            packageDownload({packDownParam: {fileList: files, packName: `${planDetail?.name}变更资料_附件.zip`}, fun: () => null});
        },
        [planDetail]
    );

    // 进度审批的撤回是删除
    const handlePlanDelete = useCallback(() => {
        Modal.confirm({
            title: <div className={cls.tipTitle}>提示</div>,
            icon: <ExclamationCircleFilled className={cls.tipIcon} />,
            content: <p className={cls.tipContent}>是否确定撤回？</p>,
            onOk: async () => {
                await deleteReform(reformDetail!.serialNum ?? "");
                back("ok");
                message.success("撤回成功！");
            }
        });
    }, [back, cls.tipContent, cls.tipIcon, cls.tipTitle, reformDetail]);

    const renderHeader = useCallback(() => (
        <Row justify="space-between" className={clsDetail.head}>
            <Space>
                <BackIcon onClick={() => back("cancel")} />
                <div className={clsDetail.headTitle}>{planDetail?.name}</div>
            </Space>
            {
                planDetail?.id !== undefined && (
                    <Space>
                        <Button onClick={() => setFlowChartVisible(true)}>审批流程图</Button>
                        {
                            // eslint-disable-next-line max-len
                            !visibleButtons.includes(RectificationActionType.Submit) && <Button onClick={() => setApprovalInfoVisible(true)}>审批信息</Button>
                        }
                        {
                            (!visibleButtons.includes(RectificationActionType.Submit) && planDetail?.changeStatus === "Changed") && <Button onClick={() => setChangeInfoModalVisible(true)}>变更信息</Button>
                        }
                        {
                            visibleButtons.length === 1 && visibleButtons.includes(RectificationActionType.Revoke) && (
                                <Button
                                    danger
                                    onClick={handlePlanDelete}
                                >
                                    撤回
                                </Button>
                            )
                        }
                    </Space>
                )
            }
        </Row>
    ), [back, clsDetail.head, clsDetail.headTitle, handlePlanDelete, planDetail, visibleButtons]);

    const renderChangeInfoBox = useMemo(() => (
        <div className={cls.changeInfoBox}>
            <Row style={{marginBottom: 16}} wrap={false}>
                <Col className={cls.infoRowLeft}>变更原因</Col>
                <Col>{planDetail?.changeReason}</Col>
            </Row>
            <Row style={{marginBottom: 16}} wrap={false}>
                <Col className={cls.infoRowLeft}>模型查看</Col>
                <Col style={{flexGrow: 1}}>
                    <PlanChangeProject planId={planDetail?.id} viewType="planView" />
                </Col>
            </Row>
            <Row wrap={false}>
                <Col className={cls.infoRowLeft}>变更影响</Col>
                <Col>{getChangeInfluence(planDetail?.currentTotalDuration, planDetail?.unchangedTotalDuration)}</Col>
            </Row>
            <div className={cls.infoRowSeparateLine} />
            <Row>
                <Col className={cls.infoRowLeft}>{`变更资料 (${planDetail?.changeFileList.length})`}</Col>
                <Col style={{flexGrow: 1}}>
                    <div>
                        <Button
                            type="link"
                            className={cls.downloadBtn}
                            icon={<BtnIcon type="icon-xiazai" />}
                            onClick={handleExportFiles}
                            disabled={planDetail?.changeFileList.length === 0}
                        >
                            下载全部文件
                        </Button>
                        <Row style={{marginTop: 16}}>
                            {
                                planDetail?.changeFileList.map((item: FileType) => (
                                    <Col span={6} style={{marginRight: 16}} key={`${item.fileUuid}${item.timeStamp ?? 0}`}>
                                        <ListItem
                                            key={`${item.fileUuid}${item.timeStamp ?? 0}`}
                                            file={item}
                                            isEditName={false}
                                            isDelete={false}
                                            isDownload
                                            onFileChange={() => ""}
                                            onDelete={() => ""}
                                        />
                                    </Col>
                                ))
                            }
                        </Row>
                    </div>
                </Col>
            </Row>
        </div>
    ), [cls.changeInfoBox, cls.downloadBtn, cls.infoRowLeft, cls.infoRowSeparateLine, planDetail, handleExportFiles]);

    const renderScheduleGantt = useMemo(() => {
        if (planDetail !== null) {
            return (
                <div style={{height: !visibleButtons.includes(RectificationActionType.Submit) === false ? 500 : "calc(100vh - 200px)"}} id="deafult">
                    <ScheduleGantt
                        showHeader={false}
                        fromType="approval"
                        planId={planDetail?.id ?? ""}
                        enterType="view"
                        toolLeftButtons={[
                            <div className={clsDetail.title14}>
                                {!visibleButtons.includes(RectificationActionType.Submit) === false ? "任务信息" : ""}
                            </div>
                        ]}
                        toolRightButtons={["export", "showGantt", "criticalPath", "timeScale"]}
                    />
                </div>
            );
        }
        return null;
    }, [clsDetail.title14, planDetail, visibleButtons]);

    const renderApprovalProcess = useMemo(() => (
        visibleButtons.includes(RectificationActionType.Submit)
            ? (
                <Form
                    style={{marginTop: 20}}
                    form={commentForm}
                    className={cls.specFormBox}
                >
                    <Form.Item
                        label="审批意见"
                        rules={[{required: false, message: "请输入审批意见"}]}
                        name="message"
                    >
                        <TextArea maxLength={300} showCount />
                    </Form.Item>
                    <Form.Item label="附件">
                        <FileBox style={{width: 564}} value={fileList} onChange={setFileList} />
                    </Form.Item>
                    <Form.Item label="审批日志">
                        <CommentComp commentList={commentList} />
                    </Form.Item>
                </Form>
            )
            : null
    ), [cls.specFormBox, commentForm, commentList, fileList, visibleButtons]);

    const renderRectifyButtons = useMemo(() => {
        if (visibleButtons.length === 0 || !Array.isArray(visibleButtons)) {
            return null;
        }
        /* 当只有一个按钮，且是打印，就不展示，直接展示在最上面 */
        if (visibleButtons.length === 1 && visibleButtons[0] === RectificationActionType.Print) {
            return null;
        }
        /* 当只有一个按钮，且是撤回，就不展示，直接展示在最上面 */
        if (visibleButtons.length === 1 && visibleButtons[0] === RectificationActionType.Revoke) {
            return null;
        }
        return (
            <RectifyButtons
                approvalType="plan"
                reformDetail={reformDetail}
                visibleButtons={visibleButtons}
                onSubmit={handleSubmit}
                loading={submitLoading}
                flowChartData={flowChartData}
                back={() => back("ok")}
            />
        );
    }, [back, flowChartData, handleSubmit, reformDetail, submitLoading, visibleButtons]);
    const list = useMemo(() => {
        if (visibleButtons.includes(RectificationActionType.Submit) && planDetail?.changeStatus === "Changed") {
            return [{id: "deafult", name: "任务信息"}, {id: "change", name: "变更信息"}, {id: "flow", name: "审批流程"}];
        }
        if (visibleButtons.includes(RectificationActionType.Submit)) {
            return [{id: "deafult", name: "任务信息"}, {id: "flow", name: "审批流程"}];
        }
        return [{id: "deafult", name: "任务信息"}];
    }, [visibleButtons, planDetail]);
    const AnchorCom = useMemo(() => <AnchorSkip list={list} />, [list]);
    useEffect(() => {
        dispatch(setAnchorSkip(AnchorCom));
    }, [AnchorCom]);
    // eslint-disable-next-line arrow-body-style
    useEffect(() => {
        return () => {
            dispatch(setAnchorSkip(null));
        };
    }, []);
    return (
        <div className={clsDetail.boxAbsolute}>
            {renderHeader()}
            <div className={clsDetail.boxContent}>
                <Spin spinning={loading} delay={300}>
                    <div style={{minHeight: 300}}>
                        {renderScheduleGantt}
                        {(visibleButtons.includes(RectificationActionType.Submit) && planDetail?.changeStatus === "Changed") && (
                            <>
                                <div className={clsDetail.title14} style={{margin: "40px 0 20px 0"}} id="change">变更信息</div>
                                {renderChangeInfoBox}
                            </>
                        )}
                        {visibleButtons.includes(RectificationActionType.Submit) && (
                            <>
                                <div className={clsDetail.title14} style={{margin: "40px 0 20px 0"}} id="flow">审批流程</div>
                                {renderApprovalProcess}
                            </>
                        )}
                    </div>
                </Spin>
            </div>
            {renderRectifyButtons}
            <FlowChart
                visible={flowChartVisible}
                onCancel={() => setFlowChartVisible(false)}
                data={flowChartData}
                flowState={reformDetail?.processStatus as FlowState}
            />
            <ComDrawer
                title="审批信息"
                width={600}
                visible={approvalInfoVisible}
                destroyOnClose
                maskClosable
                footer={null}
                onCancel={() => setApprovalInfoVisible(false)}
            >
                <div style={{padding: "15px"}}>
                    <CommentComp commentList={commentList} />
                </div>
            </ComDrawer>
            <ChangeInfoModal
                visible={changeInfoModalVisible}
                onCancel={() => setChangeInfoModalVisible(false)}
                detail={planDetail}
                fileList={planDetail?.changeFileList ?? []}
            />
        </div>
    );
};

export default OperationApproval;
