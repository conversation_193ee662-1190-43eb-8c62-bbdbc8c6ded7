import React, {useContext} from "react";
import {But<PERSON>} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import {degradeTask} from "../../../gantt/taskUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const DegradeButton = () => {
    const cls = useStyles();
    const {isWbs, checkoutStatus} = useContext(EditorContext);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-jiangji" fontSize={20} />}
            onClick={degradeTask}
            disabled={isWbs}
        >
            降级
        </Button>
    );
};

export default DegradeButton;
