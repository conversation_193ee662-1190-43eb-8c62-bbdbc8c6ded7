import React, {ReactNode} from "react";
import {createUseStyles} from "react-jss";
import clsx from "clsx";
import {Modal, ModalProps, Row, Col} from "antd";
import {CloseOutlined} from "@ant-design/icons";
import Color from "../../assets/css/Color";

const useStyles = createUseStyles({
    footerBtn: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: 48,
        color: "#FFFFFF",
        cursor: "pointer",
    },
    cancelBtn: {
        background: "#33394D",
    },
    okBtn: {
        background: Color.primary,
    },
});

interface ComModalProps extends ModalProps {
    children?: ReactNode;
    renderFooterStatus?: boolean;
}

const ComModal = (props: ComModalProps) => {
    const {
        title,
        onOk,
        onCancel,
        okText = "确定",
        cancelText = "取消",
        width = 480,
        keyboard = false,
        closable = true,
        maskClosable = false,
        bodyStyle,
        destroyOnClose = true,
        centered = true,
        children,
        renderFooterStatus = true,
        ...other
    } = props;
    const cls = useStyles();

    const renderHeader = (
        <Row justify="space-between" align="middle" style={{height: "48px", padding: "0 16px"}}>
            <Col style={{fontSize: 16, fontWeight: "bold", color: "#061127"}}>
                {title}
            </Col>
            {closable && <CloseOutlined onClick={onCancel} />}
        </Row>
    );

    const renderFooter = (
        <Row justify="space-between">
            <Col span={12}>
                {onCancel !== undefined && <div className={clsx([cls.footerBtn, cls.cancelBtn])} onClick={onCancel}>{cancelText}</div>}
            </Col>
            <Col style={{width: "calc(50% - 1px)"}}>
                {onOk !== undefined && <div className={clsx([cls.footerBtn, cls.okBtn])} onClick={onOk}>{okText}</div>}
            </Col>
        </Row>
    );

    return (
        <Modal
            width={width}
            keyboard={keyboard}
            closable={false}
            destroyOnClose={destroyOnClose}
            maskClosable={maskClosable}
            bodyStyle={{padding: 0, ...bodyStyle}}
            footer={false}
            centered={centered}
            {...other}
        >
            {renderHeader}
            {children}
            {renderFooterStatus && renderFooter}
        </Modal>
    );
};

export default ComModal;
