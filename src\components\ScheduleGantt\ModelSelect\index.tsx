/* eslint-disable */
import React, {FC, useEffect, useCallback, useRef, useState} from "react";
import ReactDOM from "react-dom";
import Spin from "antd/lib/spin";
import {useParent} from "@luban/react-open-tab";
import {message} from "antd";
import {getMotorId, TreeNode, projTypetoStr, ProjInfo, getProjInfo} from "./request";
import {getServicesUrl} from "../api/common";
import {getToken} from "../api/commonFetch";

export interface CheckBind {
    ppid?: number;
    projName?: string;
    floor?: string;
    projType?: number;
    spec?: string;
    compClass?: string;
    subClass?: string;
    attrname?: string;
    handle?: string;
    treenode?: string;
    treePaths?: string[];
    startDate?: number;
    endDate?: number;
    preStartDate?: number;
    preEndDate?: number;
}

export interface PlanData {
    bimGuid: string;
    startDate?: number;
    endDate?: number;
    preStartDate?: number;
    preEndDate?: number;
}

export interface SelectResult {
    id: string;
    path: string[];
}

export interface BimInfo {
    bimGuid: string;
    floor: string | undefined;
}
export enum BindType {
    binbdProj = 1,
    bindComp = 2,
    bindClass = 3,
    bindGroup = 4
}

export interface ModelSelectProps {
    ppid?: number | undefined;
    show: boolean;
    extra?: unknown;
    showSpin: boolean;
    projName?: string;
    onSelect?: (result: SelectResult[], extra: unknown) => void;
    onCancel?: () => void;
    selected?: string[];
    selectInfo?: CheckBind[];
    selectType?: BindType;
    planData?: PlanData[];
}

const traversalTree = (tree: TreeNode[], {path}: {path: string[]}): any => tree.map(
    (node) => (node.treeNodeList === null || node.treeNodeList.length === 0) ?
        {path: [...path, node.name]} :
        traversalTree(node.treeNodeList, {path: [...path, node.name]}))

const ModelSelect: FC<ModelSelectProps> = (props) => {
    const {ppid, show, showSpin, extra, onSelect, onCancel, selected, projName, selectInfo, selectType, planData} = props;
    // ppid = 53; // todo: remove this line;

    const motorId = useRef<string | undefined>(undefined);
    const childName = useRef<string>("");
    const childLoaded = useRef(false);
    const notifyChild = useRef<((name: string, type: string, data?: {}) => void) | undefined>(undefined);
    const projInfo = useRef<ProjInfo | undefined>(undefined);
    const [showSpinFlag, setShowSpinFlag] = useState<boolean>(false);
    // const componentTree = useRef<{path: string[], ids: string[]}[]>([]);

    const sendMessage = useCallback((message: string, data?: {}) => {
        if (notifyChild.current !== undefined && childName.current !== "") {
            notifyChild.current(childName.current, message, data);
        }
    }, []);

    const setProject = useCallback(() => {
        if (motorId.current !== undefined) {
            sendMessage("ChangeProject", {
                id: motorId.current,
                changePpid: ppid,
                openApiUrl: getServicesUrl("openapi"),
                changeTitle: projName,
                projectType: projInfo.current?.projTypeInt,
                clearSelection: true,
                commonToken: getToken(),
            });
            sendMessage("StopSelect");
            if (showSpin) {
                sendMessage("SelectComponent");
            }
            sendMessage("SetSelectComponent", {ids: []});
            if (selectInfo && selectType) {
                const info: BimInfo[] = [];
                if (selectType === BindType.bindComp) {
                    selectInfo.map(val => {
                        info.push({
                            bimGuid: val.handle || "",
                            floor: val.floor ? val.floor : val.treePaths && val.treePaths.length > 0 ? val.treePaths[0] : undefined
                        })
                    })
                }
                else if (selectType === BindType.bindClass) {

                }
                sendMessage("SetSelectComponent", {ids: info});
            }
            if (planData !== undefined && planData.length > 0) {
                sendMessage("SetPlanData", {data: planData})
            }
            // sendMessage("SetComponentTree", {tree: componentTree.current, id: motorId.current})
        }
    }, [sendMessage, selected, projName, motorId, ppid, showSpin, selectInfo, selectType, projInfo, planData]);

    const onChildMount = useCallback(() => {
        if (childLoaded.current !== true) {
            childLoaded.current = true;
            console.log("onChildMount setProject");
            setProject();
        }
    }, [setProject]);
    const onMessage = useCallback((name: string, data: unknown) => {
        switch (name) {
            case "Selected":
                sendMessage("StopSelect");
                if (onSelect !== undefined) {
                    const {selected} = data as {selected: {bimGuid: string, floor: string, main_type: string, sub_type: string, treePaths: string[]}[]};
                    if (projInfo.current) {
                        onSelect(selected.map((val) => ({
                            id: val.bimGuid,
                            path: val.treePaths !== undefined ?
                                val.treePaths.filter((data) => data !== null && data !== undefined) : [val.floor]
                        })), extra);
                    }
                }
                break;
            case "SelectCancel":
                sendMessage("StopSelect");
                if (onCancel !== undefined) {
                    onCancel();
                }
                break;
        }
    }, [extra, onSelect, onCancel, sendMessage]);
    const onChildClose = useCallback(() => {
        childLoaded.current = false;
        setShowSpinFlag(false);
        if (onCancel !== undefined) {
            onCancel();
        }
    }, [onCancel]);

    const parent = useParent(
        // `${getServicesUrl("lbmotor")}/`,
        `${(window.__IWorksConfig__ as any).motorViewUrl}`,
        // "localhost:8289",
        {onChildMount, onMessage, onChildClose, oneChild: true, crossOrigin: true},
        "iworks-table-name"
    ); // TODO: 修改url

    const {openChild} = parent;
    notifyChild.current = parent.notifyChild;

    useEffect(() => {
        if (show === true && ppid !== undefined) {
            const localPpid = ppid;
            getProjInfo(localPpid).then((data) => {
                if (data.success) {
                    projInfo.current = {
                        ppid: data.data.ppid,
                        projName: data.data.projName,
                        projType: projTypetoStr(data.data.projTypeInt),
                        projTypeInt: data.data.projTypeInt
                    };
                }
                getMotorId(localPpid).then((id) => {
                    if (id && id.length !== 0) {
                        setShowSpinFlag(showSpin === true);
                        motorId.current = id;
                        childName.current = openChild();
                        console.log("getMotorId setProject");
                        setProject();
                    }
                    else {
                        if (onCancel !== undefined) {
                            onCancel();
                        }
                        message.error(`${projName}工程轻量化未处理或抽取失败`);
                    }
                }).catch((err) => {
                    console.log(err);
                    if (onCancel !== undefined) {
                        onCancel();
                    }
                });
            })
        }
    }, [ppid, show, openChild, setProject, onCancel, setShowSpinFlag]); // TODO: 重复请求接口
    return (
        <Spin spinning={showSpinFlag && showSpin}>
            {showSpinFlag && showSpin && ReactDOM.createPortal(<div style={{
                position: "fixed",
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                zIndex: 10000,
                backgroundColor: "gray",
                opacity: 0.8,
                pointerEvents: "unset",
                fontSize: 48,
                textAlign: "center"
            }}>{"请在弹出窗口中选择构件"}</div>, document.body)}
        </Spin>
    )
}

export default ModelSelect;
