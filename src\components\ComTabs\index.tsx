import React, {forwardRef} from "react";
import {Tabs, TabsProps} from "antd";
import useComTabsStyles from "./style";

const ComTabs = (props: TabsProps, ref?: unknown) => {
    const {
        children,
        ...otherProps
    } = props;
    const cls = useComTabsStyles();

    return (
        <Tabs type="card" className={cls.tabsBox} size="large" {...otherProps}>
            {children}
        </Tabs>
    );
};

export default forwardRef(ComTabs);
