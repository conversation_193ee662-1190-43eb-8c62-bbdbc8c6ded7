import {FormInstance, Input, InputProps, message} from "antd";
import React, {useState} from "react";
import {createUseStyles} from "react-jss";
import CheckSubOption, {CheckSubOptionProps} from ".";
import {CheckSubOptionValueType} from "./MultipleChoice";

const useStyle = createUseStyles({
    box: {},
});

interface FormSingleCheckSubOptionProps extends Omit<InputProps, "value" | "onChange" | "form"> {
    value?: CheckSubOptionValueType;
    onChange?: CheckSubOptionProps["onChange"];
    form: FormInstance;
    // 工程类别key
    projectCategoryKey: string;
    // 模块类型
    moduleType: string;
}

const FormSingleCheckSubOption = (props: FormSingleCheckSubOptionProps) => {
    const {value, onChange, form, projectCategoryKey, moduleType, ...other} = props;
    const cls = useStyle();
    const [visible, setVisible] = useState<boolean>(false);
    const [projectCategoryId, setProjectCategoryId] = useState<string>("");

    const handleClick = () => {
        const tempProjectCategoryId = form.getFieldValue(projectCategoryKey);
        if (tempProjectCategoryId === undefined) {
            message.info("请先选择工程类型");
        } else {
            setProjectCategoryId(tempProjectCategoryId);
            setVisible(true);
        }
    };

    const handleClose = () => {
        setVisible(false);
    };

    return (
        <div className={cls.box}>
            <Input autoComplete="off" {...other} onClick={handleClick} value={value?.subOptionContent} />
            <CheckSubOption
                isVisible={visible}
                moduleType={moduleType}
                projectCategoryId={projectCategoryId}
                onClose={handleClose}
                value={value}
                onChange={onChange}
            />
        </div>
    );
};

export default FormSingleCheckSubOption;
