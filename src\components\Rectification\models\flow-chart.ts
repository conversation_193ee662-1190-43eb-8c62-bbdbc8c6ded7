export interface FlowType {
    id: string;
    name: string;
}

export interface LineInfo {
    id: string;
    parentIds: string[];
    childIds: string[];
}

export interface FlowLine {
    id?: string;
    sourceTenantTaskId: string;
    targetTenantTaskId: string;
    priority: number;
}

export interface ApprovalPostVO {
    postName: string;
}

export interface ApprovalRoleVO {
    roleName: string;
}

export interface ApprovalUserVO {
    id: string;
    userName: string;
    realName?: string;
    portraitUuid?: string;
    isLeave?: boolean;
}

export interface FlowNode {
    flowNodeId: string;
    flowNodeName: string;
    flowNodeType: "TASK_NODE" | "JOIN_NODE" | "END_NODE";
    isOrApproval: boolean;
    type: number;
    approvalPosts: ApprovalPostVO[];
    approvalRoles: ApprovalRoleVO[];
    approvalUsers: ApprovalUserVO[];
}

export interface ConditionFlowChartData {
    historyFlowNodeIds?: string[] | null;
    flowLineList: FlowLine[];
    flowNodeList: FlowNode[];
    rootNodeId: string;
}
