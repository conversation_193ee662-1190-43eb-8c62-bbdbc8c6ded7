import {Key} from "react";
import {EventDataNode, DataNode} from "antd/lib/tree";
import {WarnTypeListResult} from "../../../../api/sandManager/type";

export interface CheckInfo<TreeDataType extends DataNode> {
    event: "check";
    node: EventDataNode;
    checked: boolean;
    nativeEvent: MouseEvent;
    checkedNodes: TreeDataType[];
    checkedNodesPositions?: {
        node: TreeDataType;
        pos: string;
    }[];
    halfCheckedKeys?: Key[];
}
type tabOptions = "ebsList" | "workProcessList" | "defaultStatus" | "warningList";
export type CheckedInfoType = {
    [key in tabOptions]?: {
        isCheckAll: boolean;
        checkedKeys: Key[];
    };
};
export interface StatusTreeDataType {
    [k: string]: string | StatusTreeDataType[] | undefined | number;
    title: string;
    key: string;
    color?: string;
    children?: StatusTreeDataType[];
}
export interface WarningTreeDataType extends Partial<WarnTypeListResult> {
    [k: string]: string | TreeDataType[] | undefined | number;
    title: string;
    key: string;
    children?: TreeDataType[];
}
