import React from "react";
import MyIcon from "../../../../components/MyIcon";


interface Props {
    text: string;
    value: number;
}
// planApproveStatusList
const RenderApprovalStatus = (props: Props) => {
    const {text, value} = props;
    if (value === 3) {
        // 审批完成
        return (
            <div style={{padding: "3px 4px", backgroundColor: "#EBFAED", color: "#0F3916", width: "fit-content"}}>
                <MyIcon type="icon-hegemianxing" style={{color: "#2DA641", marginRight: 4}} />
                {text}
            </div>
        );
    }
    // 审批中
    return (
        <div style={{padding: "3px 4px", backgroundColor: "#E9EFFC", color: "#0A1C42", width: "fit-content"}}>
            <MyIcon type="icon-jinhangzhong" style={{color: "#1F54C5", marginRight: 4}} />
            {text}
        </div>
    );
};

export default RenderApprovalStatus;
