const UploadFetch = async (
    url: string,
    file: File,
    onProgress: (percent: number) => void
): Promise<string> => new Promise((res, rej) => {
    const form = new FormData();
    form.append("file", file as Blob);
    const xhr = new XMLHttpRequest();
    xhr.open("post", url);
    xhr.onload = (e) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (e.target?.status === 200) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            res(e.target.response);
        }
    };
    xhr.onerror = rej;
    if (<PERSON><PERSON>an(xhr.upload) === true) {
        xhr.upload.onprogress = (e) => {
            const {loaded, total} = e;
            const percent = Math.round(loaded / total * 100);
            onProgress(percent);
        };
    }
    xhr.send(form);
});

export default UploadFetch;
