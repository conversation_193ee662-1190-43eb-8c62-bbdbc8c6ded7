import {ColumnType} from "antd/lib/table";
import React from "react";
import {SecurityListSatuteGistType, QualityListReferenceType} from "../../api/hiddenDangerCheck/type";
import renderTableText from "../renderTableText";

export const getSecurityColumns = (): ColumnType<SecurityListSatuteGistType>[] => [
    {
        key: "content",
        title: "参考法规",
        dataIndex: "content",
        align: "left",
        width: 222,
    },
    {
        key: "detail",
        title: "法规详情",
        dataIndex: "detail",
        align: "left",
        width: 440,
        render: (detail: string) => <span style={{whiteSpace: "pre-line"}}>{detail.replaceAll("。", "。\n")}</span>
    }
];

export const getQualityColumns = (): ColumnType<QualityListReferenceType>[] => [
    {
        key: "name",
        title: "参考文件",
        dataIndex: "name",
        align: "left",
        width: 320,
        render: renderTableText
    }
];
