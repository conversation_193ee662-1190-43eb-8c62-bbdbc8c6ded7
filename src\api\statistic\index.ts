/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import * as TYPE from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

/** wbs节点统计（分层获取） */
export const getWbsStatisticByParent = async (data: TYPE.GetWbsStatisticBody) => Fetch<TYPE.WbsStatisticListResult>({
    methods: "post",
    url: `${baseUrl}/sphere/plan/statistic/wbsStatisticByParent`,
    data
});

/** wbs节点统计（根据名称检索） */
export const getWbsStatisticByName = async (data: TYPE.GetWbsStatisticByNameBody) => Fetch<TYPE.WbsStatisticListResult>({
    methods: "post",
    url: `${baseUrl}/sphere/plan/statistic/wbsStatisticByName`,
    data
});

/** 获取规则 */
export const getRules = async () => Fetch<TYPE.GetRulesResult>({
    methods: "get",
    url: `${baseUrl}/sphere/rules/setting/list`,
});

/** 获取规则 */
export const updateRule = async (data: TYPE.Rule) => Fetch({
    methods: "post",
    url: `${baseUrl}/sphere/rules/setting/update`,
    data
});
