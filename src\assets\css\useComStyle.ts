import {createUseStyles} from "react-jss";
import Color from "./Color";

const comStyle = {
    rootBox: {
        padding: "24px", // 蒋艳萍设计说列表的那个壳子里面都应该是24px间距
        height: "100%",
        width: "100%",
    },
    title: {
        fontWeight: 700,
        fontSize: "20px",
        color: Color["text-1"],
    },
    table: {
        overflowY: "auto",
        "& .darkRow": {
            backgroundColor: `${Color["bg-2"]}`,
            "& .ant-table-cell": {
                backgroundColor: Color["bg-2"],
            }
        },
        "& .ant-table-row:hover": {
            "& .ant-table-cell": {
                backgroundColor: Color["bg-4"]
            }
        },
        "& .ant-table-thead": {
            "& .ant-table-cell": {
                padding: "12px 24px",
                fontWeight: 700,
                color: Color["text-1"],
                backgroundColor: Color["bg-3"]
            }
        },
        "& .ant-table-tbody": {
            "& .ant-table-cell": {
                padding: "6px 24px",
            }
        },
        "&::-webkit-scrollbar": {
            width: 0,
        }
    },
    dropdownMenu: {
        padding: "6px 0",
        marginTop: 4,
        borderRadius: 6,
        "& .ant-dropdown-menu-item": {
            width: 117,
            height: 36,
            display: "flex",
            alignItems: "center",
            "&:hover": {
                backgroundColor: Color["bg-4"],
            },
            "& span": {
                display: "inline-block",
                width: "100%",
                heigth: "100%",
                cursor: "pointer",
                marginBottom: 0,
                textAlign: "center",
                color: Color["text-1"]
            }
        }
    },
    deleteWarningTip: {
        "& .ant-modal-close": {
            top: "20%",
            right: 0
        },
        "& .ant-modal-content": {
            display: "inline-block"
        },
        "& .ant-modal-confirm-body": {
            paddingLeft: 22
        },
        "& .ant-modal-confirm-btns": {
            display: "none"
        }
    }
};

const useComStyle = createUseStyles(comStyle);

export default useComStyle;
