export interface PromiseQueueOptions {
    intervalNumber: number;
    isPopMode: boolean;
}

export default class PromiseQueue {
    private interval: NodeJS.Timer | null = null;

    // 选项
    private options: PromiseQueueOptions = {
        intervalNumber: 500,
        isPopMode: false,
    };

    // 锁定
    private lock = false;

    private queue: (() => void)[] = [];

    constructor(optionsParam: PromiseQueueOptions) {
        this.options = optionsParam;
        const {intervalNumber, isPopMode} = this.options;
        this.interval = setInterval(() => {
            if (this.queue.length > 0 && !this.lock) {
                this.lock = true;
                if (isPopMode) {
                    const f = this.queue.pop();
                    this.queue = [];
                    if (typeof f !== "undefined") {
                        f();
                    }
                } else {
                    const f = this.queue.shift();
                    if (typeof f !== "undefined") {
                        f();
                    }
                }
                this.lock = false;
            }
        }, intervalNumber);
    }

    createInstance(options: PromiseQueueOptions): PromiseQueue {
        return new PromiseQueue(options);
    }

    push(f: () => void) {
        this.queue.push(f);
    }

    clear() {
        this.queue = [];
    }

    destory() {
        const {interval} = this;
        if (interval !== null) {
            clearInterval(interval);
        }

        this.queue = [];
    }
}
