import React, {FC, useEffect} from "react";
import {HashRouter as Router} from "react-router-dom";
import {renderRoutes} from "react-router-config";
import {Provider} from "react-redux";
import _ from "lodash-es";
import {PersistGate} from "redux-persist/integration/react";
import {ConfigProvider} from "antd";
import zhCN from "antd/lib/locale/zh_CN";
import qs from "query-string";
import routes from "./router";
import store, {dispatch, getState, persistor} from "./store";
import {setFrom} from "./store/common/action";
import {AppUrlSearchType} from "./assets/ts/globalType";
import "./assets/css/common.css";
import "./assets/css/ant.less";
import "./assets/css/global.less";


const App: FC = () => {
    useEffect(() => {
        const {from} = getState().commonData;
        const urlSearch: AppUrlSearchType = qs.parse(window.location.href.split("?")[1]);
        if (urlSearch.from === "newStandard" && (urlSearch.token ?? "").length > 0) {
            dispatch(setFrom(urlSearch.from));
            localStorage.setItem("viewType", "main");
            return;
        }
        if (from !== "") {
            return;
        }
        dispatch(setFrom(""));
    }, []);

    return (
        <ConfigProvider locale={zhCN}>
            <Provider store={store}>
                <PersistGate loading={null} persistor={persistor}>
                    <Router>{renderRoutes(routes)}</Router>
                </PersistGate>
            </Provider>
        </ConfigProvider>
    );
};

export default App;
