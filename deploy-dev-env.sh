#!/bin/bash

DIST_FOLDER="dist"
REMOTE_SERVER="*************"
REMOTE_FOLDER="/root/deploy-dists"
ZIP_FILE_NAME="plan-manager-web"
DOCKER_CONTAINER="1660-builder-nginx-1"
DOCKER_FOLDER="/luban/pkg/front-end"
SSH_USERNAME="root"

echo "======================Build dist...======================"
rm -rf $DIST_FOLDER
yarn build:193

echo "======================Zip dist...======================"
cd $DIST_FOLDER
zip -r $ZIP_FILE_NAME.zip *

echo "======================Copy zip file to remote server...======================"
scp -o "UserKnownHostsFile=/dev/null" -o "StrictHostKeyChecking=no" $ZIP_FILE_NAME.zip $SSH_USERNAME@$REMOTE_SERVER:$REMOTE_FOLDER

echo "======================At remote server, copy zip file to docker container...======================"
ssh -o "UserKnownHostsFile=/dev/null" -o "StrictHostKeyChecking=no" "$SSH_USERNAME@$REMOTE_SERVER" "docker cp $REMOTE_FOLDER/$ZIP_FILE_NAME.zip $DOCKER_CONTAINER:$DOCKER_FOLDER/$ZIP_FILE_NAME.zip"

echo "======================At remote server, unzip files...======================"
ssh -o "UserKnownHostsFile=/dev/null" -o "StrictHostKeyChecking=no" "$SSH_USERNAME@$REMOTE_SERVER" "docker exec $DOCKER_CONTAINER rm -rf $DOCKER_FOLDER/$ZIP_FILE_NAME"
ssh -o "UserKnownHostsFile=/dev/null" -o "StrictHostKeyChecking=no" "$SSH_USERNAME@$REMOTE_SERVER" "docker exec $DOCKER_CONTAINER unzip $DOCKER_FOLDER/$ZIP_FILE_NAME.zip -d $DOCKER_FOLDER/$ZIP_FILE_NAME"