import React, {useCallback, useEffect, useMemo, useState} from "react";
import {DatePicker, Cascader} from "antd";
import moment, {Moment} from "moment";
import {useSelector} from "react-redux";
import {getCycleDate, getDateFormat, getDateRangeWithWeek, getDateType, getWeekOfMonth} from "./utils";
import {PlanCycleCascaderOption} from "./type";
import {GetPlanListUsedCycleItemType, PlanCycleType} from "../../api/Preparation/type";
import {getPlanListUsedCycle} from "../../api/Preparation";
import {RootState} from "../../store/rootReducer";

interface PlanCycleDatePickerProps {
    cycleType: PlanCycleType;
    value?: Moment;
    onChange?: (value: Moment | null) => void;
    minDate?: Moment;
    maxDate?: Moment;
}

const PlanCycleDatePicker: React.FC<PlanCycleDatePickerProps> = (props) => {
    const {
        cycleType,
        value: _value,
        onChange,
        minDate,
        maxDate
    } = props;
    const {curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const dateType = useMemo(() => getDateType(cycleType), [cycleType]);
    const [usedCycles, setUsedCycles] = useState<GetPlanListUsedCycleItemType[]>([]);
    const [value, setValue] = useState(_value);
    const [cascaderValue, setCascaderValue] = useState<number[]>([]);
    const [cascaderOptions, setCascaderOptions] = useState<PlanCycleCascaderOption[]>([]);
    const [monthOptions] = useState<PlanCycleCascaderOption[]>(new Array(12).fill(0).map((_, index) => ({
        value: index,
        label: `${index + 1}月`,
        type: "month",
        children: [],
        isLeaf: false,
    })));

    useEffect(() => {
        if (curSectionInfo === null) {
            return;
        }
        getPlanListUsedCycle({
            nodeId: curSectionInfo.nodeId,
            nodeType: `${curSectionInfo.nodeType}`,
            type: cycleType
        }).then((res) => {
            if (res.success) {
                setUsedCycles(res.data);
            }
        });
    }, [curSectionInfo, cycleType]);

    useEffect(() => {
        if (_value !== undefined) {
            if (cycleType === "WEEK") {
                // const {year, month, daysInMonth} = _value;
                const year = _value.year();
                const month = _value.month();
                const week = getWeekOfMonth(_value);
                setCascaderValue([year, month, week]);
            } else {
                setValue(_value);
            }
        } else {
            setValue(undefined);
        }
    }, [_value, cycleType]);

    useEffect(() => {
        if (minDate !== undefined && maxDate !== undefined) {
            const minYear = minDate.year();
            const maxYear = maxDate.year();
            const yearList: PlanCycleCascaderOption[] = [];
            for (let year = minYear; year <= maxYear; year++) {
                yearList.push({
                    value: year,
                    label: `${year}年`,
                    type: "year",
                    children: monthOptions
                });
            }
            setCascaderOptions(yearList);
        }
    }, [minDate, maxDate, monthOptions]);

    const handleDatePickerChange = useCallback((val: Moment | null) => {
        if (onChange instanceof Function && val !== null) {
            onChange(getCycleDate(val, cycleType));
        }
    }, [onChange, cycleType]);

    const handleCascaderChange = useCallback((_values, selectOptions) => {
        // const options = values as PlanCycleCascaderOption[];
        // console.log("handleCascaderChange", values);
        // console.log("handleCascaderChange", selectOptions);
        if (selectOptions.length === 3) {
            const [, , weekOption] = selectOptions;
            const {date} = weekOption;
            // console.log("startDate", startDate);
            if (date !== undefined && date !== null && onChange instanceof Function) {
                onChange(date);
            }
        }
    }, [onChange]);

    const loadData = useCallback((selectedOptions) => {
        // console.log("selectedOptions", selectedOptions);
        if (selectedOptions.length !== 2) {
            return;
        }
        const yearOption = selectedOptions[0];
        const monthOption = selectedOptions[1];
        if (yearOption.type === "year" && monthOption.type === "month") {
            const year = yearOption.value;
            const month = monthOption.value;
            const endOfMonth = moment([year, month]).endOf("month");
            const weekCount = getWeekOfMonth(endOfMonth);
            monthOption.children = new Array(weekCount).fill(0).map((_, index) => {
                const week = index + 1;
                const [startDate] = getDateRangeWithWeek(year, month, week);
                return {
                    value: week,
                    label: `第${week}周`,
                    type: "week",
                    date: startDate,
                    disabled: usedCycles.some((item) => moment(item.cycle).isSame(startDate))
                };
            });
            setCascaderOptions((options) => [...options]);
        }
    }, [usedCycles]);

    if (cycleType === "WEEK") {
        return (
            <Cascader<PlanCycleCascaderOption>
                value={cascaderValue}
                onChange={handleCascaderChange}
                displayRender={() => {
                    const [year, month, week] = cascaderValue;
                    const labels = [];
                    labels.push(year !== undefined ? `${year}年` : "");
                    labels.push(month !== undefined ? `${month + 1}月` : "");
                    labels.push(week !== undefined ? `第${week}周` : "");
                    return labels.join("");
                }}
                loadData={loadData}
                options={cascaderOptions}
                placeholder="请选择计划周期"
            />
        );
    }

    return (
        <DatePicker
            value={value}
            onChange={handleDatePickerChange}
            picker={dateType}
            format={getDateFormat(dateType)}
            style={{width: "100%"}}
            placeholder="请选择计划周期"
            disabled={cycleType === "MASTER"}
            disabledDate={(date) => {
                const cycleDate = getCycleDate(date, cycleType);
                if (usedCycles.some((item) => moment(item.cycle).isSame(cycleDate))) {
                    return true;
                }
                if (date.isBetween(minDate, maxDate) === false) {
                    return true;
                }
                return false;
            }}
        />
    );
};
export default PlanCycleDatePicker;
