import Postmate, {resolve} from "postmate";
import React, {useCallback, useEffect, useState, ReactElement} from "react";
import {useDispatch} from "react-redux";
import {useHistory} from "react-router-dom";
import {RootState} from "../../../store/rootReducer";
import {
    setOrgInfo,
    setSectionList,
    setCurSectionInfo,
    setToken,
    setUserInfo,
    setMenuId,
    setAuthCodeList,
    setLastSelectedMenu,
    setLeafMenuId
} from "../../../store/common/action";
import {setChildApi} from "../../../assets/ts/utils";

const IframeBox = (props: {children: ReactElement}) => {
    const {children} = props;
    const dispatch = useDispatch();
    const history = useHistory();
    // iframe是否初始化完成,从父那里获取到数据
    const [isInit, setIsInit] = useState<boolean>(false);

    const connectInit = useCallback(
        (val: Pick<RootState["commonData"], "authCodeList" | "menuId" | "token" | "userInfo" | "lastSelectedMenu"|
        "workGuideInfo" | "leafMenuId">) => {
            // eslint-disable-next-line no-console
            console.log("connectInit", val);
            setIsInit(true);
            dispatch(setToken(val.token));
            dispatch(setUserInfo(val.userInfo));
            dispatch(setMenuId(val.menuId));

            // dispatch(setAuthCodeList(val.authCodeList));
            dispatch(setLastSelectedMenu(val.lastSelectedMenu));
            dispatch(setLeafMenuId(val.leafMenuId));
            if (val.workGuideInfo !== undefined && val.workGuideInfo !== null) {
                sessionStorage.setItem("workGuideInfo", JSON.stringify(val.workGuideInfo));
            } else {
                sessionStorage.removeItem("workGuideInfo");
            }
            const {router} = val as unknown as {router: string};
            if (router !== undefined) {
                history.push(router);
            }
        },
        [dispatch, history]
    );

    const orgChange = useCallback(
        (val: {orgId: string; orgType: number; orgName: string; deptDataType: number; clickType: 0 | 1}) => {
            dispatch(setCurSectionInfo(null));
            dispatch(setSectionList([]));
            dispatch(setOrgInfo({...val, deptDataType: Number(val.deptDataType)}));
        },
        [dispatch],
    );

    const routerChange = useCallback(
        (val: {router: string}) => {
            if (val.router !== "") {
                history.push(val.router);
            }
        },
        [history],
    );

    const init = useCallback(
        async () => Promise.race([
            // eslint-disable-next-line no-shadow
            new Promise((resolve) => {
                new Postmate.Model({
                    name: "framebox",
                    init: connectInit,
                    updateOrgId: orgChange,
                    updateRouter: routerChange,
                }).then((par: Postmate.ChildAPI) => {
                    // eslint-disable-next-line no-console
                    console.log("Postmate.Model", par);
                    setChildApi(par);
                    resolve(par);
                }).catch(() => {
                    resolve(0);
                });
            }),
            // eslint-disable-next-line no-shadow
            new Promise((resolve) => {
                setTimeout(() => {
                    // eslint-disable-next-line no-console
                    console.log("Postmate.Model 4000");
                    resolve(0);
                }, 4000);
            })
        ]),
        [connectInit, orgChange, routerChange]
    );

    useEffect(() => {
        init().then((res: unknown) => {
            // eslint-disable-next-line no-console
            console.log("init", res);
            if (Boolean(res) === false) {
                // eslint-disable-next-line no-console
                console.log("连接失败...");
            }
        });
    }, [init]);

    if (isInit) {
        return children;
    }
    return (
        <div />
    );
};

export default IframeBox;
