/* eslint-disable max-nested-callbacks */
/* eslint-disable max-lines-per-function */
import {PlusOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, Col, Pagination, Row, Space, message} from "antd";
import React, {useCallback, useEffect, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import {useParams} from "react-router-dom";
import {useForm} from "antd/lib/form/Form";
import {isEmpty} from "lodash-es";
import {useDebounceEffect} from "ahooks";
import {getHiddenDangerCheckList, delHiddenDangerCheck, getHiddenDangerCheckDetail, exportExcel} from "../../../api/hiddenDangerCheck";
import {GetHiddenDangerCheckListType} from "../../../api/hiddenDangerCheck/type";
import useComState from "../../../assets/hooks/useComState";
import QueryForm from "../../../components/QueryForm";
import {RootState} from "../../../store/rootReducer";
import AddAndEdit from "./AddAntEdit";
import {businessType, columnsInit, ComColumnsType, QueryFormType, queryItemList, reformStatusList, reformStatusType} from "./data";
import useStyles from "./style";
import View from "./view";
import TableColumnsControl from "../../../components/TableColumnsControl";
import {getReformType} from "../../../api/rectification";
import {setCurrentCheckInfo} from "../../../store/rectification/detail/actions";
import {setCurrentReformId, setCurrentTempId} from "../../../store/rectification/template/actions";
import ComTable from "../../../components/ComTable";
import {groupBy, isDefined} from "../../../assets/ts/utils";
import RectifyAdd from "../../Rectification/crud/add";
import RectifyDetail from "../../Rectification/crud/detail";
import TipModal from "../../../components/TipModal";
import PermissionCode from "../../../components/PermissionCode";
// import {bindWBSChange} from "../../../../assets/ts/redux";
// import {afterGetTable, beforeGetTable} from "../../../../assets/ts/unify";
import FormProjectCategory from "../../../components/CheckSubOption/FormProjectCategory";
import FormSingleCheckSubOption from "../../../components/CheckSubOption/FormSingleCheckSubOption";
import {dealTableData} from "../../../components/FormWbsEbs/data";
import selectQueryWbsEbsParams from "../../../store/select/wbsEbsQuery";
import {getPreparationList} from "../../../api/Preparation";
import {planTypeList} from "../../../api/planApproval/type";
import TableLayout from "../../../components/TableLayout";
import {getState} from "../../../store";
import {PlanPreparationItemType} from "../../../api/Preparation/type";
import renderTableText from "../../../components/renderTableText";
import {tableDebounceEffectOptions} from "../../../../uikit/ts/util";
import ComModalExport from "../../../components/ComModal/ComModalExport";

interface Option {
    value: string | number;
    label: string;
    children?: Option[];
}

// 判断有没标段信息
const isHasSectionInfo = () => {
    if (getState().commonData.curSectionInfo === null) {
        message.warning("请先选择标段");
        return false;
    }
    return true;
};

const HiddenDangerCheck = () => {
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {curSectionInfo} = useSelector((state: RootState) => state.commonData);
    // const {rectifyStatus} = useSelector((state: RootState) => state.rectificationTemp);
    const wbsEbsQueryParams = useSelector(selectQueryWbsEbsParams);
    const {type = "PLAN"} = useParams<{type: string}>();
    const cls = useStyles();
    const [state, setState] = useComState({queryFormInit: queryItemList});
    const [columns, setColumns] = useState<ComColumnsType[]>(columnsInit);
    const [tableData, setTableData] = useState<GetHiddenDangerCheckListType[]>([]);
    const [rowId, setRowId] = useState<string>("");
    const [queryFormData, setQueryFormData] = useState({});
    const [planList, setPlanList] = useState<PlanPreparationItemType[]>([]);
    const [form] = useForm();
    const dispatch = useDispatch();

    const refreshQueryItemList = useCallback(
        (planOptions: Option[]) => {
            const arr = queryItemList.map((item) => {
                if (item.key === "projectCategoryId") {
                    return {
                        ...item,
                        customNode: <FormProjectCategory form={form} itemNameKey="projectCategoryId" valueAuto moduleType="SECURITY" />
                    };
                }
                if (item.key === "subOptionContent") {
                    return {
                        ...item,
                        customNode: <FormSingleCheckSubOption form={form} projectCategoryKey="projectCategoryId" moduleType="SECURITY" />
                    };
                }
                if (item.key === "checkPlanId") {
                    return {
                        ...item,
                        typeConfig: {options: planOptions, placeholder: "全部", dropdownClassName: "customAntCascader"}
                    };
                }
                return item;
            });
            setState.setQueryFormList(arr);
        },
        [form, setState]
    );

    useEffect(
        () => {
            if (!isDefined(curSectionInfo) || Boolean(orgInfo.orgId) === false) {
                return;
            }
            getPreparationList({
                deptId: orgInfo.orgId,
                nodeId: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.id,
                nodeType: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.nodeType,
                pageNum: 1,
                pageSize: 9999
            }).then((res) => {
                const allPlan = res.data?.items ?? [];
                const planGroup = groupBy(allPlan, (node) => node.type);
                const tempPlanList: Option[] = planGroup.map((v) => {
                    const label = planTypeList.find((p) => p.value === v[0])?.label ?? "";
                    return {
                        value: v[0],
                        label,
                        children: v[1].map((j) => ({value: j.id, label: j.name}))
                    };
                });
                // console.log("tempPlanList", tempPlanList);
                setPlanList(allPlan);
                refreshQueryItemList(tempPlanList);
            });
        },
        [curSectionInfo, orgInfo.orgId, refreshQueryItemList]
    );

    /* useEffect(() => {
        const arr = queryItemList.map((item) => {
            if (item.key === "projectCategoryId") {
                return {
                    ...item,
                    customNode: <FormProjectCategory form={form} itemNameKey="projectCategoryId" valueAuto moduleType={type} />
                };
            }
            if (item.key === "subOptionContent") {
                return {
                    ...item,
                    customNode: <FormSingleCheckSubOption form={form} projectCategoryKey="projectCategoryId" moduleType={type} />
                };
            }
            return item;
        });
        setState.setQueryFormList(arr);
    }, [form, setState, type]); */

    const getTableData = useCallback(
        (params: {}) => {
            if (wbsEbsQueryParams?.noSelect === true) {
                setTableData([]);
                setState.setTotal(0);
                return;
            }
            // beforeGetTable();
            if (Boolean(orgInfo) === true && curSectionInfo !== null) {
                getHiddenDangerCheckList({
                    moduleType: type,
                    deptId: orgInfo.orgId,
                    checkNodeId: curSectionInfo.isAll === true ? undefined : curSectionInfo.id,
                    nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeType,
                    page: state.curPage,
                    size: state.pageSize,
                    ...wbsEbsQueryParams,
                    ...queryFormData,
                    ...params,
                })
                    .then(async (res) => {
                        if (res.success) {
                            const tempTableData = res.data.items.map((item) => {
                                if (orgInfo.deptDataType === 1) {
                                    return {
                                        ...item,
                                        checkNodeName: orgInfo.orgName
                                    };
                                }
                                return item;
                            });
                            setTableData(await dealTableData({tableData: tempTableData}));
                            setState.setTotal(res.data.totalCount);
                        }
                        // afterGetTable();
                    })
                    .catch(() => {
                        setTableData([]);
                        // afterGetTable();
                    });
            }
        },
        [curSectionInfo, orgInfo, queryFormData, setState, state.curPage, state.pageSize, type, wbsEbsQueryParams],
    );

    useDebounceEffect(
        () => {
            getTableData({});
        },
        [getTableData],
        tableDebounceEffectOptions
    );

    const toAdd = useCallback(() => {
        if (!isHasSectionInfo()) {
            return;
        }
        setState.setCurPageType("add");
    }, [setState]);

    const toView = useCallback(
        (id: string) => {
            setRowId(id);
            setState.setCurPageType("view");
        },
        [setState],
    );

    const toEdit = useCallback((id: string) => {
        setRowId(id);
        setState.setCurPageType("edit");
    }, [setState]);

    const handleBack = useCallback((backType?: string) => {
        setState.setCurPageType("");
        if (backType === "ok") {
            getTableData({
                page: 1
            });
            setState.setCurPage(1);
            // bindWBSChange();
        }
    }, [setState, getTableData]);

    // 跳转整改页面
    const gotoRectification = useCallback(async (_record: GetHiddenDangerCheckListType, _type: "edit" | "add", _tempId?: string) => {
        const detailRes = await getHiddenDangerCheckDetail(_record.id);
        if (detailRes?.success && isDefined(detailRes.data)) {
            const {data} = detailRes;
            dispatch(setCurrentCheckInfo({
                id: _record.id,
                buildType: data.buildType,
                type: data.checkType,
                nodeId: data.nodeId,
                nodeType: data.nodeType,
                beCheckNodeId: data?.checkNodeId,
                checkSubOption: data?.optionList?.map((el) => el.subOptionContent).join(";"),
            }));
            if (_type === "edit") {
                dispatch(setCurrentReformId(_record.reformId));
                setState.setCurPageType("rectificationEdit");
            } else if (_type === "add" && typeof _tempId === "string") {
                dispatch(setCurrentTempId(_tempId));
                setState.setCurPageType("rectificationAdd");
            }
            // dispatch(setRectifyStatus(_type));
        }
    }, [dispatch, setState]);

    const handleGotoDetailZG = useCallback((_record: GetHiddenDangerCheckListType) => {
        gotoRectification(_record, "edit");
    }, [gotoRectification]);

    const handleClickZG = useCallback(async (_record: GetHiddenDangerCheckListType) => {
        const {code, result} = await getReformType({
            appModule: _record.buildType,
            nodeId: orgInfo.orgId
        });
        if (code === 200 && Array.isArray(result) && result.length > 0) {
            const temp = result[0];
            gotoRectification(_record, "add", temp.typeId);
        } else {
            message.error("当前项目部无可用的审批模板");
            handleBack("ok");
        }
    }, [gotoRectification, handleBack, orgInfo.orgId]);

    useEffect(() => {
        setColumns((prevColumns) => {
            const columnsArr: ComColumnsType[] = prevColumns.map((item) => {
                if (item.key === "checkPlanId") {
                    return {
                        ...item,
                        render: (t: string) => {
                            const planName = planList.find((v) => v.id === t)?.name ?? "";
                            return renderTableText(planName);
                        }
                    };
                }
                if (item.key === "reformStatus") {
                    return {
                        key: "reformStatus",
                        title: "整改状态",
                        dataIndex: "reformStatus",
                        align: "center",
                        mustShow: true,
                        show: true,
                        fixed: "right",
                        width: 140,
                        render(_text: number, _record: GetHiddenDangerCheckListType, _index: number) {
                            if (_text === reformStatusType.REFORM_NOT_START || _text === reformStatusType.REFORM_NEED) {
                                return "待整改";
                            }
                            const showText = reformStatusList.filter((el) => el.value === _text)[0]?.label ?? "";
                            if (_text === reformStatusType.REFORMING || _text === reformStatusType.REFORM_END) {
                                return <a onClick={() => handleGotoDetailZG(_record)}>{showText}</a>;
                            }
                            return showText;
                        }
                    };
                }
                if (item.key === "operate") {
                    return {
                        key: "operate",
                        title: "操作",
                        align: "right",
                        mustShow: true,
                        show: true,
                        width: 240,
                        fixed: "right",
                        render(_text: string, _record: GetHiddenDangerCheckListType, _index: number) {
                            return (
                                <Space>
                                    <Button type="link" onClick={() => toView(_record.id)}>
                                        查看
                                    </Button>
                                    <PermissionCode
                                        authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:edit"
                                    >
                                        <Button
                                            type="link"
                                            onClick={() => toEdit(_record.id)}
                                            disabled={_record.reformStatus === reformStatusType.REFORMING
                                                || _record.reformStatus === reformStatusType.REFORM_END}
                                        >
                                            编辑
                                        </Button>
                                    </PermissionCode>
                                    <PermissionCode
                                        authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:reform"
                                    >
                                        <Button
                                            type="link"
                                            onClick={async () => handleClickZG(_record)}
                                            disabled={_record.reformStatus !== reformStatusType.REFORM_NOT_START
                                                && _record.reformStatus !== reformStatusType.REFORM_NEED}
                                            style={{paddingRight: 0}}
                                        >
                                            发起整改
                                        </Button>
                                    </PermissionCode>
                                </Space>
                            );
                        }
                    };
                }
                return item;
            });
            return columnsArr;
        });
    }, [handleClickZG, handleGotoDetailZG, planList, toEdit, toView]);

    const handleFormFinished = (val: QueryFormType) => {
        const {projectCategoryId, subOptionContent, ...other} = val;
        const queryParams = {
            ...other,
            wbsId: subOptionContent?.subOptionId,
            checkTimeStart: !isEmpty(val.checkTime) ? val.checkTime[0].startOf("day").format("x") : null,
            checkTimeEnd: !isEmpty(val.checkTime) ? val.checkTime[1].endOf("day").format("x") : null,
            checkPlanId: Array.isArray(val.checkPlanId) ? val.checkPlanId[1] : undefined,
            checkPlanType: Array.isArray(val.checkPlanId) ? val.checkPlanId[0] : undefined
        };

        setQueryFormData(queryParams);
        getTableData({
            ...queryParams,
            page: 1
        });
        setState.setCurPage(1);
    };

    const handleReset = useCallback(() => {
        setState.setCurPage(1);
        form.resetFields();
        const param = form.getFieldsValue();
        setQueryFormData(param);
        getTableData(param);
    }, [form, getTableData, setState]);

    const handleDelete = () => {
        delHiddenDangerCheck(state.selectIds).then((res) => {
            if (res.success) {
                getTableData({});
                setState.setSelectIds([]);
                message.success("删除成功！");
                // bindWBSChange();
            }
        });
    };
    const handleExport = useCallback(() => {
        if (!isHasSectionInfo()) {
            return;
        }
        let params: {} = {
            checkNodeId: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.id,
            nodeType: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.nodeType,
            deptId: orgInfo.orgId,
            moduleType: type,
        };
        const {checkPlanId = [], checkTime, ...rest} = queryFormData as QueryFormType;
        if (state.selectIds.length > 0) {
            params = {
                ...params,
                baseIds: state.selectIds
            };
        } else {
            params = {
                ...params,
                ...rest,
                checkTimeStart: !isEmpty(checkTime) ? checkTime[0].startOf("day").format("x") : null,
                checkTimeEnd: !isEmpty(checkTime) ? checkTime[1].endOf("day").format("x") : null,
                checkPlanId: Array.isArray(checkPlanId) ? checkPlanId[1] : undefined,
                checkPlanType: Array.isArray(checkPlanId) ? checkPlanId[0] : undefined
            };
        }
        exportExcel(params, "进度检查.xlsx");
    }, [curSectionInfo, orgInfo.orgId, queryFormData, state.selectIds, type]);

    const rowSelection = {
        selectedRowKeys: state.selectIds,
        onChange: setState.setSelectIds,
        columnWidth: 60,
    };

    const paginationChange = (curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(pageSizeVal ?? 10);
        getTableData({
            page: curPageVal,
            size: pageSizeVal ?? 10
        });
    };

    if (state.curPageType === "add") {
        return <AddAndEdit type={type} back={handleBack} handleClickZG={handleClickZG} />;
    }

    if (state.curPageType === "view") {
        return <View id={rowId} edit={toEdit} back={handleBack} />;
    }

    if (state.curPageType === "edit") {
        return <AddAndEdit type={type} id={rowId} back={handleBack} handleClickZG={handleClickZG} />;
    }

    if (state.curPageType === "rectificationEdit") {
        return (
            <RectifyDetail
                back={() => handleBack("ok")}
                moduleType="PLAN_REFORM"
            />
        );
    }

    if (state.curPageType === "rectificationAdd") {
        return (
            <RectifyAdd
                back={() => handleBack("ok")}
                /* onSuccess={() => {
                    dispatch(setRectifyStatus("list"));
                    handleBack("ok");
                }} */
                moduleType="PLAN_REFORM"
            />
        );
    }

    return (
        <TableLayout businessType={businessType}>
            <div className={`box ${cls.box}`}>
                <Row className="box">
                    <Col flex="auto" className="safety-quality-table">
                        {/* <Row justify="space-between">
                            <Col>
                                <span className="title">进度管理/异常事件</span>
                            </Col>
                            <Col>
                                <ThreeAndSection />
                            </Col>
                        </Row> */}
                        <QueryForm<QueryFormType>
                            queryItemList={state.queryFormList}
                            onFormFinish={handleFormFinished}
                            onFormClear={handleReset}
                            form={form}
                        />
                        <Row justify="space-between">
                            <Col>
                                <Space>
                                    <PermissionCode
                                        authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:add"
                                    >
                                        <Button type="primary" icon={<PlusOutlined />} onClick={toAdd}>新增</Button>
                                    </PermissionCode>
                                    <PermissionCode
                                        authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:detele"
                                    >
                                        <TipModal
                                            onOk={handleDelete}
                                            buttonConfig={{
                                                disabled: state.selectIds.length === 0,
                                            }}
                                        />
                                    </PermissionCode>
                                    <ComModalExport
                                        totalNum={state.total}
                                        selectedNum={state.selectIds.length}
                                        onOk={handleExport}
                                    />
                                </Space>
                            </Col>
                            <Col className="centerY">
                                <TableColumnsControl
                                    setColumnsList={setColumns}
                                    columnsList={columns}
                                />
                            </Col>
                        </Row>
                        <ComTable
                            style={{marginTop: 16}}
                            columns={columns.filter((el: ComColumnsType) => el.show || el.mustShow)}
                            dataSource={tableData}
                            rowSelection={rowSelection}
                            pagination={false}
                            rowKey="id"
                        />
                        <Row justify="space-between" style={{marginTop: 20}}>
                            <Col>{`已选 ${state.selectIds.length} 项`}</Col>
                            <Col>
                                <Pagination
                                    total={state.total}
                                    showSizeChanger
                                    showQuickJumper
                                    current={state.curPage}
                                    pageSize={state.pageSize}
                                    onChange={paginationChange}
                                    showTotal={(totalCount: number) => `共 ${totalCount} 条`}
                                />
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </div>
        </TableLayout>
    );
};

export default HiddenDangerCheck;
