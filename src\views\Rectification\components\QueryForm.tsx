import React, {use<PERSON><PERSON>back, useMemo, useState} from "react";
import {Form, Input, Select, DatePicker, Row, Col, Space, Button} from "antd";
import {DownOutlined, UpOutlined} from "@ant-design/icons";
import {checkTypeListEasy, expireStatusList, RectifyQueryFormModel, reformStatusList} from "../interface";
// import QueryFormBtn from "../../../components/WBS/queryFormBtn";
import useStyle from "../style";
import FormProjectCategory from "../../../components/CheckSubOption/FormProjectCategory";
import FormSingleCheckSubOption from "../../../components/CheckSubOption/FormSingleCheckSubOption";

const {Option} = Select;
const {RangePicker} = DatePicker;
const FormItemStyle = {
    labelCol: {flex: "0 0 140px"},
    wrapperCol: {flex: "1 1"},
};
export const buildTypeList = [
    {value: 0, label: "施工自查"},
    {value: 1, label: "监理检查"},
    {value: 3, label: "业主检查"},
];

interface QueryFormProps {
    defaultFormData: RectifyQueryFormModel;
    onSubmit?: (values: RectifyQueryFormModel) => void;
    onReset?: (values: RectifyQueryFormModel) => void;
    moduleType: string;
}

const QueryForm = (props: QueryFormProps) => {
    const cls = useStyle();
    const {defaultFormData, onSubmit, onReset} = props;
    // const {rectifyQueryOptions} = useSelector((state: RootState) => state.rectificationTemp);
    const [formInstance] = Form.useForm<RectifyQueryFormModel>();
    const [expand, setExpand] = useState(false);

    const handleSubmit = useCallback(
        (values: RectifyQueryFormModel) => {
            if (onSubmit !== undefined) {
                const {optionsList, projectCategoryId, ...other} = values;
                onSubmit({
                    ...other,
                    checkSubOption: Boolean(values.optionsList) === true ? values.optionsList?.subOptionContent : undefined
                });
            }
        },
        [onSubmit]
    );

    const handleReset = useCallback(
        () => {
            formInstance.resetFields();
            formInstance.submit();
            if (onReset !== undefined) {
                onReset(defaultFormData);
            }
        },
        [formInstance, onReset, defaultFormData]
    );

    const expandItems = useMemo(() => {
        if (!expand) {
            return null;
        }
        return (
            <>
                <Col span={6}>
                    <Form.Item
                        label="检查形式"
                        name="checkTypeId"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <Select allowClear placeholder="全部">
                            {checkTypeListEasy.map((v) => <Option key={v.value} value={v.value}>{v.label}</Option>)}
                        </Select>
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item
                        label="检查单位"
                        name="buildType"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <Select allowClear placeholder="全部">
                            {buildTypeList.map((v) => <Option key={v.value} value={v.value}>{v.label}</Option>)}
                        </Select>
                    </Form.Item>
                </Col>
                {/* <Col span={6}>
                    <Form.Item
                        label="检查标段"
                        name="checkNodeIds"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <Select allowClear mode="multiple" placeholder="全部" maxTagCount="responsive">
                            {filteredSectionList.map((v) => <Option key={v.nodeId} value={v.nodeId}>{v.nodeName}</Option>)}
                        </Select>
                    </Form.Item>
                </Col> */}
                <Col span={6}>
                    <Form.Item
                        label="工程类型"
                        name="projectCategoryId"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <FormProjectCategory form={formInstance} itemNameKey="projectCategoryId" valueAuto moduleType="SECURITY" />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item
                        label="检查分项"
                        name="optionsList"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <FormSingleCheckSubOption form={formInstance} projectCategoryKey="projectCategoryId" moduleType="SECURITY" />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item
                        label="整改期限"
                        name="time"
                        labelCol={FormItemStyle.labelCol}
                        wrapperCol={FormItemStyle.wrapperCol}
                    >
                        <RangePicker allowClear format="YYYY.MM.DD" />
                    </Form.Item>
                </Col>
            </>
        );
    }, [expand, formInstance]);

    return (
        <>
            <Form
                form={formInstance}
                initialValues={defaultFormData}
                onFinish={handleSubmit}
                className={cls.queryFormBox}
            >
                <Row>
                    {/* <Col span={6}>
                        <Form.Item
                            label="分部分项"
                            labelCol={FormItemStyle.labelCol}
                            wrapperCol={FormItemStyle.wrapperCol}
                        >
                            <QueryFormBtn businessType="security.reform.wbs" processType={String(2)} />
                        </Form.Item>
                    </Col> */}
                    <Col span={6}>
                        <Form.Item
                            label="是否逾期"
                            name="expireStatus"
                            labelCol={FormItemStyle.labelCol}
                            wrapperCol={FormItemStyle.wrapperCol}
                        >
                            <Select placeholder="全部" allowClear>
                                {expireStatusList.map((v) => <Option key={v.value} value={v.value}>{v.label}</Option>)}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            label="整改状态"
                            name="reformStatus"
                            labelCol={FormItemStyle.labelCol}
                            wrapperCol={FormItemStyle.wrapperCol}
                        >
                            <Select placeholder="全部" allowClear>
                                {reformStatusList.map((v) => <Option key={v.value} value={v.value}>{v.label}</Option>)}
                            </Select>
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            label="关键字搜索"
                            name="searchKey"
                            labelCol={FormItemStyle.labelCol}
                            wrapperCol={FormItemStyle.wrapperCol}
                        >
                            <Input allowClear placeholder="请输入整改编号、处理人进行搜索" />
                        </Form.Item>
                    </Col>
                    {expandItems}
                    <Col span={6} push={expand ? 18 : 0} style={{textAlign: "right"}}>
                        <Space>
                            <Button type="primary" htmlType="submit">
                                搜索
                            </Button>
                            <Button onClick={handleReset}>重置</Button>
                            <Button type="link" onClick={() => setExpand((prev) => !prev)}>
                                {expand ? "收起" : "展开"}
                                {expand ? <UpOutlined /> : <DownOutlined />}
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Form>
        </>
    );
};

export default QueryForm;
