import {createUseStyles} from "react-jss";

const useChangeInfoModalStyles = createUseStyles({
    changeInfoBox: {
        color: "#061127",
    },
    infoRowLeft: {
        width: 112,
        marginRight: 20,
        color: "#717784",
        height: 20,
        fontWeight: 400,
    },
    infoRowRight: {
        width: "calc(100% - 132px)",
        lineHeight: "20px",
    },
    downloadBtn: {
        "&.ant-btn-link": {
            color: "#1F54C5",
            padding: "4px 0"
        },
        "&.ant-btn-link[disabled]": {
            color: "rgba(0, 0, 0, 0.25)",
        }
    },
});

export default useChangeInfoModalStyles;
