import React, {use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState} from "react";
import {Form, Col, Row, Input, message} from "antd";
import {useSelector} from "react-redux";
import ComDrawer from "../../../../../components/ComDrawer";
import {createReform, getApprovalFormTemplateDetail, getReformDetail, submitReform} from "../../../../../api/rectification";
import {ReformInstCreateParam} from "../../../../../api/rectification/models/flowLine";
import {RootState} from "../../../../../store/rootReducer";
import {loadCurrentFlowAsync, transformFormDataToJsonValues, transformServerDataToCustomFieldData} from "../../../../../components/Rectification/rectification-helpers";
import {FormTemplateVo, ReformDetailVo} from "../../../../../api/rectification/models/process";
import {CustomFieldData, CustomFieldType} from "../../../../../components/Rectification/models/custom-field";
import {ApprovalType, Flow} from "../../../../../components/Rectification/models/rectification";
import SelectPerson from "../../../../../components/SelectPerson";
import {ApprovalStatus, PlanInfoDetailType, PlanPreparationItemType} from "../../../../../api/Preparation/type";
import {chunkArray, getChangeInfluence} from "../../../../../assets/ts/utils";
import CustomField from "../../../../../components/Rectification/CustomField";
import {RectifyAddFormModel} from "../../../../Rectification/interface";
import {BackType} from "../../../../../assets/ts/globalType";
import FileBox from "../../../../../components/FileBox";
import {FileType} from "../../../../../api/common.type";
import {getPlanInfoDetail, putPlanlaunchChange} from "../../../../../api/Preparation";
import PlanChangeProject from "../../../../../components/PlanChangeProject";

const {TextArea} = Input;
const requiredFieldType = [
    CustomFieldType.MultiSelect,
    CustomFieldType.Select,
    CustomFieldType.NumberInput
];

const layout = {
    labelCol: {span: 6},
    wrapperCol: {span: 17}
};

interface DrawerLaunchApprovalProps {
    plan: PlanPreparationItemType;
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    back: (type: BackType) => void;
}
/* 审批类型 */
const moduleType = "PLAN_APPROVAL";
/* 同时包含发起审批和变更审批，通过plan.changeStatus 来判断 */
const DrawerLaunchApproval: React.FC<DrawerLaunchApprovalProps> = (props) => {
    const {plan, visible, setVisible, back} = props;
    const [planInfo, setPlanInfo] = useState<PlanInfoDetailType>(); // 计划详情
    const [formPerson] = Form.useForm(); // 审批选人的form
    const [formInstance] = Form.useForm<RectifyAddFormModel>(); // 审批模板的表单form
    const {orgInfo} = useSelector((store: RootState) => store.commonData);
    const templateInfo = useRef<FormTemplateVo>({});
    const customFieldList = useRef<CustomFieldData[]>([]);
    const [flowList, setFlowList] = useState<Flow[]>([]); // 根据流程来显示选人
    const [isTemplateLoaded, setIsTemplateLoaded] = useState<boolean>(false); // 模板信息是否加载完成
    const [loading, setLoading] = useState(false);
    const [customFieldValues, setCustomFieldValues] = useState<Map<string, string | string[]>>(new Map()); // 存储审批表单的值
    // 变更审批
    const [changeInfoFileList, setChangeInfoFileList] = useState<FileType[]>([]);
    const [formChangeInfo] = Form.useForm();

    const [reformDetail, setReormDetail] = useState<ReformDetailVo>();

    const fetchPlanInfoDetail = useCallback(async () => {
        const res = await getPlanInfoDetail(plan.id);
        if (res.success && res.data !== undefined) {
            setPlanInfo(res.data);
        }
    }, [plan.id]);
    useEffect(() => {
        fetchPlanInfoDetail();
    }, [fetchPlanInfoDetail]);

    const updateFlowList = useCallback(async (map?: Map<string, string | string[]>) => {
        const innerList = customFieldList.current;
        const innerFields = map ?? new Map();
        const filedsContainsValue = Array.from(innerFields.entries()).filter((kv) => kv[1].length !== 0).map((kv) => kv[0]);
        const requiredFields = innerList.filter((c) => c.required && requiredFieldType.includes(c.type)).map((c) => c.id);
        const intersection = requiredFields.filter((n) => !filedsContainsValue.includes(n));
        if (intersection.length === 0) {
            const {formTmplId, procTmplId} = templateInfo.current;
            const res = await loadCurrentFlowAsync(innerFields, innerList, formTmplId ?? "", procTmplId ?? "");
            setFlowList(res.filter((item) => item.type === 3));
        } else {
            setFlowList([]);
        }
        setIsTemplateLoaded(true);
    }, []);

    /**
     * 获取审批模板详情，获取审批模板流程图详情
     */
    const init = useCallback(
        async () => {
            try {
                const detailRes = await getApprovalFormTemplateDetail(plan.approvalTemplateId);
                const customFields = (detailRes.result.components ?? []).flat().map(transformServerDataToCustomFieldData);
                templateInfo.current = detailRes.result;
                customFieldList.current = customFields;
                updateFlowList();
                setCustomFieldValues(new Map());
            } catch (error) {
                console.log(error);
            }
        },
        [plan.approvalTemplateId, updateFlowList]
    );

    useEffect(() => {
        init();
    }, [init]);

    const getReformDetailData = useCallback(async () => {
        const {result} = await getReformDetail(plan.approvalId, moduleType);
        setReormDetail(result);
    }, [plan.approvalId]);

    useEffect(() => {
        if (plan.approvalStatus === ApprovalStatus.RETURN) {
            getReformDetailData();
        }
    }, [getReformDetailData, plan.approvalStatus]);

    const onClose = useCallback(() => {
        setVisible(false);
        back("cancel");
    }, [back, setVisible]);

    /* 退回时的发起审批 */
    const handleDeclineSubmit = useCallback(async (nodeUsers) => {
        // 提交
        try {
            const submitReformRes = await submitReform({
                serialNum: reformDetail!.serialNum,
                formTaskId: reformDetail!.formTaskId,
                checkFormId: plan?.id,
                message: "",
                deptId: reformDetail?.deptId,
                nodeId: reformDetail?.nodeId,
                nodeType: reformDetail?.nodeType,
                attachments: [],
                nodeUsers
            }, moduleType);
            if (submitReformRes.success && back instanceof Function) {
                message.success("操作成功");
                back("ok");
            }
        } finally {
            setLoading(false);
        }
    }, [back, plan, reformDetail]);

    const handleFinish = useCallback(async () => {
        const formPersonValues = await formPerson.validateFields();
        await formInstance.validateFields();
        // 变更审批 formChangeInfoValues
        const formChangeInfoValues = plan.changeStatus === "Changed" ? await formChangeInfo.validateFields() : null;
        setLoading(true);
        const nodeUsers = flowList.filter((f) => f.type === ApprovalType.InitiatorSpecify).map((f) => ({
            approvalNodeId: f.id,
            approvalUsers: (formPersonValues[f.id] ?? []).map((v: {userName: string}) => v.userName ?? "")
        }));
        // 退回的走审批流程
        if (plan.approvalStatus === ApprovalStatus.RETURN) {
            handleDeclineSubmit(nodeUsers);
            return;
        }
        const jsonValues = transformFormDataToJsonValues(Object.fromEntries(customFieldValues.entries()), customFieldList.current);
        const params: ReformInstCreateParam = {
            comment: "",
            deadline: "",
            name: "",
            deptId: orgInfo.orgId,
            nodeId: planInfo?.nodeId,
            nodeType: Number(planInfo?.nodeType),
            // buildType: curSectionInfo?.classification,
            checkFormId: plan.id,
            authEntityId: templateInfo.current.authEntityId,
            formTmplId: templateInfo.current.formTmplId,
            procTmplId: templateInfo.current.procTmplId,
            nodeUsers,
            reformItem: 0,
            checkType: -1,
            jsonValues,
        };
        try {
            // createReforms创建审批
            // putPlanlaunchChange 变更审批的原因
            // 1. plan.changeStatus === Unchanged未变更时，只走createReforms创建审批；等于Changed时，先走putPlanlaunchChange创建审批，在走createReforms
            // 2. 同时如果plan.changeStatus === Unchanged未变更时，并且没有表单模板和审批人选择就会直接发起变更
            if (plan.changeStatus === "Changed") {
                const launchChangeParams = {
                    changeReason: formChangeInfoValues.changeReason,
                    fileList: changeInfoFileList,
                    id: plan.id,
                };
                const launchChangeRes = await putPlanlaunchChange(launchChangeParams);
                if (launchChangeRes.success) {
                    const res = await createReform(params, moduleType);
                    if (res.success && back instanceof Function) {
                        message.success("操作成功");
                        back("ok");
                    }
                }
            } else {
                const res = await createReform(params, moduleType);
                if (res.success && back instanceof Function) {
                    message.success("操作成功");
                    back("ok");
                }
            }
        } finally {
            setLoading(false);
        }
        // eslint-disable-next-line max-len
    }, [formPerson, formInstance, plan.changeStatus, plan.id, plan.approvalStatus, formChangeInfo, customFieldValues, flowList, orgInfo.orgId, planInfo, handleDeclineSubmit, changeInfoFileList, back]);

    const handleCustomFieldChanged = useCallback((data: CustomFieldData, payload: string | string[]) => {
        const newCustomFieldValues = new Map(customFieldValues).set(data.id, payload);
        setCustomFieldValues(newCustomFieldValues);
        formInstance.setFields([{name: data.id, value: payload}]);
        if (requiredFieldType.includes(data.type)) {
            updateFlowList(newCustomFieldValues);
        }
    }, [customFieldValues, formInstance, updateFlowList]);

    const handleCustomFieldBlur = useCallback((data: CustomFieldData, payload?: string) => {
        if (payload === undefined) {
            return;
        }
        handleCustomFieldChanged(data, payload);
    }, [handleCustomFieldChanged]);
    // 审批模板的表单
    const custFieldDom = useMemo(() => chunkArray(customFieldList.current).map((cList, cvx) => (
        <Row key={cvx.toString()}>
            {
                cList.map((c) => (
                    <Col span={24} key={c.id}>
                        <Form.Item
                            label={c.name}
                            name={c.id}
                            rules={[{required: c.required, message: "必填项"}]}
                        >
                            <CustomField
                                maxLength={c.maxLength}
                                hint={c.hint}
                                style={{width: "100%"}}
                                {...(customFieldValues.has(c.id)
                                    ? {currentValue: customFieldValues.get(c.id)}
                                    : {})}
                                onChange={(d) => handleCustomFieldChanged(c, d)}
                                onBlur={(val) => handleCustomFieldBlur(c, val)}
                                data={c}
                                nodeId={planInfo?.nodeId}
                                nodeType={planInfo?.nodeType}
                            />
                        </Form.Item>
                    </Col>
                ))
            }
        </Row>
    )), [customFieldValues, planInfo, handleCustomFieldChanged, handleCustomFieldBlur]);
    useEffect(() => {
        // 同时如果plan.changeStatus === Unchanged未变更时，并且没有表单模板和审批人选择就会直接发起变更
        if (flowList.length === 0 && chunkArray(customFieldList.current).length === 0 && isTemplateLoaded) {
            // 当是 退回到发起人节点 也直接提交
            if (plan.changeStatus === "Unchanged" || plan.approvalStatus === ApprovalStatus.RETURN) {
                handleFinish();
            }
        }
    }, [flowList.length, handleFinish, isTemplateLoaded, plan.approvalStatus, plan.changeStatus]);

    const rednerFormChangeInfo = useMemo(() => (
        <Form {...layout} form={formChangeInfo}>
            <Form.Item
                label="变更原因"
                rules={[{required: true, message: "请输入变更原因"}]}
                name="changeReason"
            >
                <TextArea maxLength={300} showCount />
            </Form.Item>
            <Form.Item label="模型查看">
                <PlanChangeProject planId={plan.id} />
            </Form.Item>
            <Form.Item label="变更影响">
                <div>{getChangeInfluence(planInfo?.currentTotalDuration, planInfo?.unchangedTotalDuration)}</div>
            </Form.Item>
            <Form.Item label="变更资料">
                <FileBox style={{width: "100%"}} value={changeInfoFileList} onChange={setChangeInfoFileList} />
            </Form.Item>
        </Form>
    ), [changeInfoFileList, formChangeInfo, plan.id, planInfo]);

    if (
        isTemplateLoaded === false || (flowList.length === 0 && chunkArray(customFieldList.current).length === 0)
    ) {
        if (plan.changeStatus === "Unchanged" || plan.approvalStatus === ApprovalStatus.RETURN) {
            return null;
        }
    }

    return (
        <>
            <ComDrawer
                title="发起审批"
                onCancel={onClose}
                visible={visible}
                width={540}
                bodyStyle={{paddingBottom: 80}}
                onOk={handleFinish}
                okLoading={loading}
                destroyOnClose
            >
                <div style={{padding: "24px"}}>
                    {(plan.changeStatus === "Changed" && plan.approvalStatus !== ApprovalStatus.RETURN) && rednerFormChangeInfo}
                    <Form {...layout} labelWrap form={formInstance}>
                        {custFieldDom}
                    </Form>
                    <Form {...layout} labelWrap form={formPerson}>
                        {
                            flowList.map((f) => (
                                <Col span={24} key={f.id}>
                                    <Form.Item
                                        label={f.name}
                                        rules={[{required: f.canSet, message: `请选择${f.name}`}]}
                                        name={f.id}
                                    >
                                        <SelectPerson nodeId={planInfo?.nodeId} nodeType={planInfo?.nodeType} />
                                    </Form.Item>
                                </Col>
                            ))
                        }
                    </Form>
                </div>
            </ComDrawer>
        </>
    );
};

export default DrawerLaunchApproval;
