/* eslint-disable no-underscore-dangle */

import Fetch from "../service/Fetch";
import {WebRes} from "./common.type";
import {TemplateCategoryInfo, GetTemplateListParams, GetTemplateListRes, AddTemplateInfo, ValidateMD5Params, MergeFileChunksParams} from "./template.type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;
const editorUrl = `${baseUrl}/editor`;

// 新增模板类型V2
export const addTemplateCategory = async (params: {name: string}): Promise<WebRes> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/template-category/add`,
    data: params,
});

// 删除模板类型V2
export const deleteTemplateCategory = async (ids: string): Promise<WebRes> => Fetch({
    methods: "delete",
    url: `${editorUrl}/v2/template-category/delete/${ids}`,
});

// 更新模板类型V2
export const updateTemplateCategory = async (params: TemplateCategoryInfo): Promise<WebRes> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/template-category/update`,
    data: params,
});

// 查询模板类型列表V2
export const getTemplateCategoryList = async (): Promise<WebRes<TemplateCategoryInfo[]>> => Fetch({
    methods: "get",
    url: `${editorUrl}/v2/template-category/list`,
});

// 新增模板
export const addTemplate = async (params: AddTemplateInfo): Promise<WebRes> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/template/add`,
    data: params,
});

// 删除模板
export const deleteTemplate = async (ids: string): Promise<WebRes> => Fetch({
    methods: "delete",
    url: `${editorUrl}/v2/template/delete/${ids}`,
});

// 查询模板分页数据
export const getTemplateList = async (params: GetTemplateListParams): Promise<WebRes<GetTemplateListRes>> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/template/page-template`,
    data: params,
});

export const validateMD5 = async (params: ValidateMD5Params): Promise<WebRes> => Fetch({
    url: `${editorUrl}/motor/v2/rf/template/file/url/separate/upload`,
    methods: "post",
    data: params
});

export const mergeFileChunks = async (params: MergeFileChunksParams): Promise<WebRes> => Fetch({
    url: `${editorUrl}/motor/v2/rf/template/file/notify/upload/success`,
    methods: "post",
    data: params
});

export const resourceReadUrl = `${editorUrl}/motor/v2/rf/resource/read`;
