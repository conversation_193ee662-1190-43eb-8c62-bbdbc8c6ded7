import React, {useCallback} from "react";
import {createUseStyles} from "react-jss";
import {useControllableValue} from "ahooks";
import {Select} from "antd";
import {useAppSelector} from "../../store";
import {SectionListType} from "../../store/common/actionTypes";

const useStyle = createUseStyles({
    box: {},
});

type ValueType = Partial<SectionListType>;

export interface FormSelectProjectProps {
    disabled?: boolean;
    value?: ValueType;
    onChange?: (val?: ValueType) => void;
}

const FormSelectProject = (props: FormSelectProjectProps) => {
    const {disabled = false} = props;
    const {sectionList} = useAppSelector((st) => st.commonData);
    const [state, setState] = useControllableValue<FormSelectProjectProps["value"]>(props);
    const cls = useStyle();
    const handleSelect = useCallback(
        (val: string) => {
            const findedSection = sectionList.find((el) => el.nodeId === val);
            setState(findedSection);
        },
        [sectionList, setState]
    );

    return (
        <Select
            disabled={disabled}
            className={cls.box}
            options={sectionList.filter((v) => v.isAll !== true).map((el) => ({label: el.nodeName, value: el.nodeId}))}
            value={state?.nodeId}
            onChange={handleSelect}
        />
    );
};

export default FormSelectProject;
