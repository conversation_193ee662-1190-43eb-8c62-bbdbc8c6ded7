import {createUseStyles} from "react-jss";
import Color from "../../../../assets/css/Color";

const useStyles = createUseStyles({
    box: {
        paddingTop: 28,
        display: "flex",
        flexDirection: "column",
    },
    head: {
        flexShrink: 0,
        flexBasis: 32,
        padding: "0 24px"
    },
    content: {
        flexGrow: 1,
        padding: "0 24px",
        marginTop: 24
    },
    footer: {
        flexShrink: 0,
        height: 52,
        display: "flex",
        justifyContent: "flex-end",
        alignItems: "center",
        borderTop: `1px solid ${Color["dark-line-2"]}`,
        padding: "0 24px"
    },
});

export default useStyles;
