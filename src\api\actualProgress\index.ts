/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import FileFetch from "../../service/FileFetch";
import {WebRes} from "../common.type";
import {WarnExportParam} from "../hiddenDangerCheck/type";
import {ActualProgressPageParam, ActualWarnItem, ActualWarnListParam, GetActualProgressPageReturn} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = window.__IWorksConfig__ as any;

export const getActualProgressPage = async (data: ActualProgressPageParam): Promise<WebRes<GetActualProgressPageReturn>> => Fetch({
    url: `${baseUrl}/sphere/plan/actual/page`,
    methods: "post",
    data,
});

export const getActualWarnList = async (data: ActualWarnListParam): Promise<WebRes<ActualWarnItem[]>> => Fetch({
    url: `${baseUrl}/sphere/plan/actual/warn-list`,
    methods: "post",
    data,
});

export const exportWarnList = (data: WarnExportParam, fileName: string) => FileFetch({
    url: `${baseUrl}/sphere/plan/actual/syncExport`,
    methods: "post",
    data,
    fileName
});
