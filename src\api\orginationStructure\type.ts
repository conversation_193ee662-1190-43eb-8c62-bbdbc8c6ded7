export interface GetGetOrgListWithPersonNumReturn {
    authFlag: boolean; // 当前用户是否有给定标段的权限
    classification: number; // 类别 0 施工 1监理 2 检测,3=建设
    constructionOrg: string; // 施工单位
    id: string;
    nodeId: string;
    nodeName: string;
    parentId: string | null; // 父节点id，只对施工单位有效，表示施工单位对应的监理单位id
    supervisorOrg: string; // 监理单位
    totalLaborPerson: number; // 当前标段作业人员总数
    totalPerson: number; // 当前标段管理人员总数
}
export interface PersonListType {
    personId: string;
    personName: string;
}
export interface GetQueryorgSectionListParams {
    nodeId?: string;
    buildType?: number;
}
export interface GetQueryorgSectionListReturn {
    parentId: string;
    sectionId: string;
    sectionName: string;
    personList: PersonListType[];
}
