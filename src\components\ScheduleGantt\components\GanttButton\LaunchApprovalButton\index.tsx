import {Button, message, Modal} from "antd";
import {WarningOutlined, InfoCircleFilled} from "@ant-design/icons";
import React, {useCallback, useContext, useMemo, useState} from "react";
import {useSelector} from "react-redux";
import {ApprovalStatus, PlanInfoDetailType} from "../../../../../api/Preparation/type";
// import {getChangeInfluence} from "../../../../../assets/ts/utils";
import {RootState} from "../../../../../store/rootReducer";
import ganttManager from "../../../gantt/ganttManager";
import {getTotalDuration} from "../../../gantt/taskUtils";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";

const getChangeInfluence = (total?: number, unChangedTotal?: number) => {
    if (total === undefined || total === null || unChangedTotal === undefined || unChangedTotal === null) {
        return "";
    }
    const offset = total - unChangedTotal;
    if (offset === 0) {
        return "总工期不变";
    }
    const changeLabel = offset > 0 ? "延长" : "提前";
    return (
        <span>
            总工期
            {changeLabel}
            &nbsp;
            {Math.abs(offset)}
            &nbsp;天，由 &nbsp;
            {unChangedTotal}
            &nbsp;天变为 &nbsp;
            <span style={{color: "#1F54C5"}}>
                {total}
                &nbsp;天
            </span>
        </span>
    );
};
const LaunchApprovalButton = () => {
    const cls = useStyles();
    const {userInfo} = useSelector((store: RootState) => store.commonData);
    const {
        loading,
        planInfo,
        checkoutStatus,
        updatePlanTaskMethodRef,
        needAutoSave,
        onLaunchApproval
    } = useContext(EditorContext);
    const [launchLoading, setLaunchLoading] = useState(false);
    const handleLaunchApproval = useCallback(async () => {
        try {
            setLaunchLoading(true);
            let saveSuccess = true;
            if (checkoutStatus && updatePlanTaskMethodRef.current instanceof Function) {
                if (loading === true) {
                    message.info({content: "正在签出，请稍后再试"});
                    return;
                }
                needAutoSave.current = false;
                const res = await updatePlanTaskMethodRef.current();
                saveSuccess = res;
            }
            if (onLaunchApproval instanceof Function && planInfo.id !== undefined && saveSuccess) {
                onLaunchApproval(planInfo as PlanInfoDetailType);
            }
        } catch (error) {
            console.log("error", error);
            if (checkoutStatus) {
                needAutoSave.current = true;
            }
            message.error("发起失败");
        } finally {
            setLaunchLoading(false);
        }
    }, [checkoutStatus, loading, needAutoSave, onLaunchApproval, planInfo, updatePlanTaskMethodRef]);

    // 变更影响
    const handleConfirmTotalChange = useCallback(() => {
        const totalDuration = getTotalDuration();
        if (planInfo.unchangedTotalDuration !== null && totalDuration !== planInfo.unchangedTotalDuration) {
            Modal.confirm({
                content: (
                    <div style={{width: 480, fontSize: 14, marginLeft: 28, padding: 0}}>
                        <div style={{color: "#061127", fontWeight: 700, marginTop: 16}}>提示通知</div>
                        <div style={{width: 394, height: 20, marginLeft: 52, marginTop: 24, fontSize: 14, fontWeight: 400}}>
                            {getChangeInfluence(totalDuration, planInfo.unchangedTotalDuration)}
                            ，确定发起?
                        </div>
                    </div>
                ),
                okText: "确定",
                cancelText: "取消",
                closable: true,
                className: cls.infoModal,
                width: 480,
                icon: <InfoCircleFilled style={{fontSize: 14, marginLeft: 18, marginTop: 4, color: "#1F54C5"}} />,
                onOk: () => {
                    handleLaunchApproval();
                }
            });
        } else {
            handleLaunchApproval();
        }
    }, [planInfo.unchangedTotalDuration, handleLaunchApproval, cls.infoModal]);

    // 判断计划时间是否和要求时间有冲突
    const handleConfirmConfict = useCallback(() => {
        const isConflict = ganttManager.checkTaskRequestDateConfict();
        if (isConflict) {
            Modal.warning({
                title: "提示",
                content: "计划存在任务时间异常，请先处理！",
                okText: "确定",
                cancelText: "取消",
                className: cls.modal,
                width: 224,
                icon: <WarningOutlined style={{fontSize: 14, marginTop: 4}} />,
            });
        } else {
            handleConfirmTotalChange();
        }
    }, [handleConfirmTotalChange, cls.modal]);

    const handleClick = useCallback(() => {
        handleConfirmConfict();
    }, [handleConfirmConfict]);

    const launchDisable = useMemo(() => {
        if (planInfo.approvalStatus === ApprovalStatus.RETURN && planInfo.approvalStartUser !== userInfo.username) {
            // 退回只有发起人可以发起审批
            return true;
        }
        return false;
    }, [planInfo.approvalStatus, planInfo.approvalStartUser, userInfo.username]);

    if (planInfo.status !== 1) {
        return null;
    }

    return (
        <Button
            loading={launchLoading}
            className={cls.button}
            size="large"
            onClick={handleClick}
            disabled={launchDisable}
        >
            发起审批
        </Button>
    );
};

export default LaunchApprovalButton;
