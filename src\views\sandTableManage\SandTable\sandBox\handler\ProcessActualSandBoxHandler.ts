import Motor from "@motor/core";
import MotorUtils from "../../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
import {MotorContext} from "../../../../../reMotor";
import {SandBoxHandler, SandDataInitializeBase} from "./dataOrInterface";

export default class ProcessActualSandBoxHandler implements SandBoxHandler {
    private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    constructor(dataContainer: SandDataInitializeBase) {
        this.bimProject = MotorContext.getCurBIMProject();
        this.dataContainer = dataContainer;
    }

    reset() {
        this.resetModel();
    }

    showSandBoxByDate(curTime: Date): void {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const processConfig = dataContainer.getFilterInfo("process");
            if (!processConfig.isCheckAll && processConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryCompSandBoxListByActualTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                if (processConfig.isCheckAll
                    || processConfig.setCheckedKeys.has(compItem.timePeriodInfo.stateKey)) {
                    const colorString = compItem.timePeriodInfo.isInterval || compItem.timePeriodInfo.isVirtualTail
                        ? `rgba(${compItem.timePeriodInfo.stateColor},0.3)`
                        : `rgb(${compItem.timePeriodInfo.stateColor})`;
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setVisibility(true, comps);
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    jumpToDate(curTime: Date) {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const processConfig = dataContainer.getFilterInfo("process");
            if (!processConfig.isCheckAll && processConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryAccumulatedCompTimePeriodListByActualTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                if (processConfig.isCheckAll
                    || processConfig.setCheckedKeys.has(compItem.timePeriodInfo.stateKey)) {
                    const colorString = compItem.timePeriodInfo.isInterval || compItem.timePeriodInfo.isVirtualTail
                        ? `rgba(${compItem.timePeriodInfo.stateColor},0.3)`
                        : `rgb(${compItem.timePeriodInfo.stateColor})`;
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            this.resetModel();
            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setVisibility(true, comps);
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    private resetModel() {
        if (isNotNullOrUndefined(this.dataContainer) && isNotNullOrUndefined(this.bimProject)) {
            const {bimProject} = this;
            MotorUtils.resestBIMProject(bimProject);
            bimProject.setVisibility(false);
        }
    }
}
