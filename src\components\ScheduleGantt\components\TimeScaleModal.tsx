import React, {FC, memo, useState} from "react";
import {Modal, Select, InputNumber, Checkbox} from "antd";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import {createUseStyles} from "react-jss";

const useStyles = createUseStyles({
    subTitle: {
        marginLeft: 16,
    },
    time: {
        width: 70,
    },
    style: {
        width: 150,
    },
    count: {
        width: 70,
    },
    ali: {
        width: 70,
    }
});

interface TimeScaleRowData {
    useful: boolean;
    timeScale: number;// 0-
    styleType: number;
    count: number;
    alignment: number;// 0->left,1->center,2->right
}

interface TimeScaleData {
    top: TimeScaleRowData;
    middle: TimeScaleRowData;
    bottom: TimeScaleRowData;
    size: number;
}

interface TimeScaleModal {
    visible: boolean;
    onCancel(): void;
    onComfirm(data: TimeScaleData): void;
}

const yearStyle = [
    {
        value: 0,
        label: "2001年",
    },
    {
        value: 1,
        label: "2001",
    },
    {
        value: 2,
        label: "二00一年",
    },
    {
        value: 3,
        label: "Y1",
    }
];

const seasonStyle = [
    {
        value: 0,
        label: "第一季度",
    },
    {
        value: 1,
        label: "Qtr 1",
    },
    {
        value: 2,
        label: "二00一年第一季度",
    },
    {
        value: 3,
        label: "2001年第一季度",
    }
];

const monthStyle = [
    {
        value: 0,
        label: "2000年1月",
    },
    {
        value: 1,
        label: "一月",
    },
    {
        value: 2,
        label: "Jan",
    },
    {
        value: 3,
        label: "1",
    },
    {
        value: 4,
        label: "1,2,3....(从开始）",
    }
];

const weekStyle = [
    {
        value: 0,
        label: "1月30日",
    },
    {
        value: 1,
        label: "Sun 1-30",
    },
    {
        value: 2,
        label: "二00一年十二月三十日",
    },
    {
        value: 3,
        label: "1-30",
    },
    {
        value: 4,
        label: "1,2,3....(从开始）",
    }
];

const dayStyle = [
    {
        value: 0,
        label: "21",
    },
    {
        value: 1,
        label: "Jan 30",
    },
    {
        value: 2,
        label: "十二月三十日",
    },
    {
        value: 3,
        label: "01-12-30",
    },
    {
        value: 4,
        label: "1,2,3....(从开始）",
    },
    {
        value: 5,
        label: "日,一,二.....",
    },
    {
        value: 5,
        label: "Sun,Mon,Tue....",
    }
];

const alignmentStyle = [
    {
        value: 0,
        label: "居左",
    },
    {
        value: 1,
        label: "居中",
    },
    {
        value: 2,
        label: "居右",
    }
];

const defData = {
    top: {
        useful: true,
        timeScale: 0,
        styleType: 0,
        count: 1,
        alignment: 1,
    },
    middle: {
        useful: true,
        timeScale: 2,
        styleType: 0,
        count: 1,
        alignment: 1,
    },
    bottom: {
        useful: true,
        timeScale: 4,
        styleType: 0,
        count: 1,
        alignment: 1,
    },
    size: 100,
};

const timeTypes = (type: number) => {
    if (type >= 4) {
        return [
            {
                value: 4,
                label: "天",
            }
        ];
    }
    const allTimeTypes = [
        {
            value: 0,
            label: "年",
        },
        {
            value: 1,
            label: "季度",
        },
        {
            value: 2,
            label: "月",
        },
        {
            value: 3,
            label: "周",
        },
        {
            value: 4,
            label: "天",
        }
    ];
    return allTimeTypes.splice(type, allTimeTypes.length - type);
};

const styleTypes = (type: number) => {
    if (type === 0) {
        return yearStyle;
    }
    if (type === 1) {
        return seasonStyle;
    }
    if (type === 2) {
        return monthStyle;
    }
    if (type === 3) {
        return weekStyle;
    }
    if (type === 4) {
        return dayStyle;
    }
    return [];
};

const subTimeScale = (preType: number, type: number) => {
    if (preType === 4) {
        return 4;
    }
    if (preType >= type) {
        return preType + 1;
    }
    return type;
};

const TimeScaleModal: FC<TimeScaleModal> = memo((props) => {
    const {visible, onCancel, onComfirm} = props;
    const {subTitle, time, style, count, ali} = useStyles();
    const [data, setData] = useState<TimeScaleData>(defData);

    const topTimeScaleChange = (value: number) => {
        const {...temp} = data;
        temp.top.timeScale = value;
        temp.middle.timeScale = subTimeScale(value, temp.middle.timeScale);
        temp.bottom.timeScale = subTimeScale(temp.middle.timeScale, temp.bottom.timeScale);
        setData(temp);
    };

    const middleTimeScaleChange = (value: number) => {
        const {...temp} = data;
        temp.middle.timeScale = value;
        temp.bottom.timeScale = subTimeScale(temp.middle.timeScale, temp.bottom.timeScale);
        setData(temp);
    };

    const bottomTimeScaleChange = (value: number) => {
        const {...temp} = data;
        temp.bottom.timeScale = value;
        setData(temp);
    };

    const topStyleChange = (value: number) => {
        const {...temp} = data;
        temp.top.styleType = value;
        setData(temp);
    };

    const middleStyleChange = (value: number) => {
        const {...temp} = data;
        temp.middle.styleType = value;
        setData(temp);
    };

    const bottomStyleChange = (value: number) => {
        const {...temp} = data;
        temp.bottom.styleType = value;
        setData(temp);
    };

    const topCountChange = (value: number | undefined | string) => {
        const {...temp} = data;
        temp.top.count = value as number;
        setData(temp);
    };

    const middleCountChange = (value: number | undefined | string) => {
        const {...temp} = data;
        temp.middle.count = value as number;
        setData(temp);
    };

    const bottomCountChange = (value: number | undefined | string) => {
        const {...temp} = data;
        temp.bottom.count = value as number;
        setData(temp);
    };

    const topAlignmentChange = (value: number) => {
        const {...temp} = data;
        temp.top.alignment = value;
        setData(temp);
    };

    const middleAlignmentChange = (value: number) => {
        const {...temp} = data;
        temp.middle.alignment = value;
        setData(temp);
    };

    const bottomAlignmentChange = (value: number) => {
        const {...temp} = data;
        temp.bottom.alignment = value;
        setData(temp);
    };

    const middleCheckBoxChange = (e: CheckboxChangeEvent) => {
        const {...temp} = data;
        temp.middle.useful = e.target.checked;
        setData(temp);
    };

    const bottomCheckBoxChange = (e: CheckboxChangeEvent) => {
        const {...temp} = data;
        temp.bottom.useful = e.target.checked;
        setData(temp);
    };

    const sizeChange = (value: number | undefined | string) => {
        const {...temp} = data;
        temp.size = value as number;
        setData(temp);
    };

    const onOk = () => {
        onComfirm(data);
    };

    return (
        <Modal
            title={<span style={{fontWeight: "bold"}}>时间刻度</span>}
            destroyOnClose
            maskClosable={false}
            visible={visible}
            onCancel={onCancel}
            onOk={onOk}
            width={700}
        >
            <div>
                <span style={{marginLeft: 20}}>顶层：</span>
                <Select className={time} options={timeTypes(0)} defaultValue={data.top.timeScale} onChange={topTimeScaleChange} />
                <span className={subTitle}>显示样式：</span>
                <Select
                    className={style}
                    options={styleTypes(data.top.timeScale)}
                    defaultValue={data.top.styleType}
                    onChange={topStyleChange}
                />
                <span className={subTitle}>计数：</span>
                <InputNumber className={count} min={1} max={50} defaultValue={data.top.count} onChange={topCountChange} />
                <span className={subTitle}>对齐：</span>
                <Select className={ali} options={alignmentStyle} defaultValue={data.top.alignment} onChange={topAlignmentChange} />
            </div>
            <div style={{marginTop: 20}}>
                <Checkbox checked={data.middle.useful} onChange={middleCheckBoxChange} />
                <span style={{marginLeft: 4}}>中层：</span>
                <Select
                    className={time}
                    disabled={!data.middle.useful}
                    options={timeTypes(data.top.timeScale + 1)}
                    value={data.middle.timeScale}
                    onChange={middleTimeScaleChange}
                />
                <span className={subTitle}>显示样式：</span>
                <Select
                    className={style}
                    disabled={!data.middle.useful}
                    options={styleTypes(data.middle.timeScale)}
                    defaultValue={data.middle.styleType}
                    onChange={middleStyleChange}
                />
                <span className={subTitle}>计数：</span>
                <InputNumber
                    className={count}
                    disabled={!data.middle.useful}
                    min={1}
                    max={50}
                    defaultValue={data.middle.count}
                    onChange={middleCountChange}
                />
                <span className={subTitle}>对齐：</span>
                <Select
                    className={ali}
                    disabled={!data.middle.useful}
                    options={alignmentStyle}
                    defaultValue={data.middle.alignment}
                    onChange={middleAlignmentChange}
                />
            </div>
            <div style={{marginTop: 20}}>
                <Checkbox checked={data.bottom.useful} onChange={bottomCheckBoxChange} />
                <span style={{marginLeft: 4}}>底层：</span>
                <Select
                    className={time}
                    disabled={!data.bottom.useful}
                    options={timeTypes(data.middle.timeScale + 1)}
                    value={data.bottom.timeScale}
                    onChange={bottomTimeScaleChange}
                />
                <span className={subTitle}>显示样式：</span>
                <Select
                    className={style}
                    disabled={!data.bottom.useful}
                    options={styleTypes(data.bottom.timeScale)}
                    defaultValue={data.bottom.styleType}
                    onChange={bottomStyleChange}
                />
                <span className={subTitle}>计数：</span>
                <InputNumber
                    className={count}
                    disabled={!data.bottom.useful}
                    min={1}
                    max={50}
                    defaultValue={data.bottom.count}
                    onChange={bottomCountChange}
                />
                <span className={subTitle}>对齐：</span>
                <Select
                    className={ali}
                    disabled={!data.bottom.useful}
                    options={alignmentStyle}
                    defaultValue={data.bottom.alignment}
                    onChange={bottomAlignmentChange}
                />
            </div>
            <div style={{marginTop: 20}}>
                <span style={{marginLeft: 20}}>大小：</span>
                <InputNumber style={{width: 70}} min={1} max={100} defaultValue={data.size} onChange={sizeChange} />
                <span style={{marginLeft: 16}}>%</span>
            </div>
        </Modal>
    );
});

export default TimeScaleModal;
