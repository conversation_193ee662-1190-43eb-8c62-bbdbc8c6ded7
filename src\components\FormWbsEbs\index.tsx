import React, {useState, useEffect} from "react";
import {createUseStyles} from "react-jss";
import {Input, TreeProps, FormInstance} from "antd";
import {uniqBy} from "lodash-es";
import ComDrawer from "../ComDrawer";
import EBSBox, {EBSBoxProps} from "./EBSBox";
import {getWBSEBSType, WbsEbsNodeType} from "./data";
import ComSelect, {ComSelectProps} from "../../../uikit/Components/ComSelect";
import WBSTree from "../WBS/WBSTree";
import {toArr} from "../../assets/ts/utils";
import {WBSType} from "../../api/common.type";
import {getLeafNode} from "../WBS/FormSelectWBS";
import {confirmOk} from "../../assets/ts/confirmLeave";
import {SectionListType} from "../../store/common/actionTypes";

const useStyle = createUseStyles({
    box: {},
});

export interface FormWbsEbsProps {
    value?: WbsEbsNodeType;
    onChange?: (val: FormWbsEbsProps["value"]) => void;
    disabled?: boolean;
    form?: FormInstance;
}

const WBSEBSOptions = [
    {label: "WBS", value: "WBS"},
    {label: "EBS", value: "EBS"}
];

const FormWbsEbs = (props: FormWbsEbsProps) => {
    const {value: propsValue, onChange, disabled = false, form} = props;
    const cls = useStyle();
    const [visible, setVisible] = useState<boolean>(false);
    const [type, setType] = useState<string>("WBS");
    const [value, setValue] = useState<FormWbsEbsProps["value"]>();
    const [nodeInfo, setNodeInfo] = useState<SectionListType>();

    useEffect(() => {
        setValue(propsValue);
    }, [propsValue]);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const handleSelect: ComSelectProps["onSelect"] = (val: any) => {
        if (typeof val === "string") {
            setType(val);
        }
    };

    const handleOpenDrawer = () => {
        setVisible(true);
        if (form !== undefined) {
            setNodeInfo(form.getFieldValue("nodeInfo"));
        }
    };
    const handleCloseDrawer = () => setVisible(false);

    const handleOk = async () => {
        if (propsValue === undefined || propsValue.bindType === 0) {
            if (onChange !== undefined) {
                onChange(value);
            }
            handleCloseDrawer();
        } else {
            const isOk = await confirmOk({content: "切换关联方式,将清空原有关联关系"});
            if (isOk === true) {
                if (onChange !== undefined) {
                    onChange(value);
                }
                handleCloseDrawer();
            }
        }
    };

    const handleCancel = () => {
        handleCloseDrawer();
        setValue(propsValue);
    };

    const handleEBSChange: EBSBoxProps["onChange"] = (val) => {
        const newBindType = getWBSEBSType(val);
        const tempValue: FormWbsEbsProps["value"] = {
            ...value,
            bindType: newBindType,
            ebsNodes: val,
            wbsNodes: [],
        };
        setValue(tempValue);
    };


    const handleWBSChange: TreeProps["onCheck"] = (_val, info) => {
        const leafNodes: WBSType[] = getLeafNode(info.node).map((el) => ({wbsNodeId: `${el.key}`, wbsNodeName: `${el.title}`}));
        const tempValue: WBSType[] = value !== undefined ? value.wbsNodes ?? [] : [];
        const leafNodeIds = leafNodes.map((el) => el.wbsNodeId);
        let newWbsNodes: WBSType[] = [];
        if (info.checked) {
            newWbsNodes = uniqBy(leafNodes.concat(tempValue), "wbsNodeId");
        } else {
            newWbsNodes = tempValue.filter((el) => !leafNodeIds.includes(el.wbsNodeId));
        }
        setValue({
            ...value,
            bindType: -1,
            ebsNodes: [],
            wbsNodes: newWbsNodes,
            wbsNodeIds: newWbsNodes.map((el) => el.wbsNodeId),
        });
    };

    const inputValue = () => {
        if (propsValue === undefined) {
            return "";
        }
        if (propsValue.bindType === -1) {
            return toArr(propsValue.wbsNodes ?? []).map((el) => el.wbsNodeName).join(",");
        }
        if (propsValue.bindType === 1) {
            return toArr(propsValue.ebsNodes ?? []).map((el) => el.projName).join(",");
        }
        if (propsValue.bindType === 2 || propsValue.bindType === 3) {
            return toArr(propsValue.ebsNodes ?? []).map((el) => toArr([el.projName, ...el.paths ?? []]).join(">")).join(";");
        }
        return "";
    };

    return (
        <div className={cls.box}>
            <Input value={inputValue()} onClick={handleOpenDrawer} disabled={disabled} />
            <ComDrawer
                title="关联WBS/EBS"
                width={722}
                visible={visible}
                onCancel={handleCancel}
                onOk={handleOk}
                bodyStyle={{padding: 16}}
                destroyOnClose
            >
                <div style={{display: "flex", height: "100%", flexDirection: "column"}}>
                    <ComSelect
                        boxStyle={{width: 160}}
                        label="关联分类"
                        options={WBSEBSOptions}
                        value={type}
                        onSelect={handleSelect}
                    />
                    <div style={{flexGrow: 1, marginTop: 12}}>
                        {type === "WBS"
                            ? (
                                <WBSTree
                                    checkable
                                    checkedKeys={value !== undefined && value.bindType === -1
                                        ? toArr(value.wbsNodes ?? []).map((el) => el.wbsNodeId)
                                        : undefined}
                                    onCheck={handleWBSChange}
                                    nodeInfo={nodeInfo}
                                />
                            )
                            : (
                                <EBSBox
                                    value={value !== undefined && value.bindType > 0 ? value.ebsNodes ?? undefined : undefined}
                                    onChange={handleEBSChange}
                                    nodeInfo={nodeInfo}
                                />
                            )}
                    </div>
                </div>
            </ComDrawer>
        </div>
    );
};

export default FormWbsEbs;
