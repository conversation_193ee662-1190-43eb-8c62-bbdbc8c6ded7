import {But<PERSON>, Drawer, Input, Space, Tree, Typography} from "antd";
import {DataNode, TreeProps} from "antd/lib/tree";
import React, {memo, useCallback, useEffect, useRef, useState} from "react";
import {cloneDeep, debounce} from "lodash-es";
import {getCheckQualityListChoose, getCheckSecurityListChoose} from "../../api/center";
import {GetCheckQualityListChooseType, GetCheckSecurityListChooseType} from "../../api/center/type";
import {filterTreeNew, generateList, ModuleType} from "../../assets/ts/utils";

export interface CheckSubOptionValueType {
    checkDescId: string;
    subOptionContent: string;
    subOptionId: string;
    content?: string;
}

export interface MultipleChoiceProps {
    isVisible: boolean; // 控制是否显示
    moduleType: string; // security(安全) quality（质量）
    // 工程类别id
    projectCategoryId: string;
    value: CheckSubOptionValueType[];
    onChange?: (value: CheckSubOptionValueType[]) => void;
    onClose: () => void;
}

const MultipleChoice = (props: MultipleChoiceProps) => {
    const inputRef = useRef<Input>(null);
    const {isVisible, onChange, moduleType, projectCategoryId, value = [], onClose} = props;
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [copyTreeData, setCopyTreeData] = useState<DataNode[]>([]); // 备份一份数据
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [treeValue, setTreeValue] = useState<CheckSubOptionValueType[]>(value);

    useEffect(() => {
        setTreeValue(value);
    }, [value]);

    // 获取安全 的原始数据
    const getSecurityData = useCallback(async (id: string) => {
        let dataList: GetCheckSecurityListChooseType[] = [];
        try {
            dataList = (await getCheckSecurityListChoose(id)).result ?? [];
        } catch (error) {
            dataList = [];
        }
        return dataList;
    }, []);
    // 转换安全 的原始数据 tree
    const getSecurityDataTree = useCallback((securityDataList: GetCheckSecurityListChooseType[]) => {
        const newSecurityDataList: GetCheckSecurityListChooseType[] = JSON.parse(JSON.stringify(securityDataList));
        newSecurityDataList.forEach((item) => {
            const newItem = item;
            newItem.key = item.id;
            newItem.title = item.content;
            newItem.children = newItem.checkSubentries;
            newItem.disabled = false;
            newItem.checkSubentries.forEach((item2) => {
                const newItem2 = item2;
                newItem2.key = item2.id;
                newItem2.title = item2.content;
                newItem2.parentId = item.id;
                newItem2.parentName = item.content;
                newItem2.children = newItem2.hiddenDangerPoints;
                newItem2.disabled = false;
                // eslint-disable-next-line max-nested-callbacks
                item2.hiddenDangerPoints.forEach((item3) => {
                    const newItem3 = item3;
                    newItem3.key = item3.id;
                    newItem3.title = item3.content;
                    newItem3.parentId = item2.id;
                    newItem3.parentName = item2.content;
                    // 最后一层节点标志
                    newItem3.lastNode = true;
                });
            });
        });
        return newSecurityDataList;
    }, []);
    // 获取质量 的原始数据
    const getQualityData = useCallback(async (id: string) => {
        let dataList: GetCheckQualityListChooseType[] = [];
        try {
            dataList = (await getCheckQualityListChoose(id)).result ?? [];
        } catch (error) {
            dataList = [];
        }
        return dataList;
    }, []);
    // 转换质量 的原始数据 tree
    const getQualityDataTree = useCallback((dataList: GetCheckQualityListChooseType[]) => {
        const newDataList: GetCheckQualityListChooseType[] = JSON.parse(JSON.stringify(dataList));
        newDataList.forEach((item) => {
            const newItem = item;
            newItem.key = item.id;
            newItem.title = item.content;
            newItem.children = newItem.checkContents;
            newItem.disabled = false;
            newItem.checkContents.forEach((item2) => {
                const newItem2 = item2;
                newItem2.key = item2.id;
                newItem2.title = item2.content;
                newItem2.parentId = item.id;
                newItem2.parentName = item.content;
                // 最后一层节点标志
                newItem2.lastNode = true;
            });
        });
        return newDataList;
    }, []);

    const init = useCallback(
        async () => {
            if (projectCategoryId === "") {
                return;
            }
            if (moduleType === ModuleType.security) {
                // 安全
                const securityDataList = await getSecurityData(projectCategoryId);
                setTreeData(getSecurityDataTree(securityDataList));
                setCopyTreeData(getSecurityDataTree(securityDataList));
            } else if (moduleType === ModuleType.quality) {
                // 质量
                const qualityDataList = await getQualityData(projectCategoryId);
                setTreeData(getQualityDataTree(qualityDataList));
                setCopyTreeData(getQualityDataTree(qualityDataList));
            }
        },
        [getQualityData, getQualityDataTree, getSecurityData, getSecurityDataTree, moduleType, projectCategoryId],
    );

    useEffect(() => {
        init();
    }, [init]);

    const handleOk = () => {
        if (onChange !== undefined) {
            onChange(treeValue);
        }
        onClose();
    };

    const handleCancel = () => {
        setTreeValue(value);
        onClose();
    };

    const defaultMatcher = (filterText: string, node: DataNode) => {
        if (typeof node?.title === "string") {
            return node?.title?.includes(filterText);
        }
        return false;
    };

    const onSearch = debounce(
        () => {
            let tempValue = "";
            if (inputRef !== null && inputRef.current !== null) {
                tempValue = inputRef.current.state.value;
            }
            const newTreeData = cloneDeep(treeData);
            if (tempValue === "") {
                setAutoExpandParent(false);
                setExpandedKeys([]);
                setCopyTreeData(treeData);
                return;
            }
            const filtered = newTreeData.map((item) => filterTreeNew(item, tempValue, defaultMatcher))
                .filter((item) => Array.isArray(item.children) && item.children.length > 0);
            setCopyTreeData(filtered);
            const newExpandedKeys = generateList(filtered, [])
                .map((item: DataNode) => item.key);
            setAutoExpandParent(true);
            setExpandedKeys(newExpandedKeys);
        },
        1000
    );
    const onExpand = (newExpandedKeys: React.Key[]) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
    };
    // 搜索结束

    const handleCheck: TreeProps["onCheck"] = (_val, e) => {
        const checkNodes = e.checkedNodes as unknown as GetCheckQualityListChooseType[];
        setTreeValue(checkNodes
            .filter((el) => el.lastNode === true)
            .map((el) => ({
                checkDescId: el.id,
                subOptionContent: el.parentName,
                subOptionId: el.parentId,
                content: el.content,
            })));
    };

    const renderNode: TreeProps["titleRender"] = (nodeData) => <Typography.Text style={{maxWidth: "400px"}} ellipsis>{nodeData.title}</Typography.Text>;
    const renderBox = () => (
        <Drawer
            onClose={handleCancel}
            visible={isVisible}
            title="检查分项"
            width={520}
            bodyStyle={{padding: 15}}
            maskClosable={false}
            closable={false}
            keyboard={false}
            footerStyle={{textAlign: "right"}}
            destroyOnClose
            footer={(
                <Space>
                    <Button onClick={handleCancel}>取消</Button>
                    <Button type="primary" onClick={handleOk}>确定</Button>
                </Space>
            )}
        >
            <div>
                <Input.Search ref={inputRef} onChange={onSearch} placeholder="请输入名称" style={{marginBottom: 10}} />
                <Tree
                    checkable
                    defaultExpandAll
                    treeData={copyTreeData}
                    onCheck={handleCheck}
                    onExpand={onExpand}
                    autoExpandParent={autoExpandParent}
                    expandedKeys={expandedKeys}
                    titleRender={renderNode}
                    checkedKeys={treeValue.map((el) => el.checkDescId)}
                />
            </div>
        </Drawer>
    );
    return <>{renderBox()}</>;
};

export default memo(MultipleChoice);
