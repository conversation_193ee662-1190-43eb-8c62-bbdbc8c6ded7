import Motor from "@motor/core";
import {isNotNullOrUndefined} from "../utils";
import {MotorContext} from "../../../reMotor";

class MotorMarkCollectionWrapper {
    private markerCollection: Motor.MarkCollectionEditor | null = null;

    private markerMap: Map<string, Motor.Model> = new Map();

    public init(): void {
        if (this.markerCollection === null) {
            const viewer = MotorContext.getViewer();
            const project = MotorContext.getProject();
            if (isNotNullOrUndefined(viewer) && isNotNullOrUndefined(project)) {
                this.markerCollection = new Motor.MarkCollectionEditor(viewer, project);
            }
        } else {
            Array.from(this.markerMap.values()).map((el) => this.markerCollection?.remove(el));
            this.markerCollection = null;
            this.markerMap.clear();
        }
    }

    public unInit(): void {
        Array.from(this.markerMap.values()).map((el) => this.markerCollection?.remove(el));
        this.markerCollection = null;
        this.markerMap.clear();
    }

    public removeById(id: string): void {
        const find = this.markerMap.get(id);
        const {markerCollection} = this;
        if (isNotNullOrUndefined(find) && isNotNullOrUndefined(markerCollection)) {
            markerCollection.remove(find);
        }
    }

    public async create(id: string, option: Motor.MotorCore.CZMLSchema,
        clockOption?: Motor.MotorCore.ClockSchema): Promise<Motor.Model | null> {
        const {markerCollection} = this;
        if (isNotNullOrUndefined(markerCollection)) {
            const model = await markerCollection.create(option, clockOption);
            if (typeof model !== "undefined") {
                this.removeById(id);
                this.markerMap.set(id, model);
                return model;
            }
        }

        return Promise.resolve(null);
    }

    public async add(option: Motor.MotorCore.CZMLSchema, clockOption?: Motor.MotorCore.ClockSchema): Promise<Motor.Model | null> {
        return this.create(option.id ?? "", option, clockOption);
    }

    public async update(model: Motor.Model, option: Motor.MotorCore.CZMLSchema): Promise<void> {
        const {markerCollection} = this;
        if (isNotNullOrUndefined(markerCollection)) {
            return markerCollection.update(model, option);
        }

        return Promise.resolve();
    }

    public async remove(model: Motor.Model): Promise<void> {
        const {markerCollection} = this;
        if (isNotNullOrUndefined(markerCollection)) {
            return markerCollection.remove(model);
        }

        return Promise.resolve();
    }

    public removeAll(): void {
        Array.from(this.markerMap.values()).map((el) => this.markerCollection?.remove(el));
        this.markerMap.clear();
    }

    public getKeyByModelId(modelId: string): string | undefined {
        const list = Array.from(this.markerMap.entries());
        const find = list.find((el) => el[1].id === modelId);
        if (isNotNullOrUndefined(find)) {
            return find[0];
        }
        return undefined;
    }
}

export default MotorMarkCollectionWrapper;
