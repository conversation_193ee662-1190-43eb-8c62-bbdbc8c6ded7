import React, {useCallback, useEffect, useMemo, useState, useRef} from "react";
import {Col, Progress, Row, Tooltip} from "antd";
import {useSelector} from "react-redux";
import moment from "moment";
import {useBoolean} from "ahooks";
import TableLayout from "../../components/TableLayout";
import {RootState} from "../../store/rootReducer";
import TableColumnsControl, {ComColumnsProps} from "../../components/TableColumnsControl";
import {ComFormItemProps} from "../../components/FormItem";
import QueryFormSingle from "../../components/QueryFormSingle";
import {getWbsStatisticByParent, getWbsStatisticByName, getRules} from "../../api/statistic";
import * as TYPE from "../../api/statistic/type";
import useStyles from "./style";
import {dfsTree, listToTree} from "../../assets/ts/utils";
import {ruleColor, includesRange} from "../BaseSettings/RuleSetting";
import VirtualTable from "./VirtualTable";

type Clumns = ComColumnsProps<TYPE.WbsStatistic>[];

interface Node extends TYPE.WbsStatistic {
    indexPath?: string;
    children?: Node[];
    level?: number;
}

const columnsInit: Clumns = [
    {
        title: "序号",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "indexPath",
        // fixed: "left",
        width: 160,
        ellipsis: true,
        render: (text, _record, index: number) => (
            <Tooltip title={text}>
                <div className="text-ellipsis">{text}</div>
            </Tooltip>
        ),
    },
    {
        title: "wbs",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "name",
        render: (text, _record, index: number) => (
            <Tooltip title={text}>
                <div className="text-ellipsis">{text}</div>
            </Tooltip>
        ),
        // fixed: "left",
        width: 270,
    },
    {
        title: "执行状态",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "executionStatus",
        width: 120,
    },
    {
        title: "持续天数",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "days",
        width: 120,
        render: (text, record: TYPE.WbsStatistic, index: number) => {
            const days = moment(record.planEndDate).diff(record.planStartDate, "day");
            return `${days}d`;
        }
    },
    {
        title: "计划开始时间",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "planStartDate",
        width: 140,
        render: (text, _record: TYPE.WbsStatistic) => (
            ![undefined, null].includes(text) ? moment(text).format("YYYY.MM.DD") : "--"
        )
    },
    {
        title: "计划完成时间",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "planEndDate",
        width: 140,
        render: (text, _record: TYPE.WbsStatistic) => (
            ![undefined, null].includes(text) ? moment(text).format("YYYY.MM.DD") : "--"
        )
    },
    {
        title: "实际开始日期",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "actualStartDate",
        width: 140,
        render: (text, _record: TYPE.WbsStatistic) => (
            ![undefined, null].includes(text) ? moment(text).format("YYYY.MM.DD") : "--"
        )
    },
    {
        title: "实际完成日期",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "actualEndDate",
        width: 140,
        render: (text, _record: TYPE.WbsStatistic) => (
            ![undefined, null].includes(text) ? moment(text).format("YYYY.MM.DD") : "--"
        )
    },
    {
        title: "计划进度",
        show: true,
        mustShow: false,
        align: "left",
        dataIndex: "ratio",
        width: 240,
    },
    {
        title: "偏差值",
        show: true,
        mustShow: false,
        align: "left",
        width: 100,
        dataIndex: "deviation",
    },
];

const queryItemList: ComFormItemProps[] = [
    {
        key: "name",
        type: "input",
        typeConfig: {placeholder: "请输入wbs"},
        itemConfig: {name: "name", label: "wbs"},
        colConfig: {span: 6},
    }
];

const Statistics = () => {
    const cls = useStyles();
    const {curSectionInfo, orgInfo} = useSelector((state: RootState) => state.commonData);
    const [tableData, setTableData] = useState<Node[]>([]);
    const [columns, setColumns] = useState<Clumns>(columnsInit);
    const [isloading, {setTrue: onLoading, setFalse: offLoading}] = useBoolean(false);
    const isDept = curSectionInfo?.id === orgInfo.orgId; // 是否是项目节点
    const tableRef = useRef({});

    // 添加路径索引
    const addTableIndexPath = (treeTable: TYPE.WbsStatistic[]): Node[] => {
        const fn = (tree: TYPE.WbsStatistic[], parentPath = ""): Node[] => {
            const root: Node[] = [];
            tree.forEach((node, index) => {
                const nodePath = parentPath === ""
                    ? `${index + 1}`
                    : `${parentPath}.${index + 1}`;
                const level = nodePath.split(".").length;
                const newRoot: Node = {
                    ...node,
                    indexPath: nodePath,
                    level
                };
                if (node.children !== undefined && node.children.length > 0) {
                    newRoot.children = fn(node.children, nodePath);
                }
                root.push(newRoot);
            });

            return root;
        };

        return fn(treeTable);
    };

    // 销毁table并重新渲染
    const handleRefreshTable = useCallback(() => {
        (tableRef.current as any).initTable();
    }, []);

    // 搜索重置
    const handleReset = useCallback(async () => {
        // console.log("===========================");
        // console.log("curSectionInfo", curSectionInfo);
        // console.log("orgInfo", orgInfo);
        if (orgInfo.orgId === "" || curSectionInfo?.isAll === true) {
            return;
        }
        if (curSectionInfo === null) {
            setTableData([]);
            return;
        }
        const body = {
            deptId: orgInfo.orgId,
            sectionId: isDept ? "" : curSectionInfo?.id ?? "",
            wbsPId: "",
            isDept,
        };
        handleRefreshTable();
        onLoading();
        const {data} = await getWbsStatisticByParent(body);
        offLoading();
        const wbsStatistic = data.map((item, index) => ({
            ...item,
            indexPath: `${index + 1}`,
            children: item.hasChild ? [] : undefined
        }));
        setTableData(wbsStatistic);
    }, [curSectionInfo, handleRefreshTable, isDept, offLoading, onLoading, orgInfo.orgId]);

    // 搜索
    const handleFormFinish = useCallback(async (params: {name: string}) => {
        try {
            if (["", undefined].includes(params.name)) {
                handleReset();
                return;
            }
            const body = {
                deptId: orgInfo.orgId,
                sectionId: isDept ? "" : curSectionInfo?.id ?? "",
                name: params.name,
                isDept,
            };
            handleRefreshTable();
            onLoading();
            const {data} = await getWbsStatisticByName(body);
            const tree = listToTree(data, {parentId: "pId"});
            const newTableData = addTableIndexPath(tree);
            offLoading();
            setTableData(newTableData);
        } catch (error) {
            offLoading();
            //
        }
    }, [orgInfo.orgId, isDept, curSectionInfo, handleRefreshTable, onLoading, offLoading, handleReset]);

    const filterColumns = useMemo(
        () => columns.filter((col) => col.show || col.mustShow),
        [columns]
    );

    // table展开配置项
    const expandable = useMemo(() => ({
        onExpand: async (expanded: boolean, record: TYPE.WbsStatistic) => {
            try {
                if (expanded && record?.children?.length === 0) {
                    const body = {
                        deptId: orgInfo.orgId,
                        sectionId: record.sectionId,
                        wbsPId: record.id,
                        isDept,
                    };
                    onLoading();
                    const {data} = await getWbsStatisticByParent(body);
                    offLoading();
                    const tree: Node[] = [];
                    dfsTree(tableData, (node) => {
                        const newNode = node;
                        if (newNode.id === record.id) {
                            if (data.length === 0) {
                                newNode.children = undefined;
                                tree.push(newNode);
                                return;
                            }
                            newNode.children = data.map((childrenItem) => (
                                {
                                    ...childrenItem,
                                    children: childrenItem.hasChild ? [] : undefined
                                }
                            ));
                        }
                        // 根节点
                        if (newNode.pId === null) {
                            tree.push(node);
                        }
                    });
                    const newTableData = addTableIndexPath(tree);
                    setTableData(newTableData);
                }
            } catch (error) {
                offLoading();
            }
        },
        indentSize: 6
    }), [isDept, offLoading, onLoading, orgInfo.orgId, tableData]);

    // 进度计划列渲染
    const renderRatioColumn = useCallback((
        text: number,
        record: TYPE.WbsStatistic,
        ruleList: TYPE.Rule[]
    ) => {
        let color = ruleColor["蓝色"];

        if (text === 100 && record.actualEndDate === null) {
            color = ruleColor["红色"];
        } else if (text === 100 && record.actualEndDate !== null) {
            color = ruleColor["绿色"];
        } else if (includesRange(ruleList[1]?.rules, text)) {
            color = ruleColor["黄色"];
        }

        return (
            <div style={{width: 178}} className={cls.progressWrap}>
                <Progress
                    percent={text}
                    strokeColor={color.strokeColor}
                    trailColor={color.trailColor}
                    strokeLinecap="butt"
                    strokeWidth={16}
                />
            </div>
        );
    }, [cls]);

    const handleGetRules = useCallback(async () => {
        const {data} = await getRules();
        const ruleList = data.sort((a, b) => a.sort - b.sort);

        setColumns((oldColumns) => {
            const newColumns = oldColumns.map((column) => {
                if (column.title === "计划进度") {
                    return {
                        ...column,
                        render: (text: number, record: TYPE.WbsStatistic) => renderRatioColumn(text, record, ruleList)
                    };
                }
                return column;
            });
            return newColumns;
        });
    }, [renderRatioColumn]);

    useEffect(() => {
        handleReset();
    }, [cls, handleReset]);

    useEffect(() => {
        handleGetRules();
    }, [handleGetRules]);

    return (
        <TableLayout hiddenAllNode>
            <div className="safety-quality-table" style={{paddingTop: 8}}>
                <Row align="middle" justify="space-between">
                    <Col span={2} />
                    <Col span={22}>
                        <QueryFormSingle
                            queryItemList={queryItemList}
                            onFormFinish={handleFormFinish}
                            onFormClear={handleReset}
                            formRow={{justify: "end"}}
                        />
                    </Col>
                </Row>
                <Row justify="end">
                    <TableColumnsControl
                        setColumnsList={setColumns}
                        columnsList={columns}
                    />
                </Row>
                <VirtualTable
                    rowKey="id"
                    loading={isloading}
                    columns={filterColumns}
                    dataSource={tableData}
                    expandable={expandable}
                    scroll={{y: 600, x: "100vw"}}
                    cRef={tableRef}
                />
            </div>
        </TableLayout>
    );
};

export default Statistics;
