import React, {ReactNode} from "react";

export interface FormRenderFunProps {
    value?: unknown;
    render?: (value: unknown) => ReactNode;
}

const FormRenderFun = (props: FormRenderFunProps) => {
    const {value = "", render} = props;

    if (typeof render !== "function") {
        return null;
    }

    return (
        <>
            {render(value)}
        </>
    );
};

export default FormRenderFun;
