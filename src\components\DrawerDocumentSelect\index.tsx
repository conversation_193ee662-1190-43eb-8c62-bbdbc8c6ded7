import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {cloneDeep, debounce} from "lodash-es";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {Col, DrawerProps, Input, Pagination, Row, Tree} from "antd";
import {DataNode} from "antd/lib/tree";
import moment from "moment";
import {getDocList, getDocPathTree} from "../../api/common.api";
import {DocType, GetDocTreeParams, PathType, FileType} from "../../api/common.type";
import Color from "../../assets/css/Color";
import useComState from "../../assets/hooks/useComState";
import {filterTreeNew, generateList} from "../../assets/ts/utils";
import {RootState} from "../../store/rootReducer";
import ComDrawer from "../ComDrawer";
import ComTable from "../ComTable";
import {columns} from "./data";
import MyIcon from "../MyIcon";

const useStyles = createUseStyles({
    root: {
        display: "flex",
        height: "100%",
    },
    leftContainer: {
        width: 300,
        height: "100%",
        padding: 24,
        borderRight: `1px solid ${Color["light-line-1"]}`,
    },
    rightContainer: {
        display: "flex",
        flexDirection: "column",
        flex: "1 1 0",
        height: "100%",
        padding: 24,
    },
});

interface DrawerDocumentSelectBoxProps {
    drawerConfig?: DrawerProps;
    visible: boolean;
    onClose: () => void;
    title?: string;
    width?: number;
    checkedDocumentArr: FileType[];
    onSelectDocument: (list: FileType[]) => void;
}

const treeIconStyle = {
    marginTop: 2,
    fontSize: 20,
};

const DrawerDocumentSelectBox = (props: DrawerDocumentSelectBoxProps) => {
    const {
        visible,
        onClose,
        title = "引用资料库",
        width = 1000,
        checkedDocumentArr,
        onSelectDocument,
    } = props;
    const cls = useStyles();
    const treeSearchInputRef = useRef<Input>(null);
    const tableSearchInputRef = useRef<Input>(null);
    const expandedKeysRef = useRef<React.Key[]>([]);
    const {orgInfo} = useSelector((rootState: RootState) => rootState.commonData);
    const [state, setState] = useComState({});
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [originPathTreeData, setOriginPathTreeData] = useState<DataNode[]>([]);
    const [pathTreeData, setPathTreeData] = useState<DataNode[]>([]);
    const [dataSource, setDataSource] = useState<DocType[]>([]);
    const [tempCheckedDocumentArr, setTempCheckedDocumentArr] = useState<FileType[]>(checkedDocumentArr);

    const showTableData = useMemo(() => {
        const start = (state.curPage - 1) * state.pageSize;
        const end = start + state.pageSize;
        return dataSource.slice(start, end);
    }, [dataSource, state.curPage, state.pageSize]);

    const mapPathTreeData = useCallback((item: PathType) => {
        const newItem: DataNode = {
            key: item.pathId,
            title: `${item.pathName}(${item.fileNum})`,
            icon: <MyIcon type="icon-unfile" style={treeIconStyle} />,
        };
        if (item.subList?.length > 0) {
            newItem.children = item.subList.map((sub) => mapPathTreeData(sub));
        }
        return newItem;
    }, []);

    const getDocPathTreeCallback = useCallback(async () => {
        if (orgInfo?.orgId !== undefined) {
            const params: GetDocTreeParams = {
                nodeType: 1,
                nodeId: orgInfo?.orgId,
                needFileNum: true,
            };
            const {code, result} = await getDocPathTree(params);
            if (code === 200) {
                const treeData = result.pathList?.map((item) => mapPathTreeData(item)) ?? [];
                setOriginPathTreeData(treeData);
                setPathTreeData(treeData);
            }
        }
    }, [orgInfo, mapPathTreeData]);

    useEffect(() => {
        if (visible) {
            setExpandedKeys([]);
            setDataSource([]);
            setTempCheckedDocumentArr(checkedDocumentArr);
            getDocPathTreeCallback();
        }
    }, [checkedDocumentArr, getDocPathTreeCallback, visible]);

    const getDocListCallback = useCallback(async (queryParams) => {
        if (orgInfo?.orgId !== undefined) {
            const params = {
                docOrgRelationParam: {
                    nodeType: 1,
                    nodeId: orgInfo?.orgId,
                    containChild: true
                },
                ...state.queryFormData,
                ...queryParams
            };
            const {code, result} = await getDocList(params);
            if (code === 200) {
                const {docList = []} = result;
                setDataSource(docList);
                setState.setTotal(docList.length);
            }
        }
    }, [orgInfo, state, setState]);

    // 树搜索
    const handleSearchTree = useCallback(debounce(() => {
        const value = treeSearchInputRef.current?.state.value ?? "";
        const newTreeData = cloneDeep(originPathTreeData);
        if (value === "") {
            setAutoExpandParent(false);
            setExpandedKeys([]);
            setPathTreeData(originPathTreeData);
            return;
        }
        const filtered = newTreeData
            .map((item) => filterTreeNew(item, value, (filterText: string, node: DataNode) => {
                if (typeof node?.title === "string") {
                    return node?.title?.includes(filterText);
                }
                return false;
            }))
            .filter((item) => Array.isArray(item.children) && item.children.length > 0);
        setPathTreeData(filtered);
        const newExpandedKeys = generateList(filtered, [])
            .map((item: DataNode) => item.key);
        setAutoExpandParent(true);
        setExpandedKeys(newExpandedKeys);
    }, 500), [originPathTreeData]);

    // 表格搜索
    const handleSearchTable = useCallback(debounce(() => {
        const value = tableSearchInputRef.current?.state.value ?? "";
        setState.setCurPage(1);
        const params = {
            ...state.queryFormData,
            docName: value,
        };
        setState.setQueryFormData(params);
        getDocListCallback(params);
    }, 500), [setState, state.queryFormData, getDocListCallback]);

    const handlePaginationChange = useCallback((curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(pageSizeVal ?? 10);
    }, [setState]);

    const updatePathTreeIcon = useCallback((node: DataNode): DataNode => {
        const iconType = expandedKeysRef.current.includes(node.key) ? "icon-openfile" : "icon-unfile";
        return {
            ...node,
            children: node.children?.map((child) => updatePathTreeIcon(child)) ?? [],
            icon: <MyIcon type={iconType} style={treeIconStyle} />
        };
    }, []);

    const handleExpand = (newExpandedKeys: React.Key[]) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
        expandedKeysRef.current = newExpandedKeys;
        setPathTreeData(pathTreeData.map((node) => updatePathTreeIcon(node)));
    };

    // 选中树节点
    const handleSelectTree = useCallback((value) => {
        setState.setCurPage(1);
        const params = {
            ...state.queryFormData,
            pathId: value[0],
        };
        setState.setQueryFormData(params);
        getDocListCallback(params);
    }, [setState, state.queryFormData, getDocListCallback]);

    // 选择文件
    const handleSelectDocument = useCallback((record: DocType, selected: boolean) => {
        if (selected === true) {
            setTempCheckedDocumentArr([
                ...tempCheckedDocumentArr,
                {
                    fileUuid: record.fileUuid,
                    fileName: record.docName,
                    fileSize: record.fileSize,
                    timeStamp: moment().valueOf(),
                }
            ]);
        } else {
            setTempCheckedDocumentArr(tempCheckedDocumentArr.filter((item) => item.fileUuid !== record.fileUuid));
        }
    }, [tempCheckedDocumentArr]);

    const handleSelectAllDocument = useCallback((selected: boolean, _: DocType[], changeRows: DocType[]) => {
        if (selected === true) {
            const newArr = [...tempCheckedDocumentArr];
            changeRows.forEach((doc) => {
                newArr.push({
                    fileUuid: doc.fileUuid,
                    fileName: doc.docName,
                    fileSize: doc.fileSize,
                    timeStamp: moment().valueOf(),
                });
            });
            setTempCheckedDocumentArr(newArr);
        } else {
            const newArr = tempCheckedDocumentArr.filter(
                (checkedDoc) => changeRows.some((item) => item.fileUuid === checkedDoc.fileUuid) === false
            );
            setTempCheckedDocumentArr(newArr);
        }
    }, [tempCheckedDocumentArr]);

    const handleOk = () => {
        onSelectDocument(tempCheckedDocumentArr);
        onClose();
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <ComDrawer
            onCancel={handleCancel}
            onOk={handleOk}
            visible={visible}
            title={title}
            width={width}
            bodyStyle={{padding: 0}}
        >
            <div className={cls.root}>
                <div className={cls.leftContainer}>
                    <Input.Search
                        placeholder="输入目录名称"
                        ref={treeSearchInputRef}
                        onChange={handleSearchTree}
                        onSearch={handleSearchTree}
                    />
                    <Tree
                        showIcon
                        style={{marginTop: 16, overflow: "auto"}}
                        autoExpandParent={autoExpandParent}
                        onExpand={handleExpand}
                        expandedKeys={expandedKeys}
                        onSelect={handleSelectTree}
                        treeData={pathTreeData}
                    />
                </div>
                <div className={cls.rightContainer}>
                    <Input.Search
                        style={{width: 300, flexShrink: 0}}
                        placeholder="输入资料名称"
                        ref={tableSearchInputRef}
                        onChange={handleSearchTable}
                        onSearch={handleSearchTable}
                    />
                    <ComTable<DocType>
                        style={{marginTop: 16, overflowY: "auto", flex: "1 1 0"}}
                        rowKey="fileUuid"
                        columns={columns}
                        dataSource={showTableData}
                        rowSelection={{
                            selectedRowKeys: tempCheckedDocumentArr.map((item: FileType) => item.fileUuid),
                            onSelect: handleSelectDocument,
                            onSelectAll: handleSelectAllDocument,
                            columnWidth: 60
                        }}
                    />
                    <Row justify="end" style={{marginTop: 20, flexShrink: 0}}>
                        <Col>
                            <Pagination
                                total={state.total}
                                showSizeChanger
                                current={state.curPage}
                                pageSize={state.pageSize}
                                onChange={handlePaginationChange}
                            />
                        </Col>
                    </Row>
                </div>
            </div>
        </ComDrawer>
    );
};

export default DrawerDocumentSelectBox;
