interface RouterItem {routes?:{path?:string}[] | undefined | [];path?:string;name?:string}
interface ChildItem { routes?: [];name?:string;}
// 获取当前路由信息
export const getCurrentRoute = (module?:string, routeList?:{path?:string}[]) => {
    const mainList = routeList?.filter((item) => item.path !== "/login") as {children:[]}[];
    let allRouteList: ChildItem[] = (mainList.reduce((pre:any, next:any) => [...pre?.routes ?? [], ...next?.routes ?? []] as unknown as { routes:[],children: [];name?:string;})  ?? []) as unknown as ChildItem[];
    allRouteList = allRouteList?.length > 0 ? allRouteList : mainList[0]?.children;
    console.log(allRouteList, "allRouteList");
    const item = allRouteList?.filter((ele) => ele.name === module)[0];
    console.log(item, "当前路由信息");
    return item;
};
/**
     * 获取路由跳转path
     * @param module {string} 路由跳转模块
     * @param subMenuPath {string} 路由跳转子菜单
     * @returns 返回路由path 如果有子模块直接拼接路由地址 #ps（1.无子模块查询当前模块路由信息.
     *                                                          1.1模块下有子菜单默认第一个子菜单path.
     *                                                          1.2无子菜单默认跳转模块地址.
     *                                                     2.有子模块直接模块拼接子模块输出精准路由定位.)
     */
export const getRoutePath = (module?:string, routeList?:[]) => {
    let pathUrl:string = "";
    const url:string[] = module?.split("/") as string[];
    if (module?.startsWith("/") === true && url?.length > 2) { // 完整路径,指定准确菜单 url = "/menu/subMenu"
        pathUrl = module;
    } else if (module?.includes("/") === true && url[0] !== "") { // 不完整路径,指定准确菜单 url = "menu/subMenu"
        pathUrl =  `/${module}`;
    } else { // 不完整路径 指定模块
        const current:RouterItem = getCurrentRoute(url[1] ?? module, routeList) as RouterItem;
        const currentChild = Array.isArray(current?.routes) && current?.routes?.length > 0;
        // const path = currentChild ? current?.children[0]?.path : current?.path;
        const subMenuPath = current?.routes !== undefined && current?.routes?.length>0
        ? current?.routes[0]?.path 
        : undefined;
        const path = currentChild ? subMenuPath : current?.path;
        pathUrl = path as string;
    }
    return pathUrl
};
