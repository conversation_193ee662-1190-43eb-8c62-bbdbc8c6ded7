import {Space, Spin, Tree, TreeProps} from "antd";
import React, {memo} from "react";
import {DataNode} from "antd/lib/tree";
import Icon from "@ant-design/icons";
import {CheckInfo, CheckedInfoType, StatusTreeDataType} from "../index.d";
import {DefaultStatusIcon} from "../../../../../assets/icons";

// import useStyle from "./style";


const DefaultStatus = (props: {setCheckedInfo: React.Dispatch<React.SetStateAction<CheckedInfoType>>}) => {
    // const cls = useStyle();
    const {setCheckedInfo} = props;

    const [checkedKeys, setCheckedKeys] = React.useState<React.Key[]>(["all", "notStarted", "ongoing", "finished"]);
    const [treeData, setTreeData] = React.useState<StatusTreeDataType[]>([]);

    const getData = React.useCallback(() => {
        setTreeData([
            {
                title: "全部",
                key: "all",
                children: [
                    {
                        title: "未开始",
                        key: "notStarted",
                        color: "#C8CBCF"
                    },
                    {
                        title: "进行中",
                        key: "ongoing",
                        color: "#FAAB0C"
                    },
                    {
                        title: "已完成",
                        key: "finished",
                        color: "#2DA641"
                    },
                ]
            }
        ]);
    }, []);
    React.useEffect(() => {
        getData();
    }, [getData]);

    const handleChecked: TreeProps["onCheck"] = (val, info: CheckInfo<DataNode>) => {
        const {checkedNodes, checked, halfCheckedKeys} = info;
        setCheckedKeys(val as unknown as React.Key[]);
        setCheckedInfo((oldInfo) => ({
            ...oldInfo,
            defaultStatus: {
                checkedKeys: checkedNodes.filter((i) => i.children === undefined).map((j) => j.key),
                isCheckAll: checked && (halfCheckedKeys ?? []).length === 0
            }
        }));
    };


    const renderTreeNode = React.useCallback((list: StatusTreeDataType[]) => list.map((item) => (
        <Tree.TreeNode
            key={item.key}
            title={(
                <div>
                    <Space>
                        {item.key !== "all" && <Icon component={DefaultStatusIcon} style={{color: item.color ?? ""}} /> }
                        <span>{item.title}</span>
                    </Space>
                </div>
            )}
        >
            {item.children !== undefined && item.children.length > 0 && renderTreeNode(item.children)}
        </Tree.TreeNode>
    )), []);

    return (
        <div style={{height: "100%"}}>
            {treeData.length === 0
                ? <Spin />
                : (
                    <Tree
                        defaultExpandAll
                        autoExpandParent
                        style={{margin: "16px 0", height: "calc( 100% - 40px )"}}
                        checkedKeys={checkedKeys}
                        onCheck={handleChecked}
                        checkable
                    >
                        {renderTreeNode(treeData)}
                    </Tree>
                )}
        </div>
    );
};

export default memo(DefaultStatus);
