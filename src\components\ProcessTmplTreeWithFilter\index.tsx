import React, {use<PERSON>allback, useMemo, useState} from "react";
import {createUseStyles} from "react-jss";
import {Tree, TreeProps, Alert, Row} from "antd";
import {DownOutlined} from "@ant-design/icons";
import {ProcessTmplTreeNode} from "../../api/processManager/type";
import {Defined, filterTree, isDefined} from "../../assets/ts/utils";
import MyInputSearch from "../MyInputSearch";

const {TreeNode} = Tree;
const alertMsg = "点击任一工序，即可修改颜色";
const useStyles = createUseStyles({
    rect: {
        display: "inline-block",
        width: 16,
        height: 16,
        borderRadius: 2,
        border: "1px solid #061127",
        marginRight: 4
    }
});
const isNodeDisabled = (key: string, keySet?: Set<string>) => {
    if (keySet instanceof Set && keySet.size > 0) {
        return keySet.has(key);
    }
    return false;
};

interface ProcessTmplDataNode {
    ["data-value"]?: ProcessTmplTreeNode;
    selected: boolean;
}

interface ProcessTmplTreeWithFilterProps extends TreeProps {
    tmplTree?: ProcessTmplTreeNode[];
    /** 引导用户进行工序模板设置的提示，一般情况下可忽略这个参数 */
    showAlert?: boolean;
    disabledKeys?: Set<string>;
    onSearch?: (keywords: string) => void;
    onLocalSelect?: (node?: ProcessTmplTreeNode) => void;
    onColorBlockClick?: (node: ProcessTmplTreeNode) => void;
}

/** 自带搜索功能的工序模板树 */
const ProcessTmplTreeWithFilter = (props: ProcessTmplTreeWithFilterProps) => {
    const cls = useStyles();
    const {tmplTree = [], showAlert = false, disabledKeys, onLocalSelect, onSearch, onColorBlockClick, ...rest} = props;
    const [keywords, setKeywords] = useState<string>("");

    const handleSearch = useCallback(
        (val: string) => {
            setKeywords(val);
            if (onSearch !== undefined) {
                onSearch(val);
            }
        },
        [onSearch]
    );

    const renderTreeNodeTitle = useCallback(
        (node: ProcessTmplTreeNode) => {
            if (node.color === null || node.color === undefined) {
                return node.name;
            }
            return (
                <Row align="middle">
                    <span
                        className={cls.rect}
                        style={{background: `rgb(${node.color})`}}
                        onClick={() => onColorBlockClick instanceof Function && onColorBlockClick(node)}
                    />
                    {node.name}
                </Row>
            );
        },
        [cls.rect, onColorBlockClick]
    );

    const renderTreeNode = useCallback(
        (data: ProcessTmplTreeNode[]) => data.map((v) => {
            if (Array.isArray(v.children) && v.children.length > 0) {
                return (
                    <TreeNode key={v.key} title={v.name} selectable={false} data-value={v}>
                        {renderTreeNode(v.children)}
                    </TreeNode>
                );
            }
            return (
                <TreeNode
                    key={v.key}
                    title={renderTreeNodeTitle(v)}
                    selectable={isDefined(v.color)}
                    data-value={v}
                    disabled={isNodeDisabled(v.stateKey ?? "", disabledKeys)}
                />
            );
        }),
        [disabledKeys, renderTreeNodeTitle]
    );

    const handleTreeNodeSelect = useCallback<Defined<TreeProps["onSelect"]>>(
        (_keys, info) => {
            const originNode = info.node as unknown as ProcessTmplDataNode;
            if (onLocalSelect instanceof Function) {
                onLocalSelect(info.selected ? originNode["data-value"] : undefined);
            }
        },
        [onLocalSelect]
    );

    const memoTreeData = useMemo(
        () => {
            if (keywords === "") {
                return tmplTree;
            }
            return filterTree(tmplTree, (node) => node.name.includes(keywords)) ?? [];
        },
        [keywords, tmplTree]
    );

    return (
        <>
            <MyInputSearch
                placeholder="输入工序名称"
                onSearch={handleSearch}
                allowClear
                value={keywords}
                style={{marginBottom: 16}}
            />
            {
                showAlert && (
                    <Alert
                        style={{marginBottom: 16}}
                        message={alertMsg}
                        type="info"
                        showIcon
                        closable
                    />
                )
            }
            <Tree
                blockNode
                switcherIcon={<DownOutlined />}
                {...rest}
                onSelect={handleTreeNodeSelect}
            >
                {renderTreeNode(memoTreeData)}
            </Tree>
        </>
    );
};

export default ProcessTmplTreeWithFilter;
