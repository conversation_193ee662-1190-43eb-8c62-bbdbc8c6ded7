import React, {useContext, useEffect, useState} from "react";
import {Button, Row, Switch} from "antd";
import {setGanttCriticalPath} from "../../../gantt/ganttConfig";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";

const CriticalPathButton = () => {
    const cls = useStyles();
    const {isShowGantt} = useContext(EditorContext);
    const [ganttCriticalPathState, setGanttCriticalPathState] = useState<boolean>(false);

    useEffect(() => {
        // 是否显示关键路径
        setGanttCriticalPath(ganttCriticalPathState);
    }, [ganttCriticalPathState]);

    if (isShowGantt === false) {
        return null;
    }

    return (
        <Row align="middle" className={cls.textButton}>
            <Button
                className={cls.textButton}
                style={{margin: 0}}
                type="text"
                onClick={() => setGanttCriticalPathState(!ganttCriticalPathState)}
            >
                关键路径
            </Button>
            <Switch
                style={{marginLeft: 4}}
                checked={ganttCriticalPathState}
                onChange={(checked) => setGanttCriticalPathState(checked)}
            />
        </Row>
    );
};

export default CriticalPathButton;
