import qs from "query-string";
import {pick, mapValues, get, has} from "lodash-es";


class ErrorMsg extends Error {
    public code?: string;

    public msg?: string;

    constructor(msg: string | undefined = undefined, code: string | undefined = undefined) {
        super();
        this.code = code;
        this.msg = msg;
    }
}
export interface CustomRequest<T extends string = string, K extends Record<string, unknown> = {}> {
    method?: string;
    url: string;
    body?: {
        [key in (T | keyof K)]: string;
    };
    query?: {
        [key in (T | keyof K)]: string;
    };
    header?: {
        [key in (T | keyof K)]: string;
    };
    extraData?: K;
    credentials?: "include" | "omit" | "same-origin" | undefined | boolean;
    responseType?: "text" | "json";
    expectStatus?: (number | [number, number])[];
    // expectJson?: {
    //     [key: string]: unknown;
    // };
    // expectText?: string[];
    retJson?: {
        [key: string]: string;
    } | string[];
    // retText?: string;
}

const fromString = (url: string): CustomRequest => ({
    method: "GET",
    url
});



const makeUrl = (url: string, values: Record<string, unknown>) => (Reflect.ownKeys(values) as string[])
    .reduce<string>((prev, key) => prev.replace(`\${${key}}`, encodeURIComponent(String(values[key]))), url);

const makeBodyOrQuery = (tar: CustomRequest["body"] | CustomRequest["query"] | CustomRequest["header"], values: Record<string, unknown>) => {
    if (tar === undefined) {
        return undefined;
    }
    return (Reflect.ownKeys(tar) as string[]).reduce((prev, key) => {
        if (has(values, key) === true) {
            return {[tar[key]]: get(values, key), ...prev};
        }
        return prev;
    }, {});
};

const getCredentials = (credentials: CustomRequest["credentials"]) => {
    switch (credentials) {
        default: return credentials;
        case true: return "include";
        case false: return "omit";
    }
};

const makePair = <T = unknown>(singleOrPair: T | [T, T]) => (Array.isArray(singleOrPair) ? singleOrPair : [singleOrPair, singleOrPair]);

// const makeRegexp = (expectText: string) => new RegExp(expectText, "su");

const makeRequest = async (values: Record<string, unknown>, request: CustomRequest<keyof typeof values> | string) => {
    const customRequest = typeof request === "string" ? fromString(request) : request;
    const realValues = {...customRequest.extraData, ...values};
    const url = makeUrl(customRequest.url, realValues);
    const body = makeBodyOrQuery(customRequest.body, realValues);
    const query = makeBodyOrQuery(customRequest.query, realValues);
    const header = makeBodyOrQuery(customRequest.header, realValues);
    const {responseType = "json", expectStatus = [200], retJson} = customRequest;
    const res = await fetch(`${url}${query === undefined ? "" : `?${qs.stringify(query)}`}`, {
        credentials: getCredentials(customRequest.credentials),
        method: customRequest.method ?? "GET",
        body: body === undefined ? undefined : JSON.stringify(body),
        headers: {
            "content-type": responseType === "json" ? "application/json" : "text/plain",
            ...header
        },
    });
    const {status} = res;
    if (expectStatus.map(makePair).some((range) => range[0] > status || range[1] < status)) {
        let json!: {message: string; infoCode: string};
        try {
            json = await res.json();
        } catch (error) {
            throw new Error(`unexpected return status[${status}]`);
        }
        if (json.message !== undefined) {
            throw new ErrorMsg(json.message, json.infoCode);
        }
        throw new Error(`unexpected return status[${status}]`);
    }
    if (responseType === "text") {
        const text = await res.text();
        return text;
    }
    const json = await res.json();
    if (retJson === undefined) {
        return json;
    }
    if (Array.isArray(retJson)) {
        return pick(json, retJson);
    }
    return mapValues(retJson, (key: string) => get(json, key));
};

export default makeRequest;
