/* eslint-disable max-lines */
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import {v4 as uuidv4} from "uuid";
import {message, Modal} from "antd";
import {cloneDeep, isNil, isEmpty} from "lodash-es";
import moment from "moment";
import {ExportToExcelProps, GanttTask, GanttLink, GanttDataset, ImportExcel} from "./interface";
import {StringDic} from "../common/interface";
import {dateStringToDate, notNullOrUndefined} from "../common/function";  // eslint-disable-line
import {CalendarType, is24HourCalendar, isSimilarCalendarType, isStandardCalendar} from "../common/calendar";
import {getServicesUrl} from "../api/common";
import {TaskStatus, taskStatusOptions} from "../common/constant";
import {calculateDuration, calculateOffset, getDutyPersonLabel, getParentPlanTasks, getTaskStatus, updateAllProjectActualTime} from "./taskUtils";
// import {staffsKey} from "./columnUtils";
import {getTaskWorkEndDate, getTaskWorkStartDate, reCalculateDurationTime, setGanttCalender} from "./calendarUtils";
import ganttManager from "./ganttManager";
import {CalendarInfo} from "../api/plan/type";
import {parseGanttData} from "./ganttConfig";
import {DutyPerson} from "../components/SelectPersonDrawer/data";
import {timeFormat} from "../../../../uikit/ts/moment";
import {getTemplateUrl, TemplateType} from "../../../assets/ts/templateManager";
import {TemplateSheetInfo, exportWithTemplate} from "../../../assets/ts/excelTemplateExporter";

let uuidCache: StringDic = {};

const uuid = (str: string) => {
    if (str === "") {
        return str;
    }
    if (uuidCache[str] === undefined) {
        uuidCache[str] = uuidv4().replace(/-/g, "");
    }
    return uuidCache[str];
};

const getExcelColumns = () => {
    const columns = [];
    columns.push({id: "wbs", header: "序号", width: 10});
    columns.push({id: "text", header: "任务名称", width: 40});
    columns.push({id: "type", header: "任务类型", width: 20});
    columns.push({id: "planDuration", header: "计划工期", width: 10});
    columns.push({id: "planStartDate", header: "计划开始", width: 20, type: "date"});
    columns.push({id: "planEndDate", header: "计划完成", width: 20, type: "date"});
    // columns.push({id: "requestDuration", header: "要求工期", width: 10});
    // columns.push({id: "requestStartDate", header: "要求开始", width: 20, type: "date"});
    // columns.push({id: "requestEndDate", header: "要求完成", width: 20, type: "date"});
    if (ganttManager.fromType === "actual") {
        columns.push({id: "taskStatus", header: "执行状态", width: 10});
        columns.push({id: "actualDuration", header: "实际工期", width: 10});
        columns.push({id: "actualStartDate", header: "实际开始", width: 20, type: "date"});
        columns.push({id: "actualEndDate", header: "实际完成", width: 20, type: "date"});
        columns.push({id: "lagDeviation", header: "滞后偏差", width: 20});
    } else {
        columns.push({id: "preTask", header: "前置任务", width: 30});
    }
    columns.push({id: "dutyUnit", header: "责任单位", width: 10});
    columns.push({id: "dutyPerson", header: "责任人", width: 10});
    return columns;
};

const rebuildTaskType = (taskType: string) => {
    if (taskType === gantt.config.types.milestone) {
        return "里程碑";
    }
    if (taskType === gantt.config.types.project || taskType === gantt.config.types.task) {
        return "任务";
    }
    return taskType;
};

// 转换成UTC时间
const convertUTCDate = (date?: Date): Date | undefined => {
    if (date === undefined) {
        return undefined;
    }
    const newDate = moment.utc(moment(date).format("YYYY-MM-DD HH:mm:ss"));
    return newDate.toDate();
};

const formatDate = (date: Date | null | undefined): string | Date => {
    if (isNil(date)) {
        return "";
    }

    if (date instanceof Date) {
        return date;
    }
    return "";
};

export const exportToExcel = (props: ExportToExcelProps) => {
    const {deptName, sectionName, planName, excelName} = props;
    const data: any[] = [];// eslint-disable-line
    const linkDic: {[k: string]: string[]} = {};
    gantt.getLinks().forEach((link: GanttLink) => {
        linkDic[link.target] = gantt.ext.formatters.linkFormatter().format(link);
    });
    gantt.eachTask((task: GanttTask) => {
        const linkStr: string[] = [];
        if (task.$target !== undefined) {
            task.$target.forEach((item) => {
                const link: GanttLink = gantt.getLink(item);
                linkStr.push(gantt.ext.formatters.linkFormatter().format(link));
            });
        }
        const offset = calculateOffset(task);
        const exportTask = {
            id: task.id,
            parent: task.parent === "0" ? undefined : task.parent,
            wbs: gantt.getWBSCode(task),
            text: task.text,
            type: rebuildTaskType(gantt.getTaskType(task)),
            planDuration: task.type !== gantt.config.types.milestone ? ganttManager.durationFormatter.format(task.duration) : "",
            planStartDate: convertUTCDate(task.start_date),
            planEndDate: task.type !== gantt.config.types.milestone ? convertUTCDate(task.end_date) : "",
            requestDuration: task.type !== gantt.config.types.milestone ? calculateDuration(task.request_duration) : "",
            requestStartDate: task.type !== gantt.config.types.milestone ? convertUTCDate(task.request_start_date) : "",
            requestEndDate: task.type !== gantt.config.types.milestone ? convertUTCDate(task.request_end_date) : "",
            taskProgress: String(task.progress !== undefined ? task.progress * 100 : 0),
            taskStatus: task.type !== gantt.config.types.milestone ? taskStatusOptions.find((option) => option.key === task.taskStatus)?.label ?? "" : "",
            actualDuration: task.type !== gantt.config.types.milestone ? calculateDuration(task.actual_duration) : "",
            actualStartDate: task.type !== gantt.config.types.milestone ? convertUTCDate(task.actual_start) : "",
            actualEndDate: task.type !== gantt.config.types.milestone ? convertUTCDate(task.actual_end) : "",
            lagDeviation: offset !== null ? `${offset}d` : "",
            dutyUnit: task.dutyUnit ?? "",
            dutyPerson: getDutyPersonLabel(task.dutyPerson),
            preTask: linkStr.join(","),
            actualPlan: isNil(task.actualPlan) ? "" : `${task.actualPlan}%`,
        };
        data.push(exportTask);
    });

    try {
    // 使用模板管理器解决iframe部署环境下的路径问题
        const templatePath = getTemplateUrl(ganttManager.fromType === "actual" ? TemplateType.ACTUAL_PROGRESS_TASK_DETAILS : TemplateType.SCHEDULE_TASK_DETAILS);
        const sheetInfo: TemplateSheetInfo = {
            data: data.map((taskItem) => ({
                ...taskItem,
                planStartDate: formatDate(taskItem.planStartDate),
                planEndDate: formatDate(taskItem.planEndDate),
                actualStartDate: formatDate(taskItem.actualStartDate),
                actualEndDate: formatDate(taskItem.actualEndDate),
            })),
            startRow: 6, // 从第2行开始填入数据（第1行是标题）
            columnMapping: ganttManager.fromType === "actual"
                ? {
                    wbs: 1, // A列
                    text: 2, // B列
                    type: 3, // C列
                    planDuration: 4, // D列
                    planStartDate: 5, // E列
                    planEndDate: 6, // F列
                    taskStatus: 7, // G列
                    remark: 8, // H 列，预留
                    actualDuration: 9, // I列
                    actualStartDate: 10, // J列
                    actualEndDate: 11, // K列
                    actualPlan: 12, // L列
                    lagDeviation: 13, // M列
                    dutyUnit: 14, // N列
                    dutyPerson: 15, // O列
                    delayDescription: 16, // P列，预留
                }
                : {
                    wbs: 1, // A列
                    text: 2, // B列
                    type: 3, // C列
                    planDuration: 4, // D列
                    planStartDate: 5, // E列
                    planEndDate: 6, // F列
                    preTask: 7, // G列
                    dutyUnit: 8, // H列
                    dutyPerson: 9, // I列
                    remark: 10, // J 列，预留
                },
            cellValues: {
                B2: deptName ?? "",
                B3: sectionName ?? "",
                B4: planName ?? "",
            }
        };
        exportWithTemplate(templatePath, sheetInfo, excelName ?? "", true);
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("导出模板失败", error);
    }

    // gantt.exportToExcel({
    //     name: `${excelName}_old.xlsx`,
    //     columns: getExcelColumns(),
    //     server: getServicesUrl("gantt-excel"),
    //     // visual:true,
    //     // cellColors:true,
    //     date_format: "yyyy.mm.dd",
    //     data,
    // });
};

export const exportToMSProject = (props: ExportToExcelProps) => {
    let worktime = {
        hours: [0, 24],
        dates: {0: true, 1: true, 2: true, 3: true, 4: true, 5: true, 6: true}
    };
    // 修改project配置，但是没生效
    // let MinutesPerDay = 24 * 60;
    // let MinutesPerWeek = MinutesPerDay * 7;
    // let DaysPerMonth = 30;
    // let DefaultStartTime = "00:00:00";
    // let WeekStartDay = 0;
    if (isStandardCalendar(ganttManager.currentCalendarInfo)) {
        worktime = {
            hours: [8, 12, 13, 17],
            dates: {0: false, 1: true, 2: true, 3: true, 4: true, 5: true, 6: false}
        };
        // MinutesPerDay = 8 * 60;
        // MinutesPerWeek = MinutesPerDay * 5;
        // DaysPerMonth = 20;
        // DefaultStartTime = "08:00:00";
        // WeekStartDay = 1;
    }

    gantt.exportToMSProject({
        name: props?.excelName ?? "project",
        // project: {
        //     MinutesPerDay,
        //     MinutesPerWeek,
        //     DaysPerMonth,
        //     DefaultStartTime,
        //     WeekStartDay
        // },
        tasks: {
            pWBS: (pTask: GanttTask) => {
                const task = gantt.getTask(pTask.id);
                return task.$wbs;
            },
            pRequestStartDate: (pTask: GanttTask) => (pTask.request_start_date !== undefined ? timeFormat(pTask.request_start_date) : ""),
            pRequestEndDate: (pTask: GanttTask) => (pTask.request_end_date !== undefined ? timeFormat(pTask.request_end_date) : ""),
            pRequestDuration: (pTask: GanttTask) => (pTask.request_duration !== undefined && pTask.request_duration >= 0
                ? ganttManager.durationFormatter.format(pTask.request_duration)
                : ""),
            pActualStartDate: (pTask: GanttTask) => (pTask.actual_start !== undefined ? timeFormat(pTask.actual_start) : ""),
            pActualEndDate: (pTask: GanttTask) => (pTask.actual_end !== undefined ? timeFormat(pTask.actual_end) : ""),
            pActualDuration: (pTask: GanttTask) => (pTask.actual_duration !== undefined && pTask.actual_duration >= 0
                ? ganttManager.durationFormatter.format(pTask.actual_duration)
                : ""),
            pDutyUnit: (pTask: GanttTask) => pTask.dutyUnit ?? "",
            pDutyPerson: (pTask: GanttTask) => getDutyPersonLabel(pTask.dutyPerson) ?? "",
        },
        config: {
            ...gantt.config,
            columns: [],
        },
        worktime,
        server: getServicesUrl("gantt-project")
    });
};

const importPlanGanttData = async (ganttDataset: GanttDataset, oldGanttDataset: GanttDataset) => new Promise<void>((resolve) => {
    const oldTasks: GanttTask[] = oldGanttDataset.data;
    const newTasks: GanttTask[] = [];
    const newLinks: GanttLink[] = [];
    const importTasks = ganttDataset.data;
    const importLinks = ganttDataset.links;
    let oldTask;
    let importTask;
    let isDiff = false;
    // console.log("importTasks", importTasks);
    // console.log("oldTasks", oldTasks);
    if (oldTasks.length === 0) {
        gantt.clearAll();
        parseGanttData(ganttDataset);
        resolve();
        return;
    }
    if (oldTasks.length !== importTasks.length) {
        isDiff = true;
    } else {
        for (let oldIdx = 0; oldIdx < oldTasks.length; oldIdx++) {
            oldTask = oldTasks[oldIdx];
            let isFind = false;
            for (let importIdx = 0; importIdx < importTasks.length; importIdx++) {
                importTask = importTasks[importIdx];
                if (importTask.$wbs === oldTask.$wbs && importTask.text === oldTask.text) {
                    const newTask = {...importTask};
                    newTasks.push(newTask);
                    importLinks.forEach((link) => {
                        if (link.target === newTask.id) {
                            newLinks.push(link);
                        }
                    });
                    isFind = true;
                    break;
                }
            }
            if (isDiff === false && isFind === false) {
                isDiff = true;
            }
        }
    }
    // console.log("newTasks", newTasks);
    // console.log("newLinks", newLinks);
    if (isDiff === true) {
        Modal.confirm({
            title: "导入将重置现有内容，是否继续？",
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
                gantt.clearAll();
                parseGanttData(ganttDataset);
            },
            onCancel: () => {
                gantt.clearAll();
                parseGanttData(oldGanttDataset);
            }
        });
    } else {
        gantt.clearAll();
        parseGanttData({
            data: newTasks,
            links: newLinks,
        });
        resolve();
    }
});

const importActualGanttData = async (ganttDataset: GanttDataset, oldGanttDataset: GanttDataset) => new Promise<void>((resolve, reject) => {
    const oldTasks: GanttTask[] = oldGanttDataset.data;
    const newTasks: GanttTask[] = [];
    const importTasks = ganttDataset.data;
    let oldTask;
    let importTask;
    if (oldTasks.length !== importTasks.length) {
        message.error("表格不匹配，导入失败");
        reject();
        return;
    }
    for (let oldIdx = 0; oldIdx < oldTasks.length; oldIdx++) {
        oldTask = oldTasks[oldIdx];
        let isFind = false;
        for (let importIdx = 0; importIdx < importTasks.length; importIdx++) {
            importTask = importTasks[importIdx];
            if (importTask.$wbs?.toString() === oldTask.$wbs?.toString() && importTask.text === oldTask.text) {
                newTasks.push({
                    ...oldTask,
                    taskStatus: importTask.taskStatus,
                    actual_duration: importTask.actual_duration,
                    actual_start: importTask.actual_start,
                    actual_end: importTask.actual_end,
                    actual_syn: importTask.actual_syn,
                });
                isFind = true;
                break;
            }
        }
        if (isFind === false) {
            message.error("表格不匹配，导入失败");
            reject();
            return;
        }
    }
    gantt.clearAll();
    // console.log("importActualGanttData newTasks", newTasks);
    parseGanttData({
        data: newTasks,
        links: oldGanttDataset.links,
    });
    resolve();
});

const importGanttData = async (ganttDataset: GanttDataset, oldGanttDataset: GanttDataset): Promise<void> => {
    if (ganttManager.fromType === "plan") {
        return importPlanGanttData(ganttDataset, oldGanttDataset);
    }
    if (ganttManager.fromType === "actual") {
        return importActualGanttData(ganttDataset, oldGanttDataset);
    }
    return new Promise<void>((resolve) => {
        gantt.clearAll();
        parseGanttData(ganttDataset);
        resolve();
    });
};

const getDuration = (dur: string | undefined | null | number) => {
    if (typeof dur === "number") {
        return {duration: dur, unit: "day"};
    }
    if (dur === "" || isNil(dur)) {
        return undefined;
    }
    // eslint-disable-next-line radix
    const duration = parseInt(dur);
    if (Number.isNaN(duration)) {
        return undefined;
    }

    if (dur.includes("year") || dur.includes("年")) {
        return {duration, unit: "year"};
    }
    if (dur.includes("mon") || dur.includes("月")) {
        return {duration, unit: "month"};
    }
    if (dur.includes("week") || dur.includes("周") || dur.includes("星期")) {
        return {duration, unit: "week"};
    }
    if (dur.endsWith("h") || dur.includes("hour") || dur.includes("时")) {
        return {duration, unit: "hour"};
    }
    if (dur.includes("min") || dur.includes("分")) {
        return {duration, unit: "minute"};
    }
    return {duration, unit: "day"};
};

const getDateWithStr = (dateStr: string | null | undefined) => {
    if (isNil(dateStr) || dateStr.length === 0) {
        return undefined;
    }
    return new Date(/^[0-9]*$/.test(dateStr) ? Number(dateStr) : dateStr);
};

const getStartDate = (date: Date | undefined, cal: CalendarInfo) => {
    if (date === undefined) {
        return undefined;
    }
    const newDate = gantt.getCalendar("custom").getClosestWorkTime({date, dir: "future"});
    if (is24HourCalendar(cal)) {
        return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
    }
    return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 8, 0, 0);
};

const getEndDate = (date: Date | undefined, cal: CalendarInfo) => {
    if (date === undefined) {
        return undefined;
    }
    if (is24HourCalendar(cal)) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
    }
    if (date.getHours() === 8) {
        let newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1, 17, 0, 0);
        newDate = gantt.getCalendar("custom").getClosestWorkTime({date: newDate, dir: "past"});
        return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 17, 0, 0);
    }
    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 17, 0, 0);
};

const getTaskDutyPerson = (dutyPersonText?: string | null | undefined) => {
    if (isNil(dutyPersonText) || dutyPersonText.length === 0) {
        return undefined;
    }
    const inputDutyPerson: string[] = [];
    const selectDutyPerson: string[] = [];
    dutyPersonText.split("，").forEach((realName) => {
        const person = ganttManager.allPerson.find((info) => info.realName === realName);
        if (person !== undefined) {
            selectDutyPerson.push(person.userName);
        } else {
            inputDutyPerson.push(realName);
        }
    });
    const dutyPerson: DutyPerson = {
        input: inputDutyPerson.join("，"),
        select: selectDutyPerson,
    };
    return dutyPerson;
};

const loadTable = (mapping: StringDic, data: ImportExcel[], cal: CalendarInfo) => {
    const parseData = () => {
        const temp: StringDic[] = [];
        data.forEach((item) => {
            const copy: StringDic = {};
            for (const i in item) { // eslint-disable-line
                if (mapping[i] !== "_") {
                    copy[mapping[i]] = item[i];
                }

                const importWBS = copy.序号 ?? copy.WBS;
                if (importWBS !== undefined) {
                    const WBS = `${importWBS}`;
                    copy.id = WBS;
                    const parts = WBS.split(".");
                    parts.pop();
                    copy.parent = parts.join(".");
                }
            }
            temp.push(copy);
        });
        return temp;
    };

    const isValible = (columnData: StringDic) => {
        if (columnData.任务名称 === "" || columnData.任务名称 === undefined) {
            return false;
        }
        if (columnData.任务类型 === "" || columnData.任务类型 === undefined) {
            return false;
        }
        if (columnData.计划开始 === "" || columnData.计划开始 === undefined) {
            return false;
        }
        if (columnData.任务类型 !== "里程碑") {
            // eslint-disable-next-line radix
            if (columnData.计划工期 === "" || columnData.计划工期 === undefined || Number.isNaN(parseInt(columnData.计划工期))) {
                return false;
            }
        }
        return true;
    };

    const getTaskType = (type: string | undefined, dur: string | undefined) => {
        if (type !== undefined) {
            if (type === "里程碑") {
                return gantt.config.types.milestone;
            }
            if (type === "任务") {
                return gantt.config.types.task;
            }
            return type;
        }
        const duration = getDuration(dur);
        if (duration !== undefined && duration.duration === 0) {
            return gantt.config.types.milestone;
        }
        return gantt.config.types.task;
    };

    const tasks: GanttTask[] = [];
    const links: GanttLink[] = [];

    const todayStart = getTaskWorkStartDate(new Date());
    const todayEnd = getTaskWorkEndDate(new Date());
    // data
    const temp = parseData();
    let number = 0;
    let actualErrorCount = 0;
    // console.log(new Date);
    // console.log(gantt.getCalendar("custom"));
    temp.forEach((copy) => {
        if (isValible(copy)) {
            const type = getTaskType(copy.任务类型, copy.实际工期);
            // 计划时间为必填
            const planStartDate = getStartDate(dateStringToDate(copy.计划开始), cal) as Date;
            const planDuration = type !== gantt.config.types.milestone ? getDuration(copy.计划工期) : getDuration(0);
            const planEndDate = gantt.getCalendar("custom").calculateEndDate({start_date: planStartDate, ...planDuration});
            // 要求时间为选填
            const requestTime = type !== gantt.config.types.milestone
                ? reCalculateDurationTime({
                    duration: getDuration(copy.要求工期)?.duration,
                    startDate: getStartDate(dateStringToDate(copy.要求开始), cal),
                    endDate: getEndDate(dateStringToDate(copy.要求完成), cal),
                })
                : {};
            const requestDuration = requestTime.duration;
            const requestStartDate = requestTime.startDate;
            const requestEndDate = requestTime.endDate;
            // 实际时间为选填
            const actualStartDate = getStartDate(dateStringToDate(copy.实际开始), cal);
            const actualDuration = getDuration(copy.实际工期)!;
            const actualEndDate = actualStartDate !== undefined && actualDuration !== undefined && actualDuration.duration > 0
                ? getEndDate(gantt.getCalendar("custom").calculateEndDate({start_date: actualStartDate, ...actualDuration}), cal)
                : undefined;
            if ((actualStartDate !== undefined && actualStartDate > todayStart) || (actualEndDate !== undefined && actualEndDate > todayEnd)) {
                actualErrorCount++;
            }
            const dutyUnit = copy.责任单位;
            const dutyPerson = getTaskDutyPerson(copy.责任人);
            const taskStatus = type !== gantt.config.types.milestone
                ? getTaskStatus(actualStartDate, actualEndDate)
                : TaskStatus.NOT_START;

            const importWBS = copy.序号 ?? copy.WBS;
            const task: GanttTask = {
                $wbs: importWBS,
                id: uuid(importWBS),
                parent: uuid(copy.parent) === "" ? "0" : uuid(copy.parent),
                text: copy.任务名称,
                type,
                duration: gantt.getCalendar("custom").calculateDuration({start_date: planStartDate, end_date: planEndDate}),
                start_date: planStartDate,
                end_date: planEndDate,
                request_start_date: requestStartDate,
                request_duration: requestDuration,
                request_end_date: requestEndDate,
                actual_start: actualStartDate,
                actual_duration: actualStartDate !== undefined && actualEndDate !== undefined
                    ? gantt.getCalendar("custom").calculateDuration({start_date: actualStartDate, end_date: actualEndDate})
                    : undefined,
                actual_end: actualEndDate,
                taskStatus,
                dutyUnit,
                dutyPerson,
                calendar_id: "custom",
                actual_syn: 0b111,
                bindType: 0
            };
            tasks.push(task);
        } else {
            number++;
        }
    });
    if (number !== 0) {
        message.info({content: `${number}条任务没有任务名称或任务类型或计划开始时间或者计划工期，未导入`});
    }
    if (actualErrorCount !== 0) {
        message.error("存在实际时间晚于今天，请检查调整！");
        return;
    }

    // 先保存一份旧数据
    const oldTasks: GanttTask[] = [];
    gantt.eachTask((task) => oldTasks.push(cloneDeep(task)));
    const oldLinks = cloneDeep(gantt.getLinks());
    const oldGanttDataset = {
        data: oldTasks,
        links: oldLinks,
    };

    // 需要先解析数据，方便后面计算link数据
    gantt.clearAll();
    gantt.parse({data: tasks});

    // links
    temp.forEach((copy) => {
        if (isValible(copy) && copy.前置任务 !== "" && copy.前置任务 !== undefined) {
            const preStr = String(copy.前置任务);
            const pre: string[] = preStr.split(",");

            pre.forEach((preItem) => {
                if (gantt.ext.formatters.linkFormatter().canParse(preItem) as boolean) {
                    let newPreItem = String(preItem);
                    if (newPreItem.includes("+") || newPreItem.includes("-")) {
                        const re = /^[1-9]+[0-9]*]*$/;
                        if (re.test(newPreItem.charAt(newPreItem.length - 1))) {
                            newPreItem += "d";
                        }
                    }
                    const tmpLink: GanttLink = gantt.ext.formatters.linkFormatter().parse(newPreItem);
                    if (tmpLink.source !== null) {
                        const link: GanttLink = {
                            id: uuidv4().replace(/-/g, ""),
                            source: tmpLink.source,
                            target: uuid(copy.序号 ?? copy.WBS),
                            type: tmpLink.type,
                            lag: tmpLink.lag,
                        };
                        links.push(link);
                    } else {
                        message.info({content: `${copy.任务名称}的前置任务"${preItem}"寻找不到`});
                    }
                } else {
                    message.info({content: `${copy.任务名称}的前置任务"${preItem}"格式不对`});
                }
            });
        }
    });
    gantt.clearAll();
    gantt.parse(oldGanttDataset);
    const ganttDataset = {
        data: tasks,
        links
    };
    importGanttData(ganttDataset, oldGanttDataset)
        .then(() => updateAllProjectActualTime())
        .catch(() => {
            parseGanttData({
                data: oldTasks,
                links: oldLinks,
            });
        });
};

export const importFromExcel = (
    file: File,
    cal: CalendarInfo,
    callback?: () => void,
    setLoading?: (loading: boolean) => void
) => {
    uuidCache = {};
    gantt.importFromExcel({
        server: getServicesUrl("gantt-excel"),
        data: file,
        callback(project: ImportExcel[]) {
            // console.log("project", project);
            if (project !== undefined && project !== null) {
                const header: string[] = [];
                const body: string[] = [];
                const headerCols: string[] = [];

                // 过滤掉字段全为空的无效任务
                const projectData = project.filter((task) => {
                    // console.log("task", task);
                    if (task === undefined || task === null) {
                        return false;
                    }
                    // eslint-disable-next-line no-restricted-syntax
                    for (const key of Object.keys(task)) {
                        if (task[key] !== undefined && task[key].length > 0) {
                            return true;
                        }
                    }
                    return false;
                });

                // console.log("projectData", projectData);
                // build header
                for (let taskIdx = 0; taskIdx < projectData.length; taskIdx++) {
                    const task = projectData[taskIdx];
                    if (task["计划开始"] === undefined) {
                        header.push("计划开始");
                    }
                    for (const i in task) { // eslint-disable-line
                        if (!header.includes(i)) {
                            header.push(i);
                        }
                    }
                }

                const options = ["序号", "任务名称", "任务类型", "计划工期", "计划开始"];
                for (const i in header) { // eslint-disable-line
                    const index = options.findIndex((item) => item === header[i]);
                    if (index !== -1) {
                        options.splice(index, 1);
                    }
                }
                if (options.length !== 0) {
                    message.info({content: `缺少${options.join("、")}必导入字段`});
                    if (callback !== undefined) {
                        callback();
                    }
                    return;
                }

                header.forEach((col) => {
                    headerCols.push(`<th data-column-name='${col}'>${col}</th>`);
                });
                body.push(`<tr>${headerCols.join("")}</tr>`);

                projectData.forEach((task) => {
                    const cols: string[] = [];
                    header.forEach((col) => {
                        const value = task[col] !== undefined ? task[col] : "";
                        cols.push(`<td>${value}</td>`);
                    });
                    body.push(`<tr>${cols.join("")}</tr>`);
                });

                const div = gantt.modalbox({
                    id: "ganttDiv",
                    title: file.name,
                    type: "excel-form",
                    text: `<table>${body.join("")}</table>`,
                    buttons: [
                        {label: "确定", css: "ant_primary", value: "确定"}, // 暂时手动修改样式，最好使用antd重写该组件
                        {label: "取消", css: "ant", value: "取消"}
                    ],
                    callback(result: string) {
                        switch (result) {
                            case "确定":
                                new Promise((resolve) => {
                                    if (setLoading instanceof Function) {
                                        setLoading(true);
                                    }
                                    setTimeout(() => {
                                        const columnNameEl = div.querySelectorAll("[data-column-name]");
                                        const mapping: StringDic = {};
                                        columnNameEl.forEach((nameEl) => {
                                            const key = nameEl.getAttribute("data-column-name");
                                            if (key !== null) {
                                                mapping[key] = key;
                                            }
                                        });
                                        resolve(mapping);
                                    });
                                }).then((mapping) => {
                                    loadTable(mapping as StringDic, projectData, cal);
                                }).catch((error) => {
                                    // eslint-disable-next-line no-console
                                    console.log("error", error);
                                }).finally(() => {
                                    if (setLoading instanceof Function) {
                                        setLoading(false);
                                    }
                                });
                                break;
                            case "取消":
                                // Cancel
                                break;
                            default:
                                break;
                        }
                    }
                });
            }
            if (callback !== undefined) {
                callback();
            }
        }
    });
};


export const importFromProject = (
    file: File,
    cal: CalendarInfo,
    callback?: (calendar: CalendarInfo) => void,
    setLoading?: (loading: boolean) => void
) => {
    if (setLoading instanceof Function) {
        setLoading(true);
    }
    uuidCache = {};
    gantt.importFromMSProject({
        server: getServicesUrl("gantt-project"),
        taskProperties: [
            "pWBS",
            "pRequestDuration",
            "pRequestStartDate",
            "pRequestEndDate",
            "pActualDuration",
            "pActualStartDate",
            "pActualEndDate",
            "pDutyUnit",
            "pDutyPerson",
            "pOwner",
        ],
        data: file,
        callback(project: any) {// eslint-disable-line
            if (project !== undefined && project !== null) {
                // console.log("project", project);

                const newCalendar = {
                    ctid: "1",
                    calendarType: CalendarType.TYPE_STANDARD,
                    restDayName: "标准日历"
                };
                if (project.worktime.hours[0] === 0 && project.worktime.hours[1] === 0) {
                    newCalendar.ctid = "0";
                    newCalendar.calendarType = CalendarType.TYPE_24_7;
                    newCalendar.restDayName = "24小时日历";
                }
                // 如果导入和当前日历类型不同, 则先设置工作日历, 再计算任务和前置任务时间
                if (isSimilarCalendarType(ganttManager.currentCalendarInfo, newCalendar as CalendarInfo) === false) {
                    setGanttCalender(newCalendar as CalendarInfo);
                }

                const currentCalendar = ganttManager.currentCalendarInfo;
                // console.log("currentCalendar", currentCalendar);
                // project工期单位，用于转换为当前日历工期
                const durationUnit = project.config.duration_unit ?? gantt.config.duration_unit;

                const todayStart = getTaskWorkStartDate(new Date());
                const todayEnd = getTaskWorkEndDate(new Date());
                let actualErrorCount = 0;

                const ganttDataset = project.data;
                ganttDataset.data = ganttDataset.data.map((task: any) => { // eslint-disable-line
                    // console.log("importFromProject project data task", task);
                    const customData = task.$custom_data as {
                        pWBS: string | null | undefined;
                        pRequestDuration: string | null | undefined;
                        pRequestStartDate: string | null | undefined;
                        pRequestEndDate: string | null | undefined;
                        pActualDuration: string | null | undefined;
                        pActualStartDate: string | null | undefined;
                        pDutyUnit: string | null | undefined;
                        pDutyPerson: string | null | undefined;
                        pOwner: string | null | undefined;
                    };
                    const {
                        pWBS,
                        pRequestDuration,
                        pRequestStartDate,
                        pRequestEndDate,
                        pActualDuration,
                        pActualStartDate,
                        pDutyUnit,
                        pDutyPerson = customData.pOwner,
                    } = customData;

                    const type = parseFloat(task.duration) === 0 ? gantt.config.types.milestone : gantt.config.types.task;
                    // 计划时间
                    const planStartDate = getStartDate(dateStringToDate(task.start_date), currentCalendar) ?? new Date("1900-01-01");
                    const planDuration = ganttManager.durationFormatter.parse(`${task.duration} ${durationUnit}`);
                    const planEndDate = getEndDate(dateStringToDate(task.end_date), currentCalendar) as Date;
                    // 要求时间为选填
                    const requestTime = type !== gantt.config.types.milestone
                        ? reCalculateDurationTime({
                            duration: getDuration(pRequestDuration)?.duration,
                            startDate: pRequestStartDate !== null && pRequestStartDate !== undefined && pRequestStartDate.length > 0
                                ? getStartDate(getDateWithStr(pRequestStartDate), currentCalendar)
                                : undefined,
                            endDate: pRequestEndDate !== null && pRequestEndDate !== undefined && pRequestEndDate.length > 0
                                ? getEndDate(getDateWithStr(pRequestEndDate), currentCalendar)
                                : undefined,
                        })
                        : {};
                    const requestDuration = requestTime.duration;
                    const requestStartDate = requestTime.startDate;
                    const requestEndDate = requestTime.endDate;
                    // 实际时间为选填
                    const actualStartDate = pActualStartDate !== null && pActualStartDate !== undefined && pActualStartDate.length > 0
                        ? getStartDate(getDateWithStr(pActualStartDate), currentCalendar)
                        : undefined;
                    const actualDuration = pActualDuration !== null && pActualDuration !== undefined && pActualDuration.length > 0
                        ? getDuration(pActualDuration)
                        : undefined;
                    const actualEndDate = actualStartDate !== null && actualDuration !== undefined && actualDuration.duration > 0
                        ? getEndDate(gantt.getCalendar("custom").calculateEndDate({start_date: actualStartDate, ...actualDuration}), cal)
                        : undefined;
                    if ((actualStartDate !== undefined && actualStartDate > todayStart) || (actualEndDate !== undefined && actualEndDate > todayEnd)) {
                        actualErrorCount++;
                    }
                    const taskStatus = getTaskStatus(actualStartDate, actualEndDate);
                    const dutyUnit = pDutyUnit !== null && pDutyUnit !== undefined && pDutyUnit.length > 0
                        ? pDutyUnit
                        : undefined;
                    const dutyPerson = getTaskDutyPerson(pDutyPerson);
                    const taskRes: GanttTask = {
                        $wbs: pWBS ?? undefined,
                        id: uuid(task.id),
                        text: task.text,
                        type,
                        parent: task.parent === project.config.root_id ? project.config.root_id : uuid(task.parent),
                        start_date: planStartDate,
                        duration: planDuration,
                        end_date: planEndDate,
                        request_start_date: requestStartDate,
                        request_duration: requestDuration,
                        request_end_date: requestEndDate,
                        actual_start: actualStartDate,
                        actual_duration: actualStartDate !== undefined && actualEndDate !== undefined
                            ? gantt.getCalendar("custom").calculateDuration({start_date: actualStartDate, end_date: actualEndDate})
                            : undefined,
                        actual_end: actualEndDate,
                        taskStatus,
                        dutyUnit,
                        dutyPerson,
                        calendar_id: "custom",
                        actual_syn: 0b111,
                        bindType: 0
                    };
                    // console.log("taskRes", taskRes);
                    return taskRes;
                });
                if (actualErrorCount !== 0) {
                    message.error("存在实际时间晚于今天，请检查调整！");
                    if (setLoading instanceof Function) {
                        setLoading(false);
                    }
                    return;
                }
                ganttDataset.links = ganttDataset.links.map((link: any) => { // eslint-disable-line
                    let lag: string | null = null;
                    if (ganttManager.durationFormatter.canParse(`${link.lag} ${durationUnit}`)) {
                        const lagNum = ganttManager.durationFormatter.parse(`${link.lag} ${durationUnit}`);
                        if (notNullOrUndefined(lagNum)) {
                            lag = String(lagNum);
                        }
                    }
                    return {
                        ...link,
                        id: uuid(link.id),
                        source: uuid(link.source),
                        target: uuid(link.target),
                        lag,
                    };
                });

                const oldTasks: GanttTask[] = [];
                gantt.eachTask((task) => oldTasks.push(cloneDeep(task)));
                const oldLinks = cloneDeep(gantt.getLinks());
                const oldGanttDataset = {
                    data: oldTasks,
                    links: oldLinks,
                };
                importGanttData(ganttDataset, oldGanttDataset)
                    .then(() => {
                        if (callback !== undefined) {
                            callback(currentCalendar);
                        }
                    }).catch(() => {
                        parseGanttData({
                            data: oldTasks,
                            links: oldLinks,
                        });
                    });
            }
            if (setLoading instanceof Function) {
                setLoading(false);
            }
        }
    });
};

export const importFromWBS = (wbsNodes: any[], setLoading: (loading: boolean) => void) => {
    uuidCache = {};
    if (setLoading instanceof Function) {
        setLoading(true);
    }
    const currentCalendar = ganttManager.currentCalendarInfo;
    const taskList: GanttTask[] = [];
    const startDate = getStartDate(new Date(ganttManager.planStartDate), currentCalendar) as Date;
    const durationData = getDuration("1d");
    const endDate = getEndDate(gantt.getCalendar("custom").calculateEndDate({start_date: startDate, ...durationData}), currentCalendar) as Date;
    const duration = gantt.getCalendar("custom").calculateDuration({start_date: startDate, end_date: getEndDate(endDate, currentCalendar)});
    const oldTasks: GanttTask[] = [];
    const leafTasks: GanttTask[] = [];
    gantt.eachTask((task: GanttTask) => {
        oldTasks.push(task);
        if (task.type === gantt.config.types.task) {
            leafTasks.push(task);
        }
        // const wbsId = task.wbsNodeIds?.[0];
        // if (wbsId !== undefined) {
        //     // 将旧的任务id按存入缓存，后面根据wbsId优先取已存在任务id
        //     uuidCache[wbsId] = `${task.id}`;
        // }
    });
    // console.log("wbsNodes", wbsNodes);
    wbsNodes.forEach((wbs) => {
        const wbsNodeIds = [wbs.key];
        const oldTask = oldTasks.find((task) => task.wbsNodeIds?.join("") === wbsNodeIds?.join(""));
        const taskRes: GanttTask = {
            ...oldTask,
            id: uuid(wbs.key),
            parent: wbs.parentId?.length > 0 && wbs.parentId !== wbs.id ? uuid(wbs.parentId) : "0",
            wbsNodeIds: wbs.businessType !== undefined && wbs.businessType !== 0 ? [wbs.key] : [],
            text: wbs.title.replace(/\n/g, " "), // 导入wbs时将所有的换行符转换为空格，因为导出project之后通过project打开如果有换行符会报错
            type: gantt.config.types.task,
            start_date: oldTask?.start_date ?? startDate,
            duration: oldTask?.duration ?? duration,
            end_date: oldTask?.end_date ?? endDate,
            request_start_date: oldTask?.request_start_date,
            request_duration: oldTask?.request_duration,
            request_end_date: oldTask?.request_end_date,
            calendar_id: "custom",
            actual_syn: oldTask?.actual_syn ?? 0,
            bindType: -1
        };
        taskList.push(taskRes);
    });
    const updateChildrenTaskTime = (parentTask: GanttTask) => {
        const children = taskList.filter((task: GanttTask) => task.parent === parentTask.id);
        children.forEach((childTask) => {
            childTask.duration = parentTask.duration;
            childTask.start_date = parentTask.start_date;
            childTask.end_date = parentTask.end_date;
        });
        if (
            parentTask.request_duration !== undefined
            && parentTask.request_start_date !== undefined
            && parentTask.request_end_date !== undefined
        ) {
            children.forEach((childTask) => {
                childTask.request_duration = parentTask.request_duration;
                childTask.request_start_date = parentTask.request_start_date;
                childTask.request_end_date = parentTask.request_end_date;
            });
        }
        children.forEach((task) => updateChildrenTaskTime(task));
    };
    // 从原叶子结点，向下传递要求时间
    leafTasks.forEach((task) => updateChildrenTaskTime(task));
    // console.log("taskList", taskList);
    gantt.clearAll();
    parseGanttData({
        data: taskList,
        links: []
    });
    if (setLoading instanceof Function) {
        setLoading(false);
    }
};

export const importFromWBSByParentPlan = (wbsNodes: any[], setLoading: (loading: boolean) => void) => {
    uuidCache = {};
    if (setLoading instanceof Function) {
        setLoading(true);
    }
    // console.log("wbsNodes", wbsNodes);
    const {parentPlanTasks, leafTasks} = getParentPlanTasks();
    // console.log("parentPlanTasks", parentPlanTasks);
    // console.log("leafTasks", leafTasks);
    const taskList: GanttTask[] = [...parentPlanTasks];

    gantt.eachTask((task: GanttTask) => {
        const wbsId = task.wbsNodeIds?.[0];
        if (wbsId !== undefined) {
            // 将旧的任务id按存入缓存，后面根据wbsId优先取已存在任务id
            uuidCache[wbsId] = `${task.id}`;
        }
    });
    // 根据已存在任务添加子任务
    const addTaskNode = (parentTask: GanttTask) => {
        // console.log("parentTask", parentTask);
        // 新添加任务也当作已存在处理
        const newChildren: GanttTask[] = [];
        // 在导入wbs节点中，筛选当前任务下的节点
        const parentWbsNodeId = parentTask.wbsNodeIds?.[0];
        // console.log("parentWbsNodeId", parentWbsNodeId);
        if (parentWbsNodeId === undefined) {
            return;
        }
        const wbsList = wbsNodes.filter((el) => el.parentId === parentWbsNodeId);
        // console.log("wbsList", wbsList);
        if (wbsList !== undefined) {
            wbsList.forEach((wbs) => {
                // console.log("parentTask", parentTask);
                // console.log("wbs", wbs);
                if (taskList.some((el) => el.wbsNodeIds?.[0] === wbs.key) === false) {
                    const taskRes: GanttTask = {
                        id: uuid(wbs.key),
                        parent: parentTask.id,
                        wbsNodeIds: [wbs.key],
                        text: wbs.title.replace(/\n/g, " "), // 导入wbs时将所有的换行符转换为空格，因为导出project之后通过project打开如果有换行符会报错
                        type: gantt.config.types.task,
                        start_date: parentTask.start_date,
                        duration: parentTask.duration,
                        end_date: parentTask.end_date,
                        request_start_date: parentTask.request_start_date,
                        request_duration: parentTask.request_duration,
                        request_end_date: parentTask.request_end_date,
                        calendar_id: "custom",
                        actual_syn: 0,
                        bindType: -1
                    };
                    taskList.push(taskRes);
                    newChildren.push(taskRes);
                } else {
                    // console.log("已存在任务则跳过");
                }
            });
        }
        const children = gantt.getTaskBy((el) => el.parent === parentTask.id, null) ?? [];
        // 老子节点和新子节点下，都执行新增节点
        [...children, ...newChildren].forEach((task) => addTaskNode(task));
    };
    leafTasks.forEach((task) => addTaskNode(task));

    // console.log("taskList", taskList);
    const oldLinks = gantt.getLinks();
    const linkList: GanttLink[] = [];
    oldLinks.forEach((link: GanttLink) => {
        if (gantt.isTaskExists(link.source) && gantt.isTaskExists(link.target)) {
            linkList.push(link);
        }
    });
    gantt.clearAll();
    parseGanttData({
        data: taskList,
        links: linkList,
    });
    if (setLoading instanceof Function) {
        setLoading(false);
    }
};
