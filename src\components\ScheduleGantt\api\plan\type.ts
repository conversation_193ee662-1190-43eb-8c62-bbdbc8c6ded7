import {CustomValueType} from "../../common/constant";

export interface PreTaskRelationItem {
    dateRelation: number;
    dateSpan: number;
    id: string;
    predTaskId: string;
    taskId: string;
}

export interface TaskItem {
    // 计划id
    planId: string;
    // 任务id
    id: string;
    // 父任务id
    parentTaskId: string;
    // 任务明细wbs
    wbsNo: string;
    // 任务名称
    name: string;
    // 任务计划开始时间
    planStartDate: number;
    // 任务计划完成时间
    planEndDate: number;
    // 任务计划工期
    planDuration: number;
    // 任务要求开始时间
    requestStartDate?: number;
    // 任务要求完成时间
    requestEndDate?: number;
    // 任务要求工期
    requestDuration?: number;
    // 任务实际开始时间
    actualStartDate?: number;
    // 任务实际完成时间
    actualEndDate?: number;
    // 任务实际工期
    actualDuration?: number;
    // 实际时间是否为同步(位运算)：1-实际开始时间为同步, 2-实际结束时间为同步
    actualSyn: number;
    // 实际执行状态
    taskStatus?: number;
    // 节点类型
    nodeType: number;
    // 次序
    sort: number;
    // 是否是里程碑
    milestone: 0 | 1;
    // 标注信息
    taskMark: string;
    // 责任单位
    dutyUnit: string;
    // 负责人
    dutyPerson: string;
    // 备注
    remarks: string;
    // 是否关联照片
    hasFile: boolean;
    // 模型绑定类型：-1-绑定wbs、0-未绑定、 1-工程、 2-构件、 3-类别
    bindType: number;
    // 变更状态：未变更-Unchanged,已变更-Changed
    changeStatus: "Unchanged" | "Changed";
    // 自定义列数据
    customValueMap?: {[key: string]: string | number};
    wbsNodeIds?: string[];
    actualPlan?: string;
}

/**
 * 更新任务列表参数
 */
export interface UpdateTaskParams {
    planId: string;
    opt: "PLAN" | "ACTUAL"; // 更新任务数据选项，PLAN-更新计划部分 ACTUAL--更新实际部分
    addTaskList: TaskItem[];
    deleteTaskIdList: string[];
    updateTaskList: TaskItem[];
    addPredTaskRelationList: PreTaskRelationItem[];
    deletePredTaskRelationIdList: string[];
    updatePredTaskRelationList: PreTaskRelationItem[];
}

/**
 * 查询任务列表返回
 */
export interface PlanTaskListRes {
    predTaskRelationList: PreTaskRelationItem[];
    taskList: TaskItem[];
}

export interface CalendarInfo {
    ctid: string; // 模板id
    ctName: string; // 模板名称
    // calendarFlag: number; // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）
    copyid: string | null; // 复制的id
    // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）、2：自定义日历（复制24小时）、3：自定义日历（复制标准）
    calendarType: number;
    // 自定义日历名称
    restDayName: string;
    // 日历有效时间（开始）
    startDate: number | null;
    // 日历有时间（结束）
    endDate: number | null;
    // 非工作日集合（这里只记录休息日期，不记录休息日期设置规则）
    restDays: number[] | null;
    // 工作日列表，仅类型为3时传值
    workDays: number[] | null;
    // 是否为EDS模板
    isTemplate: boolean;
}

export interface ColumnItem {
    columnId: string;
    name?: string;
    hidden?: boolean;
    valueType?: CustomValueType;
    editable?: boolean;
}

export interface PostCustomColumnItem {
    planId: string;
    columnId: string;
    name: string;
    valueType: CustomValueType;
    hidden: boolean;
}

export interface WbsNodeItem {
    level: number;
    wbsNodeId: string;
    wbsNodeName: string;
}

export interface EbsNodeItem {
    handle: string;
    paths: string[];
    ppid: number;
    mergePath?: string;
    projName?: string;
}

export interface BindNodeItem {
    businessId: string;
    ebsNodes?: EbsNodeItem[];
    wbsNodeIds?: string[];
}

export interface PostTaskBindNodeParams {
    deptId: string;
    nodeId: string;
    planId: string;
    bindType: number;
    businessType: "PLAN_TASK";
    list: BindNodeItem[];
}

export interface PostTaskListBindNodeItem {
    deptId: string;
    nodeId: string;
    bindType: number;
    businessId: string;
    businessType: "PLAN_TASK";
    wbsNodes: WbsNodeItem[];
    ebsNodes: EbsNodeItem[];
}

export type PostTaskListBindNodeRes = PostTaskListBindNodeItem[];

export interface GetTaskChangedProjectType {
    changedTaskIdList: string[];
    ppid: number;
    projName: string;
}

export type GetTaskChangedProjectRes = GetTaskChangedProjectType[];

export interface BindFileItem {
    fileUuid?: string;
    fileName?: string;
    fileSize?: number;
    fileExtraInfo?: string;
}

export interface PostTaskBindFilesParams {
    businessId?: string;
    businessType?: "PLAN_TASK";
    latestFiles?: BindFileItem[];
}

export interface TaskListBindFileItem {
    businessId: string;
    businessType: "PLAN_TASK";
    fileUuid: string;
    fileName: string;
    fileSize: number;
    fileExtraInfo: string;
}

export type PostTaskListBindFilesRes = TaskListBindFileItem[];

export interface PutSyncProcessInspectionTimeParams {
    planId: string;
    syncType: 1 | 2; // 同步类型：1-全覆盖同步，2-仅同步未修改过的
}
