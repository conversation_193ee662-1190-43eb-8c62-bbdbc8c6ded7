declare interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    __REDUX_DEVTOOLS_EXTENSION_COMPOSE__?: any;
    __IWorksConfig__: {
        baseUrl: string;
        devBaseUrl: string;
        basePds: string;
        productId: string;
        motorEditor: string;
        shellDownUrl: string;
        motorViewUrl: string;
    };
    titleConfig: {
        planWeb: string;
    };
    systemNameConfig: {
        planWeb: string;
    };
    PlatformHeaderConfig: unknown;
    embedPlatform: boolean; // 是否部署平台，false为独立部署
    _PLATFORM_: string; // 数字平台访问地址， 当embedPlatform为false则不读取此数据
    currentDomain: string; // 当前项目部署的域名地址，因为有时候部署在路径下，就会导致找不到文件资源
    motorFrameUrl: string; // motorFrame地址
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    childApi: any;
}
