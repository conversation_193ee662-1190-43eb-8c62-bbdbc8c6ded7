import {Col, Row} from "antd";
import React from "react";
import {ActualWarnItem} from "../../api/actualProgress/type";
import {isDefined, timeFormat} from "../../assets/ts/utils";
import {ComFormItemProps} from "../../components/FormItem";
import renderTableText from "../../components/renderTableText";
import {ComColumnsProps} from "../../components/TableColumnsControl/ColumnsControl";
import TextEllipsis from "../../components/TextEllipsis";

// interface WarnRecord {
//     /** 报警名称 */
//     name: string;
//     /** 流程分类（1：蓝色预警；2：黄色预警；3：橙色预警；4：红色预警） */
//     type: number;
//     /** 报警级别，与type相反，数值越小，级别越高 */
//     level: number;
// }

export const queryItemList: ComFormItemProps[] = [
    {
        key: "checkPlanId",
        type: "cascader",
        typeConfig: {options: [], placeholder: "全部", dropdownClassName: "customAntCascader"},
        itemConfig: {name: "checkPlanId", label: "计划选择"},
        colConfig: {span: 6},
    },
    {
        key: "warnLevel",
        type: "select",
        typeConfig: {options: [], placeholder: "全部"},
        itemConfig: {name: "warnLevel", label: "报警级别"},
        colConfig: {span: 6},
    }
];

export interface QueryFormType {
    checkPlanId?: [string, string];
    warnLevel?: number;
}

export type ComColumnsType = ComColumnsProps<ActualWarnItem>;
const executionStatusMap: Map<number, string> = new Map([[1, "未开始"], [2, "进行中"], [3, "完成"]]);
export const trnsformWarnTypeAndLevel = (val?: number) => {
    let res: number | undefined;
    switch (val) {
        case 1:
            res = 4;
            break;
        case 2:
            res = 3;
            break;
        case 3:
            res = 2;
            break;
        case 4:
            res = 1;
            break;
        default:
            break;
    }
    return res;
};

/* const warnList: WarnRecord[] = [
    {name: "Ⅳ级报警", type: 1, level: 4},
    {name: "Ⅲ级报警", type: 2, level: 3},
    {name: "Ⅱ级报警", type: 3, level: 2},
    {name: "Ⅰ级报警", type: 4, level: 1}
]; */

export const columnsInit: ComColumnsType[] = [
    {
        title: "序号",
        align: "center",
        mustShow: true,
        show: true,
        width: 100,
        fixed: "left",
        render: (_t, r) => <TextEllipsis text={r.wbsNo} />
    },
    {
        key: "name",
        title: "任务名称",
        dataIndex: "name",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        fixed: "left",
        render: (name) => (
            <Row align="middle" style={{flexWrap: "nowrap"}}>
                <Col flex="1" style={{width: "0", padding: "3px 0"}}>
                    {renderTableText(name)}
                </Col>
            </Row>
        )
    },
    {
        key: "actualStatus",
        title: "执行状态",
        dataIndex: "actualStatus",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        fixed: "left",
        render: (t: number) => executionStatusMap.get(t) ?? "--"
    },
    {
        key: "planDuration",
        title: "计划工期",
        dataIndex: "planDuration",
        align: "left",
        mustShow: false,
        show: true,
        width: 100,
        render: (t) => `${t}d`
    },
    {
        key: "planStartDate",
        title: "计划开始日期",
        dataIndex: "planStartDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (t) => timeFormat(t)
    },
    {
        key: "planEndDate",
        title: "计划完成日期",
        dataIndex: "planEndDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (t) => timeFormat(t)
    },
    {
        key: "doDays",
        title: "已进行工期",
        dataIndex: "doDays",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (t) => (isDefined(t) ? `${t}d` : "--")
    },
    {
        key: "actualStartDate",
        title: "实际开始日期",
        dataIndex: "actualStartDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (t) => timeFormat(t)
    },
    {
        key: "deviationDays",
        title: "偏差值",
        dataIndex: "deviationDays",
        align: "right",
        mustShow: false,
        show: true,
        width: 120
    },
    {
        key: "warnLevel",
        title: "报警级别",
        dataIndex: "warnLevel",
        align: "center",
        mustShow: false,
        show: true,
        width: 120
    }
];
