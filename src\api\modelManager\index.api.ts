/* eslint-disable no-underscore-dangle */

import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {DelGisModelParams, GetGisModelListParams, GetGisModelListRes, GetVectorModelListParams, GetVectorModelListRes, PostGisModelAddParams} from "./index.type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

// 矢量图接口工程列表
export const getVectorModelList = async (params: GetVectorModelListParams): Promise<WebRes<GetVectorModelListRes>> => Fetch({
    methods: "post",
    url: `${baseUrl}/editor/v2/vector-graph/page`,
    data: params,
});

// bim模型管理接口工程列表
export const getBimModelList = async (params: GetVectorModelListParams): Promise<WebRes<GetVectorModelListRes>> => Fetch({
    methods: "post",
    url: `${baseUrl}/editor/v2/bim/page`,
    data: params,
});

// 查询gis场景工程列表V2
export const getGisModelList = async (params: GetGisModelListParams): Promise<WebRes<GetGisModelListRes>> => Fetch({
    methods: "post",
    url: `${baseUrl}/editor/v2/gis/page`,
    data: params,
});

// 新增GIS
export const postGisModelAdd = async (params: PostGisModelAddParams): Promise<WebRes<string>> => Fetch({
    methods: "post",
    url: `${baseUrl}/editor/v2/gis/add`,
    data: params,
});

// 删除GIS
export const delGisModel = async (params: DelGisModelParams): Promise<WebRes<string>> => Fetch({
    methods: "delete",
    url: `${baseUrl}/editor/v2/gis/del`,
    data: params,
});
