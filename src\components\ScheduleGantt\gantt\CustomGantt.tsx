/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import ganttManager from "./ganttManager";
import {GanttTask} from "./interface";

const customGantt = () => {
    // gantt.templates.task_text = (_start, _end, task) => {
    //     if (task.progress > 0) {
    //         return `${task.text}: ${(task.progress * 100).toFixed(2)}%`;
    //     }
    //     return task.text;
    // };

    gantt.config.type_renderers[gantt.config.types.project] = (task: GanttTask) => {
        const main_el = document.createElement("div");
        main_el.setAttribute(gantt.config.task_attribute, task.id);
        const size = gantt.getTaskPosition(task, task.start_date, task.end_date);
        main_el.innerHTML = [
            "<div class='project-bar'></div>",
            "<div class='project-left'></div>",
            "<div class='project-right'></div>"
        ].join("");
        main_el.className = "gantt-project-wrapper";

        main_el.style.position = "absolute";
        main_el.style.left = `${size.left}px`;
        main_el.style.top = `${Number(size.top) + 7}px`;
        main_el.style.width = `${size.width}px`;

        return main_el;
    };

    // gantt.config.editor_types.custom_progress = {
    //     show(_: any, column: {name: any}, _config: any, placeholder: HTMLElement) {
    //         const html = `<div>
    //             <input class='gantt_input_number' type='number' min='0' max='100' name='${column.name}' >
    //         </div>`;
    //         // eslint-disable-next-line no-param-reassign
    //         placeholder.innerHTML = html;
    //     },
    //     hide() {
    //         // can be empty since we don't have anything to clean up after the editor
    //         // is detached
    //     },
    //     set_value(value: string, _id: string, _column: any, node: HTMLInputElement) {
    //         const input = node.querySelector("input");
    //         if (input !== null) {
    //             input.value = (Number(value) * 100).toFixed(2);
    //         }
    //     },
    //     get_value(_id: string, _column: any, node: HTMLInputElement) {
    //         const input = node.querySelector("input");
    //         if (input !== null) {
    //             const {value} = input;
    //             if (value.length > 0) {
    //                 const num = Number((Number(value) * 0.01).toFixed(2));
    //                 if (num < 0) {
    //                     return 0;
    //                 }
    //                 if (num > 1) {
    //                     return 1;
    //                 }
    //                 return num;
    //             }
    //         }
    //         return "";
    //     },
    //     is_valid(value: string, _id: any, _column: any, _node: HTMLInputElement) {
    //         return !Number.isNaN(parseInt(value, 10));
    //     },
    //     is_changed(value: string, id: string, column: any, node: HTMLInputElement) {
    //         const currentValue = this.get_value(id, column, node);
    //         return Number(value) !== Number(currentValue);
    //     },
    //     focus(node: HTMLInputElement) {// eslint-disable-line
    //         const input = node.querySelector("input");
    //         if (input === null) {
    //             return;
    //         }
    //         if (input.focus instanceof Function) {
    //             input.focus();
    //         }
    //         if (input.select instanceof Function) {
    //             input.select();
    //         }
    //     }
    // };

    // gantt.config.editor_types.date.is_changed = () => true;

    // 修改文本编辑输入框，如果值为undefined时，显示空
    const {text} = gantt.config.editor_types;
    text.set_value = (value: string, _id: any, _column: any, node: any) => {
        text.get_input(node).value = value ?? "";
    };

    // 自定义工期编辑，赋值逻辑
    gantt.config.editor_types.duration.set_value = (value: number, _id: string, _column: any, node: HTMLInputElement) => {
        const input = node.querySelector("input");
        if (input !== null) {
            if (value < 0) {
                input.value = "";
            } else {
                input.value = ganttManager.durationFormatter.format(value);
            }
        }
    };

    // 自定义工期编辑，取值逻辑
    gantt.config.editor_types.duration.get_value = (_id: string, _column: any, node: HTMLInputElement) => {
        const input = node.querySelector("input");
        if (input !== null) {
            if (input.value !== undefined && input.value.length > 0) {
                return ganttManager.durationFormatter.parse(input.value);
            }
        }
        return -1;
    };

    gantt.config.editor_types.custom_number = {
        show(_: any, column: {name: any}, config: any, placeholder: HTMLElement) {
            const html = `<div>
                <input class="gantt_input_custom_number" type='number' min=${config.min} max=${config.max} name='${column.name}' >
            </div>`;
            // eslint-disable-next-line no-param-reassign
            placeholder.innerHTML = html;
        },
        set_value(value: string, _id: string, _column: any, node: HTMLInputElement) {
            const input = node.querySelector("input");
            if (input !== null) {
                input.value = value;
            }
        },
        get_value(_id: string, _column: any, node: HTMLInputElement) {
            const input = node.querySelector("input");
            if (input !== null) {
                return input.value ?? "";
            }
            return "";
        },
        is_changed(value: string, id: string, column: any, node: HTMLInputElement) {
            const currentValue = this.get_value(id, column, node);
            return Number(value) !== Number(currentValue);
        },
        is_valid(value: string, _id: any, _column: any, _node: HTMLInputElement) {
            return !Number.isNaN(parseInt(value, 10));
        },
        get_input(node: HTMLInputElement) {
            return node.querySelector("input");
        },
        focus(node: HTMLInputElement) {// eslint-disable-line
            const input = node.querySelector("input");
            if (input === null) {
                return;
            }
            if (input.focus instanceof Function) {
                input.focus();
            }
            if (input.select instanceof Function) {
                input.select();
            }
        }
    };
};

export default customGantt;
