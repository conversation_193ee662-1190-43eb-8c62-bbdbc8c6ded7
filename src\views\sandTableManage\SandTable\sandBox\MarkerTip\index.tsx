import React from "react";
import MarkerTipMgr from "./MarkerTipMgr";
import useStyle from "./style";

interface Props {
    id: string;
    windowPosX: number;
    windowPosY: number;
}

const MarkerTipView = (props: Props) => {
    const classStyle = useStyle();

    const {id, windowPosX, windowPosY} = props;

    const getTipText = () => {
        const tipText = MarkerTipMgr.getTipInfo(id);
        if (tipText.includes("/r/n")) {
            const array = tipText.split("/r/n");

            const textNode = (
                <>
                    {array.map((el, index) => (
                        <div key={el + index.toString()}>
                            {el}
                            <br />
                        </div>
                    ))}
                </>
            );
            return textNode;
        }

        return tipText;
    };

    return (
        <div
            className={classStyle.root}
            id="ModelMarkerTipView"
            style={{
                left: windowPosX,
                top: windowPosY,
            }}
        >
            {getTipText()}
        </div>
    );
};

export default MarkerTipView;
