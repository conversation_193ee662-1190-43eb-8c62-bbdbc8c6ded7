/* eslint-disable max-nested-callbacks */
import {gantt} from "@iworks/dhtmlx-gantt";
import {But<PERSON>, message} from "antd";
import {useSelector} from "react-redux";
import React, {useCallback, useContext, useRef, useState} from "react";
import MyIconFont from "../../../../MyIconFont";
import {ProjNameVo} from "../../../api/planModel";
import {GanttTask} from "../../../gantt/interface";
import ModelSelect, {BindType, CheckBind, SelectResult} from "../../../ModelSelect";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import SelectProjectDrawer from "../../SelectProjectDrawer";
import {postTaskBindNode, postTaskListBindNode} from "../../../api/plan";
import {RootState} from "../../../../../store/rootReducer";
import ganttManager from "../../../gantt/ganttManager";

const RelateBimButton = () => {
    const cls = useStyles();
    const {orgInfo, curSectionInfo} = useSelector((store: RootState) => store.commonData);
    const {planInfo, checkoutStatus} = useContext(EditorContext);
    const [modelSelectVisible, setModelSelectVisible] = useState<boolean>(false);
    const [modelSpinVisible, setModelSpinVisible] = useState<boolean>(false);
    const [projectSelectVisible, setProjectSelectVisible] = useState<boolean>(false);
    // const [projectList, setProjectList] = useState<ProjNameVo[]>([]);
    const [taskSelectInfo, setTaskSelectInfo] = useState<CheckBind[]>([]);
    const [projInfo, setProjInfo] = useState<ProjNameVo | undefined>(undefined);
    const currentRelationPpid = useRef<number>(0);

    const relateBim = useCallback(() => {
        setModelSelectVisible(false);
        if (ganttManager.isWbs) {
            message.error("通过wbs导入的任务，不支持绑定模型！");
            return;
        }
        const selectedId = gantt.getSelectedId();
        if (selectedId === null) {
            message.error("请先选中一条任务");
            return;
        }
        // if (gantt.getTask(selectedId).type !== gantt.config.types.task) {
        //     message.error("父任务和里程碑不可关联模型！");
        //     return;
        // }
        // const result = await getProjListByPlanId(planId)
        //     .then((res) => res);
        // if (result.success === false || result.result === null || result.result.length === 0) {
        //     message.info("无关联模型，请在“计划信息”中关联模型后重试！");
        //     return;
        // }
        // setProjectList(result.result);
        setProjectSelectVisible(true);
    }, []);

    const selectProjectCancel = useCallback(() => {
        setProjectSelectVisible(false);
    }, [setProjectSelectVisible]);

    const selectProjectOK = useCallback((info: ProjNameVo) => {
        setProjInfo(info);
        currentRelationPpid.current = info.ppid;
        setTaskSelectInfo([]);
        const selectId = gantt.getSelectedId();
        if (selectId !== null && selectId.length !== 0) {
            postTaskListBindNode([selectId]).then((res) => {
                if (res.success && res.data !== undefined && Array.isArray(res.data)) {
                    console.log("postTaskListBindNode", res);
                    res.data.forEach((item) => {
                        const {ebsNodes} = item;
                        if (ebsNodes.length > 0) {
                            let ppid = 0;
                            const taskInfo = ebsNodes.map((val) => {
                                ppid = val.ppid;
                                return {
                                    ppid: val.ppid,
                                    floor: val.paths[0],
                                    handle: val.handle
                                };
                            });
                            if (info.ppid === ppid) {
                                setTaskSelectInfo(taskInfo);
                            }
                        }
                    });
                }
            });
        }
        setModelSelectVisible(true);
        setModelSpinVisible(true);
        setProjectSelectVisible(false);
    }, []);

    const selectModelInfoOK = useCallback(async (info: SelectResult[]) => {
        console.log("selectModelInfoOK", info);
        setModelSelectVisible(false);
        setModelSpinVisible(false);
        const selectId = gantt.getSelectedId();
        if (planInfo.id === undefined || selectId.length === 0 || projInfo === undefined) {
            return;
        }
        // 模型页面返回的构件
        const ebsNodes = info.map((val) => ({
            ppid: projInfo.ppid,
            handle: val.id,
            paths: val.path,
            mergePath: val.path.join("☎"),
        }));
        const selectedId = gantt.getSelectedId();
        const res = await postTaskBindNode({
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo?.id ?? "",
            planId: planInfo.id,
            bindType: 2,
            businessType: "PLAN_TASK",
            list: [
                {
                    businessId: selectedId,
                    ebsNodes,
                }
            ],
        });
        console.log("postTaskBindNode", res);
        const task: GanttTask = gantt.getTask(selectId);
        task.bindType = info.length === 0 ? 0 : 2;
        gantt.updateTask(selectId, task);
    }, [planInfo.id, projInfo, orgInfo.orgId, curSectionInfo]);

    const selectModelInfoCancel = () => {
        setModelSelectVisible(false);
        setModelSpinVisible(false);
    };

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <div>
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-lianjie" fontSize={18} />}
                onClick={relateBim}
            >
                关联模型
            </Button>
            <SelectProjectDrawer
                visible={projectSelectVisible}
                onCancel={selectProjectCancel}
                onOk={selectProjectOK}
            />
            <ModelSelect
                ppid={projInfo?.ppid}
                projName={projInfo?.projName}
                show={modelSelectVisible}
                showSpin={modelSpinVisible}
                onSelect={selectModelInfoOK}
                onCancel={selectModelInfoCancel}
                selectInfo={taskSelectInfo}
                selectType={BindType.bindComp}
            />
        </div>
    );
};

export default RelateBimButton;
