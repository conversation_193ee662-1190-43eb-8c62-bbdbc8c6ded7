import React from "react";
import {Input} from "antd";
import {CustomFieldProps} from "../../models/custom-field";

const RenderInput = (props: CustomFieldProps) => {
    const {data, maxLength, style, hint, onBlur, disabled, onChange} = props;

    return (
        <>
            {
                ["string", "undefined"].includes(typeof data.defaultValue)
                    ? (
                        <Input
                            maxLength={maxLength}
                            style={style}
                            placeholder={hint}
                            defaultValue={data.defaultValue}
                            onBlur={(e) => onBlur !== undefined && onBlur(e.target.value)}
                            disabled={disabled}
                            onChange={(e) => onChange(e.target.value)}
                        />
                    )
                    : null
            }
        </>
    );
};
export default RenderInput;
