/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import {isEqual} from "lodash-es";
import {GanttTask, GanttLink, stringNumberArray} from "../gantt/interface";
import {CalendarType, is24HourCalendar} from "./calendar";
import {CustomValueType, TaskStatus} from "./constant";
import ganttManager from "../gantt/ganttManager";
import {CalendarInfo, ColumnItem, PlanTaskListRes, PreTaskRelationItem, TaskItem} from "../api/plan/type";
import {DutyPerson} from "../components/SelectPersonDrawer/data";

export const notNullOrUndefined = function notNullOrUndefined(a: any) {// eslint-disable-line
    return a !== null && a !== undefined;
};

export const planStatusStr = function planStatusStr(status: number) {
    let str = "";
    switch (status) {
        case 0:
            str = "处理成功";
            break;
        case 1:
            str = "处理中";
            break;
        case 2:
            str = "处理失败";
            break;
        case 3:
            str = "新增计划未同步过";
            break;
        default:
            str = "unknow";
            break;
    }
    return str;
};

export const getStartDate = (date: Date | undefined, cal: CalendarInfo) => {
    if (date === undefined) {
        return undefined;
    }
    const newDate = gantt.getCalendar("custom").getClosestWorkTime({date, dir: "past"});
    if (is24HourCalendar(cal)) {
        return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
    }
    return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 8, 0, 0);
};

export const getEndDate = (date: Date | undefined, cal: CalendarInfo) => {
    if (date === undefined) {
        return undefined;
    }
    if (is24HourCalendar(cal)) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0);
    }
    if (date.getHours() === 8) {
        let newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1, 17, 0, 0);
        newDate = gantt.getCalendar("custom").getClosestWorkTime({date: newDate, dir: "future"});
        return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 17, 0, 0);
    }
    return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 17, 0, 0);
};

export const dateToString = function dateToString(d: Date, showHours = true) {
    if (!showHours) {
        return `${d.getFullYear()}.${(d.getMonth() + 1).toString().padStart(2, "0")}.${d.getDate().toString().padStart(2, "0")}`;
    }
    const date = `${d.getFullYear()}.${(d.getMonth() + 1).toString().padStart(2, "0")}.${d.getDate().toString().padStart(2, "0")} ${d.getHours().toString().padStart(2, "0")}:${d.getMinutes().toString().padStart(2, "0")}:${d.getSeconds().toString().padStart(2, "0")}`;
    return date;
};

export const timestampToString = function timestampToString(timestamp: number) {
    const d = new Date(timestamp);
    const date = `${d.getFullYear()}.${(d.getMonth() + 1).toString().padStart(2, "0")}.${d.getDate().toString().padStart(2, "0")} ${d.getHours().toString().padStart(2, "0")}:${d.getMinutes().toString().padStart(2, "0")}:${d.getSeconds().toString().padStart(2, "0")}`;
    return date;
};

export const dateStringToDate = function dateStringToDate(dateStr: string): Date | undefined {
    if (dateStr === undefined || dateStr.length === 0) {
        return undefined;
    }
    let dateStrTemp = dateStr.replace(/-/g, "/");
    dateStrTemp = dateStrTemp.replace(/年/g, "/");
    dateStrTemp = dateStrTemp.replace(/月/g, "/");
    dateStrTemp = dateStrTemp.replace(/日/g, "");
    const date = new Date(dateStrTemp);
    return date;
};

// 判断是否是0点
export const isEarliest = (date: Date | number) => {
    let d;
    if (typeof date === "number" || date === undefined || date === null) {
        d = new Date(date);
    } else {
        d = date;
    }
    return d.getHours() === 0 && d.getMinutes() === 0 && d.getSeconds() === 0;
};

export const adjustCalendarTime = (item: TaskItem, calendarType: number) => {
    let {actualStartDate, actualEndDate, planStartDate, planEndDate, requestStartDate, requestEndDate} = item;
    // 标准日历或复制出来的标准日历，由于pc端时间传参均为0点0分0秒，故需要调整时间到工作时间
    const needAdjustTime = calendarType === CalendarType.TYPE_STANDARD || calendarType === CalendarType.TYPE_CUSTOM_STANDARD;
    // 仅校正需要校正的，并且不是里程碑
    if (needAdjustTime && item.milestone === 0) {
        // 开始时间校正到上午8点，结束时间校正到下午17点
        if (notNullOrUndefined(planStartDate) && notNullOrUndefined(planEndDate)
            && actualStartDate !== undefined && actualEndDate !== undefined
            && isEarliest(actualStartDate) && isEarliest(actualEndDate)) {
            actualStartDate += 8 * 60 * 60 * 1000;
            actualEndDate += 17 * 60 * 60 * 1000;
        }
        if (notNullOrUndefined(planStartDate) && notNullOrUndefined(planEndDate)
            && isEarliest(planStartDate) && isEarliest(planEndDate)) {
            planStartDate += 8 * 60 * 60 * 1000;
            planEndDate += 17 * 60 * 60 * 1000;
        }
        if (notNullOrUndefined(planStartDate) && notNullOrUndefined(planEndDate)
            && requestStartDate !== undefined && requestEndDate !== undefined
            && isEarliest(requestStartDate) && isEarliest(requestEndDate)) {
            requestStartDate += 8 * 60 * 60 * 1000;
            requestEndDate += 17 * 60 * 60 * 1000;
        }
    }
    return {actualStartDate, actualEndDate, planStartDate, planEndDate, requestStartDate, requestEndDate};
};

export const splitStr = function splitStr(string: string, sepChar: string) {
    const list: string[] = [];
    let index = 0;
    let fromIndex = 0;
    let length = 0;
    do {
        index = string.indexOf(sepChar, index + length);
        if (index !== -1) {
            length = sepChar.length;
            if (index !== 0) {
                const subStr = string.substr(fromIndex, index - fromIndex);
                list.push(subStr);
                list.push(sepChar);
            } else {
                list.push(sepChar);
            }
            fromIndex = index + length;
        } else {
            const subStr = string.substr(fromIndex, string.length - fromIndex);
            list.push(subStr);
        }
    } while (index !== -1);
    return list;
};

/**
 * 解析服务端返回的任务数据
 */
export const handleServerData = (data: PlanTaskListRes, calendar: CalendarInfo, customColumnInfoList: ColumnItem[]) => {
    // console.log("handleServerData", data, customColumnInfoList);
    const taskItems: TaskItem[] = data.taskList;
    const preTaskRelationList: PreTaskRelationItem[] = data.predTaskRelationList;
    const tasks: GanttTask[] = [];
    const taskIds: stringNumberArray = [];
    const taskMap: Map<string | number, TaskItem> = new Map<string | number, TaskItem>();
    const preTasks: GanttLink[] = [];
    const preTaskIds: stringNumberArray = [];
    const preTaskMap = new Map<string | number, PreTaskRelationItem>();
    // 先排序，保证插入甘特图的顺序与itemOrder一致
    // taskItems.sort((item1, item2) => {
    //     if (item1.parNodeId !== item2.parNodeId) {
    //         return 0;
    //     }
    //     return item1.itemOrder - item2.itemOrder;
    // });
    const {currentCalendarInfo} = ganttManager;
    taskItems.sort((a, b) => a.sort - b.sort).forEach((item: TaskItem) => {
        const {...times} = adjustCalendarTime(item, calendar.calendarType);
        const startDate = new Date(times.planStartDate);
        const endDate = new Date(times.planEndDate);
        const duration = gantt.getCalendar("custom").calculateDuration({start_date: startDate, end_date: endDate});
        const requestStartDate = times.requestStartDate !== undefined && times.requestStartDate > 0
            ? getStartDate(new Date(times.requestStartDate), currentCalendarInfo)
            : undefined;
        const requestEndDate = times.requestEndDate !== undefined && times.requestEndDate > 0
            ? getEndDate(new Date(times.requestEndDate), currentCalendarInfo)
            : undefined;
        const requestDuration = requestStartDate !== undefined && requestEndDate !== undefined
            ? gantt.getCalendar("custom").calculateDuration({start_date: requestStartDate, end_date: requestEndDate})
            : -1;
        let actualStartDate = times.actualStartDate !== undefined && times.actualStartDate > 0
            ? getStartDate(new Date(times.actualStartDate), currentCalendarInfo)
            : undefined;
        let actualEndDate = times.actualEndDate !== undefined && times.actualEndDate > 0
            ? getEndDate(new Date(times.actualEndDate), currentCalendarInfo)
            : undefined;
        const actualDuration = actualStartDate !== undefined && actualEndDate !== undefined
            ? gantt.getCalendar("custom").calculateDuration({start_date: actualStartDate, end_date: actualEndDate})
            : -1;
        let taskType = "task";
        let taskStatus;

        if (item.milestone === 1) {
            taskType = gantt.config.types.milestone;
            actualStartDate = undefined;
            actualEndDate = undefined;
        } else if (taskStatus === undefined) {
            if (actualStartDate !== undefined) {
                if (actualEndDate !== undefined) {
                    taskStatus = TaskStatus.COMPLETED;
                } else {
                    taskStatus = TaskStatus.IN_PROGRESS;
                }
            } else {
                taskStatus = TaskStatus.NOT_START;
            }
        }

        let dutyPerson: DutyPerson;
        try {
            dutyPerson = JSON.parse(item.dutyPerson);
        } catch (error) {
            // console.log("error", error);
            dutyPerson = {
                input: "",
                select: [item.dutyPerson],
            };
        }
        dutyPerson.select = dutyPerson.select.filter((el) => el.length > 0);

        const task: GanttTask = {
            id: item.id,
            type: taskType,
            parent: item.parentTaskId?.length > 0 ? item.parentTaskId : "0",
            text: item.name.replace(/\n/g, " "), // 将所有的换行符转换为空格
            start_date: startDate,
            end_date: endDate,
            duration,
            request_start_date: requestStartDate,
            request_end_date: requestEndDate,
            request_duration: requestDuration,
            actual_syn: item.actualSyn,
            actual_start: actualStartDate,
            actual_end: actualEndDate,
            actual_duration: actualDuration,
            taskStatus,
            // progress: item.taskProgress !== undefined ? item.taskProgress * 0.01 : undefined,
            dutyUnit: item.dutyUnit,
            dutyPerson,
            hasPhoto: item.hasFile,
            bindType: item.bindType,
            calendar_id: "custom",
            changeStatus: item.changeStatus,
            order: item.sort,
            planId: item.planId,
            actualPlan: item.actualPlan,
        };

        if (item.planId === ganttManager.editorContext.planInfo.parentId) {
            // 来自上级计划，保存一份时间信息，并设为只读
            task.request_start_date = new Date(times.planStartDate);
            task.request_end_date = new Date(times.planEndDate);
            task.request_duration = gantt.getCalendar("custom").calculateDuration({start_date: task.request_start_date, end_date: task.request_end_date});
            task.readonly = true;
        }

        // 自定义列
        if (notNullOrUndefined(item.customValueMap)) {
            (customColumnInfoList as any).forEach((column: ColumnItem) => { // eslint-disable-line 
                const value = (item.customValueMap as any)[column.columnId]; // eslint-disable-line
                if (value !== undefined) {
                    switch (column.valueType) {
                        case CustomValueType.date:
                            (task as any)[column.columnId] = new Date(Number(value)); // eslint-disable-line
                            break;
                        case CustomValueType.number:
                            (task as any)[column.columnId] = Number(value); // eslint-disable-line
                            break;
                        default:
                            (task as any)[column.columnId] = value; // eslint-disable-line
                            break;
                    }
                }
            });
        }

        tasks.push(task);
        taskIds.push(item.id);
        taskMap.set(item.id, item);

        if (preTaskRelationList !== undefined) {
            preTaskRelationList.forEach((preTaskItem: PreTaskRelationItem) => {
                const preTask: GanttLink = {
                    id: preTaskItem.id,
                    source: preTaskItem.predTaskId,
                    target: preTaskItem.taskId,
                    type: String(preTaskItem.dateRelation),
                    lag: ganttManager.durationFormatter.parse(`${preTaskItem.dateSpan}`)
                };

                preTasks.push(preTask);
                preTaskIds.push(preTaskItem.id);
                preTaskMap.set(preTaskItem.id, preTaskItem);
            });
        }
    });
    const resData = {tasks, taskIds, taskMap, preTasks, preTaskIds, preTaskMap};
    console.log("handleServerData resData", resData);
    return resData;
};

// task的taskType只有从normalType(task或者project)到milestone 互相转变的时候才算改变
export const isTypeEqual = (type1 = "", type2 = "") => {
    if (type1 === type2) {
        return true;
    }
    const normalType = ["task", "project"];
    if (normalType.includes(type1) && normalType.includes(type2)) {
        return true;
    }
    return false;
};

export const isGanttTaskEqual = (task1: GanttTask, task2: GanttTask, customColumnInfoList: ColumnItem[]) => {
    // console.log("isGanttTaskEqual", task1, task2, customColumnInfoList);
    if (task1 === undefined || task2 === undefined) {
        return true;
    }
    // console.log("text", task1.text === task2.text);
    // console.log("dutyUnit", task1.dutyUnit === task2.dutyUnit);
    // console.log("dutyPerson", task1.dutyPerson === task2.dutyPerson);
    // console.log("duration", task1.duration === task2.duration);
    // console.log("start_date", task1.start_date.getTime() === task2.start_date.getTime());
    // console.log("end_date", task1.end_date.getTime() === task2.end_date.getTime());
    // console.log("actual_duration", task1.actual_duration === task2.actual_duration);
    // console.log("actual_start", task1.actual_start?.getTime() === task2.actual_start?.getTime());
    // console.log("actual_end", task1.actual_end?.getTime() === task2.actual_end?.getTime());
    // console.log("parent", task1.parent === task2.parent);
    // console.log("type", isTypeEqual(task1.type, task2.type));
    // console.log("order", task1.order === task2.order);
    // console.log("taskStatus", task1.taskStatus === task2.taskStatus);
    const taskEqual = task1.text === task2.text
        && task1.dutyUnit === task2.dutyUnit
        && isEqual(task1.dutyPerson, task2.dutyPerson)
        && task1.duration === task2.duration
        && task1.start_date.getTime() === task2.start_date.getTime()
        && task1.end_date.getTime() === task2.end_date.getTime()
        && task1.request_duration === task2.request_duration
        && task1.request_start_date?.getTime() === task2.request_start_date?.getTime()
        && task1.request_end_date?.getTime() === task2.request_end_date?.getTime()
        && task1.actual_duration === task2.actual_duration
        && task1.actual_start?.getTime() === task2.actual_start?.getTime()
        && task1.actual_end?.getTime() === task2.actual_end?.getTime()
        && task1.parent === task2.parent
        && isTypeEqual(task1.type, task2.type)
        && task1.order === task2.order
        && task1.taskStatus === task2.taskStatus;
    let customEqual = true;
    for (let i = 0; i < customColumnInfoList.length; i++) {
        const columnInfo = customColumnInfoList[i];
        if (isEqual((task1 as any)[columnInfo.columnId], (task2 as any)[columnInfo.columnId]) === false) { // eslint-disable-line
            customEqual = false;
            break;
        }
    }
    // console.log("taskEqual", taskEqual);
    // console.log("customEqual", customEqual);
    return taskEqual && customEqual;
};

export const isGanttLinkEqual = (link1: GanttLink, link2: GanttLink) => {
    if (link1 === undefined || link2 === undefined) {
        return true;
    }
    return link1.type === link2.type
        && link1.source === link2.source
        && link1.target === link2.target
        && link1.lag === link2.lag;
};

/**
 * 保存任务数据到服务端
 */
export const handleSaveTaskParam = (
    originalTaskIds: stringNumberArray,
    originalTaskMap: Map<string | number, GanttTask>,
    originalPreTaskIds: stringNumberArray,
    originalPreTaskMap: Map<string | number, GanttLink>,
    customColumnInfoList: ColumnItem[]
) => {
    // handle task
    const addTasks: TaskItem[] = [];
    const modifyTasks: TaskItem[] = [];
    const deleteTasks: string[] = [];
    const bindWbsTasks: GanttTask[] = [];
    const buildServerTask = (task: GanttTask, parentId: string) => {
        const serverTask: Partial<TaskItem> = {
            id: task.id,
            wbsNo: task.$wbs as string,
            name: task.text,
            planDuration: task.duration !== undefined ? Number(task.duration) : 0,
            planStartDate: task.start_date.getTime(),
            planEndDate: task.end_date.getTime(),
            sort: gantt.getTaskIndex(task.id),
            parentTaskId: parentId === "0" ? "" : parentId,
            milestone: gantt.getTaskType(task) === "milestone" ? 1 : 0,
        };
        if (serverTask.milestone === 1) {
            serverTask.actualSyn = 0;
        } else {
            // if (task.progress !== undefined) {
            //     serverTask.taskProgress = Number((task.progress * 100).toFixed(2));
            // } else {
            //     serverTask.taskProgress = 0;
            // }
            serverTask.requestDuration = task.request_duration !== undefined && task.request_duration >= 0
                ? Number(task.request_duration)
                : undefined;
            serverTask.requestStartDate = task.request_start_date !== undefined ? task.request_start_date.getTime() : undefined;
            serverTask.requestEndDate = task.request_end_date !== undefined ? task.request_end_date.getTime() : undefined;
            if (task.taskStatus !== undefined) {
                serverTask.taskStatus = task.taskStatus;
            }
            if (task.actual_start !== undefined) {
                serverTask.actualStartDate = task.actual_start.getTime();
            } else {
                serverTask.actualStartDate = undefined;
            }
            if (task.actual_end !== undefined) {
                serverTask.actualEndDate = task.actual_end.getTime();
            } else {
                serverTask.actualEndDate = undefined;
            }
            if (task.actual_duration !== undefined && task.actual_duration >= 0) {
                serverTask.actualDuration = Number(task.actual_duration);
            } else {
                serverTask.actualDuration = undefined;
            }
            if (task.actual_syn !== undefined) {
                serverTask.actualSyn = Number(task.actual_syn);
            } else {
                serverTask.actualSyn = 0;
            }
        }
        // 负责单位
        if (task.dutyUnit !== undefined) {
            serverTask.dutyUnit = task.dutyUnit;
        }
        // 负责人
        if (task.dutyPerson !== undefined) {
            serverTask.dutyPerson = JSON.stringify(task.dutyPerson);
        }

        // 自定义列
        const customColumnValues = {};
        customColumnInfoList.forEach((column) => {
            const value = (task as any)[column.columnId]; // eslint-disable-line
            if (value !== undefined) {
                switch (column.valueType) {
                    case CustomValueType.date:
                        (customColumnValues as any)[column.columnId] = value.getTime(); // eslint-disable-line
                        break;
                    default:
                        (customColumnValues as any)[column.columnId] = value; // eslint-disable-line
                        break;
                }
            }
        });
        serverTask.customValueMap = customColumnValues;
        return serverTask;
    };

    gantt.eachTask((task: GanttTask) => {
        task.order = gantt.getTaskIndex(task.id); // eslint-disable-line no-param-reassign
        if (task.bindType === -1) {
            bindWbsTasks.push(task);
        }
        const parentId = String(gantt.getParent(task.id));
        if (originalTaskIds === undefined || !originalTaskIds.includes(task.id)) {
            // 新增task
            const addTask = buildServerTask(task, parentId) as TaskItem;
            addTasks.push(addTask);
        } else {
            // 修改task
            const originalTask = originalTaskMap?.get(task.id);
            if (originalTask !== undefined) {
                if (!isGanttTaskEqual(originalTask, task, customColumnInfoList)) {
                    const modifyTask = buildServerTask(task, parentId) as TaskItem;
                    // 计划时间改变，修改changeStatus
                    if (originalTask.duration !== task.duration
                        || originalTask.start_date.getTime() !== task.start_date.getTime()
                        || originalTask.end_date.getTime() !== task.end_date.getTime()) {
                        modifyTask.changeStatus = "Changed";
                    }
                    modifyTasks.push(modifyTask);
                }
            }
        }
    });
    // 删除task
    if (originalTaskIds !== undefined) {
        originalTaskIds.forEach((id) => {
            if (!gantt.isTaskExists(id)) {
                deleteTasks.push(id as string);
            }
        });
    }

    // handle preTask
    const addPreTasks: PreTaskRelationItem[] = [];
    const modifyPreTasks: PreTaskRelationItem[] = [];
    const deletePreTaskIds: string[] = [];
    gantt.getLinks().forEach((ganttLink: GanttLink) => {
        const {id, target, source, type, lag} = ganttLink;
        let dateSpan = 0;
        if (typeof lag === "number") {
            dateSpan = Number.parseFloat(ganttManager.durationFormatter.format(`${lag}`));
        }
        const newPreTask: PreTaskRelationItem = {
            id,
            taskId: target,
            predTaskId: source,
            dateRelation: Number(type),
            dateSpan,
        };
        if (originalPreTaskIds === undefined || !originalPreTaskIds.includes(ganttLink.id)) {
            // 新增link
            addPreTasks.push(newPreTask);
        } else {
            // 修改link
            const originalPreTask = originalPreTaskMap?.get(ganttLink.id);
            if (originalPreTask !== undefined) {
                if (!isGanttLinkEqual(originalPreTask, ganttLink)) {
                    modifyPreTasks.push(newPreTask);
                }
            }
        }
    });
    // 删除link
    if (originalPreTaskIds !== undefined) {
        originalPreTaskIds.forEach((id) => {
            if (!gantt.isLinkExists(id)) {
                deletePreTaskIds.push(id as string);
            }
        });
    }
    const res = {
        addTasks,
        modifyTasks,
        deleteTasks,
        bindWbsTasks,
        addPreTasks,
        modifyPreTasks,
        deletePreTaskIds
    };
    console.log("handleSaveTaskParam", res);
    return res;
};
