import {Button, Checkbox, CheckboxProps, Col, Divider, Row} from "antd";
import {CheckboxValueType} from "antd/lib/checkbox/Group";
import {ColumnType} from "antd/lib/table";
import React, {CSSProperties, ReactNode, useEffect} from "react";

const {Group} = Checkbox;

export interface ComColumnsProps<T> extends ColumnType<T> {
    mustShow: boolean;
    show: boolean;
    title: string | ReactNode;
    visible?: boolean;
    titleText?: string;
}
interface ColumnsControlProps<T> {
    tableKey: string;
    columnsList: ComColumnsProps<T>[];
    setColumnsList: (val: ComColumnsProps<T>[]) => void;
    style?: CSSProperties;
    defaultColumnsShow: string[];
}

export const dealTitle = (title: string | ReactNode, titleText?: string) => (typeof title === "string" ? title : titleText ?? "");

const ColumnsControl = <T extends {}>(props: ColumnsControlProps<T>) => {
    const {columnsList = [], setColumnsList, style, defaultColumnsShow} = props;
    const [indeterminate, setIndeterminate] = React.useState(false);
    const [isCheckAll, setIsCheckAll] = React.useState(true);
    const [showTitleList, setShowTitleList] = React.useState<string[]>([]);
    // 表格列中可以显示控制的
    const controlColumnsList = columnsList.filter((el) => el.visible !== false && !el.mustShow);
    useEffect(() => {
        setShowTitleList(controlColumnsList.filter((item) => !item.mustShow && item.show).map((el) => dealTitle(el.title, el.titleText)));
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    useEffect(() => {
        setIndeterminate(showTitleList.length !== 0 && showTitleList.length < controlColumnsList.length);
        setIsCheckAll(showTitleList.length === controlColumnsList.length);
    }, [showTitleList.length, controlColumnsList.length]);

    const handleChange = (value: CheckboxValueType[]) => {
        const checkedTitles = value.map((el: CheckboxValueType) => el.toString());
        setColumnsList(
            columnsList.map((el: ComColumnsProps<T>) => ({
                ...el,
                show: checkedTitles?.includes(dealTitle(el.title, el.titleText))
            }))
        );
        setShowTitleList(checkedTitles);
    };

    const handleReset = () => {
        setColumnsList(columnsList.map((el) => ({
            ...el,
            show: defaultColumnsShow.includes(el.title as unknown as string),
        })));
        setShowTitleList(defaultColumnsShow);
    };

    const onCheckAllChange: CheckboxProps["onChange"] = (e) => {
        setColumnsList(
            columnsList.map((el: ComColumnsProps<T>) => ({
                ...el,
                show: e.target.checked
            }))
        );
        setShowTitleList(e.target.checked ? controlColumnsList.map((el) => dealTitle(el.title, el.titleText)) : []);
    };

    return (
        <div style={{padding: "12px 0", ...style}}>
            <Row justify="space-between" style={{padding: "0 6px 0 16px"}} align="middle">
                <Col>
                    <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={isCheckAll}>
                        <span className="title14">全选</span>
                    </Checkbox>
                </Col>
                <Col>
                    <Button type="link" onClick={handleReset}>重置</Button>
                    {/* <Button size="small" onClick={onOk}>确定</Button> */}
                </Col>
            </Row>
            <Divider style={{margin: "12px 0"}} />
            <Group onChange={handleChange} value={showTitleList} style={{padding: "0 16px"}}>
                {controlColumnsList.map((el: ComColumnsProps<T>) => (
                    <div key={dealTitle(el.title, el.titleText)}>
                        <Checkbox value={dealTitle(el.title, el.titleText)}>
                            {dealTitle(el.title, el.titleText)}
                        </Checkbox>
                    </div>
                ))}
            </Group>
        </div>
    );
};

export default ColumnsControl;
