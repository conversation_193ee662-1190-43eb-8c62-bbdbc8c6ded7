import {CheckSubOptionValueType} from "../../components/CheckSubOption/MultipleChoice";
import {WbsEbsNodeType} from "../../components/FormWbsEbs/data";
import {FileType} from "../common.type";

export interface GetHiddenDangerCheckListType {
    buildType: number;
    checkDesc: string;
    checkResult: number;
    checkTime: string;
    checkType: number;
    checkUser: string;
    dangerLevel: number;
    number: string;
    // optionContent: string;
    subOptionContentList: string[];
    // inspectionOption: InspectionOptionType;
    reformId: string;
    reformStatus: number;
    id: string;
    nodeId: string;
    checkNodeId: string;
    checkNodeName: string;
    createAt: string;
    createBy: string;
    updateAt: string;
    updateBy: string;
}

export interface GetHiddenDangerCheckListReturn {
    items: GetHiddenDangerCheckListType[];
    totalCount: number;
    ttotalPageype: number;
}



export interface GetHiddenDangerCheckDetailReturn {
    nodeName: string;
    deptName: string;
    wbsNodes: any[];
    dangerLevelName?: string;
    checkResultName?: string;
    checkTypeName?: string;
    buildTypeName?: string;
    attachmentFiles: FileType[];
    buildType: number;
    checkResult: number;
    checkTime: string;
    checkType: number;
    checkUser: {
        checkUserName: string;
        checkUserId: string;
    };
    dangerLevel: number;
    dangerSourceId: string;
    dangerSourceName: string;
    deptId: string;
    optionList: CheckSubOptionValueType[];
    moduleType: string;
    nodeId: string;
    nodeType?: number;
    checkNodeId: string;
    checkNodeName: string;
    checkPlanId: string;
    checkPlanType: string;
    number: string;
    photoFiles: FileType[];
    position: string;
    reformStatus: number;
    wbsNodeIds: string[];
    checkDesc: string;
    lawBasis: string;
    optionContent: string;
    projectCategory: string;
    projectCategoryId: string;
    patrolInspectionTaskId: string;
    wbsEbs?: WbsEbsNodeType;
}

export interface SecurityListSatuteGistType {
    id: string;
    content: string;
    detail: string;
}
export interface QuerySecurityListSatuteGistReturn {
    id: string;
    content: string;
    referenceSatutes: SecurityListSatuteGistType[];
}
export interface QualityListReferenceType {
    uuid: string;
    name: string;
}
export interface QueryQualityListReferenceReturn {
    id: string;
    content: string;
    referenceFiles: QualityListReferenceType[];
}

export interface WarnExportParam {
    /** 导出时选中的ids */
    baseIds?: string[];
    /** 项目部ID */
    deptId: string;
    /** 计划名称模糊搜索，空查询全部 */
    nameKey?: string;
    /** 组织节点ID */
    nodeId?: string;
    /** 组织节点类型 */
    nodeType?: number;
    /** 进度计划ID */
    planId?: string;
    /** 计划类型，空查询全部 */
    type?: string;
    /** 报警级别，一、二、三、四，值越小，延期越严重，空查询所有 */
    warnLevel?: number;
}
