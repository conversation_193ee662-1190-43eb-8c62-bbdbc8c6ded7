import {Input, Select, TreeProps} from "antd";
import {DataNode} from "antd/lib/tree";
import {cloneDeep} from "lodash-es";
import React, {useCallback, useEffect, useMemo, useState} from "react";
import {createUseStyles} from "react-jss";
import {useDispatch, useSelector} from "react-redux";
import {getAllWBSTypeList} from "../../api/center";
import {WBSBusinessType, WBSNodeType} from "../../api/center/type";
import {filterTreeNew, generateList, isNotNullOrUndefined} from "../../assets/ts/utils";
import {NodeTypeEnum, SectionListType} from "../../store/common/actionTypes";
import {RootState} from "../../store/rootReducer";
import ComTree from "../ComTree";
import FilterWBSTreeHandler, {DataNodeEx} from "./FilterWBSTreeHandler";
import { ConsoleSqlOutlined } from "@ant-design/icons";

const useStyle = createUseStyles({
    tree: {
        "& .ant-tree-list": {
            height: "100%",
            overflow: "hidden",
            "& .ant-tree-treenode": {
                width: "100%",
                "& .ant-tree-node-content-wrapper": {
                    flexGrow: 1,
                    width: 0,
                    whiteSpace: "nowrap"
                },
                // 可性能优化 目前来看渲染还可以 先注释
                // "& .ant-tree-checkbox .ant-tree-checkbox-inner:after": {
                //     transition: "none",
                // },
                // "& .ant-tree-checkbox-checked::after": {
                //     animation: "none",
                // }
            },
            "& .ant-tree-list-holder > div": {
                overflow: "auto !important"
            }
        }
    },
});

export interface WBSTreeProps extends TreeProps {
    isShowSearch?: boolean;
    sectionInfo?: SectionListType;
    selectedWbs?: string;
    isAppend?: boolean; // 是否追加导入
    wbsLevels?: number[];
    limitWbsLevel?: number;

    setTreeNodesFunc: (dataList: DataNode[]) => void;
}

const ImportWBSTree = (props: WBSTreeProps) => {
    const cls = useStyle();
    // const resetAntdStyle = bimAntdStyle();
    const {isShowSearch = true, sectionInfo: _sectionInfo, selectedWbs, isAppend, wbsLevels, limitWbsLevel, setTreeNodesFunc} = props;
    const dispatch = useDispatch();
    const {orgInfo, sectionList, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const {originData, treeDataObj} = useSelector((state: RootState) => state.wbsTree);
    const [copyTreeData, setCopyTreeData] = useState<DataNode[]>([]); // 备份一份数据
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [selectValue, setSelectValue] = useState<string[]>([]);
    const [fixedWbsBusinessTypeList, setFixedWbsBusinessTypeList] = useState<WBSBusinessType[]>([]);
    const [wbsBusinessTypeList, setWbsBusinessTypeList] = useState<WBSBusinessType[]>([]);
    const sectionInfo = useMemo(() => _sectionInfo ?? curSectionInfo, [_sectionInfo, curSectionInfo]);

    useEffect(
        () => {
            getAllWBSTypeList().then((res) => {
                console.log("res", res)
                if (limitWbsLevel !== undefined) {
                    // console.log("wbsLevels", wbsLevels);
                    // console.log("limitWbsLevel", limitWbsLevel);
                    const fixedList: WBSBusinessType[] = [];
                    const selectList: WBSBusinessType[] = [];
                    res.data.forEach((el) => {
                        if (el.key <= limitWbsLevel) {
                            if (wbsLevels?.includes(el.key) === true) {
                                fixedList.push(el);
                            }
                        } else {
                            selectList.push(el);
                        }
                    });
                    // 截取第一个和第三个
                    const newSelectList = [selectList[0], selectList[2]]
                    // console.log("fixedList", fixedList);
                    // console.log("selectList", selectList);
                    setFixedWbsBusinessTypeList(fixedList);
                    setWbsBusinessTypeList(newSelectList);
                } else {
                    setWbsBusinessTypeList(res.data ?? []);
                }
            })
                .catch(() => {
                    setWbsBusinessTypeList([]);
                });
        }, [wbsLevels, limitWbsLevel]
    );

    const filterTreeWithSelectWbs = useCallback((treeData) => {
        console.log("selectedWbs", selectedWbs);
        console.log("treeData", treeData);
        if (selectedWbs === undefined) {
            return treeData;
        }
        const findWbsNode = (node: any) => {
            // console.log("findWbsNode node", node);
            if (node.key === selectedWbs) {
                return true;
            }
            if (node.children !== undefined) {
                // 非根节点暂时不过滤节点数据
                const newChildren = node.children.filter((child: any) => findWbsNode(child));
                if (newChildren.length > 0) {
                    return true;
                }
            }
            return false;
        };
        const newTreeData = treeData.filter((node: any) => findWbsNode(node));
        console.log("newTreeData", newTreeData);
        return newTreeData;
    }, [selectedWbs]);

    useEffect(
        () => {
            let tempArr: DataNode[] = [];
            // 基建项目
            if (orgInfo.deptDataType === 2) {
                // 项目方
                if (sectionInfo?.nodeType === NodeTypeEnum.项目) {
                    Object.values(treeDataObj).forEach((el) => {
                        const tempTreeData = el[0];
                        const filteredTree = cloneDeep(tempTreeData);
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            const children = filteredTree.children as DataNodeEx[];
                            filteredTree.children = children.map((elem) => ({...elem, parentId: filteredTree.key}));
                            tempArr = tempArr.concat([filteredTree]);
                        }
                    });
                }
                // 施工方查看自己的
                if (sectionInfo?.classification === 0) {
                    const curSectionTreeData = treeDataObj[sectionInfo?.id ?? ""] ?? [];
                    if (curSectionTreeData.length > 0) {
                        const tempTreeData = curSectionTreeData[0];
                        const filteredTree = tempTreeData;
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            const children = filteredTree.children as DataNodeEx[];
                            filteredTree.children = children.map((elem) => ({...elem, parentId: filteredTree.key}));
                            tempArr = tempArr.concat([filteredTree]);
                        }
                    }
                }
                // 监理方查看,自己的+下属施工方的
                if (sectionInfo?.classification === 1) {
                    const belongSectionId = sectionList.filter((el) => el.parentId === sectionInfo?.id);
                    belongSectionId.forEach((el) => {
                        const tempTreeData = treeDataObj[el.nodeId][0];
                        const filteredTree = tempTreeData;
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            const children = filteredTree.children as DataNodeEx[];
                            filteredTree.children = children.map((elem) => ({...elem, parentId: filteredTree.key}));
                            tempArr = tempArr.concat([filteredTree]);
                        }
                    });
                }
            }
            // 房建项目
            if (orgInfo.deptDataType === 1) {
                const tempTreeData = treeDataObj[orgInfo.orgId] ?? [];
                if (tempTreeData.length > 0) {
                    const filteredTree = tempTreeData[0];
                    if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                        const children = filteredTree.children as DataNodeEx[];
                        filteredTree.children = children.map((elem) => ({...elem, parentId: filteredTree.key}));
                        tempArr = tempArr.concat([filteredTree]);
                    }
                }
            }
            if (isAppend === true) {
                tempArr = filterTreeWithSelectWbs(tempArr);
            }
            setTreeNodesFunc(tempArr);
            setCopyTreeData(tempArr);
        },
        [
            sectionInfo,
            dispatch,
            filterTreeWithSelectWbs,
            orgInfo.deptDataType,
            orgInfo.orgId,
            originData,
            sectionList,
            setTreeNodesFunc,
            treeDataObj,
            isAppend
        ],
    );

    const treeToWbsList = React.useCallback((treeList: DataNode[]): WBSNodeType[] => {
        let resultList: WBSNodeType[] = [];
        for (let i = 0; i < treeList.length; ++i) {
            const item = treeList[i];
            resultList.push({...item, id: item.key, name: item.title as string} as WBSNodeType);
            if (isNotNullOrUndefined(item?.children)) {
                resultList = resultList.concat(treeToWbsList(item?.children));
            }
        }
        return resultList;
    }, []);

    const handleChange = React.useCallback((value: string[]) => {
        const selectList = value.length > 0 ? value : wbsBusinessTypeList.map((el) => el.key.toString());
        // console.log("selectList", selectList);
        const filterList = [...fixedWbsBusinessTypeList.map((el) => el.key.toString()), ...selectList];
        // console.log("filterList", filterList);
        if (value.length >= 1) {
            setSelectValue(value);
        } else {
            setSelectValue([]);
        }

        const sectionMap = new Map<string, WBSNodeType[]>();
        // eslint-disable-next-line no-restricted-syntax, guard-for-in
        for (const key in originData) {
            sectionMap.set(key, originData[key]);
        }

        // eslint-disable-next-line no-restricted-syntax, guard-for-in
        for (const key in treeDataObj) {
            const treeItems = treeDataObj[key];
            if (treeItems.length > 0) {
                sectionMap.set(key, treeToWbsList(treeItems[0].children ?? []));
            }
        }

        const handler = new FilterWBSTreeHandler(sectionInfo, sectionList, sectionMap);

        const filtered = handler.filterWBSTypeTree(filterList.map((el) => Number(el)));
        setCopyTreeData(filtered);
        setTreeNodesFunc(filtered);
        const newExpandedKeys = generateList(filtered, []).map((item: DataNode) => item.key);
        setAutoExpandParent(true);
        setExpandedKeys(newExpandedKeys);
    }, [
        wbsBusinessTypeList,
        fixedWbsBusinessTypeList,
        sectionInfo,
        sectionList,
        setTreeNodesFunc,
        originData,
        treeDataObj,
        treeToWbsList
    ]);

    const defaultMatcher = (filterText: string, node: DataNode) => {
        if (typeof node?.title === "string") {
            return node?.title?.includes(filterText);
        }
        return false;
    };

    const onSearch = (value: string) => {
        const newTreeData = cloneDeep(copyTreeData);
        if (value === "") {
            setAutoExpandParent(false);
            setExpandedKeys([]);
            setCopyTreeData(copyTreeData);
            return;
        }
        const filtered = newTreeData.map((item) => filterTreeNew(item, value, defaultMatcher));
        if (filtered.some((item) => Array.isArray(item.children) && item.children.length > 0)) {
            setCopyTreeData(filtered.filter((item) => Array.isArray(item.children) && item.children.length > 0));
        } else {
            setCopyTreeData(filtered.filter((item) => defaultMatcher(value, item)));
        }
        const newExpandedKeys = generateList(filtered, [])
            .map((item: DataNode) => item.key);
        setAutoExpandParent(true);
        setExpandedKeys(newExpandedKeys);
    };

    const onExpand: TreeProps["onExpand"] = (newExpandedKeys: React.Key[]) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
    };

    const renderSelect = React.useCallback(() => (
        <Select
            allowClear
            style={{width: "100%", marginBottom: 8}}
            placeholder="选择节点"
            // open={true}
            value={selectValue}
            defaultValue={[]}
            // maxTagCount={0}
            mode={"multiple" as const}
            onChange={handleChange}
        >
            {wbsBusinessTypeList.map((item) => <Select.Option value={item.key.toString()} key={item.key} className="select_options">{item.value}</Select.Option>)}
        </Select>
    ), [handleChange, selectValue, wbsBusinessTypeList]);

    return (
        <div style={{height: "100%", display: "flex", flexDirection: "column"}}>
            {renderSelect()}
            {isShowSearch !== undefined && isShowSearch === true ? <div style={{marginBottom: "12px"}}><Input.Search onSearch={onSearch} /></div> : ""}
            {(copyTreeData.length !== 0) && (
                <ComTree
                    flex
                    className={cls.tree}
                    onExpand={onExpand}
                    autoExpandParent={autoExpandParent}
                    treeData={copyTreeData}
                    // titleRender={renderNode}
                    expandedKeys={expandedKeys}
                    {...props}
                />
            )}
        </div>
    );
};

export default ImportWBSTree;
