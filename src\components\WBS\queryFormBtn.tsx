/* eslint-disable max-nested-callbacks */
import {Space} from "antd";
import React, {useCallback, useEffect, useState, memo} from "react";
import {useDispatch, useSelector} from "react-redux";
import Color from "../../assets/css/Color";
import {emit} from "../../store/common/action";
import {RootState} from "../../store/rootReducer";
import MyIcon from "../MyIcon";
import {setCheckedKeys, setEbsBindWbs, setEbsTreeData, setIsShow, setLeafIds, setWbsBindEbs} from "./store/action";
import {WbsEbsTreeDataNode} from "./data";
import {pathSeparator, toArr} from "../../assets/ts/utils";
import {getBindEbs, getBindWBSList, getEbsBindWbs, getMyTaskBIndEBSList, getMyTaskBindWBSList, getReformBindWBSList} from "../../api/wbsEbs/api";

export interface QueryFormBtnProps {
    defaultShow?: boolean;
    businessType?: string;
    processType?: string;
    // 是否自动关闭,即组件销毁的时候关闭搜索的wbs分部分项.
    autoClose?: boolean;
}

// 废弃了
/**
 * @deprecated
 */
const QueryFormBtn = (props: QueryFormBtnProps) => {
    const {businessType, processType, autoClose = true} = props;
    const moduleTypeInit = () => {
        const {hash} = window.location;
        if (hash.includes("SECURITY")) {
            return "SECURITY";
        }
        if (hash.includes("QUALITY")) {
            return "QUALITY";
        }
        return null;
    };
    const dispatch = useDispatch();
    const [moduleType] = useState(moduleTypeInit());
    const {isShow} = useSelector((state: RootState) => state.wbsTree);
    const {orgInfo, on, curSectionInfo} = useSelector((state: RootState) => state.commonData);

    const getBindWBSIds = useCallback(
        () => {
            if (curSectionInfo !== null) {
                // 我的任务
                if (businessType === "security.inspection.routing.wbs.myTask") {
                    const params = {
                        businessType: "security.inspection.routing.wbs",
                        deptId: orgInfo.orgId,
                        moduleType,
                        nodeId: curSectionInfo.id,
                        buildType: curSectionInfo.classification
                    };
                    getMyTaskBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                        dispatch(setIsShow(res.data.length !== 0));
                    }).catch(() => {
                        dispatch(setIsShow(false));
                        dispatch(setLeafIds([]));
                    });
                    return;
                }
                // 整改
                if (processType !== undefined) {
                    const params = {
                        moduleType: `${moduleType}_V2`,
                        nodeId: curSectionInfo.id === "" ? orgInfo.orgId : curSectionInfo.id,
                        nodeType: curSectionInfo.id === "" ? 2 : 3,
                        processType,
                        buildType: curSectionInfo.classification,
                    };
                    getReformBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                        dispatch(setIsShow(res.data.length !== 0));
                    }).catch(() => {
                        dispatch(setIsShow(false));
                        dispatch(setLeafIds([]));
                    });
                    return;
                }
                // 隐患排查,巡检(除我的任务),技术交底,岗前教育,风险,危大工程,技术交底
                if (businessType !== undefined) {
                    const params = {
                        businessType,
                        deptId: orgInfo.orgId,
                        moduleType,
                        nodeId: curSectionInfo.id,
                        buildType: curSectionInfo.classification,
                    };
                    getBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                        dispatch(setIsShow(res.data.length !== 0));
                    }).catch(() => {
                        dispatch(setIsShow(false));
                        dispatch(setLeafIds([]));
                    });
                }
            }
        },
        [businessType, curSectionInfo, dispatch, moduleType, orgInfo.orgId, processType],
    );

    const buildComponentTreeWithPaths = useCallback((paths: string[], ppid: number): WbsEbsTreeDataNode[] => {
        // 1.记录根位置
        const resList: WbsEbsTreeDataNode[] | undefined = [];
        toArr(paths).forEach((path) => {
            if (path === null) {
                return;
            }
            // 2.待匹配项
            const pathList = path.split(pathSeparator);
            // 3.将移动指针重置顶层，确保每次从根检索匹配（必须！！！）
            let levelList: WbsEbsTreeDataNode[] | undefined = resList;
            // 4.遍历待询节点
            pathList.forEach((title, index) => {
                if (levelList === undefined) {
                    return;
                }
                // 5.同层同名节点查找匹配
                let obj = levelList.find((item) => item.title === title);
                // 6.若不存在则建立该节点
                if (obj === undefined) {
                    const key = `${ppid}${pathSeparator}${pathList.slice(0, index + 1).join(pathSeparator)}`;
                    obj = {
                        title,
                        key,
                        children: [],
                        ppid,
                        path,
                        type: "ebs",
                    };
                    levelList.push(obj);

                    // 7.若当前被增节点是叶子节点，则裁剪该节点子节点属性
                    if (title === pathList[pathList.length - 1]) {
                        delete obj.children;
                    }
                }
                // 8.已有则进入下一层，继续寻找
                levelList = obj.children;
            });
        });
        return resList;
    }, []);

    const getEbsTreeData = useCallback(
        async () => {
            dispatch(setEbsTreeData([]));
            if (orgInfo.orgId === "" || curSectionInfo === null) {
                return;
            }
            // 我的任务
            if (businessType === "security.inspection.routing.wbs.myTask") {
                const params = {
                    businessType: "security.inspection.routing.wbs",
                    deptId: orgInfo.orgId,
                    moduleType,
                    nodeId: curSectionInfo?.nodeId ?? "",
                    buildType: curSectionInfo.classification,
                };
                getMyTaskBIndEBSList(params).then((res) => {
                    const ebsTreeData: WbsEbsTreeDataNode[] = [];
                    toArr(res.data).forEach((proj) => {
                        const ebsNode = ebsTreeData.find((ebsItem) => ebsItem.ppid === proj.ppid);
                        const pathChildren = buildComponentTreeWithPaths(proj.path ?? [], proj.ppid as number);
                        if (ebsNode === undefined) {
                            ebsTreeData.push({
                                title: proj.projectName,
                                key: proj.ppid as number,
                                children: pathChildren,
                                ppid: proj.ppid,
                                type: "ebs",
                            });
                        } else {
                            ebsNode.children = pathChildren;
                        }
                    });
                    dispatch(setEbsTreeData(ebsTreeData));
                    dispatch(setIsShow(ebsTreeData.length > 0));
                }).catch(() => {
                    dispatch(setEbsTreeData([]));
                    dispatch(setIsShow(false));
                });
                return;
            }
            const params = {
                deptId: orgInfo.orgId,
                buildType: curSectionInfo.classification ?? 0,
                nodeId: curSectionInfo?.nodeId ?? "",
                businessType,
                moduleType,
            };

            const bindEbsRes = await getBindEbs(params);
            if (bindEbsRes.success && bindEbsRes.data !== undefined && Array.isArray(bindEbsRes.data)) {
                if (toArr(bindEbsRes.data).length !== 0) {
                    const ebsBindWbsParams = {
                        deptId: orgInfo.orgId,
                        nodeId: curSectionInfo.nodeId,
                        list: toArr(bindEbsRes.data)
                    };
                    getEbsBindWbs(ebsBindWbsParams)
                        .then((res) => {
                            const tempEbsBindWbs: RootState["wbsTree"]["ebsBindWbs"] = {};
                            toArr(res.data).forEach((el) => {
                                const key = `${el.ppid}${pathSeparator}${el.path?.split(">").join(pathSeparator)}`;
                                tempEbsBindWbs[key] = el;
                            });
                            dispatch(setEbsBindWbs(tempEbsBindWbs));
                        });
                }
                const filterData = toArr(bindEbsRes.data).filter((el) => Boolean(el.path) === true);
                const ebsTreeData: WbsEbsTreeDataNode[] = [];
                filterData.forEach((proj) => {
                    ebsTreeData.push({
                        title: proj.projectName,
                        key: proj.ppid ?? 0,
                        children: buildComponentTreeWithPaths(proj.path as string[], proj.ppid as number),
                        ppid: proj.ppid,
                        type: "ebs",
                    });
                });
                dispatch(setIsShow(ebsTreeData.length > 0));
                dispatch(setEbsTreeData(ebsTreeData));
            }
        },
        [buildComponentTreeWithPaths, businessType, curSectionInfo, dispatch, moduleType, orgInfo.orgId],
    );

    useEffect(() => {
        getBindWBSIds();
        getEbsTreeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [businessType, curSectionInfo, processType]);

    useEffect(() => {
        if (on?.type === "bindWbsChange") {
            dispatch(emit({type: ""}));
            getBindWBSIds();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [on]);

    useEffect(() => {
        dispatch(setLeafIds(undefined));
        return () => {
            if (autoClose) {
                dispatch(setIsShow(false));
            }
            dispatch(setWbsBindEbs({}));
        };
    }, [autoClose, dispatch]);

    const handleClick = () => {
        if (isShow === true) {
            dispatch(setCheckedKeys(["all"]));
        }

        dispatch(setIsShow(!isShow));
    };

    return (
        <div onClick={handleClick} style={{color: Color.primary, cursor: "pointer"}}>
            {isShow
                ? (
                    <Space style={{textAlign: "end"}}>
                        <MyIcon fontSize={20} type="icon-shanchu" style={{color: Color.primary}} />
                        <div>关联WBS/EBS</div>
                    </Space>
                )
                : (
                    <Space>
                        <MyIcon fontSize={20} type="icon-fenxiang1" style={{color: Color.primary}} />
                        <div>关联WBS/EBS</div>
                    </Space>
                )}
        </div>
    );
};

export default memo(QueryFormBtn);
