export interface PlanTabListType {
    label: string;
    key: string;
    processType: number;
}
// eslint-disable-next-line import/prefer-default-export
export const planTabList: PlanTabListType[] = [
    {label: "待处理的", key: "pendingApproval", processType: 2},
    {label: "我发起的", key: "approvalInitiatedByMe", processType: 1},
    {label: "已处理的", key: "processedApproval", processType: 3},
    {label: "抄送我的", key: "CCMyApproval", processType: 4},
    {label: "全部", key: "allApproval", processType: 6}
];
