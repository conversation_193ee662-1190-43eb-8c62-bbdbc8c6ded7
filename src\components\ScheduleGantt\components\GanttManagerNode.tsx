import {useContext, useEffect} from "react";
import ganttManager from "../gantt/ganttManager";
import EditorContext from "../views/GanttEditor/context";

// 用来保存EditorContext
const GanttManagerNode = () => {
    const editorContext = useContext(EditorContext);

    useEffect(() => {
        ganttManager.editorContext = editorContext;
    }, [editorContext]);

    return null;
};

export default GanttManagerNode;
