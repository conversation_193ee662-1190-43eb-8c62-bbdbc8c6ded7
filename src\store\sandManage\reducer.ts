import {AnyAction} from "redux";
import ActionTypes from "./actionTypes";

export interface StateType {
    playStatus: "playing" | "paused" | "idle";
    playDimension: string;
    playPattern: string[];
    sandTableType: "underConstruction" | "custom";
    authorityTabs: {key: string; label: string}[];
    isLeftExpand: boolean;
    showTodayStatus: boolean;
}

export const initState: StateType = {
    isLeftExpand: true,
    playStatus: "idle", // idle 终止 Playing 播放 Paused 暂停
    playDimension: "procedure", // 播放维度：工序状态 默认状态
    playPattern: ["actual"], // 播放模式
    sandTableType: "underConstruction", // 沙盘类型：施工沙盘 自定义沙盘
    authorityTabs: [], // tabs权限
    showTodayStatus: true,
};

const sandManageReducer = (state = initState, action: AnyAction): StateType => {
    switch (action.type) {
        case ActionTypes.SET_PLAY_DIMENSION:
            return {
                ...state,
                playDimension: action.payload
            };
        case ActionTypes.SET_PLAY_STATUS:
            return {
                ...state,
                playStatus: action.payload
            };
        case ActionTypes.SET_PLAY_PATTERN:
            return {
                ...state,
                playPattern: action.payload
            };
        case ActionTypes.SET_SANDTABLE_TYPE:
            return {
                ...state,
                sandTableType: action.payload
            };
        case ActionTypes.SET_AUTHORITY_TABS:
            return {
                ...state,
                authorityTabs: action.payload
            };
        case ActionTypes.SET_IS_LEFT_EXPAND:
            return {
                ...state,
                isLeftExpand: action.payload
            };
        case ActionTypes.SET_SHOW_TODAYSTATUS:
            return {
                ...state,
                showTodayStatus: action.payload
            };
        default:
            return state;
    }
};

export default sandManageReducer;
