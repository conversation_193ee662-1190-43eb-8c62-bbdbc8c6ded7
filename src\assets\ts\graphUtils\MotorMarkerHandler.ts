import Motor from "@motor/core";
import {isEmpty, isNotNullOrUndefined, uuid} from "../utils";
import {MotorContext} from "../../../reMotor";
import MotorMarkCollectionWrapper from "./MotorMarkCollectionWrapper";
import MotorUtils from "./MotorUtils";

export interface PixelOffsetInfo {
    cartesian2: number[];// 右移为正 下移为正
}

export interface MotorBillboardInfo {
    image: string | undefined; // default: undefined
    show: boolean; // default true
    pixelOffset: PixelOffsetInfo | undefined; // default: (0, 0)
    // eyeOffset: Motor.Vector3, // default (0.0, 0.0, 0.0)
    horizontalOrigin: "LEFT" | "CENTER" | "RIGHT"; // default CENTER
    verticalOrigin: "CENTER" | "BOTTOM" | "BASELINE" | "TOP"; // default: CENTER
    scale: number; // default: 1.0
    color: Motor.MotorCore.ColorSchema | undefined; // default: WHITE
    // rotation: number, // default: 0.0
    // width: number | undefined, // default: undefined
    // height: number | undefined, // default: undefined
    sizeInMeters: boolean;
    disableDepthTestDistance: number;
}

export interface MotorLabelInfo {
    text: string | undefined; // default: undefined
    show: boolean; // default true
    pixelOffset: PixelOffsetInfo | undefined; // default: (0, 0)
    horizontalOrigin: "LEFT" | "CENTER" | "RIGHT"; // default CENTER
    verticalOrigin: "CENTER" | "BOTTOM" | "BASELINE" | "TOP"; // default: CENTER
    scale: number; // default: 1.0
    fillColor: Motor.MotorCore.ColorSchema | undefined; // default: WHITE
    outlineColor: Motor.MotorCore.ColorSchema | undefined; // default: WHITE
    backgroundColor: Motor.MotorCore.ColorSchema | undefined; // default: WHITE
    font: string;
    style: "FILL" | "OUTLINE" | "FILL_AND_OUTLINE";
    outlineWidth: number;
    showBackground: boolean;
    disableDepthTestDistance: number;
}

export interface MotorCartesian3Info {
    id: string;
    type: string;
    point: Motor.Vector3;
    pointLLH: Motor.Vector3 | undefined;

    billBoardInfo: MotorBillboardInfo;
    labelInfo: MotorLabelInfo;
}

export interface MotorCartographic {
    id: string;
    type: string;
    x: number;
    y: number;
    z: number;
    billBoardInfo: MotorBillboardInfo;
    labelInfo: MotorLabelInfo;
}

export interface MarkerSettingsInfo {
    drawPosition: boolean;
    drawPositionLabel: boolean;
    drawPolygon: boolean;
    drawRectangle: boolean;
    height: number;
    extrudedHeight: number;
    positionLabelIsWorldCoordinate: boolean;// 显示世界坐标或者投影坐标
}

export const DefaultMarkerSettingsInfo: MarkerSettingsInfo = {
    drawPosition: true,
    drawPositionLabel: false,
    drawPolygon: false,
    drawRectangle: false,
    height: 0,
    extrudedHeight: 0,
    positionLabelIsWorldCoordinate: true,
};

export const DefaultMotorBillboardInfo: MotorBillboardInfo = {
    image: undefined, // default: undefined
    show: true, // default true
    pixelOffset: undefined, // default: (0, 0)
    horizontalOrigin: "CENTER", // default CENTER
    verticalOrigin: "CENTER", // default: CENTER
    scale: 1.0, // default: 1.0
    color: undefined, // default: WHITE
    sizeInMeters: false,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
};

export const DefaultMotorLabelInfo: MotorLabelInfo = {
    text: undefined, // default: undefined
    show: true, // default true
    pixelOffset: undefined, // default: (0, 0)
    horizontalOrigin: "CENTER", // default CENTER
    verticalOrigin: "CENTER", // default: CENTER
    scale: 0.5, // default: 1.0
    fillColor: {
        rgba: [255, 255, 255, 255]
    },
    outlineColor: {
        rgba: [0, 0, 0, 255]
    },
    backgroundColor: {rgba: [100, 100, 100, 200]},
    font: "36px 微软雅黑",
    style: "FILL_AND_OUTLINE",
    outlineWidth: 2.0,
    showBackground: true,
    disableDepthTestDistance: Number.POSITIVE_INFINITY,
};

class MotorMarkerHandler {
    private name = "";

    private markerCollection: MotorMarkCollectionWrapper | null = null;

    private markerList: MotorCartesian3Info[] = [];

    private settingsInfo: MarkerSettingsInfo = DefaultMarkerSettingsInfo;

    constructor(name = "") {
        this.name = name;
    }

    public static toCartesian(position: MotorCartographic): Motor.Vector3 | undefined {
        return MotorUtils.geoToWorld(position.x, position.y, position.z);
    }

    public static fromCartesian(cartesian: Motor.Vector3 | null, idIn = "", type = ""): MotorCartographic {
        let ptTrans = new Motor.Vector3(0, 0, 0);
        if (cartesian !== null && !(cartesian.x === 0 && cartesian.y === 0 && cartesian.z === 0)) {
            ptTrans = MotorUtils.worldToGeo(cartesian) ?? new Motor.Vector3(0, 0, 0);
        }

        return MotorMarkerHandler.createMotorCartographic(idIn, type, ptTrans.x, ptTrans.y, ptTrans.z);
    }

    public static createMotorCartographic(idIn: string, type: string, longitude: number, latitude: number,
        height: number): MotorCartographic {
        const id = idIn ?? uuid();
        const result: MotorCartographic = {
            id,
            x: longitude,
            y: latitude,
            z: height,
            type,
            billBoardInfo: JSON.parse(JSON.stringify(DefaultMotorBillboardInfo)),
            labelInfo: JSON.parse(JSON.stringify(DefaultMotorLabelInfo))
        };

        result.billBoardInfo.disableDepthTestDistance = DefaultMotorBillboardInfo.disableDepthTestDistance;
        result.labelInfo.disableDepthTestDistance = DefaultMotorLabelInfo.disableDepthTestDistance;
        return result;
    }

    public init(settings: MarkerSettingsInfo = {...DefaultMarkerSettingsInfo}): void {
        if (this.markerCollection === null) {
            const markerCollection = new MotorMarkCollectionWrapper();
            markerCollection.init();
            this.markerCollection = markerCollection;
        } else {
            this.markerList.map((el) => this.markerCollection?.removeById(el.id));
        }

        this.markerList = [];
        this.settingsInfo = settings;
    }

    public reset(): void {
        this.markerList.map((el) => this.markerCollection?.removeById(el.id));
        this.markerList = [];
    }

    public unInit(): void {
        this.markerList.map((el) => this.markerCollection?.removeById(el.id));
        this.markerCollection = null;
        this.markerList = [];
    }

    public getName(): string {
        return this.name;
    }

    public getMarkerList(): MotorCartesian3Info[] {
        return this.markerList;
    }

    public getMarkerCollection(): MotorMarkCollectionWrapper | null {
        return this.markerCollection;
    }

    public addMarker(point: Motor.Vector2): void {
        const viewer = MotorContext.getViewer();
        if (isNotNullOrUndefined(viewer)) {
            viewer.pickPosition(point).then((position) => {
                if (typeof position !== "undefined") {
                    const result: MotorCartesian3Info = {
                        point: new Motor.MotorCore.Vector3(position.x, position.y, position.z),
                        pointLLH: undefined,
                        id: uuid(),
                        type: "",
                        billBoardInfo: {...DefaultMotorBillboardInfo},
                        labelInfo: JSON.parse(JSON.stringify(DefaultMotorLabelInfo))
                    };

                    result.billBoardInfo.disableDepthTestDistance = DefaultMotorBillboardInfo.disableDepthTestDistance;
                    result.labelInfo.disableDepthTestDistance = DefaultMotorLabelInfo.disableDepthTestDistance;
                    this.markerList.push(result);

                    this.drawMarker();
                }
            });
        }
    }

    public addMarkerCartographic(position: MotorCartographic): void {
        this.addMarkerCartographicImpl(position);
        this.drawMarker();
    }

    public batchAddMarkerCartographic(markerList: MotorCartographic[]): void {
        for (let i = 0; i < markerList.length; ++i) {
            this.addMarkerCartographicImpl(markerList[i]);
        }

        this.drawMarker();
    }

    public removeMarker(id: string): void {
        const find = this.markerList.find((el) => el.id === id);
        if (isNotNullOrUndefined(find)) {
            this.markerList = this.markerList.filter((el) => el.id !== id);
        }

        const {markerCollection} = this;
        if (isNotNullOrUndefined(markerCollection)) {
            markerCollection.removeById(id);
            markerCollection.removeById(`${id}_positionLabel`);
        }
    }

    public flyTo(position: MotorCartographic, phi?: number, theta?: number, durationTime?: number, completeFunc?: () => void): void {
        MotorUtils.flyToGeo(position.x, position.y, position.z, phi, theta, durationTime, completeFunc);
    }

    public drawMarker(): void {
        const markerObjList = this.getMarkerObjectList();
        markerObjList.forEach((elem) => {
            const {markerCollection} = this;
            if (isNotNullOrUndefined(markerCollection)) {
                markerCollection.add(elem);
            }
        });
    }

    public getKeyByModelId(modelId: string): string {
        const {markerCollection} = this;
        if (isNotNullOrUndefined(markerCollection)) {
            return markerCollection.getKeyByModelId(modelId) ?? "";
        }

        return "";
    }

    private getMarkerObjectList(): Motor.MotorCore.CZMLSchema[] {
        const resultList: Motor.MotorCore.CZMLSchema[] = [];
        this.markerList.forEach((elem) => {
            this.drawPosition(elem, resultList);
            this.drawPositionLabel(elem, resultList);
        });

        this.drawPolygon(resultList);
        return resultList;
    }

    private drawPosition(elem: MotorCartesian3Info, resultList: Motor.MotorCore.CZMLSchema[]) {
        if (this.settingsInfo.drawPosition) {
            const pointObject = {
                id: elem.id,
                position: {
                    cartesian: [elem.point.x, elem.point.y, elem.point.z]
                },
                billboard: {
                    ...elem.billBoardInfo,
                },
                label: typeof elem.labelInfo.text !== "undefined" ? {...elem.labelInfo} : undefined,
                properties: {
                    type: elem.type,
                }
            };

            // console.log("pointObject", pointObject);
            resultList.push(pointObject);
        }
    }

    private drawPositionLabel(elem: MotorCartesian3Info, resultList: Motor.MotorCore.CZMLSchema[]) {
        if (this.settingsInfo.drawPositionLabel) {
            const cartographic = typeof elem.pointLLH !== "undefined" ? elem.pointLLH : MotorMarkerHandler.fromCartesian(elem.point);
            let positionDisplay = "";
            if (this.settingsInfo.positionLabelIsWorldCoordinate) {
                const cartographicHeight = cartographic.z.toFixed(3);
                positionDisplay = `${cartographic.x.toFixed(6)},${cartographic.y.toFixed(6)},${cartographicHeight}`;
            } else {
                const optResult: Motor.Vector3 = MotorUtils.worldToProjection(elem.point) ?? new Motor.Vector3(0, 0, 0);
                positionDisplay = `${optResult.x.toFixed(3)},${optResult.y.toFixed(3)},${optResult.z.toFixed(3)}`;
            }

            const positionLabel = {
                id: `${elem.id}_positionLabel`,
                position: {
                    cartesian: [elem.point.x, elem.point.y, elem.point.z]
                },
                label: {
                    text: positionDisplay,
                    font: "16px 微软雅黑",
                    scale: 1,
                    style: "FILL",
                    fillColor: {
                        rgba: [0, 0, 0, 255]
                    },
                    outlineColor: {
                        rgba: [0, 0, 255, 200]
                    },
                    outlineWidth: 0,
                    showBackground: true,
                    backgroundColor: {
                        rgba: [255, 255, 255, 255]
                    },
                    verticalOrigin: "BOTTOM",
                    horizontalOrigin: "LEFT",
                    pixelOffset: {
                        cartesian2: [20, 0] // 左移10个像素，上移30个像素
                    },
                    disableDepthTestDistance: Number.POSITIVE_INFINITY // 标注始终在最前
                },
                properties: {
                    type: elem.type,
                }
            };

            resultList.push(positionLabel as Motor.MotorCore.CZMLSchema);
        }
    }

    private drawPolygon(resultList: unknown[]) {
        if (this.markerList.length > 2 && this.settingsInfo.drawPolygon) {
            const pointList: number[] = [];

            this.markerList.forEach((elem) => {
                const cartographic = MotorMarkerHandler.fromCartesian(elem.point);
                pointList.push(cartographic.x);
                pointList.push(cartographic.y);
                pointList.push(cartographic.z);
            });

            const [item] = this.markerList;
            if (typeof item !== "undefined") {
                const uuidKey = isEmpty(item.id) ? uuid() : item.id;
                const type = item.type ?? "";
                const lineObject = {
                    id: `polygon_${uuidKey}`,
                    polygon: {
                        positions: {
                            cartographicDegrees: pointList,
                        },
                        // width: 1,
                        height: this.settingsInfo.height,
                        extrudedHeight: this.settingsInfo.extrudedHeight,
                        closeTop: false,
                        closeBottom: false,
                        material: {
                            solidColor: {
                                color: {
                                    rgba: [55, 250, 161, 50]
                                }
                            }
                        }
                    },
                    properties: {
                        type,
                    }
                };
                resultList.push(lineObject);
            }
        }
    }

    private addMarkerCartographicImpl(position: MotorCartographic): void {
        const pt3d = MotorMarkerHandler.toCartesian(position);
        const find = this.markerList.find((el) => el.id === position.id);
        if (typeof find !== "undefined") {
            find.point = pt3d ?? new Motor.Vector3(0, 0, 0);
            find.pointLLH = new Motor.Vector3(position.x, position.y, position.z);
            find.type = position.type;
            find.billBoardInfo = {...position.billBoardInfo};
            find.labelInfo = {...position.labelInfo};
        } else {
            this.markerList.push({
                point: pt3d ?? new Motor.Vector3(0, 0, 0),
                pointLLH: new Motor.Vector3(position.x, position.y, position.z),
                id: position.id,
                type: position.type,
                billBoardInfo: {...position.billBoardInfo},
                labelInfo: {...position.labelInfo}
            });
        }
    }
}

export default MotorMarkerHandler;
