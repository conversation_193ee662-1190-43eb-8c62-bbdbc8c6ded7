import {createUseStyles} from "react-jss";

const useStyles = createUseStyles({
    detail: {
        th: {
            whiteSpace: "nowrap"
        }
    },
    wrapper: {
        position: "relative"
    },
    conditionFlowChartLegend: {
        position: "absolute",
        right: "0",
        "& i": {
            display: "inline-block",
            width: "8px",
            height: "8px",
            borderRadius: "100%",
            marginRight: "10px"
        }
    },
    legendIconPastNode: {
        backgroundColor: "red"
    },
    legendIconNextNode: {
        backgroundColor: "rgb(81, 125, 206)"
    },
    titleThBox: {
        minWidth: 90
    }
});

export default useStyles;
