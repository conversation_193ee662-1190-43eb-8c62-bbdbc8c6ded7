import React, {useCallback, useEffect, useState} from "react";
import {Helmet} from "react-helmet";
import clsx from "clsx";
import {Col, message, Row, Spin} from "antd";
import {useHistory, useLocation} from "react-router-dom";
import {RouteConfigComponentProps} from "react-router-config";
// 判断环境？写入.env文件数据
import Login from "@iworks/login";
import qs from "query-string";
import {useDispatch, useSelector} from "react-redux";
import {get} from "lodash-es";
import makeRequest from "./makeRequest";
// import {getInnerSystemToken} from "../../api/common.api";
import {resetCommon, setMenuId, setOrgInfo, setLastSelectedMenu, setFrom} from "../../store/common/action";

import {RootState} from "../../store/rootReducer";
import {getOrgInfo} from "../../api/common.api";

import routeList from "../../router";
import {getRoutePath} from "../../utils/common";
import {AppUrlSearchType, ParamsInfo} from "../../assets/ts/globalType";
import {goToLoginPage, parseUrl} from "../../assets/ts/utils";
import useLoginStyle from "./style";

// 平台Platform结构定义
interface PlatformObj {token?: string}
// login信息结构定义
interface LoginObj {
    login: {
        request: {
            login?: {};
            getCompany?: {};
            setCompany?: {};
        };
    };
}

const WrappedLogin = (_props: RouteConfigComponentProps) => {
    const {tab, loading} = useLoginStyle();
    const {orgInfo} = useSelector((state: RootState) => state.commonData);

    const [btnLoading, setBtnLoading] = useState<boolean>(false);
    const [LoadingWithParam, setLoadingWithParam] = useState<boolean>(true);
    const {search} = useLocation();
    const dispatch = useDispatch();
    const history = useHistory();

    const removeOldData = useCallback(() => {
        localStorage.removeItem("token");
        localStorage.removeItem("epid");
        dispatch(resetCommon()); // 清空store
    }, [dispatch]);
    useEffect(() => {
        // 登陆之前先清掉cookie， 重置redux
        removeOldData();
    }, [removeOldData]);


    const onLoginSuccess = useCallback((info: {token: string; epid: number}, pathname?: string|undefined) => {
        localStorage.setItem("token", info.token);
        localStorage.setItem("epid", String(info.epid));
        (window.PlatformHeaderConfig as PlatformObj).token = info.token;
        if (pathname !== undefined) {
            history.push(pathname);
        } else {
            history.push("/main");
        }
    }, [history]);

    // 根据项目id来设置项目相关信息
    const setOrgInfoById = useCallback(async (deptId?: string) => {
        if (deptId === undefined || deptId.length === 0) {
            return;
        }
        const {success, data} = await getOrgInfo(deptId);
        if (success !== true || data === undefined || data === null) {
            return;
        }
        const tempOrgInfo = {
            orgId: data.id,
            orgType: data.type,
            orgName: data.name,
            deptDataType: data.deptDataType,
        };
        dispatch(setOrgInfo(tempOrgInfo));
    }, [dispatch]);

    useEffect(() => {
        const urlSearch: AppUrlSearchType = qs.parse(window.location.href.split("?")[1]);
        const {menuPath, token, epid, menuId} = urlSearch;
        let tempMenuId = (menuId ?? "")?.length > 0 ? urlSearch.menuId : "2691";
        if (urlSearch.menuId?.includes("-") === true) {
            [, tempMenuId] = urlSearch.menuId.split("-");
        }
        dispatch(setMenuId(Number(tempMenuId)));
        dispatch(setFrom(urlSearch.from ?? ""));

        dispatch(setLastSelectedMenu(undefined));
        // console.log(routeList, "routeList");
        // 判断新框架免登录，支持token免登录
        if (urlSearch.from === "newStandard" && (urlSearch.token ?? "") === "") {
            // 有的时候穿过的的参数并没有token
            goToLoginPage();
        }
        if (urlSearch.from !== undefined && urlSearch.token !== undefined && urlSearch.epid !== undefined) {
            const pathname = menuPath !== undefined ? getRoutePath(menuPath, routeList as []) : undefined;
            onLoginSuccess({
                token: urlSearch.token,
                epid: Number(urlSearch.epid),

            }, pathname);
            if ((urlSearch.orgId ?? "").length > 0) {
                setOrgInfoById(urlSearch.orgId);
            }
            // console.log("WrappedLogin setOrgInfo 2", urlSearch.orgId);
            // setOrgInfoById(urlSearch.orgId);
            // getInnerSystemToken(urlSearch.token, productId).then(
            //     (res) => {
            //         onLoginSuccess({
            //             token: res.data,
            //             epid: Number(urlSearch.epid),
            //         });
            //         dispatch(setOrgInfo({
            //             orgId: urlSearch.orgId ?? "",
            //             orgName: "",
            //             orgType: 1,
            //             deptDataType: 2
            //         }));
            //     }
            // );
        } else if ((token ?? "") !== "") { // 携带路由跳转到指定模块,token值存在
            const pathname = menuPath !== undefined ? getRoutePath(menuPath, routeList as []) : undefined;
            onLoginSuccess({
                token: token ?? "",
                epid: Number(epid),

            }, pathname);
            if ((urlSearch.orgId ?? "").length > 0) {
                setOrgInfoById(urlSearch.orgId);
            }
        } else {
            // 主要是避免这个免登录中间登录页面一闪而过
            setLoadingWithParam(false);
        }
    }, [dispatch, onLoginSuccess, orgInfo, setOrgInfoById]);


    const onLogin = useCallback((info) => {
        const {code, msg} = info;
        if (code !== 200) {
            message.error(msg);
            setLoadingWithParam(false);
            return;
        }
        // dispatch(setUserInfo(info));
        setBtnLoading(true);
        onLoginSuccess({
            token: info.loginResponse,
            epid: info.data.epid
        });
    }, [onLoginSuccess]);

    useEffect(() => {
        const onFetchLogin = async (loginParam: ParamsInfo) => {
            const requestParams = (get(window, "__IWorksConfig__") as unknown as LoginObj).login.request.login;
            const params = {
                username: loginParam.username,
                password: loginParam.password.toLowerCase()
            };

            return makeRequest(params, requestParams as string);
        };

        const onFetchGetCompanyList = async (values: Record<string, unknown>) => {
            const requestParams = (get(window, "__IWorksConfig__") as unknown as LoginObj).login?.request.getCompany;
            return makeRequest(values, requestParams as string);
        };

        const onFetchSetCurCompany = async (values: Record<string, unknown>) => {
            const requestParams = (get(window, "__IWorksConfig__") as unknown as LoginObj).login.request.setCompany;
            return makeRequest(values, requestParams as string);
        };

        const onLoginInWithPassword = (params: ParamsInfo) => {
            setLoadingWithParam(true);
            onFetchLogin(params).then((resLogin) => {
                const {success, data} = resLogin;
                if (success === true) {
                    const loginInfo = {...params, loginResponse: data};

                    onFetchGetCompanyList(loginInfo).then((resCompany) => {
                        if (resCompany.code !== 200) {
                            message.error(resCompany.msg); // 请求失败时弹出错误提示
                            setLoadingWithParam(false);
                            return;
                        }
                        if (resCompany.data.length === 1 && params.enterpriseId === resCompany.data[0].epid) { // 企业中列表只有一个企业时
                            const paramsSetCompany = {
                                epid: params.enterpriseId,
                                ...loginInfo
                            };
                            onFetchSetCurCompany(paramsSetCompany).then((resSetCompany) => {
                                if (resSetCompany.code !== 200) {
                                    message.error(resSetCompany.msg);
                                    onLogin({}); // 清除数据防止再次请求时参数错误
                                    setLoadingWithParam(false);
                                    return;
                                }
                                const loginDatas = {...resSetCompany, ...loginInfo};
                                onLogin(loginDatas);
                            });
                        }
                    });
                } else {
                    setLoadingWithParam(false);
                }
            });
        };
        // 支持账号密码免登录
        const paramsObj = parseUrl(decodeURI(search));
        if (paramsObj.username !== "" && paramsObj.password !== "" && paramsObj.enterpriseId !== 0) {
            removeOldData();
            onLoginInWithPassword(paramsObj);
        }
    }, [onLogin, removeOldData, search]);

    return (
        <>
            <Helmet>
                <title>{window.titleConfig?.planWeb}</title>
            </Helmet>
            {
                LoadingWithParam === true
                    ? (
                        <div className={clsx(loading)} style={{display: LoadingWithParam === true ? "block" : "none"}}>
                            <Row justify="center" align="middle" style={{textAlign: "center"}}>
                                <Col>
                                    <Spin size="large" />
                                </Col>
                            </Row>
                        </div>
                    )
                    : (
                        <>
                            <div className={clsx(loading)} style={{display: btnLoading === true ? "block" : "none"}} />
                            {" "}
                            {/* / 防止网络慢导致按钮重复点击/ */}
                            <div className={clsx(tab)}>
                                <Login
                                    onLogin={onLogin}
                                    renderFooter={() => (
                                        <div style={{color: "#606266", fontSize: 12}}>
                                            ©2010-2022 鲁班软件股份有限公司版权所有
                                            <br />
                                            <a style={{color: "#606266"}} href="https://beian.miit.gov.cn/" rel="noopener noreferrer" target="blank">沪ICP备09024855号-30</a>
                                        </div>
                                    )}
                                />
                            </div>
                        </>
                    )
            }
        </>
    );
};

export default WrappedLogin;
