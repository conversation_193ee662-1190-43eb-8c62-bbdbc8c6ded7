import {CompAttrResult} from "../../api/comAttr/type";
import {CompInfoNode, CompAttrItem} from "./interface";

const parserCompInfo = (info?: CompAttrResult): CompInfoNode[] => {
    if (info === undefined || info === null) {
        return [];
    }
    const attrGroups: CompInfoNode[] = (info?.attrGroups ?? []).map((item) => {
        const node: CompInfoNode = {
            groupName: item.groupName ?? "",
            it: -1,
            cpk: item.groupKey,
            attrList: [],
        };
        const attrList = (item?.attrItems ?? []).map((aItem) => {
            const itemInfo: CompAttrItem = {
                it: -1,
                ik: aItem.name ?? "",
                iv: aItem.value ?? "",
            };
            return itemInfo;
        });
        return {...node, attrList};
    });

    if (info.attr === null || info.attr === undefined) {
        return attrGroups;
    }
    const customGroups: CompInfoNode[] = (info?.attr?.subList ?? []).map((item) => {
        const node: CompInfoNode = {
            groupName: item.ik ?? "",
            it: item.it!,
            pv: item.pv,
            cpk: item.cpk,
            attrList: [],
        };
        const attrList = (item?.subList ?? []).map((aItem) => {
            const itemInfo: CompAttrItem = {
                it: aItem.it!,
                ik: aItem.ik ?? "",
                iv: aItem.iv ?? "",
                pv: aItem.pv,
                cpk: aItem.cpk,
                docInfo: aItem.docInfo
            };
            return itemInfo;
        });
        return {...node, attrList};
    });
    const array = attrGroups.concat(customGroups);
    return array;
};

export default parserCompInfo;
