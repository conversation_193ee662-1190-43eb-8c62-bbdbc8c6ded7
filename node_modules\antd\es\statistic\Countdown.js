import _extends from "@babel/runtime/helpers/esm/extends";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _inherits from "@babel/runtime/helpers/esm/inherits";
import _createSuper from "@babel/runtime/helpers/esm/createSuper";
import * as React from 'react';
import Statistic from './Statistic';
import { formatCountdown } from './utils';
import { cloneElement } from '../_util/reactNode';
var REFRESH_INTERVAL = 1000 / 30;

function getTime(value) {
  return new Date(value).getTime();
}

var Countdown = /*#__PURE__*/function (_React$Component) {
  _inherits(Countdown, _React$Component);

  var _super = _createSuper(Countdown);

  function Countdown() {
    var _this;

    _classCallCheck(this, Countdown);

    _this = _super.apply(this, arguments);

    _this.syncTimer = function () {
      var value = _this.props.value;
      var timestamp = getTime(value);

      if (timestamp >= Date.now()) {
        _this.startTimer();
      } else {
        _this.stopTimer();
      }
    };

    _this.startTimer = function () {
      if (_this.countdownId) return;
      var _this$props = _this.props,
          onChange = _this$props.onChange,
          value = _this$props.value;
      var timestamp = getTime(value);
      _this.countdownId = window.setInterval(function () {
        _this.forceUpdate();

        if (onChange && timestamp > Date.now()) {
          onChange(timestamp - Date.now());
        }
      }, REFRESH_INTERVAL);
    };

    _this.stopTimer = function () {
      var _this$props2 = _this.props,
          onFinish = _this$props2.onFinish,
          value = _this$props2.value;

      if (_this.countdownId) {
        clearInterval(_this.countdownId);
        _this.countdownId = undefined;
        var timestamp = getTime(value);

        if (onFinish && timestamp < Date.now()) {
          onFinish();
        }
      }
    };

    _this.formatCountdown = function (value, config) {
      var format = _this.props.format;
      return formatCountdown(value, _extends(_extends({}, config), {
        format: format
      }));
    }; // Countdown do not need display the timestamp
    // eslint-disable-next-line class-methods-use-this


    _this.valueRender = function (node) {
      return cloneElement(node, {
        title: undefined
      });
    };

    return _this;
  }

  _createClass(Countdown, [{
    key: "componentDidMount",
    value: function componentDidMount() {
      this.syncTimer();
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate() {
      this.syncTimer();
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.stopTimer();
    }
  }, {
    key: "render",
    value: function render() {
      return /*#__PURE__*/React.createElement(Statistic, _extends({
        valueRender: this.valueRender
      }, this.props, {
        formatter: this.formatCountdown
      }));
    }
  }]);

  return Countdown;
}(React.Component);

Countdown.defaultProps = {
  format: 'HH:mm:ss'
};
export default Countdown;