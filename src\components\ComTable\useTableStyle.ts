import {createUseStyles} from "react-jss";

const useTableStyle = createUseStyles((_theme) => ({
    // excel模版样式
    excelTempate: {
        "& .ant-checkbox-inner": {
            borderRadius: "2px"
        },
        "& .ant-table-title": {
            padding: 0,
            border: 0,
        },
        // 表头透明
        "& .ant-table-thead .ant-table-cell": {
            // background: "#fff !important"
        },
        "& .darkRow:not(.ant-table-row-selected) .ant-table-cell": {
            background: "#fff !important"
        },
        "& th.ant-table-cell,td.ant-table-cell": {
            borderColor: "#E1E2E5"
        },
        "& .ant-table-cell": {
            // textAlign: "center !important",
            // height: "45px !important",
        }
    },
    boldHeaderCell: {
        "& .ant-table-container > .ant-table-header > table > thead > tr > th": {
            fontWeight: "bold !important",
        },
        "& .ant-table-container table  thead  tr  th": {
            fontWeight: "bold !important",
        }
    },
    customTable: {
        "& .ant-table-title": {
            padding: "0 !important"
        },
        "& .ant-table-row": {
            background: "#fff !important"
        },
        "& td.ant-table-cell": {
            padding: "6px 8px !important"
        },
        "& th.ant-table-cell": {
            fontWeight: "bold !important"
        }

    },
    standardTable: {
        // lineHeight: "0 !important",
        // "& .ant-table.ant-table-bordered > .ant-table-container": {
        //     border: "0 !important"
        // },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table, .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table": {
            borderColor: "#E1E2E5 !important",
        },
        "& .ant-table-tbody .ant-table-row:nth-child(odd)": {
            background: "#fff"
        },
        "& .ant-table.ant-table-bordered > .ant-table-title": {
            border: "0px !important"
        },
        "& .ant-table.ant-table-bordered > .ant-table-container": {
            borderColor: "#E1E2E5 !important",
            border: "0 !important"
        },
        "& th.ant-table-cell": {
            background: "#F5F5F6 !important",
            borderColor: "#E1E2E5 !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td:first-child": {
            borderLeft: "1px solid #E1E2E5 !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th": {
            borderColor: "#E1E2E5 !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > .ant-table-cell-fix-right-first::after": {
            borderColor: "#E1E2E5 !important",
        },
        "&  .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > .ant-table-cell-fix-right-first::after": {
            borderColor: "#E1E2E5 !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th:first-child": {
            borderLeft: "1px solid #E1E2E5 !important",
        },
        // .ant-table-container table > thead > tr:first-child th:first-child
        "& .ant-table.ant-table-bordered > .ant-table-container table > thead > tr > th:first-child": {
            borderLeft: "1px solid #E1E2E5 !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td": {
            borderColor: "#E1E2E5 !important",
        },

        "& .ant-table-container > .ant-table-header > table > thead > tr": {
            background: "#F5F5F6 !important"
        },
        "& .ant-table-container  table  thead  tr": {
            background: "#F5F5F6 !important"
        },
        "& .ant-table-container > .ant-table-header > table > thead > tr > th": {
            textAlign: "center !important",
            height: "40px !important",
            padding: "8px 12px !important"
        },
        "& .ant-table-container table thead tr th": {
            textAlign: "center !important",
            height: "40px !important",
            padding: "8px 12px !important"
        },
        "& .ant-table.ant-table-middle .ant-table-tbody > tr[aria-hidden='true'] > td": {
            padding: "0 !important",
        },
        "& .ant-table.ant-table-middle .ant-table-tbody > tr > td": {
            padding: "3px 12px !important",
        },
        "& td.ant-table-cell": {
            padding: "3px 12px !important",
        },
        "& .ant-table.ant-table-middle .ant-table-tbody > tr:not(::first-child) > td:last-child": {
            padding: "3px 0px !important",
        },
        "& .ant-table-tbody .ant-table-cell": {
            height: "40px !important",
            // padding: "4px 12px !important",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td": {
            borderRight: "1px solid #E1E2E5",
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td:first-child": {
            borderLeft: "1px solid #E1E2E5",
        },
        "& .ant-table-row:hover .ant-table-cell": {
            backgroundColor: "#E9EFFC !important",
            // backgroundColor: "#FEF6E6 !important", // 新增底色
        },
        "& .ant-table-tbody > tr.ant-table-row-selected > td": {
            backgroundColor: "#E9EFFC !important",
        },
        "& .ant-table .ant-table-expanded-row-fixed": {
            height: "100%"
        },
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td > .ant-table-expanded-row-fixed::after": {
            borderColor: "#E1E2E5 !important",
        },
        "& .ant-table-tbody > tr.ant-table-placeholder td": {
            padding: "0 !important"
        },
        // "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body": {
        //     borderRight: "1px solid #E1E2E5 !important",
        // }
    },
    maxHeight350: {
        "& .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body ": {
            maxHeight: "350px !important",
        },
    }

}));


export default useTableStyle;
