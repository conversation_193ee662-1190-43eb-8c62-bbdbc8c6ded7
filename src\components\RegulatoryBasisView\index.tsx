import {<PERSON><PERSON>, <PERSON>u, message} from "antd";
import React, {useState, useCallback} from "react";
import {ColumnType} from "antd/lib/table";
import {createUseStyles} from "react-jss";
import ComDrawer from "../ComDrawer";
import {querySecurityListSatuteGist, queryQualityListReference} from "../../api/hiddenDangerCheck";
import ComTable from "../ComTable";
import {SecurityListSatuteGistType, QualityListReferenceType, QueryQualityListReferenceReturn} from "../../api/hiddenDangerCheck/type";
import {getSecurityColumns, getQualityColumns} from "./data";
import MyIcon from "../MyIcon";
import {getSuffix, isImg, isVideo} from "../../assets/ts/utils";
import {getPreviewUrl, getFileUrl} from "../../api/common.api";
import {dispatch} from "../../store";
import {setPreviewUrl} from "../../store/common/action";
import Color from "../../assets/css/Color";

export interface RegulatoryBasisViewProps {
    ids: string[];
    moduleType: string;
}
const useStyle = createUseStyles({
    eyesBtn: {
        alignSelf: "center",
        borderRadius: "50%",
        "&:hover .anticon": {
            color: `${Color.primary} !important`,
        },
        "& .anticon": {
            fontSize: "16px !important",
        }
    },
    statuteBox: {
        "& .ant-drawer-header-title .ant-drawer-close": {
            position: "absolute",
            right: 0
        },
        "& .qualityBox": {
            display: "flex",
            overflow: "hidden",
            height: "100%",
            "& .ant-menu": {
                overflowY: "scroll",
                padding: "24px 18px 18px 24px",
                flex: "270px 0",
                "& .ant-menu-item": {
                    color: Color["text-2"],
                    borderRadius: 4
                },
                "& .ant-menu-item-selected, & .ant-menu-item-active, & .ant-menu-item:active": {
                    backgroundColor: Color["bg-4"],
                    color: Color["text-1"],
                    fontWeight: "bolder"
                },
            },
            "& .ant-table-wrapper": {
                flex: "1 0",
                height: "100%",
                margin: 24
            }
        }
    },
});

const RegulatoryBasisView = (props: RegulatoryBasisViewProps) => {
    const {ids, moduleType = "SECURITY"} = props;
    const cls = useStyle();
    const [visible, setVisible] = useState(false);
    const [columns, setColumns] = useState<ColumnType<QualityListReferenceType>[]>([]);
    const [securityStatuteList, setSecurityStatuteList] = useState<SecurityListSatuteGistType[]>([]);
    const [qualityFileList, setQualityFileList] = useState<QualityListReferenceType[]>([]);
    const [allQualityStatuteData, setAllQualityStatuteData] = useState<QueryQualityListReferenceReturn[]>([]);
    const [selectedKeys, setselectedKeys] = useState<string[]>([]);
    const getSecurityList = useCallback(() => {
        querySecurityListSatuteGist(ids).then((res) => {
            let list: SecurityListSatuteGistType[] = [];
            res.result.forEach((item) => {
                list = [...list, ...item.referenceSatutes ?? []];
            });
            setSecurityStatuteList(list);
        });
    }, [ids]);
    const onInspectItemChange = useCallback((info) => {
        setQualityFileList(allQualityStatuteData.find((el) => el.id === info.key)?.referenceFiles ?? []);
        setselectedKeys([info.key]);
    }, [allQualityStatuteData]);
    const getQualityList = useCallback(() => {
        queryQualityListReference(ids).then((res) => {
            setAllQualityStatuteData(res.result ?? []);
            setselectedKeys([(res.result ?? [])[0].id ?? ""]);
        });
    }, [ids]);

    const handleView = useCallback((record: QualityListReferenceType) => {
        if (Boolean(record.uuid) === false) {
            message.warning("无权限查看!");
        }
        const previewFile = ["pdf", "docx", "xlsx", "doc", "xls", "ppt", "pptx", "txt"];
        if (previewFile.includes(getSuffix(record.name))) {
            getPreviewUrl({fileName: record.name, uuid: record.uuid}).then((res) => {
                if (typeof res === "string") {
                    dispatch(
                        setPreviewUrl({
                            url: res,
                            name: record.name
                        })
                    );
                } else {
                    const {message: resMessage} = res as {message: string};
                    message.warning(resMessage);
                }
            });
        } else if (isImg(record.name) || isVideo(record.name)) {
            getFileUrl([record.uuid])
                .then((res) => {
                    if (res.length > 0 && res[0].downloadUrls.length > 0) {
                        dispatch(
                            setPreviewUrl({
                                url: res[0].downloadUrls[0],
                                name: record.name
                            })
                        );
                    }
                })
                .catch((err: unknown) => {
                    console.log(err);
                });
        } else {
            getFileUrl([record.uuid])
                .then((res) => {
                    if (res.length > 0 && res[0].downloadUrls.length > 0) {
                        saveAs(res[0].downloadUrls[0], record.name);
                    }
                })
                .catch((err: unknown) => {
                    console.log(err);
                });
        }
    }, []);
    React.useEffect(() => {
        if (visible) {
            if (moduleType === "SECURITY") {
                getSecurityList();
            } else {
                getQualityList();
            }
        }
    }, [getQualityList, getSecurityList, moduleType, visible]);
    const qualityFileColumns: ColumnType<QualityListReferenceType>[] = React.useMemo(() => [
        ...getQualityColumns(),
        {
            key: "id",
            title: "操作",
            dataIndex: "id",
            align: "center",
            width: 52,
            render: (_id: string, record) => <Button type="link" onClick={() => handleView(record)} style={{padding: 0}}>查看</Button>
        }
    ], [handleView]);
    React.useEffect(() => {
        setColumns(qualityFileColumns);
    }, [qualityFileColumns]);
    return (
        <div style={{position: "relative", height: "100%", display: "flex"}}>
            <Button
                disabled={ids.length === 0}
                onClick={() => setVisible(true)}
                type="text"
                icon={<MyIcon type="icon-xianshi" />}
                style={{padding: "2.4px 0"}}
                className={cls.eyesBtn}
            />
            <ComDrawer
                visible={visible}
                title="法规依据"
                onOk={() => setVisible(false)}
                width={720}
                footer={null}
                closable
                className={cls.statuteBox}
            >
                {moduleType === "SECURITY" && (
                    <ComTable
                        columns={getSecurityColumns()}
                        dataSource={securityStatuteList}
                        bordered
                        style={{height: "calc(100% - 48px)", margin: 24}}
                    />
                )}
                {moduleType === "QUALITY" && (
                    <div className="qualityBox">
                        <Menu onClick={onInspectItemChange} selectedKeys={selectedKeys}>
                            {allQualityStatuteData.map((item) => <Menu.Item key={item.id}>{item.content}</Menu.Item>)}
                        </Menu>
                        <ComTable columns={columns} dataSource={qualityFileList} />
                    </div>
                )}
            </ComDrawer>
        </div>
    );
};

export default RegulatoryBasisView;
