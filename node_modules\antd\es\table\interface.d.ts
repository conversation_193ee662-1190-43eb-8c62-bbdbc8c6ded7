import * as React from 'react';
import { Get<PERSON><PERSON><PERSON><PERSON>, ColumnType as RcColumnType, RenderedCell as Rc<PERSON><PERSON>ed<PERSON>ell, ExpandableConfig, FixedType } from 'rc-table/lib/interface';
import { TooltipProps } from '../tooltip';
import { CheckboxProps } from '../checkbox';
import { PaginationProps } from '../pagination';
import { Breakpoint } from '../_util/responsiveObserve';
import { INTERNAL_SELECTION_ITEM } from './hooks/useSelection';
export { GetRowKey, ExpandableConfig };
export declare type Key = React.Key;
export declare type RowSelectionType = 'checkbox' | 'radio';
export declare type SelectionItemSelectFn = (currentRowKeys: Key[]) => void;
export declare type ExpandType = null | 'row' | 'nest';
export interface TableLocale {
    filterTitle?: string;
    filterConfirm?: React.ReactNode;
    filterReset?: React.ReactNode;
    filterEmptyText?: React.ReactNode;
    filterCheckall?: React.ReactNode;
    filterSearchPlaceholder?: string;
    emptyText?: React.ReactNode | (() => React.ReactNode);
    selectAll?: React.ReactNode;
    selectNone?: React.ReactNode;
    selectInvert?: React.ReactNode;
    selectionAll?: React.ReactNode;
    sortTitle?: string;
    expand?: string;
    collapse?: string;
    triggerDesc?: string;
    triggerAsc?: string;
    cancelSort?: string;
}
export declare type SortOrder = 'descend' | 'ascend' | null;
declare const TableActions: ["paginate", "sort", "filter"];
export declare type TableAction = typeof TableActions[number];
export declare type CompareFn<T> = (a: T, b: T, sortOrder?: SortOrder) => number;
export interface ColumnFilterItem {
    text: React.ReactNode;
    value: string | number | boolean;
    children?: ColumnFilterItem[];
}
export interface ColumnTitleProps<RecordType> {
    /** @deprecated Please use `sorterColumns` instead. */
    sortOrder?: SortOrder;
    /** @deprecated Please use `sorterColumns` instead. */
    sortColumn?: ColumnType<RecordType>;
    sortColumns?: {
        column: ColumnType<RecordType>;
        order: SortOrder;
    }[];
    filters?: Record<string, string[]>;
}
export declare type ColumnTitle<RecordType> = React.ReactNode | ((props: ColumnTitleProps<RecordType>) => React.ReactNode);
export declare type FilterValue = (Key | boolean)[];
export declare type FilterKey = Key[] | null;
export interface FilterConfirmProps {
    closeDropdown: boolean;
}
export interface FilterDropdownProps {
    prefixCls: string;
    setSelectedKeys: (selectedKeys: React.Key[]) => void;
    selectedKeys: React.Key[];
    confirm: (param?: FilterConfirmProps) => void;
    clearFilters?: () => void;
    filters?: ColumnFilterItem[];
    visible: boolean;
}
export interface ColumnType<RecordType> extends RcColumnType<RecordType> {
    title?: ColumnTitle<RecordType>;
    sorter?: boolean | CompareFn<RecordType> | {
        compare?: CompareFn<RecordType>;
        /** Config multiple sorter order priority */
        multiple?: number;
    };
    sortOrder?: SortOrder;
    defaultSortOrder?: SortOrder;
    sortDirections?: SortOrder[];
    showSorterTooltip?: boolean | TooltipProps;
    filtered?: boolean;
    filters?: ColumnFilterItem[];
    filterDropdown?: React.ReactNode | ((props: FilterDropdownProps) => React.ReactNode);
    filterMultiple?: boolean;
    filteredValue?: FilterValue | null;
    defaultFilteredValue?: FilterValue | null;
    filterIcon?: React.ReactNode | ((filtered: boolean) => React.ReactNode);
    filterMode?: 'menu' | 'tree';
    filterSearch?: boolean;
    onFilter?: (value: string | number | boolean, record: RecordType) => boolean;
    filterDropdownVisible?: boolean;
    onFilterDropdownVisibleChange?: (visible: boolean) => void;
    responsive?: Breakpoint[];
}
export interface ColumnGroupType<RecordType> extends Omit<ColumnType<RecordType>, 'dataIndex'> {
    children: ColumnsType<RecordType>;
}
export declare type ColumnsType<RecordType = unknown> = (ColumnGroupType<RecordType> | ColumnType<RecordType>)[];
export interface SelectionItem {
    key: string;
    text: React.ReactNode;
    onSelect?: SelectionItemSelectFn;
}
export declare type SelectionSelectFn<T> = (record: T, selected: boolean, selectedRows: T[], nativeEvent: Event) => void;
export interface TableRowSelection<T> {
    /** Keep the selection keys in list even the key not exist in `dataSource` anymore */
    preserveSelectedRowKeys?: boolean;
    type?: RowSelectionType;
    selectedRowKeys?: Key[];
    defaultSelectedRowKeys?: Key[];
    onChange?: (selectedRowKeys: Key[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => Partial<Omit<CheckboxProps, 'checked' | 'defaultChecked'>>;
    onSelect?: SelectionSelectFn<T>;
    onSelectMultiple?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    /** @deprecated This function is meaningless and should use `onChange` instead */
    onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    /** @deprecated This function is meaningless and should use `onChange` instead */
    onSelectInvert?: (selectedRowKeys: Key[]) => void;
    onSelectNone?: () => void;
    selections?: INTERNAL_SELECTION_ITEM[] | boolean;
    hideSelectAll?: boolean;
    fixed?: FixedType;
    columnWidth?: string | number;
    columnTitle?: string | React.ReactNode;
    checkStrictly?: boolean;
    renderCell?: (value: boolean, record: T, index: number, originNode: React.ReactNode) => React.ReactNode | RcRenderedCell<T>;
}
export declare type TransformColumns<RecordType> = (columns: ColumnsType<RecordType>) => ColumnsType<RecordType>;
export interface TableCurrentDataSource<RecordType> {
    currentDataSource: RecordType[];
    action: TableAction;
}
export interface SorterResult<RecordType> {
    column?: ColumnType<RecordType>;
    order?: SortOrder;
    field?: Key | readonly Key[];
    columnKey?: Key;
}
export declare type GetPopupContainer = (triggerNode: HTMLElement) => HTMLElement;
declare type TablePaginationPosition = 'topLeft' | 'topCenter' | 'topRight' | 'bottomLeft' | 'bottomCenter' | 'bottomRight';
export interface TablePaginationConfig extends PaginationProps {
    position?: TablePaginationPosition[];
}
