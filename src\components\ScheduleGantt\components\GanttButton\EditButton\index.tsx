import React, {useCallback, useContext, useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {<PERSON><PERSON>, Modal} from "antd";
import {WebRes} from "../../../../../api/common.type";
import {RootState} from "../../../../../store/rootReducer";
import PermissionCode from "../../../../PermissionCode";
import {resourceLock} from "../../../api/common";
import {ResourceLockRes} from "../../../api/common/type";
import EditorContext from "../../../views/GanttEditor/context";
import {setGanttEditable} from "../../../gantt/ganttConfig";
import useStyles from "../styles";
import {ApprovalStatus} from "../../../../../api/Preparation/type";

const EditButton = () => {
    const cls = useStyles();
    const {userInfo} = useSelector((store: RootState) => store.commonData);
    const [checkoutLoading, setCheckoutLoading] = useState<boolean>(false);
    const {planInfo, fromType, enterType, checkoutMethodRef, checkoutStatus} = useContext(EditorContext);
    const autoCheckouted = useRef<boolean>(false); // 是否自动签出过
    const editAuthCode = fromType === "actual" ? "ProjectPlatform-Plan-Actual-Progress:edit" : "ProjectPlatform-Plan-Progress-Establishment:edit";

    const isCheckoutSuccess = useCallback((response: WebRes<ResourceLockRes>) => {
        const checkoutUser = response.data.lockUser;
        if (response.data.lockSucceed === true || userInfo.username === checkoutUser) {
            return true;
        }
        // 签出失败后，提示失败信息
        Modal.warning({
            content: checkoutUser !== undefined
                ? `${checkoutUser} 正在编辑当前进度计划，请稍候重试！`
                : "当前进度计划正在被编辑，请稍候重试！"
        });
        return false;
    }, [userInfo.username]);

    const fetchCheckout = useCallback(async () => {
        if (planInfo.id === undefined) {
            return;
        }
        try {
            setCheckoutLoading(true);
            const response: WebRes<ResourceLockRes> = await resourceLock({
                businessId: planInfo.id,
                businessType: fromType === "actual" ? "ACTUAL" : "PLAN",
                leaseTime: -1,
            });
            if (response.success) {
                if (checkoutMethodRef.current instanceof Function) {
                    checkoutMethodRef.current(isCheckoutSuccess(response));
                }
            }
        } catch (error) {
            console.log("fetchCheckout error", error);
        } finally {
            setCheckoutLoading(false);
        }
    }, [planInfo.id, fromType, checkoutMethodRef, isCheckoutSuccess]);

    const handleCheckout = useCallback(() => {
        fetchCheckout();
    }, [fetchCheckout]);

    // 编辑计划自动签出
    useEffect(() => {
        setGanttEditable(false);
        if (enterType === "edit" && autoCheckouted.current === false) {
            handleCheckout();
            autoCheckouted.current = true;
        }
    }, [enterType, handleCheckout]);

    if (checkoutStatus === true) {
        return null;
    }

    if (fromType === "plan" && (planInfo.approvalStatus === ApprovalStatus.APPROVING || planInfo.approvalStatus === ApprovalStatus.APPROVAL_COMPLETED)) {
        return null;
    }

    return (
        <PermissionCode
            authcode={editAuthCode}
        >
            <Button
                className={cls.button}
                type="primary"
                size="large"
                onClick={handleCheckout}
                loading={checkoutLoading}
            >
                编辑计划
            </Button>
        </PermissionCode>
    );
};

export default EditButton;
