import {ColumnsType} from "antd/lib/table";
import {DangerListType} from "../../api/danger/type";
import {momentText} from "../../assets/ts/utils";
import renderTableText from "../renderTableText";

// eslint-disable-next-line import/prefer-default-export
export const columns: ColumnsType<DangerListType> = [
    {
        title: "序号",
        align: "center",
        width: 80,
        fixed: "left",
        render: (_text: unknown, _records: DangerListType, index: number) => index + 1
    },
    {
        key: "name",
        title: "风险名称",
        dataIndex: "name",
        align: "left",
        fixed: "left",
        render: renderTableText,
    },
    {
        key: "checkTime",
        title: "检查日期",
        dataIndex: "checkTime",
        align: "center",
        width: 120,
        render: (_text: string | number) => momentText(_text)
    },
];
