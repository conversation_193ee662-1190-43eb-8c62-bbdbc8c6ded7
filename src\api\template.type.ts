import {Category} from "../assets/ts/utils";
import {PageInfo} from "./common.type";

export interface TemplateCategoryInfo {
    id: string;
    name: string;
}

export interface GetTemplateListParams extends PageInfo {
    categoryId?: string;
    name?: string;
    status?: number;
}

export interface AddTemplateInfo {
    categoryId: string;
    fileUuid: string;
    fileName: string;
    metaType: number;
    name: string;
    separateNum: number;
    type: string;
}

export interface UpdateTemplateInfo extends AddTemplateInfo {
    id: string;
}

// eslint-disable-next-line import/prefer-default-export
export enum TemplateStatus {
    wait = 1, // 待处理
    processing = 2, // 处理中
    success = 3, // 处理成功
    failed = 4, // 处理失败
    cancel = 5, // 取消处理
}

export interface TemplateInfo {
    id: string;
    motorTemplateId: string;
    name: string;
    previewImage?: string;
    progress?: number;
    status?: TemplateStatus;
    updateAt: number;
    updateBy: string;
}

export interface UploadTemplateInfo {
    id: string;
    name: string;
    categoryId: string;
    categoryName?: string;
    border: boolean;
    file: File;
    fileMd5: string;
    fileName: string;
    metaType: Category;
    type: "FBX";
}

export interface GetTemplateListRes {
    content: TemplateInfo[];
    total: number;
}

export interface ValidateMD5Params {
    fileMd5: string;
    filename: string;
    templateType: string;
    border: boolean;
    separateNum: number;
    sys: false;
}

export interface MergeFileChunksParams {
    fileMd5: string;
    fileName: string;
    templateType: string;
    border: boolean;
    metaType: Category;
    type: string;
    separateNum: number;
    sys: false;
}
