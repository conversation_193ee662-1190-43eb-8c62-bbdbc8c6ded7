import React, {useState, useCallback, useEffect} from "react";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {ProjectTreeNodeData} from "./data";
import DeptTreeView from "./DeptTreeView";
import {RootState} from "../../../../../store/rootReducer";
import {toArr} from "../../../../../assets/ts/utils";
import {getBIMProjList, getOrgNodeTreeByDeptId} from "../../../../../api/recentProject/orgTree";
import {WebRes} from "../../../../../api/common.type";
import {PageProjInfoResult, ProjInfo} from "../../../../../api/graphicModel";
import {OrgProjNodeVo, ProjNameVo} from "../../../../../api/recentProject/orgTree.type";

const useStyle = createUseStyles({
    box: {},
});

interface DeptTreeProps {
    /** 切换工程，是否刷新页面 */
    reloadWhenProjChange?: boolean;
}

const DeptTree = (props: DeptTreeProps) => {
    const cls = useStyle();
    const {reloadWhenProjChange} = props;
    const [projectTreeData, setProjectTreeData] = useState<ProjectTreeNodeData[]>([]);
    const {orgInfo} = useSelector((state: RootState) => state.commonData);

    const transBIMNode = useCallback((bimItem: ProjNameVo, ppidMap: Map<number, ProjInfo>): ProjectTreeNodeData => {
        const result: ProjectTreeNodeData = {
            title: bimItem?.projName ?? "",
            key: bimItem?.ppid ?? "",
            orgInfo: {
                id: bimItem?.ppid?.toString() ?? "",
                name: bimItem?.projName ?? "",
                type: -1,
                isBIM: true
            },
            bimInfo: null,
        };

        if (typeof bimItem.ppid !== "undefined") {
            const find = ppidMap.get(bimItem.ppid);
            if (typeof find !== "undefined") {
                result.bimInfo = {
                    ...find,
                };
            }
        }

        return result;
    }, []);

    const transSectionNode = useCallback((sectionItem: OrgProjNodeVo,
        itemList: OrgProjNodeVo[], ppidMap: Map<number, ProjInfo>): ProjectTreeNodeData => {
        const result: ProjectTreeNodeData = {
            title: sectionItem?.name ?? "",
            key: sectionItem?.id ?? "",
            orgInfo: {
                id: sectionItem?.id ?? "",
                name: sectionItem?.name ?? "",
                type: sectionItem?.type ?? 1,
                isBIM: false
            },
            bimInfo: null,
            children: []
        };

        if (Array.isArray(sectionItem.projects)) {
            result.children = sectionItem.projects.map((el) => transBIMNode(el, ppidMap));
        }

        const childrenFilter = itemList.filter((el) => el.parentId === sectionItem.id)
            .map((el) => transSectionNode(el, itemList, ppidMap));

        result.children = result.children?.concat(childrenFilter);

        return result;
    }, [transBIMNode]);

    const generateProjectTree = useCallback((bimProjectList: ProjInfo[]) => {
        if (orgInfo.orgId === "") {
            return;
        }

        const ppidMap: Map<number, ProjInfo> = new Map();
        bimProjectList.map((el) => ppidMap.set(el.ppid, el));

        getOrgNodeTreeByDeptId(orgInfo.orgId)
            .then((res) => {
                const transRes = toArr(res);
                const projectNode = transRes.find((el) => el.id === orgInfo.orgId);
                if (projectNode === undefined) {
                    setProjectTreeData([]);
                    return;
                }
                const treeTopNode: ProjectTreeNodeData = {
                    title: projectNode?.name ?? "",
                    key: projectNode?.id ?? "",
                    orgInfo: {
                        id: projectNode?.id ?? "",
                        name: projectNode?.name ?? "",
                        type: projectNode?.type ?? 0,
                        isBIM: false
                    },
                    bimInfo: null,
                };
                // 房建项目
                if (orgInfo.deptDataType === 1) {
                    const children = toArr(projectNode.projects ?? []).map((el) => transBIMNode(el, ppidMap));

                    const childrenChild = transRes.filter((el) => el.parentId === treeTopNode.key)
                        .map((el) => transSectionNode(el, transRes, ppidMap));

                    treeTopNode.children = childrenChild.concat(children);
                }

                // 基建项目
                if (orgInfo.deptDataType === 2) {
                    treeTopNode.children = transRes.filter((el) => el.parentId === treeTopNode.key)
                        .map((el) => transSectionNode(el, transRes, ppidMap));
                }
                setProjectTreeData([treeTopNode]);
            });
    }, [orgInfo.deptDataType, orgInfo.orgId, transBIMNode, transSectionNode]);

    const getProjectTreeData = useCallback(() => {
        if (orgInfo.orgId === "") {
            return;
        }

        getBIMProjList({
            pageNum: 1,
            pageSize: 1000,
            nodeId: orgInfo.orgId,
            nodeType: orgInfo.orgType,
            extractStatus: 1,
        }).then((res: WebRes<PageProjInfoResult>) => {
            const {data} = res;
            let bimProjectList: ProjInfo[] = [];
            if (data !== null && data.items !== null && data.items.length !== 0) {
                bimProjectList = data.items;
            }

            generateProjectTree(bimProjectList);
        });
    }, [generateProjectTree, orgInfo.orgId, orgInfo.orgType]);

    useEffect(() => {
        getProjectTreeData();
    }, [getProjectTreeData]);

    const renderEbsContent = React.useCallback(() => (
        <DeptTreeView
            deptTreeData={projectTreeData}
            reloadWhenProjChange={reloadWhenProjChange}
        />
    ), [projectTreeData, reloadWhenProjChange]);

    return (
        <div className={`${cls.box} box`}>
            {renderEbsContent()}
        </div>
    );
};

export default DeptTree;
