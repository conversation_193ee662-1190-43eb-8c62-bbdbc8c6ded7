/* eslint-disable @typescript-eslint/no-explicit-any */
import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {PageProjInfoResult, ProjListParam} from "../graphicModel";
import {OrgProjNodeVo} from "./orgTree.type";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = (window as any).__IWorksConfig__;
const pdscommonUrl = `${baseUrl}/pdscommon`;

/** 获取项目部下的工程组织树列表（标段，单项工程，单位工程，工程）（iworks&iworksApp） */
export const getOrgNodeTreeByDeptId = async (deptId: string): Promise<OrgProjNodeVo[]> => Fetch({
    methods: "get",
    url: `${pdscommonUrl}/rs/org/listProjNodeByDeptId/${deptId}`,
    isDeal: false
});

//  分页查询工程列表
export const getBIMProjList = async (params: ProjListParam): Promise<WebRes<PageProjInfoResult>> => Fetch({
    methods: "post",
    url: `${pdscommonUrl}/rs/proj/page`,
    data: params,
});

