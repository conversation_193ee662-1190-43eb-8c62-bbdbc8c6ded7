import {createUseStyles} from "react-jss";
import Color from "../../assets/css/Color";

const useStyles = createUseStyles({
    mainBox: {
        padding: 24,
        height: "100%",
        display: "flex",
        flexDirection: "column"
    },
    collapseBox: {
        marginTop: 16,
        overflowY: "auto"
    },
    sectionListBox: {
        padding: "0 20px"
    },
    tipIcon: {
        color: Color["yellow-1"],
        marginRight: 8
    },
    tipTitle: {
        color: Color["text-2"],
        fontWeight: "bold",
        fontSize: 16,
    },
    tipContent: {
        color: Color["text-2"],
        fontSize: 14,
    },
    tabLineBox: {
        borderBottom: `1px solid ${Color["light-line-1"]}`,
        position: "absolute",
        left: 0,
        right: 0,
        top: 107,
        zIndex: -1,
    },
    tableWrapper: {
        marginBottom: 20,
        flexGrow: 1,
        overflowY: "auto"
    },
    mainWrapper: {
        display: "flex",
        flexDirection: "column",
        height: "100%",
        paddingTop: 24,
        backgroundColor: "#fff",
    },
    headWrapper: {
        padding: "0 20px"
    },
    contentWrapper: {
        padding: 24,
        overflowY: "auto",
        flexGrow: 1
    },
    footerWrapper: {
        borderTop: `1px solid ${Color["dark-line-2"]}`,
        padding: "10px 24px",
        display: "flex",
        justifyContent: "flex-end"
    },
    loadingBox: {
        display: "flex",
        height: "100%",
        justifyContent: "center",
        alignItems: "center"
    },
    decLineBox: {
        height: 250
    },
    desBox: {
        "& .ant-descriptions-item-label": {
            color: "#606266",
            width: "100px",
            display: "block",
            textAlign: "right"
        },
        "& .ant-descriptions-item-content": {
            color: "#303133"
        }
    },
    decLeftBox: {
        "& .ant-descriptions-item-label": {
            textAlign: "left"
        }
    },
    specFormBox: {
        "& .ant-form-item-label": {
            width: 100,
            textAlign: "right"
        }
    },
    queryFormBox: {
        paddingTop: 24,
        paddingBottom: 16,
        "& .ant-form-item": {
            marginBottom: 16
        }
    }
});

export default useStyles;
