import Motor from "@motor/core";
import {Moment} from "moment";
import {ParentAPI} from "postmate";
import {CompNecInfo, CompStateParam} from "../../../../../api/processManager/type";

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class StatusColor {
    static notStartColor = "#727272";

    static ongoingColor = "#FAAB0C";

    static completeColor = "#2DA641";
}

/** 构件信息 */
export interface StateInfoOfTimePeriod {

    /** 开始时间 */
    startTime: Date;

    /** 结束时间 */
    endTime: Date;

    /** 状态颜色,取值型如255,0,0 */
    stateColor: string;

    /** 工序id */
    stateKey: string;

    /** 工序名称 */
    stateName: string;

    /** 该时间段是否为工序之间的间隙，工序间隙沿用上一段工序的颜色加上半透明效果 */
    isInterval: boolean;

    /** 该时间段是否为构件最后一道工序与构件结束时间之间的时间段工序 */
    isVirtualTail: boolean;
}

/** 构件信息 */
export interface CompSandBoxItem {

    /** 完工时间 */
    finishEndDate: number;

    /** 工序集合 */
    stateItems: CompStateParam[];

    /** 构件信息 */
    compInfo: CompNecInfo;

    /** 构件唯一码 */
    compKey: string;

    /** 计划开始时间 */
    planStartTime: Date;

    /** 计划结束时间 */
    planEndTime: Date;

    /** 工序中最早的计划结束时间 */
    earliestPlanEndTime: Date;

    /** 实际开始时间 */
    actualStartTime: Date | null;

    /** 实际开始时间 */
    actualEndTime: Date | null;

    /** 计划时间分段信息，每一段对应一个工序 */
    planTimePeriodList: StateInfoOfTimePeriod[];

    /** 实际时间分段信息，每一段对应一个工序 */
    actualTimePeriodList: StateInfoOfTimePeriod[];
}

/** 构件信息 */
export interface CompWithTimeSandBoxItem {

    /** 构件信息 */
    compInfo: CompNecInfo;

    /** 构件唯一码 */
    compKey: string;

    /** 计划开始时间 */
    planStartTime: Date;

    /** 计划结束时间 */
    planEndTime: Date;

    /** 工序中最早的计划结束时间 */
    earliestPlanEndTime: Date;

    /** 实际开始时间 */
    actualStartTime: Date | null;

    /** 实际开始时间 */
    actualEndTime: Date | null;
}

/** 构件信息 */
export interface CompSandBoxItemOfSingleTimePeriod {

    /** 完工时间 */
    finishEndDate: number;

    /** 构件信息 */
    compInfo: CompNecInfo;

    /** 构件唯一码 */
    compKey: string;

    /** 构件名称 */
    compName: string;

    /** 最早的计划完工时间 */
    earliestPlanEndTime: number;
    /** 一个构件会对应多道工序（即多个计划结束时间），取最晚的计划结束时间 */
    latestPlanEndTime: number;

    /** 时间分段信息，每一段对应一个工序 */
    timePeriodInfo: StateInfoOfTimePeriod;

    /** 构件的工序结束时间(计划/实际) */
    completeTime: number;
}

/** 构件信息 */
export interface CompStatusChangedInfo {

    /** 构件唯一码 */
    compKey: string;

    /** 构件信息 */
    compInfo: CompNecInfo;

    status: "ongoing" | "complete";
}

export interface SandBoxTimeScope {
    planStartTime: Date | null;

    planEndTime: Date | null;

    actualStartTime: Date | null;

    actualEndTime: Date | null;
}

export interface SandBoxHandler {

    reset: () => void;

    showSandBoxByDate: (curTime: Date) => void;

    jumpToDate: (curTime: Date) => void;
}

export type SandBoxFilterType = "ebs" | "process" | "defaultStatus" | "warn";

/** 构件信息 */
export interface CompCheckedInfo {

    isCheckAll: boolean;

    /** 构件信息 */
    comps: Motor.Element[];

}

export interface SandDataInitializeBase {

    getFilterInfo(filterType: SandBoxFilterType): SandBoxFilterItem;

    setFilterInfo(filterType: SandBoxFilterType, filterInfo: SandBoxFilterItem): void;

    queryVisibilityByEbsFilter(): CompCheckedInfo;

    queryComponentsByCompKeyFromCache(compKeyList: string[]): Motor.Element[];

    queryComponentNameFromCache(compKey: string): string;

    getHandleFromCompKey(compKey: string): string | undefined;

    getPathFromCompKey(compKey: string): string | undefined;

    getWarnIdByType(warnType: number): string | undefined;

    getWarnNameById(warnId: string): string;

    getWarnTypeById(warnId: string): number;

    queryWarningListByTime(date: Date): WarningTypeInfo | undefined;

    queryAccumulatedWarningListByTime(date: Date): WarningTypeInfo | undefined;

    queryAccumulatedCompListByActualTime(date: Date): string[];

    hasSandBoxData(): boolean;

    getPlaybackStartEndTimes(playPattern: string[]): Moment[];

    queryCompStatusListByPlanTime(date: Date): CompStatusChangedInfo[];

    queryAccumulatedCompStatusListByPlanTime(date: Date): CompStatusChangedInfo[];

    queryCompStatusListByActualTime(date: Date): CompStatusChangedInfo[];

    queryAccumulatedCompStatusListByActualTime(date: Date): CompStatusChangedInfo[];

    queryCompSandBoxListByPlanTime(date: Date): CompSandBoxItemOfSingleTimePeriod[];

    queryAccumulatedCompSandBoxListByPlanTime(date: Date): CompSandBoxItemOfSingleTimePeriod[];

    queryCompSandBoxListByActualTime(date: Date): CompSandBoxItemOfSingleTimePeriod[];

    queryAccumulatedCompTimePeriodListByActualTime(date: Date): CompSandBoxItemOfSingleTimePeriod[];

}

export interface SandBoxHandlerForMotorFrame extends SandBoxHandler{
    setChildApi: (motorFrame: ParentAPI | null) => void;
}

export interface SandBoxFilterItem {
    isCheckAll: boolean;
    setCheckedKeys: Set<string>;
}

export interface SandBoxFilterInfo {
    ebsInfo: SandBoxFilterItem;
    processInfo: SandBoxFilterItem;
    defaultStatusInfo: SandBoxFilterItem;
    warnInfo: SandBoxFilterItem;
}

export interface WarningTypeInfo {
    warningType1CompList: string[];
    warningType2CompList: string[];
    warningType3CompList: string[];
    warningType4CompList: string[];
    disappearedCompList: string[];
}

export interface WarningTypeExpiredInfo {
    warningType1ExpiredDays: number;
    warningType2ExpiredDays: number;
    warningType3ExpiredDays: number;
    warningType4ExpiredDays: number;

    warningType1Key: string;
    warningType2Key: string;
    warningType3Key: string;
    warningType4Key: string;
}

export interface ComponentWarningTypeExpiredTimeInfo {
    warningType1ExpiredTime: number;
    warningType2ExpiredTime: number;
    warningType3ExpiredTime: number;
    warningType4ExpiredTime: number;
    disappearedTime: number;
}
