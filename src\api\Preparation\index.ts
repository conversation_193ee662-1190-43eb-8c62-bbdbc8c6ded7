import Fetch from "../../service/Fetch";
import {WindowType, PlanInfoDetailType, GetPreparationListParams, AddPlanInfoType, UpdatePlanInfoType, PlanPreparationListReturn, CalendarInfoRes, CalendarListItem, PlanPreparationItemType, PlanlaunchChangeParams, GetPlanListUsedCycleParams, GetPlanListUsedCycleItemType, PlanExportParams, PlanPreparationTabNumReturn, GetPreparationTabNumParams} from "./type";
import {WebRes} from "../common.type";
import FileFetch from "../../service/FileFetch";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = (window as unknown as WindowType).__IWorksConfig__;

// 获取计划详情
export const getPlanInfoDetail = async (planId: string): Promise<WebRes<PlanInfoDetailType>> => Fetch({
    url: `${baseUrl}/sphere/plan/detail/${planId}`,
    methods: "get",
});
// 根据组织节点获取计划列表信息（iworksWeb新增）
export const getPreparationList = async (params: GetPreparationListParams): Promise<WebRes<PlanPreparationListReturn>> => Fetch({
    url: `${baseUrl}/sphere/plan/page`,
    methods: "post",
    data: params,
});
// 查询计划编制页签内数字
export const getPreparationTabNum = async (params: GetPreparationTabNumParams): Promise<WebRes<PlanPreparationTabNumReturn>> => Fetch({
    url: `${baseUrl}/sphere/plan/tab-num`,
    methods: "post",
    data: params,
});
// 查询计划编制页签内数字
export const getApprovalTabNum = async (params: GetPreparationTabNumParams): Promise<WebRes<PlanPreparationTabNumReturn>> => Fetch({
    url: `${baseUrl}/sphere/plan/approval/tab-num`,
    methods: "put",
    data: params,
});
// 批量删除计划
export const deletePlanInfo = async (ids: string[]) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/batch-delete`,
    methods: "delete",
    data: ids,
});
// 更新计划（iworksWeb
export const updatePlanInfo = async (params: UpdatePlanInfoType) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/update`,
    methods: "put",
    data: params,
});
// 添加计划（iworksWeb）
export const addPlanInfo = async (params: AddPlanInfoType) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/create`,
    methods: "post",
    data: params,
});

// 获取可关联的父计划列表
export const getPlanListUsedCycle = async (params: GetPlanListUsedCycleParams) => Fetch<WebRes<GetPlanListUsedCycleItemType[]>>({
    url: `${baseUrl}/sphere/plan/list-used-cycle`,
    methods: "get",
    data: params,
});

// 变更【审批完成->未审批，未变更->变更】
export const putPlanChange = async (planId: string) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/change/${planId}`,
    methods: "put",
});

// 获取日历
export const getCalendarList = async () => Fetch<WebRes<CalendarListItem[]>>({
    url: `${baseUrl}/sphere/plan/calendar/list`,
    methods: "get",
});

// 获取日历
export const getCalendarInfo = async (id: string) => Fetch<WebRes<CalendarInfoRes>>({
    url: `${baseUrl}/sphere/plan/calendar/${id}`,
    methods: "get",
});

// 修改计划下面所有相关的任务的原时间
export const changePreDate = async (planId: string) => Fetch({
    url: `${baseUrl}/sphere/plan/change-pre-date?planId=${planId}`,
    methods: "put"
});

// 获取可关联的父计划列表
export const getListAssociativeParentPlan = async (id: string) => Fetch<WebRes<PlanPreparationItemType[]>>({
    url: `${baseUrl}/sphere/plan/list-associative-parent-plan/planId/${id}`,
    methods: "get",
});

// 变更【审批完成->未审批，未变更->变更】
export const putPlanlaunchChange = async (params: PlanlaunchChangeParams) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/launch-change`,
    methods: "put",
    data: params
});

// 计划编制分页导出
export const postPlanExport = (params: PlanExportParams, fileName: string) => FileFetch({
    url: `${baseUrl}/sphere/plan/page/export`,
    methods: "post",
    data: params,
    fileName,
});
