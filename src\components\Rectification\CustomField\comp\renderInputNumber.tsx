import React from "react";
import {Input} from "antd";
import {CustomFieldProps} from "../../models/custom-field";

const RenderInputNumber = (props: CustomFieldProps) => {
    const {data, style, hint, onBlur, disabled} = props;

    return (
        <>
            {
                ["string", "undefined", "number"].includes(typeof data.defaultValue)
                    ? (
                        <Input
                            style={style}
                            placeholder={hint}
                            defaultValue={data.defaultValue}
                            onBlur={(e) => onBlur !== undefined && onBlur(e.target.value)}
                            disabled={disabled}
                        />
                    )
                    : null
            }
        </>
    );
};
export default RenderInputNumber;
