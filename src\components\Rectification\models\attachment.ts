export interface Attachment {

    name: string;
    uuid: string;
    fileType: string;
    md5?: string;
    size?: number;

    thumbnail?: Omit<Attachment, "thumbnail">;
}

export interface UpdateParams {
    fileMd5: string;
    fileName: string;
    fileSize: number;
    isBPUpload: boolean;
    isCheckFastUpload: boolean;
    suportModes: number[];
}

export interface FileInfo {
    fileName: string;
    fileSysMd5: string;
    fileSize: number;
    fileExtension: string;
    fileSysUuid: string;
    fileType: number;
}
