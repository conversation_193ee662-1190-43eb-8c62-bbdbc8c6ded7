import React, {Key, useCallback, useEffect, useMemo, useState} from "react";
import {Form, Input, DatePicker, Switch, Row, Space, Button, Drawer, message} from "antd";
import {useSelector} from "react-redux";
import moment, {isMoment, Moment} from "moment";
import {CheckOutlined, CloseOutlined, MenuOutlined} from "@ant-design/icons";
import ProcessTmplTreeWithFilter from "../../../../../components/ProcessTmplTreeWithFilter";
import {RootState} from "../../../../../store/rootReducer";
import {CompStateParam, ProcessTmplTreeNode} from "../../../../../api/processManager/type";
import {isDefined} from "../../../../../assets/ts/utils";
import {isValidDate, pathSeparator} from "../../helps";
import useStyle from "./style";

const formItemLayout = {
    labelAlign: "left" as const,
    colon: false
};
const dateFormItemLayout = {
    labelAlign: "left" as const,
    colon: false,
    style: {width: "calc(50% - 16px)"}
};

interface WrapInputProps {
    value?: ProcessTmplTreeNode;
    onClick: () => void;
}
const WrapInput = (props: WrapInputProps) => {
    const {value, onClick} = props;
    return (
        <Input
            value={value?.pathStr}
            readOnly
            suffix={<MenuOutlined />}
            onClick={onClick}
        />
    );
};

const formatPath = (path: string) => {
    const pathList = path.split(pathSeparator);
    if (path.length <= 1) {
        return pathList.join("/");
    }
    const [, ...rest] = pathList;
    return rest.join("/");
};

const getMillisecond = (time: Moment | undefined, type: "start" | "end") => {
    if (!isDefined(time)) {
        return undefined;
    }
    if (type === "start") {
        return time.startOf("day").valueOf();
    }
    return time.endOf("day").valueOf();
};

const getTime = (time?: number) => {
    if (isValidDate(time)) {
        return moment(time);
    }
    return undefined;
};

const getFormValues = (data: CompStateParam): FormProps => ({
    startDate: getTime(data.lifeCycles.startDate),
    endDate: getTime(data.lifeCycles.endDate),
    planStartDate: getTime(data.lifeCycles.planStartDate)!,
    planEndDate: getTime(data.lifeCycles.planEndDate)!,
    earlyWarn: data.earlyWarn,
    processNode: {
        key: data.stateKey,
        name: data.statePath,
        color: data.stateColor,
        isParent: false,
        pathStr: data.statePath
    }
});

const getSaveParam = (values: FormProps): CompStateParam | undefined => {
    const {startDate, endDate, planStartDate, planEndDate, earlyWarn, processNode} = values;
    if (!isDefined(processNode)) {
        return undefined;
    }
    return {
        earlyWarn,
        stateColor: processNode.color ?? "",
        stateKey: processNode.key,
        stateName: processNode.pathStr,
        statePath: processNode.pathStr,
        lifeCycles: {
            startDate: getMillisecond(startDate, "start"),
            endDate: getMillisecond(endDate, "end"),
            planStartDate: getMillisecond(planStartDate, "start")!,
            planEndDate: getMillisecond(planEndDate, "end")!
        }
    };
};

/**
 * 工序计划开始日期的禁用逻辑
 * @param date 当前展示的日期
 * @param projPlanStartDate 工程的计划开工日期，必传
 * @param projPlanEndDate 工程的计划完工日期，必传
 * @param planEndDate 当前工序的计划完成日期，可选
 */
const getPlanStartDateDisabled = (
    date: Moment,
    projPlanStartDate: Moment,
    projPlanEndDate: Moment,
    planEndDate?: Moment | null
) => {
    if (isMoment(planEndDate)) {
        return date.startOf("day") < projPlanStartDate.startOf("day") || date.endOf("day") > planEndDate.endOf("day");
    }
    return date.startOf("day") < projPlanStartDate.startOf("day") || date.endOf("day") > projPlanEndDate.endOf("day");
};

/**
 * 工序计划完成日期的禁用逻辑
 * @param date 当前展示的日期
 * @param projPlanStartDate 工程的计划开工日期，必传
 * @param projPlanEndDate 工程的计划完工日期，必传
 * @param planEndDate 当前工序的计划开始日期，可选
 */
const getPlanEndDateDisabled = (
    date: Moment,
    projPlanStartDate: Moment,
    projPlanEndDate: Moment,
    planStartDate?: Moment | null
) => {
    if (isMoment(planStartDate)) {
        return date.startOf("day") < planStartDate.startOf("day") || date.endOf("day") > projPlanEndDate.endOf("day");
    }
    return date.startOf("day") < projPlanStartDate.startOf("day") || date.endOf("day") > projPlanEndDate.endOf("day");
};

interface FormProps {
    startDate?: Moment;
    endDate?: Moment;
    planStartDate: Moment;
    planEndDate: Moment;
    earlyWarn: boolean;
    processNode?: ProcessTmplTreeNode;
}

interface ProcessFormProps {
    data?: CompStateParam;
    /** 当前工序模板应禁用的节点 */
    tmplTreeDisabledKeys?: Set<string>;
    onSubmit?: (info: CompStateParam) => void;
    onCalcel?: () => void;
}

const ProcessForm = (props: ProcessFormProps) => {
    const cls = useStyle();
    const {data, tmplTreeDisabledKeys, onSubmit, onCalcel} = props;
    const [formInstance] = Form.useForm<FormProps>();
    const {tmplTree} = useSelector((state: RootState) => state.processData);
    const {curProjSettingInfo} = useSelector((state: RootState) => state.statusData);
    const [processTmplVisble, setProcessTmplVisble] = useState(false);
    const [endDateDisabled, setEndDateDisabled] = useState<boolean>(true); // 有实际开始日期，才能有实际完成日期
    const [expandedKeys, setExpandedKeys] = useState<string[]>([tmplTree[0]?.key ?? ""]);

    useEffect(
        () => {
            if (!isDefined(data)) {
                return;
            }
            formInstance.setFieldsValue(getFormValues(data));
            setEndDateDisabled(!isDefined(data.lifeCycles.startDate));
        },
        [data, formInstance]
    );

    const handleOpenProcessTmpl = useCallback(
        () => {
            setProcessTmplVisble(true);
        },
        []
    );

    const handleCloseProcessTmpl = useCallback(
        () => {
            setProcessTmplVisble(false);
        },
        []
    );

    const handleTreeNodeSelect = useCallback(
        (node?: ProcessTmplTreeNode) => {
            formInstance.setFieldsValue({
                processNode: node === undefined ? undefined : {...node, pathStr: formatPath(node.pathStr)}
            });
        },
        [formInstance]
    );

    const handleCalcel = useCallback(
        () => {
            if (onCalcel !== undefined) {
                onCalcel();
            }
            formInstance.resetFields();
        },
        [formInstance, onCalcel]
    );

    const handleSubmit = useCallback(
        async () => {
            const values = await formInstance.validateFields();
            const param = getSaveParam(values);
            if (onSubmit !== undefined && param !== undefined) {
                onSubmit(param);
            }
        },
        [formInstance, onSubmit]
    );

    const handleFormValuesChange = useCallback(
        (_changeVals: unknown, values: FormProps) => {
            const hasStartDate = isDefined(values.startDate);
            setEndDateDisabled(!hasStartDate);
            if (!hasStartDate) {
                formInstance.setFieldsValue({endDate: undefined});
            }
        },
        [formInstance]
    );

    /** 工程开工日期 <= 工序计划开始日期 <= 工序计划完成日期 <= 工程完工日期 */
    const disabledPlanStartDate = useCallback(
        (date: Moment) => {
            if (typeof curProjSettingInfo?.planStartDate === "number" && typeof curProjSettingInfo.planEndDate === "number") {
                const planEndDate = formInstance.getFieldValue("planEndDate") as Moment | null | undefined;
                return getPlanStartDateDisabled(
                    date,
                    moment(curProjSettingInfo.planStartDate),
                    moment(curProjSettingInfo.planEndDate),
                    planEndDate
                );
            }
            message.error("未设置工程计划开工日期！");
            return true;
        },
        [curProjSettingInfo, formInstance]
    );

    /** 工程完工日期 >= 工序计划完成日期 >= 工序计划开始日期 >= 工程开工日期 */
    const disabledPlanEndDate = useCallback(
        (date: Moment) => {
            if (typeof curProjSettingInfo?.planEndDate === "number" && typeof curProjSettingInfo.planStartDate === "number") {
                const planStartDate = formInstance.getFieldValue("planStartDate") as Moment | null | undefined;
                return getPlanEndDateDisabled(
                    date,
                    moment(curProjSettingInfo.planStartDate),
                    moment(curProjSettingInfo.planEndDate),
                    planStartDate
                );
            }
            message.error("未设置工程计划完工日期！");
            return true;
        },
        [curProjSettingInfo, formInstance]
    );

    /** 只需保证实际开始日期不晚于实际完成日期即可 */
    const disabledStartDate = useCallback(
        (date: Moment) => {
            const endDate = formInstance.getFieldValue("endDate") as Moment | null | undefined;
            if (isMoment(endDate)) {
                return date.endOf("day") > endDate.endOf("day") || date.endOf("day") > moment().endOf("day");
            }
            return date.endOf("day") > moment().endOf("day");
        },
        [formInstance]
    );

    /** 只需保证实际完成日期不早于实际开始日期即可 */
    const disabledEndDate = useCallback(
        (date: Moment) => {
            const startDate = formInstance.getFieldValue("startDate") as Moment | null | undefined;
            if (isMoment(startDate)) {
                return date.startOf("day") < startDate.startOf("day") || date.endOf("day") > moment().endOf("day");
            }
            return date.endOf("day") > moment().endOf("day");
        },
        [formInstance]
    );

    const handleExpand = useCallback(
        (keys: Key[]) => {
            setExpandedKeys(keys as string[]);
        },
        []
    );

    const processTmplView = useMemo(() => (
        <Drawer
            title="工序管理"
            visible={processTmplVisble}
            onClose={handleCloseProcessTmpl}
            width={480}
            bodyStyle={{padding: 16}}
        >
            <ProcessTmplTreeWithFilter
                tmplTree={tmplTree}
                onLocalSelect={handleTreeNodeSelect}
                disabledKeys={tmplTreeDisabledKeys}
                expandedKeys={expandedKeys}
                onExpand={handleExpand}
            />
        </Drawer>
    ), [processTmplVisble, handleCloseProcessTmpl, tmplTree, handleTreeNodeSelect, tmplTreeDisabledKeys, expandedKeys, handleExpand]);

    return (
        <>
            <Form
                form={formInstance}
                onValuesChange={handleFormValuesChange}
                className={cls.formBox}
            >
                <Form.Item style={{marginBottom: 12}}>
                    <Row justify="space-between" align="middle">
                        <span style={{fontWeight: 700}}>添加工序</span>
                        <Space size={1}>
                            <Button
                                type="link"
                                htmlType="button"
                                icon={<CloseOutlined />}
                                onClick={handleCalcel}
                            >
                                取消
                            </Button>
                            <Button
                                type="link"
                                htmlType="submit"
                                icon={<CheckOutlined />}
                                onClick={handleSubmit}
                            >
                                保存
                            </Button>
                        </Space>
                    </Row>
                </Form.Item>
                <Form.Item
                    name="processNode"
                    label="选择工序"
                    rules={[{required: true, message: "请选择工序"}]}
                    {...formItemLayout}
                >
                    <WrapInput onClick={handleOpenProcessTmpl} />
                </Form.Item>
                <Form.Item noStyle>
                    <Row justify="space-between" align="middle">
                        <Form.Item
                            label="计划开始日期"
                            rules={[{required: true, message: "请选择计划开始日期"}]}
                            name="planStartDate"
                            {...dateFormItemLayout}
                        >
                            <DatePicker
                                disabledDate={disabledPlanStartDate}
                                style={{width: "100%"}}
                            />
                        </Form.Item>
                        <Form.Item
                            label="计划完成日期"
                            rules={[{required: true, message: "请选择计划完工日期"}]}
                            name="planEndDate"
                            {...dateFormItemLayout}
                        >
                            <DatePicker disabledDate={disabledPlanEndDate} style={{width: "100%"}} />
                        </Form.Item>
                    </Row>
                </Form.Item>
                <Form.Item noStyle>
                    <Row justify="space-between" align="middle">
                        <Form.Item
                            label="实际开始日期"
                            name="startDate"
                            {...dateFormItemLayout}
                        >
                            <DatePicker disabledDate={disabledStartDate} style={{width: "100%"}} />
                        </Form.Item>
                        <Form.Item
                            label="实际完成日期"
                            name="endDate"
                            {...dateFormItemLayout}
                        >
                            <DatePicker disabledDate={disabledEndDate} style={{width: "100%"}} disabled={endDateDisabled} />
                        </Form.Item>
                    </Row>
                </Form.Item>
                <Form.Item
                    name="earlyWarn"
                    label="提前预警通知"
                    {...formItemLayout}
                    valuePropName="checked"
                    style={{marginBottom: 0}}
                >
                    <Switch />
                </Form.Item>
            </Form>
            {processTmplView}
        </>
    );
};

export default ProcessForm;
