/* eslint-disable no-underscore-dangle */

import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {CompAtrrQueryParam, CompAttrResult, ProjectAttrControlQueryParam, RevitUserAttrRemote} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

/** 获取构件属性信息（IworksWeb1.0.0新增） */
export const getCompAttr = async (
    params: CompAtrrQueryParam
): Promise<WebRes<CompAttrResult>> => Fetch({
    methods: "post",
    url: `${baseUrl}/pdscommon/rs/comp/attr`,
    data: params
});

/** 获取工程属性用户设置信息,目前仅支持Revit&Tekla【iworksWeb1.1.1新增】 */
export const getProjAttrControl = async (
    params: ProjectAttrControlQueryParam
): Promise<WebRes<RevitUserAttrRemote>> => Fetch({
    methods: "post",
    url: `${baseUrl}/pdscommon/rs/comp/attr/project_attr_control`,
    data: params
});
