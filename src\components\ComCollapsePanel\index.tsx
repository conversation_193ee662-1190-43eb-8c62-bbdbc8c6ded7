import {Collapse, CollapsePanelProps, Row, Space} from "antd";
import React, {ReactNode} from "react";
import {createUseStyles} from "react-jss";
import Color from "../../assets/css/Color";

const {Panel} = Collapse;

const useStyle = createUseStyles({
    panel: {
        paddingBottom: "24px",
        "& .ant-collapse-header": {
            backgroundColor: Color["bg-2"]
        },
        "& .ant-collapse-content-box": {
            padding: "18px 0",
        },
        "&.ant-collapse-item > .ant-collapse-header": {
            paddingTop: "6px",
            paddingBottom: "6px",
            "& .ant-collapse-arrow": {
                paddingTop: 6
            }
        }
    },
    headerBox: {
        display: "flex",
        justifyContent: "space-between"
    },
    downloadBtnBox: {
        "& .ant-btn": {
            color: Color.primary,
            height: 24,
            padding: "0 8px",
            "&:hover, &:focus": {
                color: Color["primary-2"],
                background: Color["bg-2"]
            },
            "&:active": {
                color: Color["primary-3"]
            }
        },
        "& .ant-btn-text[disabled], & .ant-btn-text[disabled]:hover, & .ant-btn-text[disabled]:focus, & .ant-btn-text[disabled]:active": {
            color: "rgba(0, 0, 0, 0.25)"
        }
    }
});

export interface ComCollapsePanelProps extends CollapsePanelProps {
    children: ReactNode;
    extraText?: string;
    required?: boolean;
    operationBtn?: ReactNode;
    row?: boolean;
    classNamePanel?: string;
}

const ComCollapsePanel = (props: ComCollapsePanelProps) => {
    const {
        header,
        extraText,
        required = false,
        children,
        operationBtn,
        classNamePanel,
        row = false,
        ...other
    } = props;
    const cls = useStyle();
    return (
        <Panel
            className={`${cls.panel} ${classNamePanel}`}
            header={(
                <div className={cls.headerBox}>
                    <Space align="center">
                        <div className="title" style={{fontSize: 14}}>{header}</div>
                        {required ? <div style={{color: Color["red-1"]}}>*</div> : null}
                        <div style={{color: Color["text-3"]}}>{extraText}</div>
                    </Space>
                    <div className={cls.downloadBtnBox}>{operationBtn}</div>
                </div>
            )}
            {...other}
        >
            {row ? <Row>{children}</Row> : children}
        </Panel>
    );
};

export default ComCollapsePanel;
