import {<PERSON><PERSON><PERSON>} from "postmate";
import {ReactNode} from "react";
import {AnyAction} from "redux";
import ActionTypes, {EbsProjectType} from "./actionTypes";
import {MenuListProps} from "../../api/common.type";
import {BimProjInfo, CheckBind} from "../../components/Rectification/models/custom-field";
import {PersonOriginalType, PersonTreeType} from "../../api/center/type";


export interface StateType {
    child: ChildAPI | undefined;
    iframeEditStatus: boolean;
    workBenchDetails: ReactNode | undefined;
    personData: PersonOriginalType[];
    // 人员树
    personTree?: PersonTreeType;
    // 人员树返回通行证
    personTreeFlag?: PersonTreeType;
    menuList: MenuListProps[];
    showModelSelect: boolean;
    showModelSpin: boolean;
    bimProjInfo: BimProjInfo;
    bindInfo: CheckBind[];
    ebsProjectList?: EbsProjectType[];
    pageArr: ReactNode[];
    leftBoxVisibleObj: {[key: string]: boolean};
    anchor: ReactNode | undefined;
}

export const initState: StateType = {
    child: undefined,
    iframeEditStatus: false,
    workBenchDetails: null,
    personData: [],
    menuList: [],
    showModelSelect: false,
    showModelSpin: false,
    bimProjInfo: {nPPid: 0, projType: 0, projName: "", bindType: 0},
    bindInfo: [],
    ebsProjectList: [],
    pageArr: [],

    leftBoxVisibleObj: {},
    anchor: null,
};

const reducer = (state = initState, action: AnyAction): StateType => {
    switch (action.type) {
        case ActionTypes.SAVE_CHILD_API:
            return {
                ...state,
                child: action.payload
            };
        case ActionTypes.SET_IFRAME_EDIT_STATUS:
            return {
                ...state,
                iframeEditStatus: action.payload
            };
        case ActionTypes.SET_WORK_BENCH_DETAILS:
            return {
                ...state,
                workBenchDetails: action.payload
            };
        case ActionTypes.SET_PERSON_DATA:
            return {
                ...state,
                personData: action.payload,
            };
        case ActionTypes.SET_PERSON_TREE:
            return {
                ...state,
                personTree: action.payload,
            };
        case ActionTypes.SET_PERSON_TREE_FLAG:
            return {
                ...state,
                personTreeFlag: action.payload,
            };
        case ActionTypes.SET_MENU_LIST:
            return {
                ...state,
                menuList: action.payload
            };
        case ActionTypes.SET_SHOW_MODEL_SELECT:
            return {
                ...state,
                showModelSelect: action.payload
            };
        case ActionTypes.SET_SHOW_MODEL_SPIN:
            return {
                ...state,
                showModelSpin: action.payload
            };
        case ActionTypes.SET_BIM_PROJ_INFO:
            return {
                ...state,
                bimProjInfo: action.payload
            };
        case ActionTypes.SET_BIND_INFO:
            return {
                ...state,
                bindInfo: action.payload
            };
        case ActionTypes.SET_PAGE_ARR:
            return {
                ...state,
                pageArr: action.payload
            };
        case ActionTypes.ADD_PAGE:
            return {
                ...state,
                pageArr: [...state.pageArr, action.payload]
            };
        case ActionTypes.POP_PAGE:
            state.pageArr.pop();
            return {
                ...state,
                pageArr: [...state.pageArr]
            };
        case ActionTypes.CHANGE_LEFT_BOX_VISIBLE:
            return {
                ...state,
                leftBoxVisibleObj: {...state.leftBoxVisibleObj, ...action.payload}
            };
        case ActionTypes.RESET_LEFT_BOX_VISIBLE:
            return {
                ...state,
                leftBoxVisibleObj: {}
            };
        case ActionTypes.SET_ANCHORSKIP:
            return {
                ...state,
                anchor: action.payload
            };
        default:
            return state;
    }
};

export default reducer;
