import React, {useState, use<PERSON><PERSON>back, Key, useEffect} from "react";
import {createUseStyles} from "react-jss";
import {Space, Tree, TreeProps} from "antd";
import {ProjectTreeNodeData, EBSNodeType, CompCategoryTreeNodeData, transformTree, getWBSEBSType, filterTreeByKeys} from "./data";
import ProjectSearch, {ProjectSearchProps} from "./ProjecSearch";
import ModelSelect, {BindMsgVo} from "../ModelSelect";
import {dispatch} from "../../store";
import {setShowModelSelect, setShowModelSpin, setBimProjInfo, setBindInfo} from "../../store/no-persist/action";
import {OrgProjNodeVo, ProjNameVo} from "../../api/common.type";
import {BimProjInfo, TreeNodeType} from "../Rectification/models/custom-field";
import {toArr, pathSeparator} from "../../assets/ts/utils";
import {getProjectDetail, getProjectDetailInfo} from "../../api/common.api";
import MyIcon from "../MyIcon";
import renderText from "../renderTableText/renderText";


const useStyle = createUseStyles({
    box: {},
    tree: {
        flexGrow: 1,
        maxWidth: "50%",
        "& .ant-tree-checkbox-inner": {
            backgroundColor: "#d9d9d9",
            cursor: "not-allowed",
        },
        "& .ant-tree-checkbox-checked .ant-tree-checkbox-inner::after": {
            border: "2px solid #00000040",
            borderLeft: 0,
            borderTop: 0,
        }
    }
});

interface RelatedCompProps {
    projectTreeData: ProjectTreeNodeData[];
    value?: EBSNodeType[];
    onChange?: (val: EBSNodeType[]) => void;
}

const RelatedComp = (props: RelatedCompProps) => {
    const {projectTreeData, value, onChange} = props;
    const cls = useStyle();
    const [treeData, setTreeData] = useState<ProjectTreeNodeData[]>([]);
    // 项目树选中的,单选
    const [projectSelected, setProjectSelected] = useState<ProjNameVo>();
    const [projectTreeChecked, setProjectTreeChecked] = useState<Key[]>([]);
    // 构件类别树
    const [compCategoryTreeData, setCompCategoryTreeData] = useState<CompCategoryTreeNodeData[]>([]);
    const [compKeys, setCompKeys] = useState<string[]>([]);
    const tempType = getWBSEBSType(value);

    useEffect(() => {
        if (tempType === 2) {
            setProjectTreeChecked(toArr(value ?? []).map((el) => Number(el.ppid)));
            setCompKeys(toArr(value ?? []).map((el) => toArr(el.paths ?? []).join(pathSeparator)));
        } else {
            setProjectTreeChecked([]);
            setCompKeys([]);
        }
    }, [tempType, value]);

    const handleProjectTreeChange: ProjectSearchProps["onProjectTreeChange"] = useCallback(
        (val) => {
            setTreeData(val);
        },
        []
    );

    const getCompCategotyList = useCallback((ppid: string | number) => {
        getProjectDetail(`${ppid}`).then((res) => {
            if (res.data === null || Boolean(res.data) === false) {
                setCompCategoryTreeData([]);
                return;
            }
            const tempTreeData = toArr(res.data.treeNodeList ?? []).map((el) => transformTree(el, []));
            setCompCategoryTreeData(tempTreeData);
        });
    }, []);

    const handleProjectTreeSelect: TreeProps["onSelect"] = (_key, _node) => {
        const node = _node.node as unknown as {originData: ProjNameVo};
        if (_node.selected === true && node.originData.ppid !== undefined) {
            setProjectSelected(node.originData);
            getCompCategotyList(node.originData.ppid);
        } else {
            setCompCategoryTreeData([]);
            setProjectSelected(undefined);
        }
    };

    const handleOpenMotor = async (e: React.MouseEvent, _val: ProjNameVo) => {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        if (_val.ppid === undefined) {
            return;
        }
        const {success, data} = await getProjectDetailInfo(_val.ppid);
        if (success === false) {
            return;
        }
        const selectedBinds = toArr(value ?? []).filter((el) => el.ppid === _val.ppid).map((el) => ({handle: el.handle}));
        const tempInfo: BimProjInfo = {
            nPPid: _val.ppid ?? 0,
            nodeId: `${_val.ppid}`,
            projType: _val.projType as unknown as TreeNodeType,
            projName: _val.projName ?? "",
            bindType: 2,
            bindBws: data.isExtractLocationTree === 1,
        };
        setProjectSelected(_val);
        dispatch(setBindInfo(selectedBinds));
        dispatch(setBimProjInfo(tempInfo));
        dispatch(setShowModelSelect(true));
        dispatch(setShowModelSpin(true));
    };

    const handleModelSelect = useCallback(
        (infos: BindMsgVo[]) => {
            let tempValue: EBSNodeType[] = toArr(infos).map((el) => ({
                handle: el.handle,
                paths: el.path?.split(pathSeparator),
                ppid: el.ppid,
                projName: projectSelected?.projName,
            }));
            if (tempType === 2) {
                tempValue = tempValue.concat(value ?? []);
            }
            if (onChange !== undefined) {
                onChange(tempValue);
            }
        },
        [onChange, projectSelected, tempType, value]
    );

    const renderTreeNode: TreeProps["titleRender"] = (_nodeData) => {
        const node = _nodeData as unknown as {originData: ProjNameVo};
        const nodeData = node.originData as OrgProjNodeVo;
        if (nodeData.type === 0) {
            return (
                <Space>
                    <div><MyIcon type="icon-gaosuxiangmu" fontSize={16} color="#000" style={{position: "relative", top: 2}} /></div>
                    <div>{_nodeData.title}</div>
                </Space>
            );
        }
        if (nodeData.type === 1) {
            return (
                <Space>
                    <div style={{backgroundColor: "#2DA641", padding: "2px", color: "#fff", lineHeight: 1}}>标</div>
                    <div>{_nodeData.title}</div>
                </Space>
            );
        }
        return (
            <div style={{display: "flex", justifyContent: "space-between"}}>
                <div style={{flexGrow: 1, width: 0}}>{renderText(_nodeData.title as string)}</div>
                {Boolean(node.originData.ppid) === true && (
                    <div
                        style={{flexShrink: 0, marginLeft: 12}}
                        onClick={async (e) => handleOpenMotor(e, node.originData)}
                    >
                        <MyIcon type="icon-moxing" color="#000" fontSize={16} />
                    </div>
                )}
            </div>
        );
    };

    const compTree = filterTreeByKeys(compCategoryTreeData, compKeys);

    return (
        <div className={cls.box} style={{display: "flex", flexDirection: "column", height: "100%"}}>
            <ProjectSearch projectTreeValue={value} projectTree={projectTreeData} onProjectTreeChange={handleProjectTreeChange} />
            <div style={{display: "flex", flexGrow: 1, height: 0, marginTop: 16, overflowY: "auto"}}>
                {
                    treeData.length > 0 && (
                        <Tree
                            defaultExpandAll
                            blockNode
                            titleRender={renderTreeNode}
                            className={cls.tree}
                            treeData={treeData}
                            checkable
                            onSelect={handleProjectTreeSelect}
                            checkedKeys={projectTreeChecked}
                        />
                    )
                }
                {compTree.length > 0 && (
                    <div style={{width: 328, backgroundColor: "#E1E2E5"}}>
                        <Tree
                            style={{background: "#E1E2E5"}}
                            treeData={filterTreeByKeys(compCategoryTreeData, compKeys)}
                        />
                    </div>
                )}
            </div>
            <ModelSelect onSelect={handleModelSelect} />
        </div>
    );
};

export default RelatedComp;
