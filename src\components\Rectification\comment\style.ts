import {createUseStyles} from "react-jss";
import Color from "../../../assets/css/Color";

const useStyle = createUseStyles({
    topBox: {
        width: "100%",
        padding: "0 20px",
        display: "flex",
        justifyContent: "flex-start",
        alignItems: "center"
    },
    boxWithoutAvatar: {
        display: "flex",
        flexDirection: "column",
        marginLeft: 10
    },
    fileListBox: {
        marginLeft: 52
    },
    commentMsgBox: {
        marginLeft: 72
    },
    itemBox: {
        background: Color["bg-2"],
        width: 564,
        borderRadius: 4,
        padding: 12,
        marginBottom: 40,
        position: "relative",
        "&:after": {
            content: "''",
            width: 1,
            height: 41,
            background: Color["dark-line-2"],
            position: "absolute",
            left: 52,
            bottom: -40
        },
        "&:last-child": {
            marginBottom: 0
        },
        "&:last-child:after": {
            display: "none"
        }
    },
    itemTag: {
        display: "inline-flex",
        alignItems: "center",
        padding: "2px 4px",
        borderRadius: 2
    },
    itemName: {
        fontSize: 14,
        fontWeight: "bold",
        color: Color["text-1"]
    },
    itemAction: {
        fontSize: 12,
        fontWeight: "bold",
        color: Color["text-1"]
    }
});

export default useStyle;
