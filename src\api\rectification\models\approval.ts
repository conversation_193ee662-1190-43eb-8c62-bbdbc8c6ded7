/** 审批状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
export enum FlowState {
    /** 退回 */
    Declined = "backed",
    /** 撤销 */
    Revoked = "canceled",
    /** 进行中 */
    InProgress = "in-progress",
    /** 已通过 */
    Done = "passed"
}

/** 操作类型：1-发起；4-撤销；5-提交；6-退回；7-抄送；8-评论; 9-自动抄送; 10-转交 */
export enum RectificationOperationType {
    Initiate = 1,
    Revoke = 4,
    Submit = 5,
    Decline = 6,
    CopyTo = 7,
    Comment = 8,
    AutoCopyTo = 9,
    handover = 10
}

/** 安全检查-整改表单详情 */
export interface ApprovalFormDetailRes {
    approvalRoles: number[];
    approvalTypeId: string;
    approvalTypeName: string;
    startFlowNode: boolean;
    /** 整改编号 */
    serialNum: string;
    projName: string;
    productId: number;
    /** 审批状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
    processStatus: FlowState;
    ppid: number;
    /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
    nodeType: number;
    nodeName: string;
    nodeId: string;
    name: string;
    formTemplId: string;
    formTaskId: string;
    formId: string;
    /** 流程结束时间(流程未归档是为null) */
    endTime: number | null;
    deptName: string;
    deptId: string;
    currentFlowNodeName: string;
    currentFlowNodeId: string;
    createTime: number;
    creatorInfo: ApprovalUserVos;
    comments: ApprovalCommentVo[];
    componentJsons?: ComponentValueDetailWrap[][];
    reformComment: string | null;
    reformDeadline: number | null;
}

/** 审批流程图 */
export interface ListApprovalLineNode {
    approvalNodeId: string; // 节点id
    approvalNodeName: string; // 节点名称
    approvalPostVos?: ApprovalPostVos[];
    approvalRoleVos?: ApprovalRoleVos[];
    approvalType: number; // 审批方式 1：或签 2：会签
    approvalUserVos: ApprovalUserVos[] | null;
    approverType: number; // 批人类型，0:指定角色 1:指定人 2:指定岗位 3:发起人指定
}

/** 审批岗位 */
export interface ApprovalPostVos {
    postId: string; // 审批岗位id
    postName: string; // 审批岗位名称
}

/** 审批角色 */

export interface ApprovalRoleVos {
    roleId: string; // 审批角色id
    roleName: string; // 审批角色名称
}

/** 审批用户 */
export interface ApprovalUserVos {
    isLeave: boolean; // 是否离职：true-离职，false-在职
    portraitUuid: string | null; // 头像uuid
    realName: string; // 真是姓名
    sourceType: number; // 用户的来源，0:内部 ;1：外部人员
    userName: string; // 用户名
}

/** 审批意见 */
export interface ApprovalCommentVo {
    attachments: FileItem[] | null;
    commentMsg: string;
    commentTime: number;
    flowNodeId: string;
    flowNodeName: string;
    nextFlowNodeName: string;
    /** 操作类型：1-发起；4-撤销；5-提交；6-退回；7-抄送；8-评论; 9自动抄送; 10转交 */
    operationType: RectificationOperationType;
    transferFromUser: ApprovalUserVos | null;
    transferToUser: ApprovalUserVos | null;
    nextReceivers: ApprovalUserVos[] | null;
    commentator: ApprovalUserVos | null;
}

export interface ComponentValueDetailWrap {
    componentJson: string;
    type: string;
}

export interface FileItem {
    name: string; // 附件名称
    uuid: string; // 附件uuid
    md5: string; // 附件md5
    size: number; // 附件大小
    extension: string;// 附件扩展名
    thumbnailUuid?: string; // 缩略图uuid
    thumbnailMd5?: string; // 缩略图md5
    thumbnailSize?: number; // 缩略图大小
    type?: number; // 附件类型
}
export interface ComponentOptionAttributeDetail {
    name?: string;
    options?: OptionDetail[];
}

export interface OptionDetail {
    checked?: boolean;
    id: string;
    index?: number;
    value: string;
}

export interface ComponentBooleanAttributeDetail {
    name?: string;
    value?: boolean;
}

export interface ServerCustomFieldPersonData {
    isLeave: boolean;
    realName: string;
    userName: string;
}
export interface ServerCustomFieldData extends ComponentValueDetail {
    value: string | null;
    optionIds: string[] | ServerCustomFieldPersonData[];
    valueElements: string;
    refType: string;
    attachments: string;
}
export interface ComponentValueDetail {
    /**
     * 权限: 1-代表隐藏 2-代表仅可读 4-代表可编辑
     */
    auth?: number;
    /**
     * 表单元素的bool属性列表
     */
    booleanAttributes: ComponentBooleanAttributeDetail[];
    /**
     * 是否有修改记录：true-有修改记录，false-无修改记录
     */
    hasHistory: boolean;
    /**
     * 表单元素id
     */
    id: string;
    /**
     * 表单元素的选项属性列表
     */
    optionAttributes: ComponentOptionAttributeDetail[];
    /**
     * 表单元素的引用属性列表
     */
    refAttributes?: ComponentRefAttributeDetail[];
    /**
     * 表单元素的字符属性列表
     */
    stringAttributes: ComponentStringAttributeDetail[];
    /**
     * 表单元素类型
     */
    type?: string;
    /**
     * 一个审批表单组件中的内容的唯一标识，组件未填些内容时，用于判断内容是否为新增
     */
    valueId?: string | null;
}
export interface ComponentRefAttributeDetail {
    name?: string;
    refType?: string;
    refs?: RefDetail[];
}
export interface RefDetail {
    ref?: string;
    refedResourceName?: string;
}
export interface ComponentStringAttributeDetail {
    name?: string;
    value?: string;
}

export interface ExamineRecord {
    name: string;
    role: string;
    operationType: number;
    date: Date;
    avatar: string;
    comment: string;
    attachments: Attachment[];
    copyToName?: string;
    handOverName?: string;
}

export interface Attachment {

    name: string;
    uuid: string;
    fileType: string;
    md5?: string;
    size?: number;

    thumbnail?: Omit<Attachment, "thumbnail">;
}

export interface AvatarModel {
    uuid?: string;
    url: string;
}
