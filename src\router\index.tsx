import lodable from "@loadable/component";
import {RouteConfig} from "react-router-config";
import React from "react";
import {Redirect} from "react-router-dom";

const routes: RouteConfig[] = [
    {
        path: "/",
        exact: true,
        render: () => <Redirect to="/login" />
    },
    {
        name: "登录",
        path: "/login",
        // eslint-disable-next-line import/no-cycle
        component: lodable(async () => import("../views/login"))
    },
    {
        name: "主页",
        path: "/main",
        component: lodable(async () => import("../views/MenuLayout")),
        routes: [
            {
                name: "沙盘管理",
                path: "/main/scheduleManagement/home",
                component: lodable(async () => import("../views/sandTableManage")),
            },
            {
                name: "计划编制",
                path: "/main/schedule/list",
                // path: "/main/schedule/preparation",
                component: lodable(async () => import("../views/schedule/Preparation"))
            },
            {
                name: "计划审批",
                // path: "/main/schedule/planApproval/:routerType",
                path: "/main/schedule/planApproval",
                component: lodable(async () => import("../views/PlanApprovalManage"))
            },
            {
                name: "实际进度",
                path: "/main/actual/list",
                component: lodable(async () => import("../views/ActualProgress"))
            },
            {
                name: "进度检查",
                path: "/main/scheduleManagement/abnormalEvent/inspect",
                component: lodable(async () => import("../views/Inspect/HiddenDangerCheck"))
            },
            {
                name: "整改记录",
                path: "/main/scheduleManagement/abnormalEvent/rectification",
                component: lodable(async () => import("../views/Rectification"))
            },
            {
                name: "进度报警",
                path: "/main/scheduleManagement/alarm",
                component: lodable(async () => import("../views/Alarm"))
            },
            {
                name: "进度统计",
                path: "/main/statistics",
                component: lodable(async () => import("../views/Statistics"))
            },
            {
                name: "规则设置",
                path: "/main/baseSettings/ruleSetting",
                component: lodable(async () => import("../views/BaseSettings/RuleSetting"))
            },
        ]
    },
];

export default routes;
