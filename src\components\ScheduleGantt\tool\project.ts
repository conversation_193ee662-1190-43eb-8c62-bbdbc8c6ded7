/* eslint-disable import/prefer-default-export */
import anzhuang from "../../../../static/image/project_type_anzhuang_ic.png";
import bentely from "../../../../static/image/project_type_bentely_ic.png";
import c3d from "../../../../static/image/project_type_c3d_ic.png";
import catia from "../../../../static/image/project_type_catia_ic.png";
import gangjin from "../../../../static/image/project_type_gangjin_ic.png";
import ifc from "../../../../static/image/project_type_ifc_ic.png";
import jzx from "../../../../static/image/project_type_jzx_ic.png";
import revit from "../../../../static/image/project_type_revit_ic.png";
import rhino from "../../../../static/image/project_type_rhino_ic.png";
import site from "../../../../static/image/project_type_site_ic.png";
import tekla from "../../../../static/image/project_type_tekla_ic.png";
import tujian from "../../../../static/image/project_type_tujian_ic.png";
import pkpm from "../../../../static/image/project_type_pkpm_ic.png";
import unmatch from "../../../../static/image/project_type_unmatch_ic.png";

export const getProjectTypeIcon = (type: number) => {
    switch (type) {
        case 1:
            return tujian;
        case 2:
            return anzhuang;
        case 3:
            return gangjin;
        case 4:
            return revit;
        case 5:
            return tekla;
        case 8:
            return jzx;
        case 9:
            return site;
        case 10:
            return c3d;
        case 11:
            return bentely;
        case 12:
            return rhino;
        case 13:
            return ifc;
        case 14:
            return catia;
        case 15:
            return pkpm;
        default:
            return unmatch;
    }
};
