/** 分页信息 */
export interface LBPageInfo {
    /** 当前页数，必传，默认1 */
    currentPage: number;
    /** 每页显示多少条， 必传 */
    pageSize: number;
    /** 总数，不传 */
    totalNumber: number;
    /** 总共有多少页，不传 */
    totalPage: number;
}

/** 工序模板列表 */
export interface ProcessTemplateInfo {
    /** 模板ID */
    id: string;
    /** 0-普通模板 1-默认模板 */
    isDefault: 0 | 1;
    /** 模板修改时间 */
    modifyTime: string;
    /** 模板最后修改者 */
    modifyUser: string;
    /** 模板名称 */
    templateName: string;
}

export interface ProcessTemplateListRes {
    lbPageInfo: LBPageInfo;
    list: ProcessTemplateInfo[];
}

export interface ProcessTemplateTreeNode {
    /** 状态颜色(注：只有name表示状态的时候才有color属性) */
    color?: string;
    /** 是否是父节点 */
    isParent: boolean;
    /** 节点/状态名称 */
    name: string;
    /** 节点下的子节点 */
    children?: ProcessTemplateTreeNode[];
}

export interface ProcessTmplTreeNode extends ProcessTemplateTreeNode {
    /**
     * 本地生成的工序模板树节点的唯一标识符
     *
     * 叶子节点的key由后端返回的stateKey代替
    */
    key: string;
    /** 由根节点到当前节点的全部路径的name组合而成的字符串 */
    pathStr: string;
    /** 节点下的子节点 */
    children?: ProcessTmplTreeNode[];
    /** 由后端生成的工序id，只有叶子节点才有 */
    stateKey?: string;
}

/** 当前工程绑定的工序模板信息 */
export interface IworksProcessTemplate {
    /** 代理工程Id */
    ppid: number;
    /** 模板内容 */
    projStateNode?: IworksProcessTemplateTreeNode;
    /** 模板id，为空表示用的默认模板 */
    templateId?: string;
}

export interface IworksProcessTemplateTreeNode {
    /** 节点名称，作为根节点时固定为“全部” */
    nodeName?: string;
    /** 当前节点下的工程节点集合，作为参数时，没有则留空 */
    states?: IworksProcessTemplateLeafNode[];
    /** 当前节点下的子节点集合,作为参数时，没有则留空 */
    subNodes?: IworksProcessTemplateTreeNode[];
}

export interface IworksProcessTemplateLeafNode {
    /** 状态颜色,取值型如255,0,0 */
    stateColor?: string;
    /** 工序id,后端自动生成 */
    stateKey?: string;
    /** 状态名称，作为参数时必填，不能有特殊字符，长度不超过50个字符 */
    stateName?: string;
    earlyWarn?: boolean;
}

export interface CompProcessListParam {
    /** 当前页,从1开始 */
    page: number;
    /** 工程id */
    ppid: number;
    /** 构件信息集合 */
    projectInfos?: CompNecInfo[];
    /** 分页大小 */
    size: number;
}

export interface CompProcessSaveParam {
    /** 完工时间：不为空表示完工，为空表示不完工（手动选择：选择的日期不得早于最后一道工序完成日期，不得晚于当前时间） */
    finishEndDate?: number;
    /** 完工工序id，最晚一条完工工序的id */
    finishStateKey?: string;
    /** 工序集合,为空代表删除除记录 */
    infos?: CompStateParam[];
    /** 工程id */
    ppid: number;
    /** 构件信息集合 */
    projectInfos: CompNecInfo[];
}

/** 识别一个构件的必要信息，包含handle和path */
export interface CompNecInfo {
    /** 构件handle */
    handle: string;
    /** 构件path */
    path: string;
    /** 构件楼层 */
    floor: string;
}

/** 构件绑定的单条工序信息 */
export interface CompProcessItem {
    /** 完工时间 */
    finishEndDate?: number;
    /** 完工工序id，最晚一条完工工序的id */
    finishStateKey?: string;
    /** id */
    id: string;
    /** 工序集合,为空代表删除改记录 */
    infos?: CompStateParam[];
    /** 工程id */
    ppid: number;
    /** 构件信息 */
    projectInfo: CompNecInfo;
    /** 工序md5，用于判断工序集合内容是否一致 */
    stateMd5: string;
}

export type CompWithoutProcess = Omit<CompProcessItem, "infos">;

export interface CompStateParam {
    /** 提前报警 true:是，false：否 */
    earlyWarn: boolean;
    /** 构件状态的生命周期信息 */
    lifeCycles: ComponentStateLifeCycleParam;
    /** 状态颜色,取值型如255,0,0 */
    stateColor: string;
    /** 工序id */
    stateKey: string;
    /** 工序名称 */
    stateName: string;
    /** 工序路径 */
    statePath: string;
}

export interface ComponentStateLifeCycleParam {
    /** 实际结束日期 */
    endDate?: number;
    /** 计划结束日期 */
    planEndDate: number;
    /** 计划开始日期 */
    planStartDate: number;
    /** 实际开始日期 */
    startDate?: number;
}

export interface StateWarningPeriodInfo {
    /** 计划结束日期 */
    planEndTime: number;

    /** 实际结束日期 */
    actualEndTime: number;

    /** 比较唯一码 */
    periodKey: string;
}

/** 工序完工状态 */
// eslint-disable-next-line import/prefer-default-export
export enum ProcessStatus {
    /** 未开始 */
    Undo,
    /** 进行中 */
    Doing,
    /** 已完成 */
    Done
}
