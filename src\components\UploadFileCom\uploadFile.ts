import OSS from "ali-oss";
import {getAttachmentUploadUrl} from "../../api/common.api";
import {FileType} from "../../api/common.type";
import {createFileMd5, emptyFun, toArr, Base64Decode, Base64Encode} from "../../assets/ts/utils";
import UploadFetch from "../../service/UploadFetch";

interface UploadFileProps {
    file: File;
    onSuccess?: (val: FileType) => void;
    onProgress?: (percent: number) => void;
}

interface AliOssInfoType {
    "oss-AccessKeyId": string;
    "oss-AccessKeySecret": string;
    "oss-Endpoint": string;
    "oss-sts_token": string;
    "oss-BucketName": string;
    "oss-ObjectName": string;
    "callbackUrl": string;
    "callbackBody": string;
    "callbackBodyType": string;
    "x:lbUploadInfo": unknown;
}

const uploadFileOss = async (props: UploadFileProps) => {
    const {file, onSuccess = emptyFun, onProgress = emptyFun} = props;
    const fileMd5 = await createFileMd5(file);
    const uploadUrlParams = {
        fileMd5,
        fileSize: file.size,
        fileName: file.name,
        suportModes: [2],
        isBPUpload: false,
        isCheckFastUpload: true,
    };
    const uploadUrlRes = await getAttachmentUploadUrl([uploadUrlParams]);
    if (toArr(uploadUrlRes).length === 0) {
        return;
    }
    const uploadUrlInfo = uploadUrlRes[0];
    if (uploadUrlInfo.finished === true) {
        onSuccess({
            fileUuid: uploadUrlInfo.fileUUID,
            fileName: file.name,
            fileSize: file.size,
        });
        return;
    }
    if (toArr(uploadUrlInfo.uploadUrls).length !== 0) {
        const uploadUrl = uploadUrlInfo.uploadUrls[0];
        const uploadRes = await UploadFetch(
            uploadUrl,
            file,
            onProgress
        );
        onSuccess({
            fileUuid: uploadRes,
            fileName: file.name,
            fileSize: file.size
        });
    }
    if (toArr(uploadUrlInfo.moreParams).length !== 0) {
        const authInfo = uploadUrlInfo.moreParams.find((el) => el.key === "authInfo");
        if (authInfo === undefined) {
            return;
        }
        const aliOSSInfo: AliOssInfoType = JSON.parse(Base64Decode(authInfo.value));
        const client = new OSS({
            endpoint: aliOSSInfo["oss-Endpoint"],
            accessKeyId: aliOSSInfo["oss-AccessKeyId"],
            accessKeySecret: aliOSSInfo["oss-AccessKeySecret"],
            stsToken: aliOSSInfo["oss-sts_token"],
            bucket: aliOSSInfo["oss-BucketName"],
        });
        const options = {
            callback: {
                url: aliOSSInfo.callbackUrl,
                body: aliOSSInfo.callbackBody,
                contentType: aliOSSInfo.callbackBodyType,
                customValue: {
                    lbUploadInfo: Base64Encode(aliOSSInfo["x:lbUploadInfo"])
                }
            }
        };
        const result = await client.put(
            aliOSSInfo["oss-ObjectName"],
            file,
            options
        );
        const resData = result.data as unknown as {uuid: string};
        onSuccess({fileName: file.name, fileSize: file.size, fileUuid: resData.uuid});
    }
};

export default uploadFileOss;
