/* eslint-disable @typescript-eslint/camelcase */
import React, {useCallback, useEffect, useRef, useState} from "react";
import {Checkbox, Col, DatePicker, Form, InputNumber, message, Radio, Row} from "antd";
import {FormInstance} from "antd/lib/form";
import {gantt} from "@iworks/dhtmlx-gantt";
import moment, {Moment} from "moment";
import {GanttTask} from "../gantt/interface";
import {TaskStatus} from "../common/constant";
import {isStandardCalendar} from "../common/calendar";
import ganttManager from "../gantt/ganttManager";

interface ActualWorkItemsProps {
    form: FormInstance;
    editTask: GanttTask | undefined;
}

const ActualWorkItems: React.FC<ActualWorkItemsProps> = (props) => {
    const {form, editTask} = props;
    const [completionLimit, setCompletionLimit] = useState<{min: number; max: number}>({min: 0, max: 100});
    const [execStatus, setExecStatus] = useState<number | undefined>(undefined);
    const lastStartDateRef = useRef<Date | undefined>(undefined);

    useEffect(() => {
        if (editTask !== undefined) {
            const status = editTask.taskStatus ?? TaskStatus.NOT_START;
            setExecStatus(status);
            form.setFieldsValue({
                taskStatus: status,
                progress: editTask.progress !== undefined ? (editTask.progress * 100).toFixed(2) : undefined,
            });
            if (editTask.actual_start !== undefined) {
                const startDate = gantt.getClosestWorkTime({
                    dir: "future",
                    date: gantt.date.day_start(editTask.actual_start),
                    unit: gantt.config.duration_unit,
                    task: editTask
                });
                lastStartDateRef.current = startDate;
                form.setFieldsValue({
                    actual_start_date: moment(startDate),
                });
            }
            if (editTask.actual_end !== undefined) {
                form.setFieldsValue({
                    actual_end_date: moment(editTask.actual_end),
                });
            }
        }
    }, [form, editTask]);

    useEffect(() => {
        if (execStatus === TaskStatus.NOT_START) {
            setCompletionLimit({min: 0, max: 0});
            lastStartDateRef.current = undefined;
            form.setFieldsValue({
                progress: 0,
                actual_start_date: undefined,
                actual_end_date: undefined,
            });
        } else if (execStatus === TaskStatus.IN_PROGRESS) {
            setCompletionLimit({min: 0, max: 100});
            form.setFieldsValue({actual_end_date: undefined});
        } else if (execStatus === TaskStatus.COMPLETED) {
            setCompletionLimit({min: 100, max: 100});
            form.setFieldsValue({progress: 100});
        }
    }, [form, execStatus]);

    /**
     * 修改开始时间
     */
    const handleStartDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        // console.log("oldDate", lastStartDateRef.current);
        if (lastStartDateRef.current !== undefined && date.toDate() > new Date()) {
            message.error("实际开始时间不能晚于当前时间");
            form.setFieldsValue({actual_start_date: moment(lastStartDateRef.current)});
            return;
        }
        const startDate = gantt.getClosestWorkTime({
            dir: "future",
            date: gantt.date.day_start(date.toDate()),
            unit: gantt.config.duration_unit,
            task: editTask
        });
        lastStartDateRef.current = startDate;
        form.setFieldsValue({actual_start_date: moment(startDate)});
    }, [editTask, form]);

    /**
     * 修改结束时间
     */
    const handleEndDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        const endDate = gantt.date.add(gantt.date.day_start(date.toDate()), isStandardCalendar(ganttManager.currentCalendarInfo) ? 17 : 0, "hour");
        form.setFieldsValue({actual_end_date: moment(endDate)});
    }, [form]);

    const actualStartDisable = execStatus === TaskStatus.NOT_START || editTask?.type === gantt.config.types.project;
    const actualEndDisable = execStatus !== TaskStatus.COMPLETED || editTask?.type === gantt.config.types.project;

    return (
        <Row>
            <Col span={24}>
                <Form.Item
                    label="执行状态"
                    name="taskStatus"
                    labelCol={{span: 4}}
                    wrapperCol={{span: 20}}
                >
                    <Radio.Group
                        style={{width: "100%"}}
                        disabled={editTask?.type === gantt.config.types.project}
                        onChange={(e) => setExecStatus(e.target.value)}
                    >
                        <Radio value={TaskStatus.NOT_START}>未开始</Radio>
                        <Radio value={TaskStatus.IN_PROGRESS}>进行中</Radio>
                        <Radio value={TaskStatus.COMPLETED}>已完成</Radio>
                    </Radio.Group>
                </Form.Item>
            </Col>
            <Col span={16}>
                <Form.Item
                    style={{position: "relative"}}
                    label="累计实际完成"
                    labelCol={{span: 6}}
                    wrapperCol={{span: 16}}
                >
                    <Form.Item
                        noStyle
                        name="progress"
                        validateTrigger="onChange"
                        rules={[
                            ({getFieldValue}) => ({
                                validator: async () => {
                                    let progress = getFieldValue("progress");
                                    if (execStatus === TaskStatus.IN_PROGRESS) {
                                        if (progress === null || progress === undefined) {
                                            return Promise.reject(new Error("请输入累计实际完成"));
                                        }
                                        progress = Number.parseFloat(progress);
                                        if (progress <= 0 || progress >= 100) {
                                            return Promise.reject(new Error("累计实际完成须在0到100之间!"));
                                        }
                                    }
                                    return Promise.resolve();
                                },
                            }),
                        ]}
                    >
                        <InputNumber
                            style={{width: "100%"}}
                            disabled={execStatus !== TaskStatus.IN_PROGRESS}
                            precision={2}
                            min={completionLimit.min}
                            max={completionLimit.max}
                            placeholder="请输入累计实际完成"
                        />
                    </Form.Item>
                    <span style={{position: "absolute", right: "-20px", lineHeight: "32px"}}>%</span>
                </Form.Item>
            </Col>
            <Col span={8}>
                <Form.Item
                    label="典型偏差"
                    name="is_typical_offset"
                    labelCol={{span: 20}}
                    wrapperCol={{span: 4}}
                    valuePropName="checked"
                >
                    <Checkbox />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    label="实际开始"
                    name="actual_start_date"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                    rules={[
                        {
                            required: !actualStartDisable,
                            message: "请选择实际开始时间"
                        }
                    ]}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={actualStartDisable}
                        onChange={handleStartDateChange}
                        placeholder="请选择实际开始时间"
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    label="实际完成"
                    name="actual_end_date"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                    rules={[
                        {
                            required: !actualEndDisable,
                            message: "请选择实际完成时间"
                        }
                    ]}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={actualEndDisable}
                        onChange={handleEndDateChange}
                        placeholder="请选择实际完成时间"
                    />
                </Form.Item>
            </Col>
        </Row>
    );
};

export default ActualWorkItems;
