import * as Types from "./actionTypes";
import {PersonState, UserItems, ApprovalUserInfo, Person, PersonTreeData} from "./interface";
import {getUsersOfOrg, getUsersOfRole, getUrlByUuid} from "../../api/person";

export const setPersonData = (info: PersonState) => ({
    type: Types.SET_PERSON_DATA,
    payload: info
});

export const clearPesonData = () => ({
    type: Types.CLEAR_PERSON_DATA
});

export const setPersonDataLoading = (payload: boolean) => ({
    type: Types.SET_PERSON_DATA_LOADING,
    payload
});

const searchUser = <T extends UserItems>(list: T[], searchKey: string) => {
    const res: T[] = [];
    list.forEach((item) => {
        const user = item.users ?? [];
        const newUser = user.filter((u) => {
            const realName = u.realName ?? "";
            const userName = u.userName ?? "";
            return realName.includes(searchKey) || userName.includes(searchKey);
        });
        res.push({
            ...item,
            users: newUser
        });
    });
    return res;
};

const transformUserToPerson = (u: ApprovalUserInfo, avatarMap: Map<string, string>) => {
    const avatarImg = avatarMap.get(u.portraitUuid ?? "");
    const ret: Person = {
        name: u.userName ?? "",
        id: u.userName ?? "",
        avatar: avatarImg ?? ""
    };
    return ret;
};


export const loadPersonDataAsync = async (searchKey: string, deptId: string) => {
    const orgRes = await getUsersOfOrg(searchKey, deptId);
    const roleRes = await getUsersOfRole(searchKey, deptId);
    if (orgRes === null || roleRes === null) {
        throw new Error("no response");
    }
    const orgUuids = orgRes.map((o) => o.users).flat().map((u) => u.portraitUuid);
    const roleUuids = roleRes.map((r) => r.users).flat().map((u) => u.portraitUuid);
    const uuids = Array.from(new Set([...orgUuids, ...roleUuids])).filter((i) => typeof i === "string") as string[];
    const urlList = await getUrlByUuid(uuids);
    const avatarMap = new Map<string, string>();
    urlList.forEach((urlInfo) => {
        const urlLink = urlInfo.downloadUrls ?? [""];
        avatarMap.set(urlInfo.fileUUID, urlLink[0] ?? "");
    });

    const searchedOrgData = searchUser(orgRes, searchKey);
    const filteredSearchedOrgData = searchedOrgData.filter((u) => {
        const hasUsers = u.users.length > 0;
        const hasParent = searchedOrgData.find((p) => p.parentId === u.id) !== undefined;
        return hasUsers || hasParent;
    });
    const orgTreeData = filteredSearchedOrgData.map((d) => {
        const ret: PersonTreeData = {
            key: d.id,
            parentId: d.parentId,
            name: d.name,
            children: d.users.map((u) => ({
                name: `${u.realName ?? u.userName!}(${u.userName})`,
                key: u.userName ?? "",
                parentId: "",
                person: transformUserToPerson(u, avatarMap),
                children: []
            }))
        };
        return ret;
    });

    const roleTreeData = searchUser(roleRes, searchKey).filter((i) => i.users.length > 0).map((d, idx) => {
        const ret: PersonTreeData = {
            name: d.role,
            key: idx.toString(),
            children: d.users.map((u) => {
                const retInner: PersonTreeData = {
                    key: u.userName ?? "",
                    name: `${u.realName ?? u.userName!}(${u.userName})`,
                    person: transformUserToPerson(u, avatarMap),
                    children: []
                };
                return retInner;
            })
        };
        return ret;
    });

    orgTreeData.forEach((data) => {
        const parent = orgTreeData.find((d) => d.key === data.parentId && d.key !== data.key);
        if (parent === undefined) {
            return;
        }
        parent.children.unshift(data);
    });

    const orgTreeRoot = orgTreeData.find((o) => o.key === o.parentId)!;
    let users = orgRes.map((u) => u.users).flat().concat(roleRes.map((r) => r.users).flat());
    users = users.filter((u) => u.userName !== undefined);

    const ret: PersonState = {
        orgData: [orgTreeRoot],
        roleData: roleTreeData,
        userNames: users.map((u) => u.userName!),
        userNameMap: new Map<string, string>(users.map((u) => [u.userName!, u.realName!])),
        avatarMap,
        personDataLoading: false
    };

    return ret;
};

type SetPersonData = ReturnType<typeof setPersonData>;
type ClearPersonData = ReturnType<typeof clearPesonData>;
type SetPersonDataLoading = ReturnType<typeof setPersonDataLoading>;
type Actions = SetPersonData | ClearPersonData | SetPersonDataLoading;
export default Actions;
