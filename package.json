{"name": "plan-manager-web", "version": "2.2.0", "description": "进度系统", "main": "", "scripts": {"start": "lubangoWebpack start 8289 --env 144", "start:193": "lubangoWebpack start 8289 --env 193", "start:144": "lubangoWebpack start 8289 --env 144", "build": "lubangoWebpack build", "build:193": "lubangoWebpack build --env 193", "buildtsc": "yarn tsc", "clean": "rm -rf .cache && rm -rf dist && rm -f tsconfig.*.tsbuildinfo", "build:dry": "yarn tsc -p tsconfig.ci.json", "build:clean": "yarn clean && yarn build", "build:watch": "yarn tsc -w", "release": "npm run clean && npm run build && node parcelPostHTML.js", "delZip": "node script/delZip.js", "zip:144": "node script/zip.js 144", "zip:246": "node script/zip.js 246", "zip:193": "node script/zip.js 193", "zip:saas-beta": "node script/zip.js saas-beta", "zip:prod": "node script/zip.js prod", "zip:xmjc": "node script/zip.js xmjc", "zip:xmjc-beta": "node script/zip.js xmjc-beta", "zip:nxsl": "node script/zip.js nxsl", "zip:xmjc-prod": "yarn delZip && yarn zip:xmjc && yarn zip:xmjc-beta", "zip:nxsl-prod": "yarn delZip && yarn release && yarn zip:nxsl", "upload:144": "node script/upload.js plan http://**************:8282/deploy", "upload:246": "node script/upload.js plan http://**************:8282/deploy", "upload:193": "node script/upload.js plan http://*************:8282/deploy", "release:upload:161": "yarn delZip && yarn release && yarn zip:161 && yarn upload:161", "release:upload:246": "yarn delZip && yarn release && yarn zip:246 && yarn upload:246", "release:upload:193": "yarn delZip && yarn release && yarn zip:193 && yarn upload:193", "release:upload:144": "yarn delZip && yarn release && yarn zip:144 && yarn upload:144", "fix-memory-limit": "cross-env LIMIT=8192 increase-memory-limit"}, "author": "", "license": "ISC", "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@luban/devenv": "^1.3.0", "@luban/linter": "1.10", "@lubango/webpack": "^1.1.14", "@types/ali-oss": "^6.16.4", "@types/backbone": "^1.4.15", "@types/dagre": "^0.7.47", "@types/file-saver": "^2.0.5", "@types/jspdf": "^2.0.0", "@types/loadable__component": "^5.13.3", "@types/lodash-es": "^4.17.6", "@types/react": "^17.0.3", "@types/react-color": "^3.0.6", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.5", "@types/react-portal": "^4.0.4", "@types/react-router-config": "^5.0.2", "@types/react-router-dom": "^5.1.6", "@types/react-virtualized": "^9.21.21", "@types/react-window": "^1.8.5", "@types/spark-md5": "^3.0.2", "@types/video.js": "^7.3.44", "archiver": "^5.0.0", "cross-env": "5.0.5", "husky": "^4.3.7", "increase-memory-limit": "1.0.3", "lint-staged": "^10.5.3", "request": "^2.88.2", "request-promise": "^4.2.6"}, "dependencies": {"@ant-design/icons": "^4.3.0", "@daihy8759/protocol-check": "^1.0.4", "@iworks/dhtmlx-gantt": "^7.0.9", "@iworks/iworksframe": "^1.12.6", "@iworks/login": "1.10.7", "@iworks/plan": "1.11.0-feat-20220330.10", "@loadable/component": "^5.14.1", "@luban/react-open-tab": "^1.2.2", "@motor/core": "3.4.4-2.4.0-Release", "@types/postmate": "^1.5.2", "ahooks": "^3.5.2", "ali-oss": "^6.17.1", "antd": "4.18.9", "clsx": "^1.1.1", "compressorjs": "^1.1.1", "crypto-js": "^4.1.1", "dagre": "^0.8.5", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "jointjs": "^3.5.5", "js-md5": "^0.7.3", "jspdf": "^2.5.1", "jss": "^10.9.0", "jss-preset-default": "^10.9.0", "lodash-es": "^4.17.21", "moment": "^2.29.1", "nanoid": "^3.3.2", "postmate": "^1.5.2", "query-string": "^7.1.1", "rc-resize-observer": "^1.2.1", "react": "^16.13.1", "react-color": "^2.19.3", "react-dom": "^16.13.1", "react-helmet": "^6.1.0", "react-jss": "^10.6.0", "react-portal": "^4.2.2", "react-redux": "^7.2.3", "react-router-config": "^5.1.1", "react-router-dom": "^5.2.0", "react-virtualized": "^9.22.3", "react-window": "^1.8.8", "redux": "^4.0.5", "redux-dynamic-modules": "^5.2.3", "redux-dynamic-modules-thunk": "^5.2.3", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.1", "reselect": "^4.1.6", "spark-md5": "^3.0.2", "typescript": "3.9", "uuid": "^8.3.2", "video.js": "^7.20.2"}}