/* eslint-disable max-len */
import React, {useCallback, useEffect, useState, useMemo} from "react";
import {Col, Radio, RadioChangeEvent, Row} from "antd";

import {useSelector} from "react-redux";
import {DataNode, TreeProps} from "antd/lib/tree";
import useStyle from "../style";
import WarningList from "./warningList";
import DefaultStatus from "./defaultStatus";
import BimDirTreeWithFilter from "../../../../components/BimDirTreeWithFilter";
import {MotorContext, DirTreeWithPath} from "../../../../reMotor";
import {RootState} from "../../../../store/rootReducer";
import ProcessTmplTreeWithFilter from "../../../../components/ProcessTmplTreeWithFilter";
import {Defined, isDefined, dfsTree, isNonEmptyArray} from "../../../../assets/ts/utils";
import {pathSeparator, sandTableFullScreenDomId} from "../helps";
import {CheckedInfoType, CheckInfo} from "./index.d";
import MyIcon from "../../../../components/MyIcon";
import useFullScreenChange from "../../../../hooks/useFullScreenChange";
import Color from "../../../../assets/css/Color";

const radioOptions = [
    {label: "EBS", value: "ebsList"},
    {label: "工序列表", value: "workProcessList"},
    {label: "默认状态", value: "defaultStatus"},
    {label: "报警列表", value: "warningList"}
];

const BimLeftView = (props: {handleCheckedInfo: (activeTab: string, checkedInfo: CheckedInfoType) => void}) => {
    const cls = useStyle();
    const {handleCheckedInfo} = props;
    const {playDimension, playStatus, sandTableType} = useSelector((state: RootState) => state.sandManageData);
    const {tmplTree} = useSelector((state: RootState) => state.processData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);

    const [activeTab, setActiveTab] = useState<string>("ebsList");
    const [ebsTree, setEbsTree] = useState<DirTreeWithPath[]>();

    const [ebsCheckedKeys, setEbsCheckedKeys] = useState<string[]>(["root"]);
    const [procedureCheckedKeys, setProcedureCheckedKeys] = useState<string[]>(["0"]);
    const [checkedInfo, setCheckedInfo] = useState<CheckedInfoType>({});
    const [isExpand, setIsExpand] = useState<boolean>(false);
    // const [ebsExpandedKeys, setEbsExpandedKeys] = useState<string[]>();
    const [tmplExpandedKeys, setTmplExpandedKeys] = useState<string[]>();
    const {isTargetFullScreen} = useFullScreenChange(sandTableFullScreenDomId);
    const handleFullScreenChange = useCallback(
        () => {
            if (isTargetFullScreen) {
                setIsExpand(false);
            }
        },
        [isTargetFullScreen]
    );

    useEffect(
        () => {
            handleFullScreenChange();
        },
        [handleFullScreenChange]
    );

    const initEbsDirTree = useCallback(
        async () => {
            const curProj = MotorContext.getProject();
            if (!isDefined(curSandTable) || !isDefined(curProj)) {
                return;
            }
            const tempBimDirTree = await MotorContext.getOpenedCompTree();
            setEbsTree(tempBimDirTree);


            const allCompPath: string[] = [];
            const leafNodeCompPaths: string[] = [];
            dfsTree(tempBimDirTree, (item) => {
                allCompPath.push(item.path);
                if (!isNonEmptyArray(item.children)) {
                    leafNodeCompPaths.push(item.path);
                }
            });
            setEbsCheckedKeys(allCompPath);
            setEbsTree(tempBimDirTree);
            // setEbsExpandedKeys([tempBimDirTree[0]?.path ?? ""]);
        },
        [curSandTable]
    );
    // const handleEbsExpand = useCallback((keys: React.Key[]) => {
    //     setEbsExpandedKeys(keys as string[]);
    // }, []);

    const handleTmplExpand = useCallback((keys: React.Key[]) => {
        setTmplExpandedKeys(keys as string[]);
    }, []);
    useEffect(() => {
        initEbsDirTree();
    }, [initEbsDirTree]);
    useEffect(() => {
        if (playStatus === "playing") {
            setIsExpand(false);
        }
    }, [playStatus]);
    useEffect(() => {
        handleCheckedInfo(activeTab, checkedInfo);
    }, [activeTab, checkedInfo, handleCheckedInfo]);

    const handleModeChange = useCallback((e: RadioChangeEvent) => {
        setActiveTab(e.target.value);
    }, []);

    const onEbsCheck = useCallback<Defined<TreeProps["onCheck"]>>((checkedKeys, info) => {
        const curProj = MotorContext.getProject();
        if (curProj === null) {
            return;
        }
        setEbsCheckedKeys(checkedKeys as string[]);
        const {checkedNodes, checked, halfCheckedKeys, node: {key}} = info;
        setCheckedInfo((oldInfo) => ({
            ...oldInfo,
            ebsList: {
                checkedKeys: checkedNodes.filter((i) => i.children === undefined).map((j) => j.key),
                isCheckAll: checked && (halfCheckedKeys ?? []).length === 0
            }
        }));
        if (playDimension === "default") {
            if (key === "root") {
                curProj.setVisibility(checked);
            } else {
                const pathDirList = (key as string).split(pathSeparator);
                curProj.setVisibility(checked, [pathDirList]);
            }
        }
    }, [playDimension]);

    const onProcedureCheck = useCallback((selectKeys, info: CheckInfo<DataNode>) => {
        const {checkedNodes, checked, halfCheckedKeys} = info;
        setProcedureCheckedKeys(selectKeys);
        setCheckedInfo((oldInfo) => ({
            ...oldInfo,
            workProcessList: {
                checkedKeys: checkedNodes.filter((i) => i.children === undefined).map((j) => j.key),
                isCheckAll: checked && (halfCheckedKeys ?? []).length === 0
            }
        }));
    }, []);

    const sandOptions = useMemo(() => {
        setActiveTab("ebsList");
        if (sandTableType === "underConstruction") {
            return radioOptions.filter((i) => i.value !== "workProcessList" && i.value !== "defaultStatus");
        }
        if (playDimension === "default") {
            return radioOptions.filter((i) => i.value !== "workProcessList");
        }
        return radioOptions.filter((i) => i.value !== "defaultStatus");
    }, [playDimension, sandTableType]);
    const handleExpandSwitch = useCallback(
        () => {
            if (playStatus === "playing") {
                return;
            }
            setIsExpand((prev) => !prev);
        },
        [playStatus],
    );

    const ebsTreeView = useMemo(
        () => {
            if (ebsTree === undefined) {
                return null;
            }
            return (
                <BimDirTreeWithFilter
                    bimDirTree={ebsTree}
                    onCheck={onEbsCheck}
                    checkedKeys={ebsCheckedKeys}
                    hasSelector={false}
                />
            );
        },
        [ebsCheckedKeys, ebsTree, onEbsCheck]
    );

    return (
        <Col className={`${cls.left} ${cls.antTreeWrapper}`} style={{marginLeft: isExpand ? 0 : -480}}>
            <Radio.Group
                optionType="button"
                options={sandOptions}
                buttonStyle="solid"
                value={activeTab}
                onChange={handleModeChange}
                style={{marginBottom: 16}}
                className={cls.radioBox}
            />
            <Row style={{height: "calc( 100% - 56px )"}}>
                <Col span={24} style={{display: activeTab === "ebsList" ? "block" : "none"}}>
                    {ebsTreeView}
                </Col>
                <Col span={24} style={{display: activeTab === "workProcessList" ? "block" : "none"}}>
                    <ProcessTmplTreeWithFilter checkable tmplTree={tmplTree} expandedKeys={tmplExpandedKeys} onExpand={handleTmplExpand} onCheck={onProcedureCheck} checkedKeys={procedureCheckedKeys} />
                </Col>
                <Col span={24} style={{display: activeTab === "warningList" ? "block" : "none"}}>
                    <WarningList setCheckedInfo={setCheckedInfo} />
                </Col>
                <Col span={24} style={{display: activeTab === "defaultStatus" ? "block" : "none"}}>
                    <DefaultStatus setCheckedInfo={setCheckedInfo} />
                </Col>
            </Row>
            <div className={cls.leftSwitch} onClick={handleExpandSwitch}>
                <MyIcon type={isExpand ? "icon-xiangzuo" : "icon-xiangyou"} color={Color.primary} />
            </div>
        </Col>
    );
};

export default BimLeftView;
