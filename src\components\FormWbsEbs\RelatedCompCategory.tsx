import React, {useState, useCallback, useEffect, Key} from "react";
import {createUseStyles} from "react-jss";
import {Space, Tree, TreeProps} from "antd";
import {ProjectTreeNodeData, CompCategoryTreeNodeData, EBSNodeType, getWBSEBSType, transformTree} from "./data";
import {getProjectDetail} from "../../api/common.api";
import {OrgProjNodeVo, ProjNameVo} from "../../api/common.type";
import {toArr, pathSeparator} from "../../assets/ts/utils";
import ProjectSearch, {ProjectSearchProps} from "./ProjecSearch";
import MyIcon from "../MyIcon";
import ComTree from "../ComTree";
import renderText from "../renderTableText/renderText";


const useStyle = createUseStyles({
    box: {},
    tree: {
        flexGrow: 1,
        maxWidth: "50%",
        "& .ant-tree-checkbox-inner": {
            backgroundColor: "#d9d9d9",
            cursor: "not-allowed"
        },
        "& .ant-tree-treenode": {
            width: "100%",
            "& .ant-tree-node-content-wrapper": {
                flexGrow: 1,
                width: 0
            }
        }
    }
});

const getFirstProject = (val: ProjectTreeNodeData[]) => {
    let firstProject: ProjNameVo | undefined;
    const dealTree = (nodeData: ProjectTreeNodeData) => {
        if (firstProject !== undefined) {
            return;
        }
        const tempNodeData = nodeData as unknown as {originData: OrgProjNodeVo};
        if (tempNodeData.originData.projects !== undefined
                && tempNodeData.originData.projects !== null
                && toArr(tempNodeData.originData.projects).length > 0) {
            // eslint-disable-next-line prefer-destructuring
            firstProject = tempNodeData.originData.projects[0];
        } else {
            toArr(nodeData.children ?? []).forEach((el) => dealTree(el));
        }
    };
    val.forEach((el) => dealTree(el));
    return firstProject;
};

interface RelatedCompCategoryProps {
    projectTreeData: ProjectTreeNodeData[];
    value?: EBSNodeType[];
    onChange?: (val: EBSNodeType[]) => void;
}

const RelatedCompCategory = (props: RelatedCompCategoryProps) => {
    const {projectTreeData, value, onChange} = props;
    const cls = useStyle();
    // 项目树选中的,单选
    const [projectSelected, setProjectSelected] = useState<ProjNameVo>();
    // 构件类别树
    const [compCategoryTreeData, setCompCategoryTreeData] = useState<CompCategoryTreeNodeData[]>([]);
    // 构件类别树选中的,多选
    const [compCateChecked, setCompCateChecked] = useState<string[]>([]);
    // 项目树
    const [treeData, setTreeData] = useState<ProjectTreeNodeData[]>([]);
    // 项目树选中的,多选
    const [projectTreeChecked, setProjectTreeChecked] = useState<Key[]>([]);

    const tempType = getWBSEBSType(value);

    const getCompCategotyList = useCallback((ppid: string | number) => {
        getProjectDetail(`${ppid}`).then((res) => {
            if (res.data === null || Boolean(res.data) === false) {
                setCompCategoryTreeData([]);
                return;
            }
            const tempTreeData = toArr(res.data.treeNodeList ?? []).map((el) => transformTree(el, []));
            setCompCategoryTreeData(tempTreeData);
        });
    }, []);

    useEffect(() => {
        if (projectSelected === undefined) {
            const firstProjectData = getFirstProject(projectTreeData);
            if (firstProjectData === undefined) {
                return;
            }
            setProjectSelected(firstProjectData);
            getCompCategotyList(firstProjectData?.ppid as number | string);
        }
    }, [getCompCategotyList, projectSelected, projectTreeData]);


    useEffect(() => {
        if (tempType === 3) {
            setCompCateChecked(toArr(value ?? []).map((el) => toArr(el.paths ?? []).join(pathSeparator)));
            setProjectTreeChecked(toArr(value ?? []).map((el) => el.ppid));
        } else {
            setCompCateChecked([]);
            setProjectTreeChecked([]);
        }
    }, [tempType, value]);

    const handleProjectTreeSelect: TreeProps["onSelect"] = (_key, _node) => {
        const node = _node.node as unknown as {originData: ProjNameVo};
        if (_node.selected === true && node.originData.ppid !== undefined) {
            setProjectSelected(node.originData);
            getCompCategotyList(node.originData.ppid);
        } else {
            setCompCategoryTreeData([]);
            setProjectSelected(undefined);
        }
    };

    const handleCompCategoryTreeChecked: TreeProps["onCheck"] = (_checkedKeys, _info) => {
        if (projectSelected === undefined || projectSelected.ppid === undefined) {
            return;
        }
        const checkedNodes = _info.checkedNodes as unknown as CompCategoryTreeNodeData[];
        let tempNode: EBSNodeType[] = checkedNodes
            .filter((el) => toArr(el.children ?? []).length !== 0)
            .map((el) => ({ppid: projectSelected.ppid ?? 0, projName: projectSelected.projName, handle: undefined, paths: `${el.key}`.split(pathSeparator)}));
        let tempCheckedPpid = [...projectTreeChecked];
        if (tempNode.length === 0) {
            tempCheckedPpid = tempCheckedPpid.filter((el) => el !== projectSelected.ppid);
        } else {
            tempCheckedPpid.push(projectSelected.ppid);
        }
        if (tempType === 3) {
            const tempValue = toArr(value ?? []).filter((el) => el.ppid !== projectSelected.ppid);
            tempNode = tempNode.concat(tempValue);
        }
        if (onChange !== undefined) {
            onChange(tempNode);
        }
    };

    const handleProjectTreeChange: ProjectSearchProps["onProjectTreeChange"] = useCallback(
        (val) => {
            setTreeData(val);
        },
        []
    );

    const renderTreeNode = useCallback((node: ProjectTreeNodeData) => {
        const nodeData = node.originData as OrgProjNodeVo;
        if (nodeData.type === 0) {
            return (
                <Space>
                    <div><MyIcon type="icon-gaosuxiangmu" fontSize={16} color="#000" style={{position: "relative", top: 2}} /></div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        if (nodeData.type === 1) {
            return (
                <Space>
                    <div style={{backgroundColor: "#2DA641", padding: "2px", color: "#fff", lineHeight: 1}}>标</div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        return (
            <div style={{width: "100%"}}>{renderText(node.title as string)}</div>
        );
    }, []);

    return (
        <div className={cls.box} style={{display: "flex", flexDirection: "column", height: "100%"}}>
            <ProjectSearch projectTreeValue={value} projectTree={projectTreeData} onProjectTreeChange={handleProjectTreeChange} />
            <div style={{display: "flex", flexGrow: 1, height: 0, marginTop: 16, overflowY: "auto"}}>
                {
                    treeData.length > 0 && (
                        <Tree
                            titleRender={renderTreeNode}
                            defaultExpandAll
                            className={cls.tree}
                            treeData={treeData}
                            selectedKeys={projectSelected?.ppid === undefined ? undefined : [projectSelected.ppid]}
                            checkable
                            onSelect={handleProjectTreeSelect}
                            checkedKeys={projectTreeChecked}
                        />
                    )
                }
                {
                    compCategoryTreeData.length > 0 && (
                        <div style={{backgroundColor: "#E1E2E5", width: 328, overflowY: "auto", height: "100%"}}>
                            <ComTree
                                defaultExpandAll
                                style={{background: "#E1E2E5"}}
                                checkable
                                treeData={compCategoryTreeData}
                                onCheck={handleCompCategoryTreeChecked}
                                checkedKeys={compCateChecked}
                            />
                        </div>
                    )
                }
            </div>
        </div>
    );
};

export default RelatedCompCategory;
