import React from "react";
import {ModalProps} from "antd/lib/modal";
import {createUseStyles} from "react-jss";
import {ConditionFlowChartData} from "../models/flow-chart";
import {FlowState} from "../models/rectification";
import AsyncCondition<PERSON>low<PERSON>hart from "./flowChartContainer";
import ComModal from "../../ComModal";

export interface FlowChartProps extends Omit<ModalProps, "onOk"> {
    data?: ConditionFlowChartData;
    flowState?: FlowState;
}

const useStyles = createUseStyles({
    flowChartModal: {
        "@global": {
            ".ant-modal-body": {
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                overflowY: "auto",
                maxHeight: "500px",
                ">*:not(:first-child)": {
                    marginTop: "10px"
                }
            }
        }
    },
    flowChartContainer: {
        width: "100%",
        height: "100%",
        overflowX: "auto",
        overflowY: "auto",
        padding: "16px"
    }
});

// const AsyncConditionFlow<PERSON>hart = lazy(async () => import("./flowChartContainer"));

const FlowChart = (props: FlowChartProps) => {
    const {data, flowState, onCancel} = props;
    const cls = useStyles();

    if (data === undefined) {
        return null;
    }

    return (
        <ComModal
            maskClosable={false}
            width={720}
            closable
            footer={null}
            renderFooterStatus={false}
            title="流程图"
            {...props}
            onCancel={onCancel}
        >
            <div className={cls.flowChartContainer}>
                <AsyncConditionFlowChart
                    flowState={flowState}
                    data={data}
                />
            </div>
            {/* <Button onClick={onCancel}>返回</Button> */}
        </ComModal>
    );
};
export default FlowChart;
