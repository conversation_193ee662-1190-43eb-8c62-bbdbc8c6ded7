import {Row} from "antd";
import React from "react";
import FormItemView, {FormItemViewProps} from ".";
import ComCollapsePanel, {ComCollapsePanelProps} from "../ComCollapsePanel";

export interface FormViewCollapsePanelProps extends Omit<ComCollapsePanelProps, "children"> {
    formItemList: FormItemViewProps[];
}

const FormViewCollapsePanel = (props: FormViewCollapsePanelProps) => {
    const {formItemList, ...other} = props;

    return (
        <ComCollapsePanel {...other}>
            <Row>
                {formItemList.map((item) => <FormItemView {...item} />)}
            </Row>
        </ComCollapsePanel>
    );
};

export default FormViewCollapsePanel;
