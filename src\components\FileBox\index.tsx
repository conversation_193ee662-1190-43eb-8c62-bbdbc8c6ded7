import {CloudUploadOutlined} from "@ant-design/icons";
import {Col, Empty, EmptyProps, Row, Button} from "antd";
import React, {CSSProperties, ReactNode, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {FileType} from "../../api/common.type";
import Color from "../../assets/css/Color";
import {emptyFun} from "../../assets/ts/utils";
import DrawerDocumentSelectBox from "../DrawerDocumentSelect";
import MyIcon from "../MyIcon";
import UploadFileCom, {UploadFileProps} from "../UploadFileCom";
import ListItem from "./ListItem";

const useStyles = createUseStyles({
    fileBoxBorder: {
        minHeight: 150,
        padding: 24,
        border: `1px solid ${Color["light-line-1"]}`,
        borderRadius: "6px",
        boxSizing: "border-box",
    }
});

export interface FileBoxProps {
    accept?: string;
    uploadIcon?: ReactNode;
    uploadText?: string;
    isUpload?: boolean;
    isEditName?: boolean;
    isDelete?: boolean;
    isDownload?: boolean;
    style?: CSSProperties;
    emptyConfig?: EmptyProps;
    documentLibEnable?: boolean; // 是否可以引用资料库
    bordered?: boolean;
    value?: FileType[];
    onChange?: (val: FileType[]) => void;
}

let tempFileListData: FileType[] = [];

const FileBox = (props: FileBoxProps) => {
    const {
        accept,
        uploadIcon = <CloudUploadOutlined style={{fontSize: 16, color: "#606266", verticalAlign: "middle"}} />,
        uploadText = "上传文件",
        isUpload = true,
        isEditName = true,
        isDelete = true,
        isDownload = false,
        style,
        emptyConfig,
        documentLibEnable = true,
        bordered = true,
        value = [],
        onChange = emptyFun,
    } = props;
    const cls = useStyles();
    const [usedTotalFileSize, setUsedTotalFileSize] = React.useState(0);
    const [selectDocmentVisible, setSelectDocmentVisible] = React.useState(false);

    React.useEffect(() => {
        const total = value.reduce((pre, cur) => pre + cur.fileSize, 0);
        setUsedTotalFileSize(Math.round((total / 1024 / 1024) * 100) / 100);
    }, [value]);

    const handleFileListChange = useCallback(
        (val: FileType[]) => {
            tempFileListData = val;
            onChange(val);
        },
        [onChange],
    );

    const onFileSuccess: UploadFileProps["fileUploadSuccess"] = (val) => {
        const newFileList = tempFileListData.map((e: FileType) => {
            if (val.timeStamp === e.timeStamp) {
                return {
                    fileUuid: val.uuid,
                    thumbUuid: val.thumbUuid,
                    fileName: e.fileName,
                    fileSize: e.fileSize,
                    percent: val.percent,
                    timeStamp: e.timeStamp,
                    failed: val.failed,
                };
            }
            return e;
        });
        handleFileListChange(newFileList);
    };

    const handleFileAdd = useCallback(
        (_val: FileType, _valList: FileType[]) => {
            const newFileListData = [
                ...value,
                ..._valList,
            ];
            handleFileListChange(newFileListData);
        },
        [value, handleFileListChange],
    );

    const handleDelete = (val: FileType) => {
        const newFileListData = value
            .filter((item: FileType) => !(item.timeStamp === val.timeStamp && item.fileUuid === val.fileUuid));
        handleFileListChange(newFileListData);
    };

    const handleFileChange = (val: FileType) => {
        const newFileListData = value.map((el) => ({
            ...el,
            fileName: el.fileUuid === val.fileUuid ? val.fileName : el.fileName,
        }));
        handleFileListChange(newFileListData);
    };

    const handleSelectDocument = (docList: FileType[]) => {
        const newFileListData = value;
        docList.forEach((doc) => {
            if (newFileListData.some((item) => item.fileUuid === doc.fileUuid) === false) {
                doc.thumbUuid = doc.thumbUuid ?? "";
                newFileListData.push(doc);
            }
        });
        handleFileListChange(docList);
    };

    return (
        <Row className={bordered ? cls.fileBoxBorder : undefined} style={{maxHeight: 300, overflowY: "auto", ...style}}>
            {isUpload && (
                <Col span={24} style={{marginBottom: 16}}>
                    <UploadFileCom
                        accept={accept}
                        fileUploadSuccess={onFileSuccess}
                        onFileSelected={handleFileAdd}
                        usedTotalFileSize={usedTotalFileSize}
                    >
                        <Button icon={uploadIcon}>{uploadText}</Button>
                    </UploadFileCom>
                    {
                        documentLibEnable === true && (
                            <>
                                <Button
                                    type="link"
                                    style={{marginLeft: 8}}
                                    icon={(
                                        <MyIcon
                                            type="icon-xuanze1"
                                            style={{
                                                position: "relative",
                                                top: 2,
                                                marginRight: 2,
                                                fontSize: 18,
                                                color: Color["primary-1"],
                                            }}
                                        />
                                    )}
                                    onClick={() => setSelectDocmentVisible(true)}
                                >
                                    引用资料库
                                </Button>
                                <DrawerDocumentSelectBox
                                    visible={selectDocmentVisible}
                                    onClose={() => setSelectDocmentVisible(false)}
                                    checkedDocumentArr={value}
                                    onSelectDocument={handleSelectDocument}
                                />
                            </>
                        )
                    }
                </Col>
            )}
            <Col span={24}>
                {
                    value.map((item: FileType) => (
                        <ListItem
                            key={`${item.fileUuid}${item.timeStamp ?? 0}`}
                            file={item}
                            isEditName={isEditName}
                            isDelete={isDelete}
                            isDownload={isDownload}
                            onFileChange={handleFileChange}
                            onDelete={handleDelete}
                        />
                    ))
                }
                {!isUpload && value.length === 0 && <Empty image={<MyIcon type="icon-nofile" style={{fontSize: 100}} />} description="未上传文件" {...emptyConfig} />}
            </Col>
        </Row>
    );
};

export default FileBox;
