/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {forwardRef, Ref, useCallback, useEffect, useState} from "react";
import {Select, SelectProps} from "antd";
import CurDeptTree from "../curDeptTree";
import {CurDeptTreeType} from "../curDeptTree/typeAndData";


export interface CurDeptTreeSelectProps extends SelectProps {
    value?: CurDeptTreeType;
    onChange?: (value: CurDeptTreeType | null) => void;
    treeData: CurDeptTreeType[];
}

const CurDeptTreeSelect = (props: CurDeptTreeSelectProps, ref: Ref<any>) => {
    const {value: _value, onChange, treeData, ...otherProps} = props;
    const [value, setValue] = useState<CurDeptTreeType>();
    const [orgTreeVisible, setOrgTreeVisible] = useState(false);

    useEffect(() => {
        setValue(_value);
    }, [_value]);

    const handleOrgNodeClick = useCallback((node: CurDeptTreeType | null) => {
        if (onChange instanceof Function) {
            onChange(node);
        }
    }, [onChange]);

    const handleClear = useCallback(() => {
        handleOrgNodeClick(null);
    }, [handleOrgNodeClick]);

    useEffect(() => {
        // 监听如果树数据变了，就清除之前的记录
        handleOrgNodeClick(null);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [treeData]);

    const handleClick = useCallback(() => {
        setOrgTreeVisible(true);
    }, []);

    const orgTreeSelectClose = (type: string, item?: CurDeptTreeType) => {
        setOrgTreeVisible(false);
        if (type !== "cancel") {
            handleOrgNodeClick(item ?? null);
        }
    };

    return (
        <>
            <Select
                ref={ref}
                value={value?.title}
                onClear={handleClear}
                onClick={handleClick}
                open={false}
                dropdownMatchSelectWidth={320}
                dropdownStyle={{padding: 0}}
                {...otherProps}
            />
            <CurDeptTree
                key="curDeptTreeSelect"
                selectedKeyProps={_value?.id}
                treeData={treeData ?? []}
                isVisible={orgTreeVisible}
                onClose={orgTreeSelectClose}
            />
        </>
    );
};

export default forwardRef(CurDeptTreeSelect);
