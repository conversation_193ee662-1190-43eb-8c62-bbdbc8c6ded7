export interface ProRes<T> {
    code: number;
    success: boolean;
    msg: string;
    result: T;
}

/** 审批表单模板信息 */
export interface FormTemplateVo {
    /** 权限实体id，用于校验模板权限是否修改 */
    authEntityId?: string;
    /** 流程模板上的表单元素，第一层list代表行，第二层list代表一行中从左到右排序的表单元素 */
    components?: ComponentValueDetail[][];
    /** 表单模板id */
    formTmplId?: string;
    /** 流程模板id,用于校验审批流程是否修改 */
    procTmplId?: string;
}

/** 控件属性值详情 */
export interface ComponentValueDetail {
    /** 权限: 1-代表隐藏 2-代表仅可读 4-代表可编辑 */
    auth?: number;
    /** 表单元素的bool属性列表 */
    booleanAttributes?: ComponentBooleanAttributeDetail[];
    /** 是否有修改记录：true-有修改记录，false-无修改记录 */
    hasHistory?: boolean;
    /** 表单元素id */
    id?: string;
    /** 表单元素的选项属性列表 */
    optionAttributes?: ComponentOptionAttributeDetail[];
    /** 表单元素的引用属性列表 */
    refAttributes?: ComponentRefAttributeDetail[];
    /** 表单元素的字符属性列表 */
    stringAttributes?: ComponentStringAttributeDetail[];
    /** 表单元素类型 */
    type?: string;
    /** 一个审批表单组件中的内容的唯一标识，组件未填些内容时，用于判断内容是否为新增 */
    valueId?: string;
}

/** 表单控件bool型属性 */
export interface ComponentBooleanAttributeDetail {
    /** 表单控件属性的名称 内容为以下几种：TITLE(标题)，REQUIRED(必填项目)，PLACE_HOLDER(提示文字)，OPTION(选项)，MULTI_SELECT(多选)，REF_PROC_TMPL(关联流程类型) */
    name?: string;
    /** 表单控件bool属性的具体值 */
    value?: boolean;
}

/** 表单控件的选项属性 */
export interface ComponentOptionAttributeDetail {
    /** 表单控件属性的名称 内容为以下几种：TITLE(标题)，REQUIRED(必填项目)，PLACE_HOLDER(提示文字)，OPTION(选项)，MULTI_SELECT(多选)，REF_PROC_TMPL(关联流程类型) */
    name?: string;
    /** 表单控件选项属性的候选项 */
    options?: OptionDetail[];
}

/** 表单控件引用属性 */
export interface ComponentRefAttributeDetail {
    /** 表单控件属性的名称 内容为以下几种：TITLE(标题)，REQUIRED(必填项目)，PLACE_HOLDER(提示文字)，OPTION(选项)，MULTI_SELECT(多选)，REF_PROC_TMPL(关联流程类型) */
    name?: string;
    /** 表单控件引用属性对应的业务数据类型，目前只有一种类型 PROC_TMPL */
    refType?: string;
    /** 表单控件引用属性的引用项 */
    refs?: RefDetail[];
}

/** 表单控件的选项属性的候选项 */
export interface OptionDetail {
    /** true-默认选中 （co3.1.0&iworksApp1.1.0新增） */
    checked?: boolean;
    /** 表单控件选项属性候选项id */
    id?: string;
    /** 表单控件选项属性的候选项排列顺序 */
    index?: number;
    /** 表单控件选项属性的候选项具体值 */
    value?: string;
}

/** 表单控件引用属性的引用项 */
export interface RefDetail {
    /** 被引用资源的标识 */
    ref?: string;
    /** 被引用资源的名称 */
    refedResourceName?: string;
}

/** 表单控件字符型属性 */
export interface ComponentStringAttributeDetail {
    /** 表单控件属性的名称 内容为以下几种：TITLE(标题)，REQUIRED(必填项目)，PLACE_HOLDER(提示文字)，OPTION(选项)，MULTI_SELECT(多选)，REF_PROC_TMPL(关联流程类型) */
    name?: string;
    /** 表单控件字符型属性的具体内容 */
    value?: string;
}

/** 整改详情 */
export interface ReformDetailVo {
    /** 当前用户审批角色：1-发起人，2-审批人，3-抄送人 */
    approvalRoles: number[];
    /** 流程类型id */
    approvalTypeId: string;
    /** 流程类型名称 */
    approvalTypeName?: string;
    /** 检查任务id */
    checkId: string;
    /** 检查任务type */
    checkType: number;
    /** 审批意见列表 */
    comments?: ApprovalCommentVo[];
    /** 审批表单数据，第一层list代表行，第二层list代表一行中从左到右排序的表单元素 */
    componentJsons?: ComponentValueDetailWrap[][];
    /** 流程发起时间 */
    createTime?: string;
    /** 发起人信息 */
    creatorInfo?: ApprovalUserInfo;
    /** 当前流程节点Id */
    currentFlowNodeId?: string;
    /** 当前流程节点名称 */
    currentFlowNodeName?: string;
    /** 项目部id */
    deptId?: string;
    /** 项目部名称 */
    deptName?: string;
    /** 流程结束时间(流程未归档是为null) */
    endTime?: string;
    /** 表单id */
    formId?: string;
    /** 审批过程任务id（用于用户进行审批操作） */
    formTaskId: string;
    /** 表单模板id */
    formTemplId?: string;
    /** 主题名称 */
    name: string;
    /** 节点id */
    nodeId?: string;
    /** 节点名称 */
    nodeName?: string;
    /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
    nodeType?: number;
    /** 代理工程id */
    ppid?: number;
    /** 审批状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
    processStatus: string;
    /** 工程所属产品id */
    productId?: number;
    /** 工程名称 */
    projName?: string;
    /** 整改批注 */
    reformComment: string;
    /** 整改期限 */
    reformDeadline: number;
    /** 整改单 0 不显示 1显示 */
    reformItem?: number;
    /** 审批编号 */
    serialNum: string;
    startFlowNode: boolean;
}

/** 表单元素属性值详情包装类 */
export interface ComponentValueDetailWrap {
    /** 表单元素属性值json */
    componentJson?: string;
    /** 表单元素类型 */
    type?: string;
}

/** 通用-用户信息 */
export interface GeneralUserInfo {
    /** 真实姓名 */
    realName?: string;
    /** 用户名 */
    userName?: string;
}

/** 审批意见列表 */
export interface ApprovalCommentVo {
    /** 评论附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentVo[];
    /** 评论内容 */
    commentMsg?: string;
    /** 评论时间 */
    commentTime?: number;
    /** 操作人信息 */
    commentator?: ApprovalUserInfo;
    /** 流程节点id */
    flowNodeId?: string;
    /** 流程节点名称 */
    flowNodeName?: string;
    /** 下一流程节点名称 */
    nextFlowNodeName?: string;
    /** 接收人列表（审批人进行抄送操作时有效） */
    nextReceivers?: ApprovalUserInfo[];
    /** 操作类型：{1-发起；4-撤销；5-提交；6-退回；7-抄送；8-评论;9自动抄送;10转交} */
    operationType?: number;
    /** 转交人 */
    transferFromUser?: ApprovalUserInfo;
    /** 被转交人 */
    transferToUser?: ApprovalUserInfo;
}

/** 用户信息 */
export interface ApprovalUserInfo {
    id: string;
    /** 是否离职：true-离职，false-在职 */
    isLeave: boolean;
    /** true-操作过，false-未操作 */
    operated?: boolean;
    /** 头像uuid */
    portraitUuid: string;
    /** 真实姓名 */
    realName: string;
    /** 用户的来源，0:内部 ;1：外部人员 */
    sourceType: number;
    /** 用户名 */
    userName: string;
}

/** 审批操作-附件展示信息 */
export interface ApprovalOperationAttachmentVo {
    /** 文件后缀名 */
    extension?: string;
    /** 文件名称 */
    name?: string;
    /** 文件大小 */
    size?: number;
    /** 缩略图uuid,无缩略图则不返回 */
    thumbnailUuid?: string;
    /** 文件uuid */
    uuid?: string;
}

/** 整改提交传参 */
export interface ReformInstSubmitParam {
    /** 附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 安全检查表id */
    checkFormId?: string;
    /** 整改批注【发起人节点且有值时必填】 */
    comment?: string;
    /** 整改期限【发起人节点且非不限期时必填】 */
    deadline?: string;
    /** 项目部id,不传表删除绑定关系 */
    deptId?: string;
    /** 审批过程任务id，必传 */
    formTaskId: string;
    /** 表单元素保存数据 */
    jsonValues?: ComponentJsonValueSaveParam[];
    /** 审批意见 */
    message?: string;
    /** 节点id */
    nodeId?: string;
    /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
    nodeType?: number;
    /** 发起人指定审批人的节点 */
    nodeUsers?: ApprovalNodeUserParam[];
    /** 代理工程id,不传表删除绑定关系 */
    ppid?: number;
    /** 审批编号,必传 */
    serialNum: string;
}

/** 审批操作-附件信息保存参数 */
export interface ApprovalOperationAttachmentSaveParam {
    /** 文件后缀名 */
    extension?: string;
    /** 文件md5 */
    md5?: string;
    /** 文件名称 */
    name?: string;
    /** 文件大小 */
    size?: number;
    /** 缩略图信息 */
    thumbnail?: ApprovalOperationThumbnailSaveParam;
    /** 文件uuid */
    uuid?: string;
}

/** 缩略图信息保存参数 */
export interface ApprovalOperationThumbnailSaveParam {
    /** 缩略图md5 */
    thumbnailMd5?: string;
    /** 缩略图大小 */
    thumbnailSize?: number;
    /** 缩略图uuid */
    thumbnailUuid?: string;
}

/** 表单元素保存数据 */
export interface ComponentJsonValueSaveParam {
    /** 表单元素json数据 */
    jsonVaule?: string;
    /** 表单元素类型 */
    type?: string;
}

/** 审批类型发起人自定义的审批人节点信息 */
export interface ApprovalNodeUserParam {
    /** 节点id */
    approvalNodeId?: string;
    /** 指定用户列表 */
    approvalUsers?: string[];
}

/** 可用整改类型列表查询条件 */
export interface ListReformTypeQueryParam {
    /** 流程模板归属的应用模块：0-施工 1-监理 3-业主 */
    appModule: number;
    /** 流程模板归属的组织节点id(目前仅支持项目部节点) */
    nodeId: string;
    /** 搜索关键字 */
    searchKey?: string;
    /** 控件元素类型（目前仅支持BIM控件过滤） */
    types?: string[];
}

/** 审批类型 */
export interface ApprovalTypeVo {
    /** 审批类型key */
    typeId: string;
    /** 审批类型名称 */
    typeName?: string;
}
