/**
 * Excel模板资源管理器
 *
 * 解决iframe部署环境下模板路径问题的优雅方案
 * 通过webpack的import机制确保正确的资源路径
 */

// 导入所有模板文件（webpack会处理正确的路径）
import actualProgressTaskDetailsTemplate from "../../../static/xlsx/actualProgress_taskDetails.xlsx";
import actualProgressTaskDetailsImportTemplate from "../../../static/xlsx/actualProgress_taskDetails_import.xlsx";
import scheduleTaskDetailsTemplate from "../../../static/xlsx/schedule_taskDetails.xlsx";
import scheduleTaskDetailsImportTemplate from "../../../static/xlsx/schedule_taskDetails_import.xlsx";

/**
 * 模板类型枚举
 */
export enum TemplateType {
    /** 计划任务详情导出模板 */
    SCHEDULE_TASK_DETAILS = "SCHEDULE_TASK_DETAILS",
    /** 计划任务详情导入模板 */
    SCHEDULE_TASK_DETAILS_IMPORT = "SCHEDULE_TASK_DETAILS_IMPORT",
    /** 实际进度任务详情导出模板 */
    ACTUAL_PROGRESS_TASK_DETAILS = "ACTUAL_PROGRESS_TASK_DETAILS",
    /** 实际进度任务详情导入模板 */
    ACTUAL_PROGRESS_TASK_DETAILS_IMPORT = "ACTUAL_PROGRESS_TASK_DETAILS_IMPORT",
}

/**
 * 模板映射表
 */
const TEMPLATE_MAP: Record<TemplateType, string> = {
    [TemplateType.ACTUAL_PROGRESS_TASK_DETAILS]: actualProgressTaskDetailsTemplate,
    [TemplateType.ACTUAL_PROGRESS_TASK_DETAILS_IMPORT]: actualProgressTaskDetailsImportTemplate,
    [TemplateType.SCHEDULE_TASK_DETAILS]: scheduleTaskDetailsTemplate,
    [TemplateType.SCHEDULE_TASK_DETAILS_IMPORT]: scheduleTaskDetailsImportTemplate,
};

/**
 * 模板资源管理器类
 */
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class TemplateManager {
    /**
     * 获取模板URL
     * @param templateType 模板类型
     * @returns 模板的完整URL路径
     */
    static getTemplateUrl(templateType: TemplateType): string {
        const url = TEMPLATE_MAP[templateType];
        if (url === undefined) {
            throw new Error(`Template not found: ${templateType}`);
        }
        return url;
    }

    /**
     * 获取实际进度相关模板
     */
    static getActualProgressTemplates() {
        return {
            export: this.getTemplateUrl(TemplateType.ACTUAL_PROGRESS_TASK_DETAILS),
            import: this.getTemplateUrl(TemplateType.ACTUAL_PROGRESS_TASK_DETAILS_IMPORT)
        };
    }

    /**
     * 获取计划相关模板
     */
    static getScheduleTemplates() {
        return {
            export: this.getTemplateUrl(TemplateType.SCHEDULE_TASK_DETAILS),
            import: this.getTemplateUrl(TemplateType.SCHEDULE_TASK_DETAILS_IMPORT)
        };
    }

    /**
     * 获取所有可用的模板类型
     */
    static getAllTemplateTypes(): TemplateType[] {
        return Object.values(TemplateType);
    }

    /**
     * 检查模板类型是否有效
     */
    static isValidTemplateType(templateType: string): templateType is TemplateType {
        return Object.values(TemplateType).includes(templateType as TemplateType);
    }
}

/**
 * 便捷函数：获取模板URL
 * @param templateType 模板类型
 * @returns 模板URL
 */
export const getTemplateUrl = (templateType: TemplateType): string => TemplateManager.getTemplateUrl(templateType);

/**
 * 便捷函数：获取实际进度模板
 */
export const getActualProgressTemplates = () => TemplateManager.getActualProgressTemplates();

/**
 * 便捷函数：获取计划模板
 */
export const getScheduleTemplates = () => TemplateManager.getScheduleTemplates();
