import moment, {Moment} from "moment";
import {PlanCycleType} from "../../api/Preparation/type";
import {TimeType} from "./type";

export const getDateType = (cycleType: PlanCycleType): TimeType => {
    if (cycleType === "MASTER") {
        return "date";
    }
    return cycleType.toLocaleLowerCase() as TimeType;
};

export const getCycleDate = (date: Moment, cycleType: PlanCycleType) => moment(date).startOf(getDateType(cycleType));

export const getDateFormat = (dateType: TimeType): string | undefined => {
    switch (dateType) {
        case "year":
            return "YYYY年";
        case "quarter":
            return "YYYY年Q季度";
        case "month":
            return "YYYY年M月";
        default:
            return undefined;
    }
};

/**
 * 获取当前日期在本月第几周
 */
export const getWeekOfMonth = (date: Moment) => {
    const dayOfMonth = date.date(); // 月中第几天
    const isoWeekday = date.isoWeekday(); // 周中第几天
    const weekOfMonth = Math.ceil((dayOfMonth + 7 - isoWeekday) / 7);
    return weekOfMonth;
};

/**
 * 获取当前年-月-周的时间范围
 */
export const getDateRangeWithWeek = (year: number, month: number, week: number): [Moment | null, Moment | null] => {
    let startDate: Moment | null = null;
    let endDate: Moment | null = null;
    const daysInMonth = moment([year, month]).daysInMonth();
    for (let day = 1; day <= daysInMonth; day++) {
        const date = moment([year, month, day]);
        if (startDate === null && getWeekOfMonth(date) === week) {
            startDate = moment(date);
        }
        if (endDate === null) {
            const nextDay = date.add(1, "day");
            if (nextDay.isValid() === false || getWeekOfMonth(date.add(1, "day")) === week + 1) {
                endDate = date;
            }
        }
    }
    return [startDate, endDate];
};

/**
 * 根据开始时间和范围类型获取结束时间
 */
export const getEndDate = (startDate: Moment, picker: PlanCycleType): Moment => {
    if (picker === "WEEK") {
        const startWeek = getWeekOfMonth(startDate);
        let endDate = moment(startDate);
        let week = getWeekOfMonth(endDate);
        while (week === startWeek) {
            endDate = moment(endDate).add(1, "day");
            week = getWeekOfMonth(endDate);
        }
        return endDate.subtract(1, "day");
    }
    return moment(startDate).endOf(getDateType(picker));
};
