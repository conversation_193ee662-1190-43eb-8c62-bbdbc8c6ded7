/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable no-param-reassign */
import {gantt} from "@iworks/dhtmlx-gantt";
import {message, Modal} from "antd";
import {v4 as uuidv4} from "uuid";
import {debounce} from "lodash-es";
import moment from "moment";
import {GanttTask, GanttState, GanttLink, DurationDateValue} from "./interface";
import {linksWithPredecessors, linksWithTargetId, updateProjectActualTimeWithTaskList, updateTaskWithProgess, updateModifyEndSync, updateModifyStartSync, updateProjectActualTimeWithTask} from "./taskUtils"; // eslint-disable-line 
import {isStandardCalendar} from "../common/calendar";
import {TaskStatus} from "../common/constant";
import ganttManager from "./ganttManager";
import {getTaskWorkEndDate, getTaskWorkStartDate, calculateTaskChangeDurationDate} from "./calendarUtils";
// import {setShowGanttTimeLine} from "./ganttConfig";

// const debounceShowGanttTimeLine = debounce(() => {
//     setShowGanttTimeLine(false, true);
// }, 500, {leading: true});

const debounceAutoSchedule = debounce(() => {
    gantt.autoSchedule();
}, 500, {leading: true});

export const initEvent = () => {
    gantt.detachEvent("onTaskCreated");
    gantt.attachEvent("onTaskCreated", (task: GanttTask) => {
        // 任务id用32位uuid生成
        task.id = uuidv4().replace(/-/g, "");
        task.calendar_id = "custom";
        // task.bindType = 0;
        return true;
    }, null);

    gantt.detachEvent("onAfterTaskAdd");
    gantt.attachEvent("onAfterTaskAdd", (id: string | number, item: GanttTask) => {
        // 当task新建完后，才能添加link
        if (item.predecessors !== undefined && item.predecessors !== "") {
            linksWithTargetId(id).forEach((link) => {
                gantt.deleteLink(link.id);
            });
            const links = linksWithPredecessors(item.predecessors, item);
            links.forEach((link) => {
                gantt.addLink(link);
            });
        }
    }, null);

    gantt.detachEvent("onBeforeTaskUpdate");
    gantt.attachEvent("onBeforeTaskUpdate", (_id: string, _state: GanttTask) => {
        // console.log("onBeforeTaskUpdate", id, state);
    }, null);

    gantt.detachEvent("onAfterTaskUpdate");
    gantt.attachEvent("onAfterTaskUpdate", (_id: string, _item: GanttTask) => {
        // if (item.type !== "task") {
        //     item.bindType = 0;
        // }
        // console.log("onAfterTaskUpdate", _item);
        debounceAutoSchedule();
    }, null);

    gantt.detachEvent("onAfterTaskDelete");
    gantt.attachEvent("onAfterTaskDelete", (_id: string | number, _item: GanttTask) => {
        // updateProjectActualTimeWithTask(item);
    }, null);

    gantt.detachEvent("onAfterTaskDrag");
    gantt.attachEvent("onAfterTaskDrag", (id: string, mode: string, _e: Event) => {
        // mode the drag-and-drop mode ("resize", "progress", "move", "ignore")
        const task = gantt.getTask(id);
        if (mode === "move" || mode === "resize") {
            debounceAutoSchedule();
            // debounceShowGanttTimeLine();
        }
        if (mode === "progress") {
            updateTaskWithProgess(task);
        }
    }, null);


    let oldParent: string | number;
    gantt.detachEvent("onBeforeTaskMove");
    gantt.attachEvent("onBeforeTaskMove", (id) => {
        // 记录一下移动前的父任务
        const task = gantt.getTask(id);
        oldParent = task.parent;
    }, null);

    gantt.detachEvent("onAfterTaskMove");
    gantt.attachEvent("onAfterTaskMove", (_id, parent) => {
        if (oldParent !== undefined && parent !== undefined && oldParent !== parent) {
            updateProjectActualTimeWithTaskList([oldParent, parent]);
        }
        // 检测是否有父子任务存在前置关系，有则删掉
        gantt.getLinks().forEach((linkItem: GanttLink) => {
            if (gantt.isChildOf(linkItem.source, linkItem.target) || gantt.isChildOf(linkItem.target, linkItem.source)) {
                gantt.deleteLink(linkItem.id);
            }
        });
    }, null);

    gantt.detachEvent("onBeforeLinkAdd");
    gantt.attachEvent("onBeforeLinkAdd", (_id: string | number, link: GanttLink) => {
        link.id = uuidv4().replace(/-/g, "");
        return true;
    }, null);

    gantt.detachEvent("onLinkDblClick");
    gantt.attachEvent("onLinkDblClick", (id: string | number) => {
        if (ganttManager.editorContext.checkoutStatus === false) {
            return false;
        }
        const link: GanttLink = gantt.getLink(id);
        const sourceTask: GanttTask = gantt.getTask(link.source);
        const targetTask: GanttTask = gantt.getTask(link.target);
        Modal.confirm({
            title: `是否删除关联${sourceTask.text} - ${targetTask.text}`,
            okText: "确定",
            cancelText: "取消",
            onOk: () => {
                if (gantt.isReadonly(id) === false) {
                    gantt.deleteLink(id);
                }
            }
        });
        return false;
    }, null);

    gantt.detachEvent("onTaskSelected");
    gantt.attachEvent("onTaskSelected", () => {
        // 选中一条任务时，保存当前正在编辑inline editor
        const controller = gantt.ext.inlineEditors;
        if (controller.isVisible() === true) {
            if (controller.isChanged() === true) {
                controller.save();
            } else {
                controller.hide();
            }
        }
    }, null);

    gantt.detachEvent("onBeforeTooltip");
    gantt.attachEvent("onBeforeTooltip" as any, (event) => {
        // TODO 如果是责任人单元格则不显示tooltip，返回了false但没有用
        if (event.target.classList.contains("duty_person") === true) {
            return false;
        }
        return true;
    }, null);

    // gantt.detachEvent("onAfterAutoSchedule");
    // gantt.attachEvent("onAfterAutoSchedule", (id, tasks) => {
    //     console.log("id", id);
    //     console.log("tasks", tasks);
    //     return false;
    // }, null);

    // gantt.detachEvent("onBeforeDragStart");
    // gantt.attachEvent("onBeforeDragStart" as any, (task: GanttTask) => {
    //     console.log("onBeforeDragStart", task);
    // }, []);
};

// 实际日期是否变化
const isActualDateChanged = (date1?: Date, date2?: Date) => {
    // console.log("isActualDateChanged", date1, date2);
    if ((date1 === undefined && date2 !== undefined) || (date2 === undefined && date1 !== undefined)) {
        return true;
    }
    const isChanged = moment(date1).format("YYYY.MM.DD") !== moment(date2).format("YYYY.MM.DD");
    return isChanged;
};

const updateTaskDateWithChange = (task: GanttTask, change: DurationDateValue) => {
    const newTask: DurationDateValue = calculateTaskChangeDurationDate(
        {startDate: task.start_date, endDate: task.end_date, duration: task.duration},
        change
    );
    if (newTask.startDate !== undefined && newTask.endDate !== undefined && newTask.duration !== undefined) {
        task.start_date = newTask.startDate;
        task.end_date = newTask.endDate;
        task.duration = newTask.duration;
        gantt.updateTask(task.id, task);
    }
};

const updateTaskRequestDateWithChange = (task: GanttTask, change: DurationDateValue) => {
    const newTask: DurationDateValue = calculateTaskChangeDurationDate(
        {startDate: task.request_start_date, endDate: task.request_end_date, duration: task.request_duration},
        change
    );
    task.request_start_date = newTask.startDate;
    task.request_end_date = newTask.endDate;
    task.request_duration = newTask.duration;
    if (
        task.type === gantt.config.types.task
        && newTask.startDate !== undefined
        && newTask.endDate !== undefined
        && newTask.duration !== undefined
    ) {
        // 叶子结点任务的要求时间编辑后需要同步至计划时间
        task.start_date = newTask.startDate;
        task.end_date = newTask.endDate;
        task.duration = newTask.duration;
    }
    gantt.updateTask(task.id, task);
};

export const initExtInlineEditorEvents = () => {
    // console.log("initExtInlineEditorEvents");
    gantt.ext.inlineEditors.attachEvent("onBeforeEditStart", (state: GanttState) => {
        const col = state.columnName;
        const task: GanttTask = gantt.getTask(state.id);
        // console.log("onBeforeEditStart", state, task);

        if (task.type === gantt.config.types.milestone
            && (
                col === "duration"
                || col === "end_date"
                || col === "actual_duration"
                || col === "actual_start"
                || col === "actual_end"
                || col === "request_duration"
                || col === "request_start_date"
                || col === "request_end_date"
            )
        ) {
            return false;
        }
        if (task.type === gantt.config.types.project) {
            if (
                col === "duration"
                || col === "start_date"
                || col === "end_date"
                || col === "actual_duration"
                || col === "actual_start"
                || col === "actual_end"
            ) {
                return false;
            }
        }
        if (ganttManager.editorContext.hasParentPlan) {
            // 下级计划不允许编辑要求时间
            if (
                col === "request_duration"
                || col === "request_start_date"
                || col === "request_end_date"
            ) {
                return false;
            }
        }
        return true;
    });

    gantt.ext.inlineEditors.attachEvent("onEditStart", (state: GanttState) => {
        // const col = state.columnName;
        // console.log("onEditStart", state, task, col);
        const task: GanttTask = gantt.getTask(state.id);
        const editDom = document.getElementsByClassName("gantt_grid_editor_placeholder");
        if (editDom.length > 0) {
            const [inputDom] = editDom[0].getElementsByTagName("input");
            if (inputDom !== undefined && inputDom !== null) {
                if (state.columnName === "request_duration") {
                    if (task.request_duration === undefined || task.request_duration < 0) {
                        inputDom.value = "";
                    }
                }
            }
        }
        return true;
    });

    gantt.ext.inlineEditors.attachEvent("onSave", (state: GanttState) => {
        const {durationFormatter, currentCalendarInfo} = ganttManager;
        const col = state.columnName;
        const task: GanttTask = gantt.getTask(state.id);
        // const cloneTask = cloneDeep(task);
        // console.log("onSave task------------", state, task, cloneTask);
        if (col === "duration") {
            gantt.updateTask(state.id, task);
        }
        if (col === "start_date") {
            const startDate = state.newValue as Date;
            updateTaskDateWithChange(task, {startDate});
        }
        if (col === "end_date" && task.start_date !== undefined) {
            const endDate = state.newValue as Date;
            updateTaskDateWithChange(task, {endDate});
        }
        if (col === "request_duration") {
            const duration = state.newValue as number;
            updateTaskRequestDateWithChange(task, {duration});
        }
        if (col === "request_start_date") {
            const startDate = state.newValue as Date;
            updateTaskRequestDateWithChange(task, {startDate});
        }
        if (col === "request_end_date") {
            const endDate = state.newValue as Date;
            updateTaskRequestDateWithChange(task, {endDate});
        }
        if (col === "predecessors") {
            // 组件自带的编辑逻辑修改已存在的前置任务时会删除，可以先自定义保存逻辑
            linksWithTargetId(task.id).forEach((item) => {
                gantt.deleteLink(item.id);
            });
            const links = linksWithPredecessors((state.newValue as string[]).join(","), task);
            links.forEach((item) => {
                gantt.addLink(item);
            });
            gantt.updateTask(state.id, task);
        }
        // 编辑计划工期，自动计算计划完成时间并更新task
        if (col === "actual_duration") {
            const now = getTaskWorkEndDate(new Date());
            if (task.actual_start === undefined) {
                task.actual_duration = -1;
                // console.log("edit actual_duration", task);
                gantt.updateTask(state.id, task);
            } else {
                let newDuration: number = state.newValue as number ?? 0;
                if (newDuration < 0) {
                    newDuration = 0;
                }
                let endDate = gantt.getCalendar("custom").calculateEndDate({
                    start_date: task.actual_start,
                    duration: state.newValue,
                });
                if (endDate > now) {
                    message.error("实际完成时间不能晚于当前时间");
                    newDuration = state.oldValue as number;
                }
                if (newDuration !== undefined && newDuration !== null) {
                    task.actual_duration = newDuration;
                    endDate = gantt.getCalendar("custom").calculateEndDate({
                        start_date: task.actual_start,
                        duration: newDuration,
                    });
                    if (isActualDateChanged(task.actual_end, endDate)) {
                        task.actual_syn = updateModifyEndSync(task);
                    }
                    task.actual_end = endDate;
                    task.taskStatus = TaskStatus.COMPLETED;
                    // task.progress = 1;
                    // task.estimated_completion_date = undefined;
                    // updateProjectActualTimeWithTask(task);
                } else {
                    task.actual_duration = -1;
                    if (isActualDateChanged(task.actual_end, endDate)) {
                        task.actual_syn = updateModifyEndSync(task);
                    }
                    task.actual_end = undefined;
                    if (task.actual_start !== undefined) {
                        task.taskStatus = TaskStatus.IN_PROGRESS;
                    } else {
                        task.taskStatus = TaskStatus.NOT_START;
                    }
                }
                // console.log("edit actual_duration", task);
                gantt.updateTask(state.id, task);
                updateProjectActualTimeWithTask(task);
            }
        }
        // 编辑计划开始时间，自动计算计划完成时间并更新task
        if (col === "actual_start") {
            const todayStart = getTaskWorkStartDate(new Date());
            const todayEnd = getTaskWorkEndDate(new Date());
            let newStart;
            if ((state.newValue as Date) > todayStart) {
                message.error("实际开始时间不能晚于当前时间");
                task.actual_start = undefined;
            } else {
                const newEnd = gantt.getCalendar("custom").calculateEndDate({
                    start_date: state.newValue as Date,
                    duration: task.actual_duration,
                });
                if (newEnd > todayEnd) {
                    message.error("实际完成时间不能晚于当前时间");
                    newStart = state.oldValue as Date;
                } else {
                    newStart = state.newValue as Date;
                }
                if (newStart !== undefined) {
                    let startDate = gantt.date.add(gantt.date.day_start(newStart), isStandardCalendar(currentCalendarInfo) ? 8 : 0, "hour");
                    startDate = gantt.getCalendar("custom").getClosestWorkTime({
                        date: startDate,
                    });
                    if (isActualDateChanged(state.oldValue as Date, startDate)) {
                        task.actual_syn = updateModifyStartSync(task);
                    }
                    task.actual_start = startDate;
                    // console.log("edit actual_start", task);
                    if (task.actual_duration !== undefined && task.actual_duration !== null && task.actual_duration >= 0) {
                        // 有实际工期表明是完成状态，计算实际完成日期
                        const endDate = gantt.getCalendar("custom").calculateEndDate({
                            start_date: task.actual_start,
                            duration: task.actual_duration,
                        });
                        if (isActualDateChanged(task.actual_end, endDate)) {
                            task.actual_syn = updateModifyEndSync(task);
                        }
                        task.actual_end = endDate;
                        task.taskStatus = TaskStatus.COMPLETED;
                        // task.estimated_completion_date = undefined;
                    } else {
                        task.taskStatus = TaskStatus.IN_PROGRESS;
                        // task.progress_record_date = new Date();
                        // task.estimated_completion_date = calculateEstimatedEnd(task);
                    }
                } else {
                    task.actual_start = newStart;
                }
            }
            gantt.updateTask(state.id, task);
            updateProjectActualTimeWithTask(task);
        }
        // 编辑计划完成时间，自动计算计划工期并更新task
        if (col === "actual_end") {
            if (task.taskStatus === TaskStatus.NOT_START) {
                // 未开始不能设置实际完成时间
                task.actual_end = undefined;
            } else if (task.actual_start !== undefined) {
                const now = getTaskWorkEndDate(new Date());
                let newEnd;
                if ((state.newValue as Date) > now) {
                    message.error("实际完成时间不能晚于当前时间");
                    newEnd = state.oldValue as Date;
                } else {
                    newEnd = state.newValue as Date;
                }
                if (newEnd !== undefined) {
                    let endDate = gantt.date.add(gantt.date.day_start(newEnd), isStandardCalendar(currentCalendarInfo) ? 17 : 0, "hour");
                    if (endDate < task.actual_start) {
                        task.actual_duration = durationFormatter.parse("0");
                        endDate = gantt.getCalendar("custom").calculateEndDate({
                            start_date: task.actual_start,
                            duration: task.actual_duration,
                        });
                        if (isActualDateChanged(state.oldValue as Date, endDate)) {
                            task.actual_syn = updateModifyEndSync(task);
                        }
                        task.actual_end = endDate;
                    } else {
                        endDate = gantt.getCalendar("custom").getClosestWorkTime({
                            date: endDate,
                        });
                        if (isActualDateChanged(state.oldValue as Date, endDate)) {
                            task.actual_syn = updateModifyEndSync(task);
                        }
                        task.actual_end = endDate;
                        task.actual_duration = gantt.getCalendar("custom").calculateDuration({
                            start_date: task.actual_start,
                            end_date: task.actual_end,
                        });
                    }
                    task.taskStatus = TaskStatus.COMPLETED;
                    task.progress = 1;
                } else {
                    task.actual_end = newEnd;
                }
            }
            gantt.updateTask(state.id, task);
            updateProjectActualTimeWithTask(task);
        }
        return true;
    });
};
