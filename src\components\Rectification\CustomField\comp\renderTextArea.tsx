import React from "react";
import {Input} from "antd";
import {CustomFieldProps} from "../../models/custom-field";

const {TextArea} = Input;

const RenderTextArea = (props: CustomFieldProps) => {
    const {maxLength, style, hint, onBlur, disabled, onChange, currentValue} = props;

    return (
        <>
            {
                ["string", "undefined"].includes(typeof currentValue)
                    ? (
                        <TextArea
                            maxLength={maxLength}
                            style={style}
                            placeholder={hint}
                            defaultValue={currentValue}
                            onBlur={(e) => onBlur !== undefined && onBlur(e.target.value)}
                            disabled={disabled}
                            onChange={(e) => onChange(e.target.value)}
                        />
                    )
                    : null
            }
        </>
    );
};
export default RenderTextArea;
