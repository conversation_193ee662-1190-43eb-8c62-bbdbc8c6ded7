import React, {use<PERSON><PERSON>back, Key, useState, useEffect} from "react";
import {Col, Input, Row, Select, Tree} from "antd";
import {useSelector} from "react-redux";
import {cloneDeep} from "lodash-es";
import {ProjNameVo} from "../../api/planModel";
import ComDrawer from "../../../ComDrawer";
import {OrgProjNodeVo} from "../../../../api/recentProject/orgTree.type";
import {ProjectTreeNodeData} from "./type";
import {RootState} from "../../../../store/rootReducer";
import {getOrgListProjNodeByDeptId} from "../../../../api/common.api";
import {majorOptions} from "../../../../views/sandTableManage/RecentProject/recentOpendProject/DeptTree/data";

const {Search} = Input;
const {Option} = Select;

export interface SelectProjectProps {
    visible: boolean;

    onCancel?: () => void;
    onOk?: (info: ProjNameVo) => void;
}

const SelectProjectDrawer = (props: SelectProjectProps) => {
    const {visible, onOk, onCancel} = props;
    const {orgInfo} = useSelector((store: RootState) => store.commonData);

    const [originTreeData, setOriginTreeData] = useState<ProjectTreeNodeData[]>([]);
    const [projectTreeData, setProjectTreeData] = useState<ProjectTreeNodeData[]>([]);
    const [major, setMajor] = useState(0);
    const [search, setSearch] = useState("");

    const getProjectTreeData = useCallback(() => {
        if (orgInfo.orgId === "") {
            return;
        }
        getOrgListProjNodeByDeptId(orgInfo.orgId)
            .then((res) => {
                const projectNode = (res ?? []).find((el) => el.id === orgInfo.orgId);
                if (projectNode === undefined) {
                    setOriginTreeData([]);
                    return;
                }
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                const treeTopNode = {
                    title: projectNode?.name ?? "",
                    key: projectNode?.id ?? "",
                    originData: projectNode,
                } as ProjectTreeNodeData;
                // 房建项目
                if (orgInfo.deptDataType === 1 && (projectNode.projects ?? []).length !== 0) {
                    treeTopNode.children = (projectNode.projects ?? []).map((el) => ({
                        key: el.ppid ?? "",
                        title: el.projName,
                        originData: {...el}
                    }));
                }
                treeTopNode.children = res.filter((el) => el.projects !== null)
                    .map((el) => ({
                        key: el.id ?? "",
                        title: el.name,
                        originData: el,
                        // eslint-disable-next-line max-nested-callbacks
                        children: (el.projects ?? []).map((e) => ({key: e.ppid ?? "", title: e.projName, originData: e}))
                    }));

                // const curSectionNodeData = res.find((el) => el.id === curNodeInfo.nodeId);
                // // 基建,节点为项目节点
                // if (orgInfo.deptDataType === 2 && curSectionNodeData?.type === 0) {
                //     treeTopNode.children = res.filter((el) => el.projects !== null)
                //         .map((el) => ({
                //             key: el.id ?? "",
                //             title: el.name,
                //             originData: el,
                //             // eslint-disable-next-line max-nested-callbacks
                //             children: (el.projects ?? []).map((e) => ({key: e.ppid ?? "", title: e.projName, originData: e}))
                //         }));
                // }
                // // 基建项目,节点为标段
                // if (orgInfo.deptDataType === 2 && curSectionNodeData?.type === 1) {
                //     treeTopNode.children = [
                //         {
                //             key: curSectionNodeData.id ?? "",
                //             title: curSectionNodeData.name,
                //             originData: curSectionNodeData,
                //             children: (curSectionNodeData.projects ?? []).map((el) => ({
                //                 key: el.ppid ?? "",
                //                 title: el.projName,
                //                 originData: {...el}
                //             }))

                //         }
                //     ];
                // }
                console.log("setOriginTreeData", treeTopNode);
                setOriginTreeData([treeTopNode]);
                setProjectTreeData([treeTopNode]);
            });
    }, [orgInfo.deptDataType, orgInfo.orgId]);

    useEffect(() => {
        getProjectTreeData();
    }, [getProjectTreeData]);

    const handleSelect = useCallback((_selectedKeys: Key[], selectInfo) => {
        console.log("onSelect", selectInfo);
        const selectProject = selectInfo?.node?.originData;
        if (selectProject !== undefined && selectProject.ppid !== undefined && onOk instanceof Function) {
            onOk(selectProject);
        }
    }, [onOk]);

    const filterTree = useCallback(
        (val: ProjectTreeNodeData) => {
            const tempVal = val as unknown as {originData: OrgProjNodeVo};
            if ((tempVal.originData.projects ?? []).length === 0) {
                val.children = (val.children ?? []).map((el) => filterTree(el));
            } else {
                val.children = (val.children ?? []).filter((el) => {
                    let isShow = true;
                    const tempOriginData = el.originData as unknown as ProjNameVo;
                    if (major !== 0 && tempOriginData.projType !== major) {
                        isShow = false;
                    }
                    // 根据名称搜索
                    if (isShow && Boolean(search) === true) {
                        isShow = tempOriginData.projName?.includes(search ?? "") ?? false;
                    }
                    return isShow;
                });
            }
            return val;
        },
        [major, search]
    );

    useEffect(() => {
        const tempProjectTree = cloneDeep(originTreeData);
        setProjectTreeData(tempProjectTree.map((el) => filterTree(el)));
    }, [filterTree, originTreeData]);

    const handleMajorChange = (value: number) => {
        setMajor(value);
    };

    const handleSearch = (value: string) => {
        setSearch(value);
    };

    return (
        <ComDrawer
            width={600}
            title={<span style={{fontWeight: "bold"}}>选择模型</span>}
            visible={visible}
            onCancel={onCancel}
            rightClose
            destroyOnClose
            footer={null}
            maskClosable={false}
            keyboard={false}
        >
            <Row gutter={[8, 8]} style={{marginBottom: 16, padding: "0 16px"}}>
                <Col span={12}>
                    <Select
                        value={major}
                        style={{width: "100%"}}
                        onChange={(value) => handleMajorChange(value)}
                    >
                        {majorOptions.map((v) => <Option key={v.value} value={v.value}>{v.label}</Option>)}
                    </Select>
                </Col>
                <Col span={12}>
                    <Search
                        onSearch={(value) => handleSearch(value)}
                        onChange={(e) => setSearch(e.target.value)}
                        value={search}
                        placeholder="搜索关键字"
                        style={{width: "100%", marginRight: 15}}
                    />
                </Col>
            </Row>
            <div style={{padding: "0 16px"}}>
                <Tree
                    blockNode
                    defaultExpandAll
                    height={800}
                    onSelect={handleSelect}
                    treeData={projectTreeData as []}
                />
            </div>
        </ComDrawer>
    );
};


export default SelectProjectDrawer;
