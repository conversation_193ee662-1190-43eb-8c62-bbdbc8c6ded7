import {<PERSON><PERSON>, <PERSON>, Pa<PERSON><PERSON>, Row} from "antd";
import React, {memo, useCallback, useEffect, useState} from "react";
import {useSelector} from "react-redux";
import clsx from "clsx";
import {getActualProgressPage} from "../../../api/actualProgress";
import {ActualProgressItem} from "../../../api/actualProgress/type";
import useComState from "../../../assets/hooks/useComState";
import ComTable from "../../../components/ComTable";
import PermissionCode from "../../../components/PermissionCode";
import QueryFormSingle from "../../../components/QueryFormSingle";
import ScheduleGantt from "../../../components/ScheduleGantt";
// import TableColumnsControl, {ComColumnsProps} from "../../../components/TableColumnsControl";
import {ComColumnsProps} from "../../../components/TableColumnsControl";
import TableLayout from "../../../components/TableLayout";
import {RootState} from "../../../store/rootReducer";
import {columnsInit, QueryFormType, QueryFormInit} from "./dataAndType";
import updateWorkGuideTask from "../../../components/CompleteWorkGuideTask";
import ComTitle from "../../../components/ComTable/ComTitle";
import useTableStyle from "../../../components/ComTable/useTableStyle";

const ActualProgressBox = () => {
    const {orgInfo, curSectionInfo, leafMenuId} = useSelector((state: RootState) => state.commonData);
    const tableCls = useTableStyle();
    const [state, setState] = useComState({queryFormInit: QueryFormInit});
    const [queryFormData, setQueryFormData] = useState<QueryFormType | undefined>(); // 搜索条件
    const [columns, setColumns] = useState<ComColumnsProps<ActualProgressItem>[]>([]);
    const [tableData, setTableData] = useState<ActualProgressItem[]>([]);
    const [totalItems, setTotalItems] = useState<number>(0);

    // const rowSelection = {
    //     selectedRowKeys: state.selectIds,
    //     onChange: setState.setSelectIds,
    //     columnWidth: 60
    // };

    useEffect(() => {
        state.queryForm.setFieldsValue({type: ""});
    }, [state.queryForm]);

    useEffect(() => {
        const otherColumns: ComColumnsProps<ActualProgressItem>[] = [
            {
                key: "operate",
                title: "操作",
                align: "right",
                mustShow: true,
                show: true,
                fixed: "right",
                width: 160,
                render(_text: string, record: ActualProgressItem, _index: number) {
                    return (
                        <span style={{textAlign: "right"}}>
                            <PermissionCode authcode="ProjectPlatform-Plan-Actual-Progress:view" admin>
                                <Button
                                    type="link"
                                    onClick={() => {
                                        setState.setCurPageType("add");
                                        setState.setRowId(record.id);
                                    }}
                                    style={{paddingRight: 0}}
                                >
                                    查看
                                </Button>
                            </PermissionCode>
                            <PermissionCode authcode="ProjectPlatform-Plan-Actual-Progress:edit">
                                <Button
                                    type="link"
                                    onClick={() => {
                                        setState.setCurPageType("edit");
                                        setState.setRowId(record.id);
                                    }}
                                    style={{paddingRight: 0}}
                                >
                                    编辑
                                </Button>
                            </PermissionCode>
                        </span>
                    );
                }
            }
        ];
        setColumns([...columnsInit(), ...otherColumns]);
    }, [setState]);

    useEffect(() => {
        // 组件挂载时执行
        // 查看成功，调用updateWorkGuideTask更新任务完成状态
        if (orgInfo?.orgId !== "" && curSectionInfo?.id !== undefined && leafMenuId !== "") {
            updateWorkGuideTask(orgInfo?.orgId, curSectionInfo.id, leafMenuId, "VIEW");
        }
        return () => {
            // console.log("Component will unmount");
            // 在这里添加清理逻辑
        };
    }, [curSectionInfo, leafMenuId, orgInfo]);

    const getTableListData = useCallback((paramIn?: QueryFormType) => {
        if (orgInfo.orgId === "" || curSectionInfo === null) {
            return;
        }

        const params = {
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : "",
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
            pageNum: state.curPage,
            pageSize: state.pageSize,
            ...queryFormData,
            ...paramIn,
        };

        getActualProgressPage(params).then(({code, data}) => {
            if (code === 200) {
                setTableData(data.items);
                setTotalItems(data.total);
            }
        }).catch(() => {
            setTableData([]);
            setTotalItems(0);
        });
    }, [curSectionInfo, orgInfo.orgId, queryFormData, state.curPage, state.pageSize]);

    useEffect(() => {
        // 切换项目部，回到列表页
        setState.setCurPageType("");
        getTableListData();
    }, [getTableListData, orgInfo.orgId, setState]);

    const handleFormFinished = useCallback((val: QueryFormType) => {
        setState.setCurPage(1);
        setQueryFormData(val);
        // getTableListData(val);
    }, [setState]);

    const handleFormClear = useCallback(() => {
        setState.setCurPage(1);
        state.queryForm.setFieldsValue({type: ""});
        setQueryFormData(undefined);
        // getTableListData();
    }, [setState, state.queryForm]);

    const handleFormValuesChange = useCallback((values) => {
        const keys = Object.keys(values);
        if (keys.includes("type") || keys.includes("status")) {
            setState.setCurPage(1);

            const isType = keys.includes("type");
            const param: QueryFormType = isType
                ? {
                    ...values,
                    type: values.type === "" ? undefined : values.type,
                }
                : values;
            setQueryFormData({...queryFormData, ...param});
            // getTableListData(param);
        }
    }, [queryFormData, setState]);

    const renderFilter = useCallback(() => (
        <div>
            <QueryFormSingle<QueryFormType>
                form={state.queryForm}
                queryItemList={state.queryFormList}
                onFormValuesChange={handleFormValuesChange}
                onFormFinish={handleFormFinished}
                onFormClear={handleFormClear}
                labelMaxWidth={115}
                isOperationBtn={false}
            />
        </div>
    ), [handleFormClear, handleFormFinished, handleFormValuesChange, state.queryForm, state.queryFormList]);

    const paginationChange = useCallback((curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(Number(pageSizeVal));
        // getTableListData();
    }, [setState]);

    // const renderTableHeader = useCallback(() => (
    //     <Row justify="center">
    //         <Col>
    //             <TableColumnsControl
    //                 tableKey="id"
    //                 setColumnsList={setColumns}
    //                 columnsList={columns}
    //             />
    //         </Col>
    //     </Row>
    // ), [columns]);

    const renderTable = useCallback(() => (
        <ComTable
            title={() => <ComTitle text="实际进度台账" />}
            columns={columns.filter((el: ComColumnsProps<ActualProgressItem>) => el.show || el.mustShow)}
            dataSource={tableData}
            // rowSelection={rowSelection}
            pagination={false}
            rowKey="id"
            className={clsx([
                tableCls.boldHeaderCell,
                tableCls.excelTempate,
                tableCls.customTable,
                tableCls.standardTable,
            ])}
        />
    ), [columns, tableCls.boldHeaderCell, tableCls.customTable, tableCls.excelTempate, tableCls.standardTable, tableData]);

    const renderPagination = useCallback(() => (
        <Row justify="space-between" style={{marginTop: 16}}>
            <Col>{`已选 ${state.selectIds.length} 项`}</Col>
            <Col>
                <Pagination
                    total={totalItems}
                    showSizeChanger
                    showQuickJumper
                    current={state.curPage}
                    pageSize={state.pageSize}
                    onChange={paginationChange}
                    showTotal={(totalCount: number) => `共 ${totalCount} 条`}
                />
            </Col>
        </Row>
    ), [paginationChange, state.curPage, state.pageSize, state.selectIds.length, totalItems]);

    const renderHeader = useCallback(() => (
        <Row align="middle" justify="space-between">
            <Col span={24}>
                {renderFilter()}
            </Col>
            {/* <Col span={1}>
                {renderTableHeader()}
            </Col> */}
        </Row>
    ), [renderFilter]);

    if (state.curPageType === "add") {
        return (
            <div style={{height: "100%", padding: "24px"}}>
                <ScheduleGantt
                    fromType="actual"
                    planId={state.rowId}
                    enterType="view"
                    headerRightButtons={[
                        "import",
                        "export",
                        "edit",
                        "save",
                    ]}
                    tableTitle={<ComTitle text="实际进度任务填报详情清单" />}
                    toolLeftButtons={[
                        "columnSetting",
                        "relateBim",
                        "relatePhoto",
                        "syncActualData",
                        "planInfo",
                        "calendar",
                        "showGantt",
                        "criticalPath",
                        "timeScale",
                    ]}
                    onBack={() => {
                        setState.setCurPageType("");
                        getTableListData();
                    }}
                />
            </div>
        );
    }
    if (state.curPageType === "edit") {
        return (
            <div style={{height: "100%", padding: "24px"}}>
                <ScheduleGantt
                    fromType="actual"
                    planId={state.rowId}
                    enterType="edit"
                    headerRightButtons={[
                        "import",
                        "export",
                        "edit",
                        "save",
                    ]}
                    toolLeftButtons={[
                        "columnSetting",
                        "relateBim",
                        "relatePhoto",
                        "syncActualData",
                        "planInfo",
                        "calendar",
                        "showGantt",
                        "criticalPath",
                        "timeScale",
                    ]}
                    onBack={() => {
                        setState.setCurPageType("");
                        getTableListData();
                    }}
                    tableTitle={<ComTitle text="实际进度任务填报详情清单" />}
                />
            </div>
        );
    }

    return (
        <TableLayout>
            <div style={{padding: "8px 24px 24px 24px"}}>
                {renderHeader()}
                {renderTable()}
                {renderPagination()}
            </div>
        </TableLayout>
    );
};

export default memo(ActualProgressBox);
