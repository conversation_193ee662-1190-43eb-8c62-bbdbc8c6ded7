export interface BwsTreeNode {
    name?: string;
    rel?: string;
    ext?: string;
    level?: number;
    treeNodeList?: BwsTreeNode[];
}


export interface WebRes<T> {
    code: number;
    message: string;
    success: boolean;
    data: T | null;
    result?: T | null;
    msg?: string;
}

export interface UploadParams {
    fileMd5: string;
    fileName: string;
    fileSize: number;
    isBPUpload: boolean;
    isCheckFastUpload: boolean;
    suportModes: number[];
}

export type UploadRes = [
    {
        fileMd5: string;
        fileSize: number;
        fileUUID: string | null;
        uploadUrls: [string];
    }
];

export interface HearainUploadRes {
    offset?: number;
    uuid?: string;
}

export interface DownloadUrlRes {
    downloadUrls?: [string | null] | null;
    fileName: string;
    fileMD5: string;
    fileSize: number;
    fileUUID: string;
    fileType: number;
}

export interface ReferenceRes {
    id: string;
    content: string;
    referenceFiles: ReferFileRes[] | null;
}

export interface ReferFileRes {
    uuid: string;
    name: string;
}


export interface WbsSectionRes {
    epid: number;
    id: string;
    name: string;
    num: string;
    parentId: string;
    projectId: string;
    sectionId: string;
    sort: number;
    sourceType: number;
}

export interface LBPageInfo {
    pageSize: number;
    currentPage: number;
    totalPage: number;
    totalNumber: number;
}

export interface CompGroupItem {
    compGroupId: string | null;
    name: string | null;
    updateTime: string;
    finishTime: string;
    attrValue: string | null;
}

export interface BwsNodeTreeRes {
    epid: number;
    ppid: number;
    type: string;
    isExtractLocationTree: 0 | 1;
    projType: number;
    treeNodeList: BwsTreeNode[] | null;
}
