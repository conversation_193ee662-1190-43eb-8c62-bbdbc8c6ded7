import React from "react";
import {Select} from "antd";
import {CustomFieldType, CustomFieldProps} from "../../models/custom-field";

const {Option} = Select;

interface RenderSelectProps extends CustomFieldProps {
    onMultiDeselect?: (val: string) => void;
    onSingleSelect?: (val: string) => void;
    onMultiSelect?: (val: string) => void;
    onMultiBlur?: (val: string[]) => void;
}

const RenderSelect = (props: RenderSelectProps) => {
    const {
        data,
        style,
        hint,
        onBlur,
        onChange,
        disabled,
        value: _value,
        currentValue,
        onMultiDeselect,
        onMultiSelect,
        onSingleSelect,
        onMultiBlur,
        ...otherProps
    } = props;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const [value, setValue] = React.useState<any>();

    React.useEffect(() => {
        setValue(currentValue);
    }, [currentValue]);

    const handleChange = React.useCallback((val) => {
        setValue(val);
    }, []);

    const handleBlur = React.useCallback(() => {
        if (onBlur instanceof Function) {
            onBlur();
        }
        if (onMultiBlur instanceof Function) {
            onMultiBlur(value);
        }
    }, [value, onBlur, onMultiBlur]);

    if (typeof data.data === "string") {
        return null;
    }

    return (
        <Select<string>
            dropdownMatchSelectWidth
            style={style}
            onDeselect={(e: string) => onMultiDeselect !== onMultiDeselect?.(e)}
            onSelect={(e: string) => (data.type === CustomFieldType.Select ? onSingleSelect?.(e) : onMultiSelect?.(e))}
            mode={data.type === CustomFieldType.MultiSelect ? "multiple" : undefined}
            defaultValue={data.defaultValue as string}
            placeholder={hint}
            value={value}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={disabled}
            {...otherProps}
        >
            {data.data.map((d) => <Option key={d.id} value={d.id}>{d.value}</Option>)}
        </Select>
    );
};
export default RenderSelect;
