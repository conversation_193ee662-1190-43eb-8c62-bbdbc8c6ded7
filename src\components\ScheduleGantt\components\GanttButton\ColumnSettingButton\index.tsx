import React, {useContext, useState} from "react";
import {Button} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import ColumnSettingModal from "../../ColumnSettingModal";
import MyIconFont from "../../../../MyIconFont";

const ColumnSettingButton = () => {
    const cls = useStyles();
    const {planInfo, checkoutStatus} = useContext(EditorContext);
    const [columnControlModalVisible, setColumnSettingModalVisible] = useState<boolean>(false);

    /**
     *  打开列设置对话框
     */
    const handleColumnSetting = () => {
        setColumnSettingModalVisible(true);
    };

    return (
        <>
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-lieshezhi" fontSize={18} />}
                onClick={handleColumnSetting}
            >
                列设置
            </Button>
            {
                planInfo.id !== undefined && (
                    <ColumnSettingModal
                        visible={columnControlModalVisible}
                        setVisible={setColumnSettingModalVisible}
                        editable={checkoutStatus}
                        planId={planInfo.id}
                    />
                )
            }
        </>
    );
};

export default ColumnSettingButton;
