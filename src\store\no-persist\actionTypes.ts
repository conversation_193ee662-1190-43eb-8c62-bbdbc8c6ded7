export default {
    SAVE_CHILD_API: "SAVE_CHILD_API",
    SET_IFRAME_EDIT_STATUS: "SET_IFRAME_EDIT_STATUS",
    SET_WORK_BENCH_DETAILS: "SET_WORK_BENCH_DETAILS",
    SET_PERSON_DATA: "SET_PERSON_DATA",
    SET_PERSON_TREE: "SET_PERSON_TREE",
    SET_PERSON_TREE_FLAG: "SET_PERSON_TREE_FLAG",
    SET_MENU_LIST: "SET_MENU_LIST",
    SET_SHOW_MODEL_SELECT: "SET_SHOW_MODEL_SELECT",
    SET_SHOW_MODEL_SPIN: "SET_SHOW_MODEL_SPIN",
    SET_BIM_PROJ_INFO: "SET_BIM_PROJ_INFO",
    SET_BIND_INFO: "SET_BIND_INFO",
    SET_EBS_PROJECT_LIST: "SET_EBS_PROJECT_LIST",
    SET_PAGE_ARR: "SET_PAGE_ARR",
    ADD_PAGE: "ADD_PAGE",
    POP_PAGE: "POP_PAGE",
    CHANGE_LEFT_BOX_VISIBLE: "CHANGE_LEFT_BOX_VISIBLE",
    RESET_LEFT_BOX_VISIBLE: "RESET_LEFT_BOX_VISIBLE",
    SET_ANCHORSKIP: "SET_ANCHORSKIP",
};


export interface EbsProjectType {
    ppid: string;
    projectName: string;
    selectedGuids?: string[];
}
