/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {ComponentStateLifeCycle, ComponentStateResult, WbsComponentStateParams} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

// 【沙盘播放】获取工程总体时间数据
export const getComponentDateStatistics = async (ppid: number): Promise<WebRes<ComponentStateLifeCycle>> => Fetch({
    methods: "get",
    url: `${baseUrl}/sphere/plan/manage/getComponentDate/${ppid}`
});

// 【沙盘播放】获取wbs进度数据
export const getWbsComponentState = async (params: WbsComponentStateParams): Promise<WebRes<ComponentStateResult>> => Fetch({
    methods: "post",
    url: `${baseUrl}/sphere/plan/manage/getwbsComponentState`,
    data: params
});
