import React, {CSSProperties} from "react";
import {Icon} from ".";

interface BtnIconProps {
    type: string;
    fontSize?: number;
    style?: CSSProperties;
    className?: string;
    onClick?: React.MouseEventHandler<HTMLDivElement>;
}

const BtnIcon = (props: BtnIconProps) => {
    const {type, fontSize = 16, style, className, onClick} = props;
    return (
        <Icon
            className={`anticon ${className}`}
            type={type}
            style={{
                fontSize,
                verticalAlign: "middle",
                ...style
            }}
            onClick={onClick}
        />
    );
};

export default BtnIcon;
