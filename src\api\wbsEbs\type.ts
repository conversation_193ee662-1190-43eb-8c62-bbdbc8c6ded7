// npx openapi-typescript@5.4.0 http://**************:8182/gateway/sphere/v2/api-docs?group=进度模块 --output swagger.ts

import {definitions} from "./swagger";

export type BindWbsEbsType = definitions["CommonBindNodeResponse"];

export type EbsType = definitions["BindEbsResult"];

export type EbsBindWbsType = definitions["PathEbsListByWbsResponse"];

export type WbsBindEbsType = definitions["PathWbsListByEbsResponse"];

export type EbsNodeResponse = definitions["EbsNodeResponse"];

export type ListEbsNodeResponse = definitions["EbsNode"];

export interface ListWbsEbsNodeType {
    businessId: string;
    ebsNodes: definitions["EbsNodeResponse"][];
}

export type EbsNodeWithTimeType = EbsNodeResponse & {
    startDate: number;
    endDate: number;
    preStartDate: number;
    preEndDate: number;
};

export interface TaskEbsNodeWithTimeType {
    businessId: string;
    ebsNodes: EbsNodeWithTimeType[];
}

export type PostListWbsEbsNodeRes = ListWbsEbsNodeType[];

export type PostTaskEbsNodeWithTimeListRes = TaskEbsNodeWithTimeType[];

export interface TaskBindEbsNodeType {
    businessId: string;
    bindEbs: boolean;
}

export interface PostWbsBindEbsParams {
    wbsIds: string[];
    containChildren: boolean;
}

export interface PostWbsBindEbsNode {
    ppid: number;
    projName: string;
    wbsIds: string[];
}
