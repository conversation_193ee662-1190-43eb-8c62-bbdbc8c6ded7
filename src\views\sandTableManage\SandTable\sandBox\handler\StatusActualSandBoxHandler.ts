import Motor from "@motor/core";
import MotorUtils from "../../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
import {MotorContext} from "../../../../../reMotor";
import StatusColor, {SandBoxHandler, SandDataInitializeBase} from "./dataOrInterface";
import {getColorByTime} from "./utils";


export default class StatusActualSandBoxHandler implements SandBoxHandler {
    private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    constructor(dataContainer: SandDataInitializeBase) {
        this.bimProject = MotorContext.getCurBIMProject();
        this.dataContainer = dataContainer;
    }

    reset() {
        this.resetModel();
    }

    showSandBoxByDate(curTime: Date): void {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const statusConfig = dataContainer.getFilterInfo("defaultStatus");
            if (!statusConfig.isCheckAll && statusConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryCompSandBoxListByActualTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = getColorByTime(curTime, compItem.finishEndDate);
                if (statusConfig.isCheckAll
                || (colorString === StatusColor.ongoingColor && statusConfig.setCheckedKeys.has("ongoing"))
                || (colorString === StatusColor.completeColor && statusConfig.setCheckedKeys.has("finished"))) {
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    jumpToDate(curTime: Date) {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const statusConfig = dataContainer.getFilterInfo("defaultStatus");
            if (!statusConfig.isCheckAll && statusConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryAccumulatedCompTimePeriodListByActualTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = getColorByTime(curTime, compItem.finishEndDate);
                if (statusConfig.isCheckAll
                    || (colorString === StatusColor.ongoingColor && statusConfig.setCheckedKeys.has("ongoing"))
                    || (colorString === StatusColor.completeColor && statusConfig.setCheckedKeys.has("finished"))) {
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            this.resetModel();
            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    private resetModel() {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            MotorUtils.resestBIMProject(bimProject);
            bimProject.setColor(Motor.MotorCore.Color.fromCssColorString(StatusColor.notStartColor));
            const {isCheckAll, comps} = dataContainer.queryVisibilityByEbsFilter();
            if (!isCheckAll) {
                bimProject.setVisibility(false);
                bimProject.setVisibility(true, comps);
            }
        }
    }
}
