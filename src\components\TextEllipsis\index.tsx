import React, {useEffect, useRef, useState} from "react";
import {Tooltip} from "antd";
import {TooltipProps} from "antd/lib/tooltip";
import {createUseStyles} from "react-jss";
import clsx from "clsx";

export type TextEllipsisProps = {
    text: string | React.ReactNode;
    className?: string;
} & Partial<TooltipProps>;

const useStyles = createUseStyles({
    titleText: {
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis"
    }
}, {name: "TextEllipsis"});

const TextEllipsis: React.FC<TextEllipsisProps> = (props) => {
    const {text, className, children, style} = props;
    const [canShowTooltip, setCanShowTooltip] = useState(false);
    const wrapperRef = useRef<HTMLDivElement>(null);
    const {titleText} = useStyles();

    useEffect(() => {
        if (wrapperRef.current !== null) {
            setCanShowTooltip(wrapperRef.current.clientWidth < wrapperRef.current.scrollWidth);
        }
    }, [text]);

    return (
        <>
            {
                canShowTooltip
                    ? (
                        <Tooltip title={text}>
                            <div ref={wrapperRef} style={style} className={clsx(titleText, className)}>
                                {text}
                                {children}
                            </div>
                        </Tooltip>
                    )
                    : (
                        <div ref={wrapperRef} style={style} className={clsx(titleText, className)}>
                            {text}
                            {children}
                        </div>
                    )
            }
        </>
    );
};

export default TextEllipsis;
