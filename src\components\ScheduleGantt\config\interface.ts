import {TaskListBindFileItem} from "../api/plan/type";

export interface OrgItem {
    projectOrgid: string;
    cityCoordinate: string | null;
    proviceCoordinate: string | null;
    countyCoordinate: string | null;
    deptCode: string | null;
    id: string;
    name: string;
    parentId: string;
    type: number;
}

export interface Project {
    cityCoordinate: string | null;
    proviceCoordinate: string | null;
    countyCoordinate: string | null;
    deptCode: string | null;
    id: string;
    name: string;
    parentId: string;
    type: number;
    children?: Project[];
}

export interface PhotoInfo extends TaskListBindFileItem {
    downloadUrl?: string;
}

export interface UploadFileInfo {
    fileMd5: string;
    fileName: string;
    fileSize: number;
    isBPUpload: boolean;
    isCheckFastUpload: boolean;
    suportModes: number[];
}

export type FromType = "plan" | "actual" | "approval"; // 来自页面类型

export type EnterType = "view" | "edit"; // 当前页面类型
