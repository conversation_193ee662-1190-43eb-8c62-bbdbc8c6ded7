import {<PERSON><PERSON>, <PERSON>, Row} from "antd";
import React, {memo, useCallback, useMemo, useState} from "react";
import {packageDownload} from "../../../../../api/common.api";
import {DownFileListType, FileType} from "../../../../../api/common.type";
import {PlanInfoDetailType} from "../../../../../api/Preparation/type";
import {getChangeInfluence} from "../../../../../assets/ts/utils";
import ComModal from "../../../../../components/ComModal";
import FileBox from "../../../../../components/FileBox";
import BtnIcon from "../../../../../components/MyIcon/BtnIcon";
import PlanChangeProject from "../../../../../components/PlanChangeProject";
import useChangeInfoModalStyles from "./style";

interface Props {
    visible: boolean;
    onCancel: (e: React.MouseEvent<HTMLElement>) => void;
    detail: PlanInfoDetailType | null;
    fileList: FileType[];
}
const ChangeInfoModal = (props: Props) => {
    const {visible = false, onCancel, detail, fileList} = props;
    const cls = useChangeInfoModalStyles();
    const [isDownloadFile, setIsDownloadFile] = useState(false);

    const handleExportFiles = useCallback((e) => {
        setIsDownloadFile(true);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        const downFileList: DownFileListType[] = [
            {
                dir: true,
                name: `${detail?.name}_变更资料`,
                fileList: fileList.map((i) => ({fileUuid: i.fileUuid, name: i.fileName, dir: false})),
            }
        ];
        packageDownload({packDownParam: {fileList: downFileList, packName: `${detail?.name}_变更资料.zip`}, fun: () => setIsDownloadFile(false)});
    }, [detail, fileList]);

    const renderChangeInfoBox = useMemo(() => (
        <div className={cls.changeInfoBox}>
            <Row style={{marginBottom: 21}}>
                <Col className={cls.infoRowLeft}>变更原因</Col>
                <Col className={cls.infoRowRight}>{detail?.changeReason}</Col>
            </Row>
            <Row style={{marginBottom: 16}}>
                <Col className={cls.infoRowLeft}>模型查看</Col>
                <Col className={cls.infoRowRight} style={{maxHeight: 200, overflow: "auto"}}>
                    <PlanChangeProject planId={detail?.id} />
                </Col>
            </Row>
            <Row style={{marginBottom: 16}}>
                <Col className={cls.infoRowLeft}>变更影响</Col>
                <Col className={cls.infoRowRight}>{getChangeInfluence(detail?.currentTotalDuration, detail?.unchangedTotalDuration)}</Col>
            </Row>
            <Row>
                <Col className={cls.infoRowLeft}>{`变更资料 (${fileList.length})`}</Col>
                <Col style={{flexGrow: 1}}>
                    <div>
                        <Button type="link" onClick={(e) => handleExportFiles(e)} disabled={fileList.length === 0} loading={isDownloadFile} className={cls.downloadBtn} icon={<BtnIcon type="icon-xiazai" />}>下载全部文件</Button>
                        <Row style={{marginTop: 16}}>
                            <FileBox style={{width: "100%"}} value={fileList} isEditName={false} isDelete={false} isDownload isUpload={false} bordered={false} />
                        </Row>
                    </div>
                </Col>
            </Row>
        </div>
    ), [cls.changeInfoBox, cls.downloadBtn, cls.infoRowLeft, cls.infoRowRight, detail, fileList, handleExportFiles, isDownloadFile]);
    if (detail === null) {
        return null;
    }
    return (
        <ComModal
            visible={visible}
            maskClosable={false}
            width={720}
            closable
            footer={null}
            renderFooterStatus={false}
            title="变更信息"
            onCancel={onCancel}
        >
            <div style={{padding: "16px"}}>
                {renderChangeInfoBox}
            </div>
        </ComModal>
    );
};

export default memo(ChangeInfoModal);
