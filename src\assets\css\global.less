@import "./globalColor.less";

.progress-table-content {
    overflow-y: auto;
    margin-top: 30px;
    padding: 0 24px;
    flex-grow: 1;
    // .ant-form {
        // .ant-radio-group {
        //     .ant-radio-button-wrapper{
        //         background-color: #F5F5F6;
        //         color: #33394D;
        //         font-size: 14px;
        //         line-height: 40px;
        //         border: none;
        //         padding: 0 16px;
        //         &:hover, &:active {
        //             background-color: #E1E2E5;
        //             color: #061127;
        //         }
        //     }
        //     .ant-radio-button-wrapper:not(:first-child)::before {
        //         background-color: #fff;
        //         width: 2px;
        //     }
        //     .ant-radio-button-wrapper-checked {
        //         background-color: #E9EFFC;
        //         color: @primary-1;
        //     }
        // }
    // }
}
.planSearchRadioButton {
    &.ant-radio-group {
        .ant-radio-button-wrapper{
            background-color: #F5F5F6;
            color: #33394D;
            font-size: 14px;
            line-height: 40px;
            height: 40px;
            border: none;
            padding: 0 16px;
            &:hover, &:active {
                background-color: #E9EFFC;
                color: @primary-1;
            }
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
            background-color: #E1E2E5;
            width: 1px;
            height: 20px;
            margin-top: 10px;
        }
        .ant-radio-button-wrapper-checked {
            background-color: #E9EFFC;
            color: @primary-1;
            border: 1px solid @primary-1;
            line-height: 40px;
            &:not(:first-child)::before {
                width: 0;
            }
        }
    }
}

.safety-quality{
    .coverTabs {
        & .ant-tabs-tab {
            margin-left: 20px;
        }
    };
    // 创建 编辑是底部 固定
    .safety-quality-add-content{
        overflow-y: auto;
        margin-top: 24px;
        padding: 0 24px;
        flex-grow: 1;
    }
    .safety-quality-add-footer{
        width: 100%;
        background-color: white;
        padding: 10px 25px;
        border-top: 1px solid @dark-line-2;
    }
}
// 表格的样式
.com-table{
    .darkRow {
        background-color: @bg-2;
        & .ant-table-cell {
            background-color: @bg-3,
        }
    };
    .ant-table-row:hover {
        & .ant-table-cell {
            background-color: @bg-4
        }
    };
    .ant-table-thead {
        .ant-table-cell {
            padding: 12px 12px;
            font-weight: 700;
            color: @text-1;
            background-color: @bg-3;
        }
    };
    .ant-table-tbody {
        .ant-table-cell {
            padding: 6px 12px;
        }
    }
}
// 导入导出按钮,下拉的样式
.com-dropdownMenu {
    padding: 6px 0;
    margin-top: 4;
    border-radius: 6;
    .ant-dropdown-menu-item {
        width: 117px;
        height: 36px;
        display: flex;
        align-items: center;
        &:hover {
            background-color: @bg-4;
        };
        span {
            display: inline-block;
            width: 100%;
            height: 100%;
            cursor: pointer;
            margin-bottom: 0;
            text-align: center;
            color: @text-1;
        };
    };
};

// 编辑页面,上下固定,中间滚动
.safety-quality-edit {
    display: flex;
    flex-direction: column;
    padding-top: 24px;
    height: 100%;
    .head {
        padding: 0 24px;
        flex: 0 0 32px;
    }
    .center-content {
        padding: 24px;
        flex-grow: 1;
        overflow-y: scroll;
    }
    .footer {
        border-top: 1px solid @dark-line-2;
        height: 52px;
        padding: 10px 24px;
    }
}
// 表格页面
.safety-quality-table {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 24px 24px;
}

.com-tabs {
    height: 100%;
    .ant-tabs-content-holder {
        height: 100%;
        .ant-tabs-content {
            height: 100%;
        }
    }
}

.popConfirmBtn {
    .ant-btn {
        padding: 0 7px
    }
}

.customAntCascader {
    .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover{
        background-color: #EAEFFB;
    }
}
