import {RectifyQueryFormModel} from "../../../views/Rectification/interface";
import {createSFAPayloadAction} from "../../utils";

export const setCurrentTempId = createSFAPayloadAction("SET_CURRENT_TEMP_ID" as const, (payload: string) => payload);
export const setRectifyStatus = createSFAPayloadAction("SET_RECTIFY_STATUS" as const, (payload: "list" | "add" | "edit") => payload);
export const setCurrentReformId = createSFAPayloadAction("SET_CURRENT_REFORM_ID" as const, (payload: string) => payload);
export const setRectifyQueryOptions = createSFAPayloadAction("SET_RECTIFY_QUERY_OPTIONS" as const, (payload: RectifyQueryFormModel & {processType?: number}) => payload);

type SetCurrentTempId = ReturnType<typeof setCurrentTempId>;
type SetRectifyStatus = ReturnType<typeof setRectifyStatus>;
type SetCurrentReformId = ReturnType<typeof setCurrentReformId>;
type SetRectifyQueryOptions = ReturnType<typeof setRectifyQueryOptions>;
type Actions = SetCurrentTempId | SetRectifyStatus | SetCurrentReformId | SetRectifyQueryOptions;
export default Actions;
