import React, {useCallback, useContext, useState} from "react";
import {But<PERSON>} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import EditorContext from "../../../views/GanttEditor/context";
import CalendarModal from "../../CalendarModal";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";
import {setGanttCalender} from "../../../gantt/calendarUtils";
import {CalendarInfo} from "../../../api/plan/type";

const CalendarButton = () => {
    const cls = useStyles();
    const {planInfo, saveMethodRef, checkoutStatus, currentCalendarRef} = useContext(EditorContext);
    const [calendarVisible, setCalendarVisible] = useState(false);

    const onCalendarCancel = () => {
        setCalendarVisible(false);
    };

    const onCalendarConfirm = useCallback((calendar: CalendarInfo) => {
        console.log("onCalendarConfirm", calendar);
        setCalendarVisible(false);
        const {...c} = calendar;
        // 保存当前日历并更新甘特图
        currentCalendarRef.current = c;
        setGanttCalender(currentCalendarRef.current);
        gantt.render();
        // // 仅保存数据，不删除签出状态
        if (saveMethodRef.current !== undefined) {
            saveMethodRef.current(2);
        }
    }, [currentCalendarRef, saveMethodRef]);

    const handleClick = useCallback(() => {
        setCalendarVisible(true);
    }, []);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <>
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-gongzuorili" fontSize={18} />}
                onClick={handleClick}
            >
                工作日历
            </Button>
            {planInfo.id !== undefined && currentCalendarRef.current !== undefined && (
                <CalendarModal
                    visible={calendarVisible}
                    setVisible={setCalendarVisible}
                    planId={planInfo.id}
                    // calendars={calendarOptions}
                    // currentCalendar={currentCalendarRef.current}
                    onConfirm={onCalendarConfirm}
                    onCancel={onCalendarCancel}
                />
            )}
        </>
    );
};

export default CalendarButton;
