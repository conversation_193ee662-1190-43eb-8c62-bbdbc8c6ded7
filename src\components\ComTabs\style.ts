import {createUseStyles} from "react-jss";

const useComTabsStyles = createUseStyles({
    tabsBox: {
        height: 40,
        "& .ant-tabs-tab-btn": {
            transition: "none"
        },
        "& .ant-tabs-nav .ant-tabs-tab": {
            width: 128,
            cursor: "pointer",
            height: 40,
            textAlign: "center",
            lineHeight: "40px",
            marginRight: 2,
            backgroundColor: "#E1E2E5",
            border: "none",
            fontSize: 14,
            "& .ant-tabs-tab-btn": {
                width: "100%"
            }
        },
        "&.ant-tabs-card.ant-tabs-large > .ant-tabs-nav .ant-tabs-tab": {
            padding: 0,
        },
        "&.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab": {
            marginLeft: 0
        },
        "&.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active": {
            color: "#061127",
            fontWeight: 700,
            lineHeight: "38px",
            backgroundColor: "white",
            borderLeft: "1px solid rgba(225,226,229,0.6)",
            borderRight: "1px solid rgba(225,226,229,0.6)",
            borderTop: "2px solid #1F54C5",
        },
        "& .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn": {
            color: "#061127",
            textShadow: "none"
        },
        // "&.ant-tabs-top > .ant-tabs-nav::before, &.ant-tabs-top > div > .ant-tabs-nav::before": {
        //     display: "none",
        // },
        "&.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab, &.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab": {
            borderRadius: 0,
        }
    },
});

export default useComTabsStyles;
