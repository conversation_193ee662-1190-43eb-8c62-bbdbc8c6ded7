import React, {useCallback} from "react";
import {<PERSON>, <PERSON>con<PERSON>rm, <PERSON>, Space, Typography} from "antd";
import moment from "moment";
import {CompStateParam, ProcessStatus} from "../../../../../api/processManager/type";
import MyIcon from "../../../../../components/MyIcon";
import {ComStateWithProgress, getProcessItemStatus, getProcessStatusName, isValidDate} from "../../helps";
import useStyle from "./style";
import {isDefined} from "../../../../../assets/ts/utils";
import {CehckeoutIcon} from "../../../../../assets/icons";

const {Text} = Typography;

interface ProcessListItemProps {
    /** 当前选中构件是否已经完工 */
    isCompFinished?: boolean;
    data: ComStateWithProgress;
    onEdit?: (info: CompStateParam) => void;
    onDelete?: (info: CompStateParam) => void;
}

const getTimeStr = (startTime?: number, endTime?: number) => {
    if (isValidDate(startTime) && isValidDate(endTime)) {
        return `${moment(startTime).format("YYYY.MM.DD")} - ${moment(endTime).format("YYYY.MM.DD")}`;
    }
    if (isValidDate(startTime)) {
        return `${moment(startTime).format("YYYY.MM.DD")} - 至今`;
    }
    return "--";
};

const ProcessListItem = (props: ProcessListItemProps) => {
    const cls = useStyle();
    const {data, isCompFinished, onEdit, onDelete} = props;

    const handleEdit = useCallback(
        () => {
            if (onEdit !== undefined) {
                onEdit(data);
            }
        },
        [data, onEdit]
    );

    const handleDelete = useCallback(
        () => {
            if (onDelete !== undefined) {
                onDelete(data);
            }
        },
        [data, onDelete]
    );

    const renderProgress = useCallback(
        () => {
            if (!isDefined(data.progressInfo)) {
                return null;
            }
            const {progressInfo: {timePer, timeOffset, planTimePer, planTimeOffset, hasStartTime}, stateColor} = data;
            return (
                <Row className={cls.progressBox}>
                    {
                        hasStartTime && (
                            <div
                                className={cls.timeProgressBox}
                                style={{
                                    width: `${timePer}%`,
                                    top: "50%",
                                    transform: "translateY(-50%)",
                                    left: `${timeOffset}%`,
                                    background: `rgb(${stateColor})`
                                }}
                            />
                        )
                    }
                    <div
                        className={cls.planTimeProgressBox}
                        style={{
                            width: `${planTimePer}%`,
                            left: `${planTimeOffset}%`,
                            background: `rgb(${stateColor},0.35)`
                        }}
                    />
                </Row>
            );
        },
        [cls.planTimeProgressBox, cls.progressBox, cls.timeProgressBox, data]
    );

    const renderStatusText = useCallback(
        () => {
            if (isCompFinished === true) {
                return (
                    <Row className="statusNameBox" style={{color: "#2DA641"}} align="middle">
                        <CehckeoutIcon />
                        <span style={{marginLeft: 8}}>已完成</span>
                    </Row>
                );
            }
            const status = getProcessItemStatus(data);
            const color = status === ProcessStatus.Undo ? "#D40000" : "#717784";
            return (
                <span className="statusNameBox" style={{color}}>
                    {getProcessStatusName(status)}
                </span>
            );
        },
        [data, isCompFinished]
    );

    return (
        <>
            <Row justify="space-between" align="middle">
                <Col span={20}>
                    <Text ellipsis={{tooltip: data.statePath}} strong>{data.statePath}</Text>
                </Col>
                <Col span={4} style={{textAlign: "right"}}>
                    <Space className="actionBox">
                        <MyIcon
                            type="icon-xiugai"
                            onClick={handleEdit}
                        />
                        <Popconfirm
                            title="删除后无法恢复，是否确认删除？"
                            onConfirm={handleDelete}
                            okText="确认"
                            cancelText="取消"
                        >
                            <MyIcon type="icon-lajitong" />
                        </Popconfirm>
                    </Space>
                    {renderStatusText()}
                </Col>
            </Row>
            <Row justify="space-between" align="middle" className={cls.processDateBox}>
                <Col span={12}>
                    <span className={cls.iconRect} style={{background: `rgb(${data.stateColor})`}} />
                    <span className={cls.statusText}>实际起止日期：</span>
                    {getTimeStr(data.lifeCycles.startDate, data.lifeCycles.endDate)}
                </Col>
                <Col span={12}>
                    <span className={cls.iconRect} style={{background: `rgba(${data.stateColor},0.35)`}} />
                    <span className={cls.statusText}>计划起止日期：</span>
                    {getTimeStr(data.lifeCycles.planStartDate, data.lifeCycles.planEndDate)}
                </Col>
            </Row>
            {renderProgress()}
        </>
    );
};

export default ProcessListItem;
