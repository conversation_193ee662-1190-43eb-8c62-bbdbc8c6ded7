import {createSFAPayloadAction} from "../utils";
import ActionTypes from "./actionTypes";
import {StateType} from "./reducer";

export const setPlayDimension = createSFAPayloadAction(ActionTypes.SET_PLAY_DIMENSION, (payload: StateType["playDimension"]) => payload);
export const setPlayStatus = createSFAPayloadAction(ActionTypes.SET_PLAY_STATUS, (payload: StateType["playStatus"]) => payload);
export const setPlayPattern = createSFAPayloadAction(ActionTypes.SET_PLAY_PATTERN, (payload: StateType["playPattern"]) => payload);
export const setSandTableType = createSFAPayloadAction(ActionTypes.SET_SANDTABLE_TYPE, (payload: StateType["sandTableType"]) => payload);
export const setAuthorityTabs = createSFAPayloadAction(ActionTypes.SET_AUTHORITY_TABS, (payload: StateType["authorityTabs"]) => payload);
export const setIsLeftExpand = createSFAPayloadAction(ActionTypes.SET_IS_LEFT_EXPAND, (payload: StateType["isLeftExpand"]) => payload);
export const setShowTodayStatus = createSFAPayloadAction(ActionTypes.SET_SHOW_TODAYSTATUS, (payload: StateType["showTodayStatus"]) => payload);
