import React, {useContext} from "react";
import {Row, Select} from "antd";
import {scaleDay, scaleHour, scaleMonth, scaleWeek, scaleYear} from "../../../gantt/calendarUtils";
import useStyles from "../styles";
import EditorContext from "../../../views/GanttEditor/context";

const TimeScaleButton = () => {
    const cls = useStyles();
    const {scale, isShowGantt} = useContext(EditorContext);

    const handleScaleSelectChange = (value: string) => {
        switch (value) {
            case "hour":
                scaleHour();
                break;
            case "day":
                scaleDay();
                break;
            case "week":
                scaleWeek();
                break;
            case "month":
                scaleMonth();
                break;
            case "year":
                scaleYear();
                break;
            default:
                scaleDay();
                break;
        }
    };

    if (isShowGantt === false) {
        return null;
    }

    return (
        <Row className={cls.textButton}>
            <span>时间刻度:</span>
            <Select
                style={{width: 80, marginLeft: 8}}
                onChange={handleScaleSelectChange}
                value={scale}
            >
                <Select.Option className={cls.dropDown} value="hour">小时</Select.Option>
                <Select.Option className={cls.dropDown} value="day">天</Select.Option>
                <Select.Option className={cls.dropDown} value="week">周</Select.Option>
                <Select.Option className={cls.dropDown} value="month">月</Select.Option>
                <Select.Option className={cls.dropDown} value="year">年</Select.Option>
            </Select>
        </Row>
    );
};

export default TimeScaleButton;
