import React from "react";
import moment from "moment";
// import {Checkbox} from "antd";
import {ComFormItemProps} from "../../../components/FormItem";
import renderTableText from "../../../components/renderTableText";
import {ComColumnsProps} from "../../../components/TableColumnsControl";
import {getCycleMomentText} from "../../../assets/ts/utils";
import {planTypeList} from "../../../api/planApproval/type";
import {ActualProgressItem, actualProgressStatusList, actualTypeList} from "../../../api/actualProgress/type";
import RenderExecutionStatus from "../components/RenderExecutionStatus";

const momentDisplayText = (text: number | string, type = "YYYY.MM.DD") => (<PERSON><PERSON><PERSON>(text) === true ? moment(text).format(type) : "--");

export interface QueryFormType {
    nameKey?: string;
    type?: string;
    status?: number;
}

// 列表 筛选 条件
export const QueryFormInit: ComFormItemProps[] = [
    {
        key: "type",
        type: "radio",
        typeConfig: {options: actualTypeList, optionType: "button", className: "planSearchRadioButton"},
        itemConfig: {name: "type", label: "计划类型", labelCol: {span: 0}, wrapperCol: {span: 24}},
        colConfig: {span: 6},
        nativeLabel: true,
    },
    {
        key: "nameKey",
        type: "search",
        typeConfig: {},
        itemConfig: {name: "nameKey"},
        colConfig: {span: 6, offset: 12}
    }
    // {
    //     key: "status",
    //     type: "select",
    //     typeConfig: {
    //         options: actualProgressStatusList,
    //         allowClear: true
    //     },
    //     itemConfig: {name: "status", label: "执行状态"},
    //     colConfig: {span: 6}
    // },
    // {
    //     key: "nameKey",
    //     type: "input",
    //     typeConfig: {placeholder: "请输入名称", options: [], allowClear: true},
    //     itemConfig: {name: "nameKey", label: "计划名称"},
    //     colConfig: {span: 6, offset: 3},
    // },
];

export const columnsInit = (): ComColumnsProps<ActualProgressItem>[] => [
    {
        title: "序号",
        align: "center",
        mustShow: true,
        show: true,
        fixed: "left",
        width: 80,
        render: (_text: unknown, _record: ActualProgressItem, index: number) => index + 1
    },
    {
        key: "name",
        title: "计划名称",
        dataIndex: "name",
        align: "left",
        mustShow: false,
        fixed: "left",
        show: true,
        width: 150,
        //     filterDropdown:
        // <div style={{display: "flex", flexDirection: "column", width: 120, fontSize: 14}}>
        //     <Checkbox style={{height: 40, padding: 12}}>新增计划</Checkbox>
        //     <Checkbox style={{height: 40, margin: 0, padding: 12}}>变更计划</Checkbox>
        // </div>,
        render: renderTableText
    },
    {
        key: "nodeName",
        title: "所属组织",
        dataIndex: "nodeName",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: renderTableText
    },
    {
        key: "type",
        title: "计划类型",
        dataIndex: "type",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (_text: unknown, _record: unknown) => {
            const planTypeListFilter = planTypeList.find((item) => item.value === _text);
            const text = planTypeListFilter === undefined ? "" : planTypeListFilter.label;
            return renderTableText(text);
        }
    },
    {
        key: "cycle",
        title: "计划周期",
        dataIndex: "cycle",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number, record: ActualProgressItem) => getCycleMomentText(text, record.type)
    },
    {
        key: "startDate",
        title: "计划开始日期",
        dataIndex: "startDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentDisplayText(Number(text))
    },
    {
        key: "endDate",
        title: "计划完成日期",
        dataIndex: "endDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentDisplayText(Number(text))
    },
    {
        key: "actualStartDate",
        title: "实际开始日期",
        dataIndex: "actualStartDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentDisplayText(Number(text))
    },
    {
        key: "actualEndDate",
        title: "实际完成日期",
        dataIndex: "actualEndDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: number) => momentDisplayText(Number(text))
    },
    {
        key: "actualStatus",
        title: "执行状态",
        dataIndex: "actualStatus",
        align: "left",
        mustShow: false,
        show: true,
        width: 150,
        render: (text: unknown, _record: unknown) => {
            const actualStatusListFilter = actualProgressStatusList.find((item) => item.value === text);
            return <RenderExecutionStatus text={actualStatusListFilter?.label ?? ""} value={Number(actualStatusListFilter?.value)} />;
        }
    },
];
