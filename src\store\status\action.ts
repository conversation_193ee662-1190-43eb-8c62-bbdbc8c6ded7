import {StatusActionTypes, CUR_SANDTABLE, BIMSandTableInfo, StatusStateType, CUR_PROJ_SETTING_INFO, BIM_FULL_SCREEN, SET_MOTOR_FRAME, SET_MOTOR_FRAME_INIT_STATUS} from "./types";

export const setCurSandTable = (val: BIMSandTableInfo | null): StatusActionTypes => ({
    type: CUR_SANDTABLE,
    payload: val
});

export const setCurProjSettingInfo = (val: StatusStateType["curProjSettingInfo"]): StatusActionTypes => ({
    type: CUR_PROJ_SETTING_INFO,
    payload: val
});

export const setBimFullScreen = (val: boolean): StatusActionTypes => ({
    type: BIM_FULL_SCREEN,
    payload: val
});

export const setMotorFrame = (val: StatusStateType["motorFrame"]): StatusActionTypes => ({
    type: SET_MOTOR_FRAME,
    payload: val
});

export const setMotorFrameInitStatus = (val: StatusStateType["motorFrameInitStatus"]): StatusActionTypes => ({
    type: SET_MOTOR_FRAME_INIT_STATUS,
    payload: val
});
