import {FileType} from "../../common.type";
import {ApprovalNodeUserParam, ApprovalOperationAttachmentSaveParam, ApprovalUserInfo, GeneralUserInfo} from "./process";

/** 获取审批路径节点传参 */
export interface ListApprovalLineNodeParam {
    /** 表单模板id */
    formTmplId?: string;
    /** 表单元素保存数据 */
    jsonValues?: ComponentJsonValueSaveParam[];
    /** 流程模板id */
    procTmplId?: string;
    /** 审批编号 */
    serialNum?: string;
}

/** 表单元素保存数据 */
export interface ComponentJsonValueSaveParam {
    /** 表单元素json数据 */
    jsonVaule?: string;
    /** 表单元素类型 */
    type?: string;
}

/** 审批类型对应的节点信息 */
export interface ApprovalNodeVo {
    /** 节点id */
    approvalNodeId: string;
    /** 节点名称 */
    approvalNodeName: string;
    /** 指定岗位列表 */
    approvalPostVos?: ApprovalPostVo[];
    /** 指定角色列表 */
    approvalRoleVos?: ApprovalRoleVo[];
    /** 审批方式 1：或签 2：会签 */
    approvalType: number;
    /** 指定用户列表 */
    approvalUserVos: ApprovalUserInfo[] | null;
    /** 审批人类型，0:指定角色 1:指定人 2:指定岗位 3:发起人指定 */
    approverType: number;
}

/** 审批流程岗位信息 */
export interface ApprovalPostVo {
    /** 审批岗位id */
    postId: string;
    /** 审批岗位名称 */
    postName: string;
}

/** 审批流程角色信息 */
export interface ApprovalRoleVo {
    /** 审批角色id */
    roleId: string;
    /** 审批角色名称 */
    roleName: string;
}

/** 创建整改实例传参 */
export interface ReformInstCreateParam {
    /** 权限实体id，用于校验模板权限是否修改 */
    authEntityId?: string;
    /** 检查单位：0-施工自查, 3-业主检查，1-监理检查 */
    buildType?: number;
    /** 检查任务id */
    checkFormId: string;
    /** 检查分项 */
    checkSubOption?: string;
    /** 检查类型 */
    checkType: number;
    /** 整改批注 */
    comment: string;
    /** 整改期限 */
    deadline: string;
    /** 项目部id */
    deptId?: string;
    /** 表单模板id */
    formTmplId?: string;
    /** 表单元素保存数据 */
    jsonValues?: ComponentJsonValueSaveParam[];
    /** 表单名称 */
    name: string;
    /** 节点id */
    nodeId?: string;
    /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
    nodeType?: number;
    /** 发起人指定审批人的节点 */
    nodeUsers?: ApprovalNodeUserParam[];
    /** 代理工程id */
    ppid?: number;
    /** 流程模板id,用于校验审批流程是否修改 */
    procTmplId?: string;
    /** 是否生成整改单 1：生成 0：不生成 */
    reformItem?: number;
}

/** 审批转交操作信息传参 */
export interface ApprovalTransferParam {
    /** 附件信息 */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 审批过程任务id */
    formTaskId: string;
    /** 转交谁的审批权 */
    fromUser: string;
    /** 审批意见 */
    message?: string;
    /** 审批编号,必传 */
    serialNum: string;
    /** 将审批权转交给谁 */
    toUser: string;
}

/** 审批人抄送传参 */
export interface ApprovalCopyToOperationMessageParam {
    /** 附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 指定的抄送人列表 */
    copyToUsers?: string[];
    /** 审批过程任务id */
    formTaskId?: string;
    /** 审批意见 */
    message?: string;
}

/** 发起人撤销传参 */
export interface ApprovalCancelOperationMessageParam {
    /** 附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 审批意见 */
    message?: string;
    /** 审批流程编号 */
    serialNum?: string;
}

/** 审批人驳回传参 */
export interface ApprovalBackOperationMessageParam {
    /** 附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 退回的流程节点id */
    flowNodeId?: string;
    /** 审批过程任务id */
    formTaskId?: string;
    /** 审批意见 */
    message?: string;
}

/** 整改列表查询条件 */
export interface ListReformQueryParam {
    /** 项目部筛选条件 */
    approvalDeptId?: string;
    /** 项目部工程筛选条件 */
    approvalProjectId?: string;
    /** 指定的流程类型名称 */
    approvalTypeName?: string;
    /** 检查单位：0-施工自查 3-业主检查 1-监理检查 */
    buildType?: number;
    /** 业务筛选条件 */
    businessQueryParam?: ProcessInstanceBusinessQueryParam[];
    /** 检查分项 */
    checkSubOption?: string;
    /** 检查类型筛选条件 */
    checkTypeId?: number;
    /** 整改期限 截止筛选条件 */
    deadlineEnd?: number;
    /** 整改期限 起始筛选条件 */
    deadlineStart?: number;
    /** 排除的审批表单编号集合 */
    excludedSerialNums?: string[];
    /** 逾期状态: 0-全部 1-已逾期 2-未逾期 */
    expireStatus?: number;
    /** 节点id */
    nodeId?: string;
    /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
    nodeType?: number;
    /** 分页参数 */
    pageParam: PageParam;
    /** 审批处理类别：1-我发起，2-待处理，3-已处理，4-抄送我的 */
    processType?: number;
    /** 整改状态: 0-全部 1-整改中 2-已整改 */
    reformStatus?: number;
    /** 查询来源：1-CO客户端；2-MylubanApp */
    requestSource?: number;
    /** 搜索关键字 */
    searchKey?: string;
    /** 分部分项（wbs节点id） */
    wbsNodeIds?: string[];
    // 查询未关联wbs的数据
    wbsBind?: boolean;
}

/** 业务筛选条件 */
export interface ProcessInstanceBusinessQueryParam {
    /** 业务value列表 */
    businessIds: string[];
    /** 业务key：WBS-分布分项，CHECKED_UNIT-被检查标段,可用值:WBS,CHECKED_UNIT, 未关联wbs数据-WBS_UN_BIND */
    businessType: "WBS" | "CHECKED_UNIT" | "CHECK_SUB_OPTION" | "WBS_UN_BIND";
}

export interface PageParam {
    /** 排序条件,为null或长度为0表示不用排序 */
    orders: SortParam[];
    /** 请求的页码，从1开始 */
    page: number;
    /** 每页的记录数,不指定表示不分页 */
    size: number;
}

export interface SortParam {
    /** 排序方式 0 ASC  1 DESC */
    direction: number;
    /** 要排序的字段名 */
    property: string;
}

/** 整改表单条目信息 */
export interface ReformFormItemVo {
    /** 当前流程节点审批人列表 */
    approvalUsers?: ApprovalUserInfo[];
    /** 检查单位：1-业主检查，2-监理检查，3-施工自查（新安全质量） */
    buildType: number;
    /** 业务绑定信息 */
    businessInfos: ProcessInstanceBusinessInfo[];
    /** 检查日期 */
    checkDate?: string;
    /** 检查描述（App） */
    checkDesc?: string;
    /** 检查单id */
    checkId: string;
    /** 检查分项 */
    checkOptions?: string[];
    /** 检查类型 */
    checkType: number;
    /** 检查类型名称 */
    checkTypeName?: string;
    /** 发起人信息 */
    creator?: GeneralUserInfo;
    /** 整改结束时间(流程未归档是为null) */
    endTime?: number;
    /** 整改名称 */
    name?: string;
    /** 图片uuid（App） */
    pictureUuid?: string;
    /** 整改发起日期（App） */
    reformDate?: string;
    /** 整改期限 */
    reformExpireDate: number;
    /** 整改单（App）0 不显示 1显示 */
    reformForm?: number;
    /** 整改状态 */
    reformStatus?: string;
    /** 整改类型 */
    reformType?: string;
    /** 安全级别 */
    securityGradeName?: string;
    /** 审批编号 */
    serialNum: string;
}

/** 审批操作信息传参 */
export interface ApprovalOperationMessageParam {
    /** 附件信息（mylubanApp5.11.1&co2.9.1新增） */
    attachments?: ApprovalOperationAttachmentSaveParam[];
    /** 审批过程任务id */
    formTaskId?: string;
    /** 审批意见 */
    message?: string;
}

/** 业务绑定信息 */
export interface ProcessInstanceBusinessInfo {
    /** 业务id */
    businessId: string;
    /** 业务名称 */
    businessName: string;
    /** 业务类型: WBS-分部分项 CHECKED_UNIT-被检查标段 PICTURE_UUID-图片uuid CHECK_POSITION-检查部位 检查分项-CHECK_SUB_OPTION */
    businessType: string;
}

export interface NewPageRes<T> {
    content?: T[];
    first?: boolean;
    last?: boolean;
    number?: number;
    numberOfElements?: number;
    size?: number;
    totalElements?: number;
    totalPages?: number;
}

/** 审批流程图信息 */
export interface ApprovalFlowVo {
    /** 流程图节点连线 */
    flowLineList: ApprovalFlowLineVo[];
    /** 流程图节点列表 */
    flowNodeList: ApprovalFlowNodeVo[];
    /** 历史走向的节点列表 （iworksApp1.1.0&co3.1.0新增） */
    historyFlowNodeIds?: string[];
    /** 审批状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
    processStatus?: string;
    /** 根节点id */
    rootNodeId: string;
}

export interface ApprovalFlowLineVo {
    /** 优先级 */
    priority: number;
    /** 流程节点线头 */
    sourceTenantTaskId: string;
    /** 流程节点线尾 */
    targetTenantTaskId: string;
}

export interface ApprovalFlowNodeVo {
    /** 审批岗位名称列表 */
    approvalPosts: ApprovalPostVo[];
    /** 审批角色名称列表 */
    approvalRoles: ApprovalRoleVo[];
    /** 审批人列表 */
    approvalUsers: ApprovalUserInfo[];
    /** 流程节点id */
    flowNodeId: string;
    /** 流程节点名称 */
    flowNodeName: string;
    /** 流程节点类型 TASK_NODE, JOIN_NODE, END_NODE */
    flowNodeType: "TASK_NODE" | "JOIN_NODE" | "END_NODE";
    /** 是否或签：true-或签；false-会签 */
    isOrApproval: boolean;
    /** 审批类型 ：0:指定角色 ；1:指定人； 2:指定岗位 3：发起人指定 */
    type: number;
}
export interface ProcessNodeItem {
    createAt: number;
    createBy: string;
    dateLx: number;
    dateLy: string;
    lx: number;
    ly: number;
    id: string;
    processTemplateId: string;
    processTemplateNodeId: string;
    epid: number;
    signType: number;
    templateId: string;
    templateItemId: string;
    updateAt: number;
    updateBy: string;
}
export interface ProcessNodePageParams {
    processTemplateId: string;
    processTemplateNodeId: string;
    templateItemId: string;
}
export interface ProcessNodePageReturn {
    result: ProcessNodeItem[];
    totalCount: number;
}
export interface SignPageUrlParams {
    bid: string; // 业务id
    id: string; // 高级表单实例id
    serviceKey: string; // 业务分类（0：模版-表单库，1：模版-aaa，2：实例-ccc）
    tid: string; // 高级表单模版id
}
export interface SignSealType {
    base64Data: string; // 签章BASE64编码（优先级高于字节数组）
    lx: number;
    ly: number;
    pages: number[];
    bytes?: string; // 签章字节数组（优先级高于文件Id）
    fileId?: string; // 签章文件Id
}
export interface CfcaSignParams {
    pdfFileId: string;
    pdfName: string; // pdf名称
    processInstanceId: string; // 流程实例id
    sealList: SignSealType[]; // 签章策略list
    signPassword: string; // md5密码
    pdfBase64Data?: string; // pdf文件BASE64编码（优先级高于pdfFileId）
}
export interface CfcaSignReturn {
    fileId: string;
    md5: string;
    name: string;
    size: number;
}
export interface QuerySignByNodeReturn {
    noticefileId: string;
    noticefileName: string;
    replyFileId: string;
    replyFileName: string;
    flowNodeId: string;
    id: string;
    serialNum: number;
}
export interface QuerySignByNodeParams {
    serialNum: string;
    flowNodeId: string;
    flowNodeIdList: {
        flowNodeId: string;
        operationType: number; // 操作类型：{1-发起；4-撤销；5-提交；6-退回；7-抄送；8-评论;9自动抄送;10转交}
    }[];
}
export interface GetReportFormTemplateType {
    buildType: number;
    createTime: number;
    createUser: string;
    id: string;
    instanceIds: string;
    moduleType: string;
    name: string;
    nodeId: string;
    templateIds: string;
    type: string;
    updateTime: number;
    updateUser: string;
    sign: number;
}
export interface LaunchReformType { // serialNum不存在时传值
    // 业务信息适配类名称,安全整改=securityAndQualityV2BusinessBindAdapter，质量整改=securityAndQualityV2BusinessBindAdapter
    businessBindAdapterBeanName: string;
    checkId: string; // 检查id,
    checkType: number; // 检查类型,
    processInstanceName?: string; // 整改编号
    reformComment: string; // 整改批注,
    reformDeadline: number; // 整改期限
    startTime: number; // 流程开始时间
    startUser: string; // 发起人通行证账号
}
export interface TemporarySaveFormParams extends Partial<LaunchReformType>{
    approvalOperationAttachmentVos?: FileType[];
    serialNum?: string; // 审批编号
    userName: string;
    epid: number;
    templateType?: string;
    operateMsg?: string; // 整改意见【整改步骤时传入】
    operator?: string; // 整改人鲁班通行证账号【整改步骤时，传入当前人员账号】
}
