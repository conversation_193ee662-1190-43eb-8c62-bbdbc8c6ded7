import React, {useCallback, useContext, useEffect} from "react";
import {Button, Row, Switch} from "antd";
import useStyles from "../styles";
import {setShowGanttTimeLine} from "../../../gantt/ganttConfig";
import EditorContext from "../../../views/GanttEditor/context";

const ShowGanttButton = () => {
    const cls = useStyles();
    const {isShowGantt, setIsShowGantt} = useContext(EditorContext);

    useEffect(() => {
        // 是否显示横道图
        setShowGanttTimeLine(false, true);
    }, []);

    const handleClick = useCallback((show: boolean) => {
        setIsShowGantt(show);
        setShowGanttTimeLine(show);
    }, [setIsShowGantt]);

    return (
        <Row align="middle" className={cls.textButton}>
            <Button
                className={cls.textButton}
                style={{margin: 0}}
                type="text"
                onClick={() => handleClick(!isShowGantt)}
            >
                横道图
            </Button>
            <Switch
                style={{marginLeft: 4}}
                checked={isShowGantt}
                onChange={(checked) => handleClick(checked)}
            />
        </Row>
    );
};

export default ShowGanttButton;
