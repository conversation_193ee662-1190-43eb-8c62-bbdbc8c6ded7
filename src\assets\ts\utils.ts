/* eslint-disable no-underscore-dangle */
import {message} from "antd";
import {DataNode} from "antd/lib/tree";
import {cloneDeep} from "lodash-es";
import moment from "moment";
import {nanoid} from "nanoid";
import SparkMD5 from "spark-md5";
import Postmate from "postmate";
import {TreeType} from "../../api/center/type";
import {MenuListProps, OrgNode} from "../../api/common.type";
import {CurDeptTreeType} from "../../components/curDeptTree/typeAndData";
import {getWeekOfMonth} from "../../components/PlanCycleDatePicker/utils";
import isDev from "./env";
import {ParamsInfo} from "./globalType";

export const setChildApi = (childApi: Postmate.ChildAPI) => {
    window.childApi = childApi;
};

interface TreeBase<T> {
    children?: T[];
}
type Key = string | number;

export const ModuleType = {
    quality: "QUALITY",
    security: "SECURITY",
};

/** 模型 treePaths分隔符 */
export const pathSeparator = "☎";

/**
 * base64 编码
 */
export const Base64Encode = (val: unknown) => window.btoa(JSON.stringify(val));

/**
  * base64 解码
  */
export const Base64Decode = (str: string) => window.atob(str);

export const goToLoginPage = () => {
    if (isDev === true) {
        window.location.href = `${window.location.href.split("#")[0]}#/login`;
        return;
    }
    if (window.embedPlatform === true) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.location.href = (window as any)._PLATFORM_;
    } else {
        window.location.href = `${window.location.href.split("#")[0]}#/login`;
    }
};

/**
 *
 * @param href 下载文件地址
 * @param name 文件名,不一定有用
 */
export const downloadFile = (href: string, name: string): void => {
    const a = document.createElement("a");
    a.download = name;
    a.href = href;
    a.click();
};

const sortList = (list: OrgNode[]) => {
    list.forEach((el) => {
        if (el.children !== undefined) {
            el.children.forEach((el1) => {
                if (el1.children !== undefined) {
                    sortList(el1.children);
                }
            });
            el.children.sort((a, b) => a.sortOrder - b.sortOrder);
        }
    });
    return list;
};

// 递归创建树结构
export const translateDataToTree = (data: OrgNode[]) => {
    // 没有父节点的数据
    const parents = data.filter((value) => value.parentId === value.id).map((el) => ({
        ...el,
        title: el.name,
        key: el.id
    }));

    // 有父节点的数据
    const children = data.filter((value) => value.parentId !== value.id).map((el) => ({
        ...el,
        title: el.name,
        key: el.id
    }));

    // 定义转换方法的具体实现
    const translator = (parentList: OrgNode[], childrenList: OrgNode[]) => {
        childrenList.sort((a, b) => a.sortOrder - b.sortOrder);
        // 遍历父节点数据
        parentList.forEach((parent) => {
            // 遍历子节点数据
            childrenList.forEach((current, index) => {
                // 此时找到父节点对应的一个子节点
                if (current.parentId === parent.id) {
                    // 对子节点数据进行深复制，这里只支持部分类型的数据深复制，对深复制不了解的童靴可以先去了解下深复制
                    const temp = cloneDeep(childrenList);
                    // 让当前子节点从temp中移除，temp作为新的子节点数据，这里是为了让递归时，子节点的遍历次数更少，如果父子关系的层级越多，越有利
                    temp.splice(index, 1);
                    // 让当前子节点作为唯一的父节点，去递归查找其对应的子节点
                    translator([current], temp);
                    // 把找到子节点放入父节点的children属性中
                    if (parent.children !== undefined) {
                        if (parent.id !== parent.parentId && current.type !== 1) {
                            parent.children.unshift(current);
                        } else {
                            parent.children.push(current);
                        }
                    } else {
                        parent.children = [current]; //eslint-disable-line
                    }
                }
            });
        });
    };
    // 调用转换方法
    translator(parents, children);
    // 返回排序后的数据
    return sortList(parents);
};

// 时间格式
export const momentText = (text: number | string, type = "YYYY.MM.DD") => (Boolean(text) === true ? moment(text).format(type) : "");


/**
 *
    id 解释
    拿 156630123 举例
    | 中国 | 省or直辖市 | 市 | 县 |
    | 156  |    63     | 01 | 23 |
    直辖市 没有子级市 默认 01 也有02 的情况

 */

export const getProvinceId = (id: string) => {
    const provinceId = `${id.slice(0, 5)}0000`;
    return provinceId;
};

export const getCityId = (id: string) => {
    const cityId = `${id.slice(0, 7)}00`;
    return cityId;
};


// 获取 选中点的id
export const getCheckedIds = (item: OrgNode): string[] => {
    const cloneNewTreeData = cloneDeep(item);
    const ids: string[] = [];
    ids.push(item.id);
    const changeData = (data: OrgNode[]) => {
        data.forEach((listItem) => {
            ids.push(listItem.id);
            if (Array.isArray(listItem.children) && listItem.children.length > 0) {
                changeData(listItem.children);
            }
        });
    };
    if (Array.isArray(cloneNewTreeData.children) && cloneNewTreeData.children.length > 0) {
        changeData(cloneNewTreeData.children);
    }
    return ids;
};

// 获取文件分片md5
export const calculateMd5 = async (file: File) => new Promise<string>((resolve, reject) => {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    const blobSlice = File.prototype.slice;
    const chunkSize = 2 * 1024 * 1024; // 2MB 每次读取的值
    const chunks = Math.ceil(file.size / chunkSize);
    let curChunk = 0;
    const spark = new SparkMD5.ArrayBuffer();
    // const startTime = Date.now();
    const fileReader = new FileReader();
    const loadNext = () => {
        const start = curChunk * chunkSize;
        const end = start + chunkSize >= file.size ? file.size : start + chunkSize;
        fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    };
    fileReader.onloadend = (e) => {
        if (e !== undefined && e.target !== null && e.target.result !== null) {
            spark.append(e.target.result as ArrayBuffer);
            curChunk++;
            if (curChunk < chunks) {
                // console.log(' read chunk curChunk: ', curChunk, ' of ', chunks)
                loadNext();
            } else {
                // const endTime = Date.now();
                const md5 = spark.end();
                // console.log('md5 ', md5)
                // console.log(' time cost ', (endTime - startTime) / 1000 + 's')
                if (resolve instanceof Function) {
                    resolve(md5);
                }
            }
        }
    };
    fileReader.onerror = () => reject();
    loadNext();
});

/**
 * 生成文件的md5
 * @param file File 文件
 * @returns MD5
 */
export const createFileMd5 = async (file: File): Promise<string> => new Promise((resolve, reject) => {
    const chunkSize = 2 * 1024 * 1024;
    const chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    const spark = new SparkMD5.ArrayBuffer();
    const fileReader = new FileReader();

    const loadNext = () => {
        const start = currentChunk * chunkSize;
        const end = start + chunkSize >= file.size ? file.size : start + chunkSize;
        fileReader.readAsArrayBuffer(file.slice(start, end));
    };

    fileReader.onload = (e): string | void => {
        spark.append(e.target?.result as ArrayBuffer);
        currentChunk++;

        if (currentChunk < chunks) {
            loadNext();
        } else {
            resolve(spark.end());
        }
    };

    fileReader.onerror = (): void => {
        reject(new Error("文件md5生成是失败..."));
    };
    loadNext();
});


export const getFileExt = (filename: string) => {
    const lastIndexOfDot = filename.lastIndexOf(".");
    if (lastIndexOfDot === -1) {
        return "";
    }
    return filename.slice(lastIndexOfDot + 1);
};

export const CategoryShowName = [
    "全部类型",
    "BIM模型",
    "GIS模型",
    "FBX模型",
    "DWG模型",
];

export enum Category {
    ALL_TYPE = 0,
    BIM_MODEL = 1,
    GIS_MODEL = 2,
    FBX_MODEL = 3,
    VECTOR_MODEL = 4,
}

// Category 和 CategorySubType 一一对应
export const CategorySubType = [
    [],
    ["pds", "rvt", "ifc"],
    ["gis"],
    ["fbx", "lbg"],
    ["dwg"],
];

export type SubType = "pds" | "gis" | "fbx" | "dwg" | "rvt" | "ifc" | "lbg";

export const SubTypeToCategoryMap: {
    [key in SubType]: Category
} = {
    pds: Category.BIM_MODEL,
    gis: Category.GIS_MODEL,
    fbx: Category.FBX_MODEL,
    lbg: Category.FBX_MODEL,
    dwg: Category.VECTOR_MODEL,
    rvt: Category.BIM_MODEL,
    ifc: Category.BIM_MODEL,
};

export const getCategoryShowName = (category: Category) => CategoryShowName[category];

export const getCategoryFrom = (subType: SubType) => SubTypeToCategoryMap[subType];

// 支持账号密码免登录
export const parseUrl = (url: string) => {
    const ParamStringSet: Set<string> = new Set(["username", "password", "enterpriseId"]);

    const params: ParamsInfo = {
        username: "",
        password: "",
        enterpriseId: 0,
    };
    const paramsStr = url.slice(1);
    if (paramsStr !== "") {
        const paramsArr = paramsStr.split("&");
        paramsArr.forEach((item) => {
            const paramSplit = item.split("=");
            if (paramSplit.length === 2 && ParamStringSet.has(paramSplit[0])) {
                const key = paramSplit[0];
                const value = paramSplit[1];
                if (Object.prototype.hasOwnProperty.call(params, key)) {
                    if (key === "username") {
                        params.username = value;
                    } else if (key === "password") {
                        params.password = value;
                    } else {
                        params.enterpriseId = Number(value);
                    }
                }
            }
        });
    }

    return params;
};

export const dealMenuData = (list: MenuListProps[]) => {
    const newMenuList = cloneDeep(list);
    return newMenuList.map((el: MenuListProps) => {
        let {menuType, iconName} = el;
        if (Boolean(el.remark) === true) {
            try {
                const remarkObj = JSON.parse(el.remark.replace(/\\/g, ""));
                if (Boolean(remarkObj.menuType) === true) {
                    menuType = remarkObj.menuType;
                }
                if (Boolean(remarkObj.iconName) === true) {
                    iconName = remarkObj.iconName;
                }
            } catch (error) {
                // eslint-disable-next-line no-console
                console.log(error);
            }
        }
        if (el.children.length > 0) {
            // eslint-disable-next-line no-param-reassign
            el.children = dealMenuData(el.children);
        }
        return {
            ...el,
            menuType,
            iconName,
            label: el.menuName ?? "",
            key: new RegExp("[\u4E00-\u9FA5]+").test(el.path) ? nanoid() : el.path,
        };
    });
};

const defaultTreeConfig = {
    id: "id",
    parentId: "parentId",
    children: "children"
};

export const listToTree = <T extends {}, Config extends {
    id: keyof T;
    parentId: keyof T;
    children: keyof T;
}>(
    list: T[],
    config?: Partial<Config>
): T[] => {
    const treeConfig = {...defaultTreeConfig, ...config};
    const {id, parentId, children} = treeConfig as Config;
    const listMap = new Map<string, T>(list.map((v) => [v[id] as unknown as string, v]));
    const root: T[] = [];

    list.forEach((v) => {
        const parent = listMap.get((v[parentId] as unknown as string) ?? "");
        if (parent !== undefined && v[id] !== v[parentId]) {
            if (parent[children] === undefined) {
                (parent[children] as unknown as T[]) = [];
            }
            (parent[children] as unknown as T[]).push(v);
        } else {
            root.push(v);
        }
    });
    return root;
};

/**
 * 判断是不是图片
 * @param fileName 文件名,字符串
 * @returns boolean
 */
export const isImg = (fileName: string): boolean => {
    if (Boolean(fileName) === false) {
        return false;
    }
    const pngSuffix = ["png", "jpg", "jpeg", "bmp", "gif", "webp", "tif", "pjp", "xbm", "jxl", "svgz", "ico", "tiff", "svg", "jfif", "pjpeg", "avif"];
    const urlArr = fileName.split(".");
    const suffix = urlArr[urlArr.length - 1];
    if (Boolean(suffix) === false) {
        return false;
    }
    const urlSuffix = suffix.toLocaleLowerCase();
    const result = pngSuffix.find((item) => item === urlSuffix);
    return Boolean(result);
};

export const isVideo = (fileName: string): boolean => {
    if (Boolean(fileName) === false) {
        return false;
    }
    const videoSuffix = ["mp4", "avi", "mov", "rmvb"];
    const urlArr = fileName.split(".");
    const suffix = urlArr[urlArr.length - 1];
    if (Boolean(suffix) === false) {
        return false;
    }
    const urlSuffix = suffix.toLocaleLowerCase();
    const result = videoSuffix.find((item) => item === urlSuffix);
    return Boolean(result);
};

export const parseFileSize = (size: number | string): {all: string; unit: string; value: number} => {
    let unit = "Bt";
    let newSize = Math.round(Number(size));
    if (newSize > 1024.0) {
        newSize = Math.round(newSize / 1024);
        unit = "Kb";
    }
    if (newSize > 1024.0) {
        newSize = Math.round(newSize / 1024);
        unit = "Mb";
    }
    if (newSize > 1024.0) {
        newSize = Math.round(newSize / 1024);
        unit = "Gb";
    }
    return {all: `${newSize} ${unit}`, unit, value: newSize};
};

export const showMessage = (msg: string) => {
    message.error({content: msg, style: {}});
};

/**
 * 判断字符串是否为空或undefined
 * @param str
 * @returns {boolean}
 */
export const isEmpty = function isEmpty(str: string | undefined): boolean {
    if (str === null
        || str === ""
        || str === undefined
        || str.length === 0
    ) {
        return true;
    }
    return false;
};

type ToArr = <T extends unknown>(val: T) => typeof val;

export const toArr: ToArr = (val) => (Array.isArray(val) ? val : [] as unknown as typeof val);

export type Defined<T> = T extends undefined | null ? never : T;
export const isDefined = <T>(val: T): val is Defined<T> => val !== undefined && val !== null;

export const isNumber = (val: unknown): val is number => typeof val === "number";
export const isString = (val: unknown): val is string => typeof val === "string";

/** 判断一个数据是否是数组且非空 */
export const isNonEmptyArray = <T>(arr: T): arr is Defined<T> => Array.isArray(arr) && arr.length > 0;

export const filterTree = <T extends TreeBase<T>>(
    tree: T[] | undefined,
    predicate: (node: T) => boolean
): T[] | undefined => {
    if (Array.isArray(tree) && tree.length > 0) {
        return tree.map((v) => ({...v})).filter((node) => {
            // eslint-disable-next-line no-param-reassign
            node.children = filterTree(node.children ?? [], predicate);
            return predicate(node) || (Array.isArray(node.children) && node.children.length > 0);
        });
    }
    return undefined;
};

/** dfs tree的后序遍历 */
export const dfsTree = <T extends TreeBase<T>>(tree: T[], callback: (node: T) => void): void => {
    tree.forEach((v) => {
        if (isNonEmptyArray(v.children)) {
            dfsTree(v.children, callback);
        }
        callback(v);
    });
};

/**
 * tree的广度优先遍历
 * @param node tree
 * @returns 展开后的tree
 */
export const bfsTree = <T extends TreeBase<T>>(node: T) => {
    const res: T[] = [];
    const queue: T[] = [];
    queue.push(node);
    while (queue.length > 0) {
        const item = queue.shift();
        if (item !== undefined) {
            res.push(item);
            if (Array.isArray(item.children)) {
                item.children.forEach((v) => queue.push(v));
            }
        }
    }
    return res;
};

export const isNotNullOrUndefined = <T>(object: T | undefined | null): object is T => typeof object !== "undefined" && object !== null;

/**
 * 将对象数组按照指定的key进行分组
 */
export const groupBy = <T extends object>(
    list: T[],
    keyRender: (node: T) => Key
) => {
    const listMap: Map<Key, T[]> = new Map();
    list.forEach((v) => {
        const k = keyRender(v);
        const curList = listMap.get(k) ?? [];
        curList.push(v);
        listMap.set(k, curList);
    });
    return Array.from(listMap);
};

export const uuid = (): string => "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    // eslint-disable-next-line no-bitwise
    const r = (Math.random() * 16) | 0;
    // eslint-disable-next-line no-bitwise
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
});

export const getPercent = (num: number, total: number): number => {
    if (total === 0 || num === 0) {
        return 0;
    }
    return Number(((num / total) * 100).toFixed(2));
};

/**
 *
 * @param type 存储数据的key
 * @param data 存储内容
 */
export const saveData = (type: string, data: unknown): void => {
    sessionStorage.setItem(type, JSON.stringify({data}));
};

/**
 *
 * @param type 存储数据的key
 */
export const getData = (type: string): unknown => {
    const data = sessionStorage.getItem(type);
    if (data === null) {
        return undefined;
    }
    try {
        const obj = JSON.parse(data) as {data: unknown};
        if (isNotNullOrUndefined(obj) && isNotNullOrUndefined(obj.data)) {
            return obj.data;
        }
        return undefined;
    } catch {
        return undefined;
    }
};
export const debounce = (fn: () => void, wait: number) => {
    let timeout: NodeJS.Timeout | null = null;
    if (timeout !== null) {
        clearTimeout(timeout);
    }
    timeout = setTimeout(fn, wait);
};

/**
 * 获取文件后缀名
 */
export const getSuffix = (fileName: string) => {
    const end = fileName.lastIndexOf(".");
    return fileName.slice(end + 1);
};

export const emptyFun = () => null;

// 将树形节点改为一维数组
export const generateList = (data: DataNode[], dataList: DataNode[]) => {
    for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const {key, title} = node;
        dataList.push({key, title});
        if (Array.isArray(node.children) && node.children.length > 0) {
            generateList(node.children, dataList);
        }
    }
    return dataList;
};

// 树搜索时会用到

// matcher的原因是因为 判断条件会不一样
export const findNode = (node: DataNode, filter: string, matcher: (filterText: string, node: DataNode) => boolean) => {
    if (matcher(filter, node) === true) {
        return matcher(filter, node);
    }
    if (Array.isArray(node.children)
        && node.children.length > 0
        && node.children.some((child: DataNode) => findNode(child, filter, matcher))
    ) {
        return true;
    }
    return false;
};

export const filterTreeNew = (node: DataNode, filter: string, matcher: (filterText: string, node: DataNode) => boolean) => {
    // 如果 只有一层，就直接返回
    if (matcher(filter, node) || !Array.isArray(node.children)) {
        return node;
    }
    // 如果不是，则只保留匹配或具有匹配后代的那些
    const filtered: DataNode[] = node.children
        .filter((child: DataNode) => findNode(child, filter, matcher))
        .map((child: DataNode) => filterTreeNew(child, filter, matcher));
    return {...node, children: filtered};
};

export const getFileNameNoSuffix = (fileName: string) => {
    const end = fileName.lastIndexOf(".");
    return fileName.slice(0, end);
};

/**
 * 将数组按照指定长度进行分割
 * @param list 需要分割的数组
 * @param size 指定分割长度
 * @returns 分割后的二维数组
 */
export const chunkArray = <T>(list: T[], size = 2): T[][] => {
    if (size <= 0) {
        return [];
    }
    const tempList: T[][] = [];
    let index = 0;
    while (index < list.length) {
        tempList.push(list.slice(index, index + size));
        index += size;
    }
    return tempList;
};

export const timeFormat = (val: unknown, format = "YYYY.MM.DD", returnVal: null | string = "") => {
    if (val === undefined || val === null) {
        return returnVal;
    }
    if (moment.isMoment(val)) {
        return val.format(format);
    }
    if (moment(val as number).isValid()) {
        return moment(val as number).format(format);
    }
    return returnVal;
};

/**
 * 将树状的数组,转化为扁平化的数组
 * @param val 有children
 * @returns 数组
 */
export const flatTree = <T extends {}>(val: T[]): T[] => {
    const arr: TreeType[] = [];
    const tempTreeData = cloneDeep(val) as unknown as TreeType[];
    const traverData = (treeData: TreeType) => {
        arr.push(treeData);
        if (Array.isArray(treeData.children)) {
            treeData.children.forEach((item) => traverData(item));
        }
    };
    tempTreeData.forEach((item) => traverData(item));
    return arr as unknown as T[];
};

/* 获取计划周期展示文本 */
export const getCycleMomentText = (text: number, type: string) => {
    switch (type) {
        case "YEAR":
            return momentText(text, "YYYY年");
        case "QUARTER":
            return momentText(text, "YYYY年Q季度");
        case "MONTH":
            return momentText(text, "YYYY年M月");
        case "WEEK":
            return momentText(text, `YYYY年M月第${getWeekOfMonth(moment(text))}周`);
        default:
            return "--";
    }
};

// 判断object是否包含某个key
export const isObjHasKey = (obj: {}, key: string) => Object.prototype.hasOwnProperty.call(obj, key);

// 获取变更影响
export const getChangeInfluence = (total?: number, unChangedTotal?: number) => {
    if (total === undefined || total === null || unChangedTotal === undefined || unChangedTotal === null) {
        return "";
    }
    const offset = total - unChangedTotal;
    if (offset === 0) {
        return "总工期不变";
    }
    const changeLabel = offset > 0 ? "延长" : "提前";
    return `总工期${changeLabel} ${Math.abs(offset)} 天，由 ${unChangedTotal} 天变为 ${total} 天`;
};

/**
 * 循环分页获取数据
 * const data = await fetchDataWithPage<T>(async ({page, size}) => {
 *     const res = await fetchFunction(id, page, size);
 *     return {
 *         total: res.data.totalCount,
 *         data: res.data.items,
 *     };
 * });
 */
export const fetchDataWithPage = async <T extends {}>(
    fetchList: (params: {page: number; size: number}) => Promise<{total: number; data: T[]}>,
    options: {size: number},
): Promise<T[]> => {
    let page = 1;
    const {size = 5000} = options;
    const allData: T[] = [];
    const res = await fetchList({page, size});
    const {total, data} = res;
    allData.push(...data ?? []);
    if (total === undefined) {
        return allData;
    }
    if (total > size) {
        while (true) {
            const promiseList = [];
            for (let i = 0; i < 4; i++) {
                if (page * size >= total) {
                    break;
                }
                page++;
                const pageParams = {page, size};
                promiseList.push(fetchList(pageParams));
            }
            const resList = await Promise.all(promiseList);
            resList.forEach((resItem) => {
                if (resItem.data !== undefined) {
                    allData.push(...resItem.data ?? []);
                }
            });
            if (page * size >= total) {
                break;
            }
        }
    }
    return allData;
};
