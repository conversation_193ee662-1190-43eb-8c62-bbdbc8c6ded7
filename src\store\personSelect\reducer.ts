import {Reducer} from "redux";
import {PersonState} from "./interface";
import Actions from "./action";

export const initialState: PersonState = {
    roleData: [],
    orgData: [],
    userNames: [],
    userNameMap: new Map<string, string>(),
    avatarMap: new Map<string, string>(),
    personDataLoading: false
};

const PersonReducer: Reducer<PersonState, Actions> = (state = initialState, action) => {
    switch (action.type) {
        case "SET_PERSON_DATA":
            return {...state, ...action.payload};
        case "CLEAR_PERSON_DATA":
            return {...state, roleData: [], orgData: []};
        case "SET_PERSON_DATA_LOADING":
            return {...state, personDataLoading: action.payload};
        default:
            return {...state};
    }
};

export default PersonReducer;
