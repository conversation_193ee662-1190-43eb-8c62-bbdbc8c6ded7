import React, {useState, useEffect} from "react";
import {useControllableValue} from "ahooks";
import ComTree, {ComTreeProps} from "../../../uikit/Components/ComTree";
import {ValueType} from "./data";
import * as PdsApi from "../../api/pds";
import {toArr} from "../../assets/ts/utils";
import {TreeNodeType} from "../../../uikit/ts/type";


interface PersonTreeRoleProps extends ComTreeProps {
    originPersonData?: PdsApi.UserInfoTypeOfRoleByOrg[];
    value?: ValueType[];
    onChange?: (val: ValueType[]) => void;
    disabledKeys?: string[];
}

interface OriginType extends PdsApi.UserInfoType {
    role: string;
}

type TreeDataType = TreeNodeType<OriginType>;

const PersonTreeRole = (props: PersonTreeRoleProps) => {
    const {disabledKeys, searchKey, originPersonData = []} = props;
    // const {orgInfo, orgList} = useSelector((state: RootState) => state.commonData);
    const [state, setState] = useControllableValue<ValueType[]>(props);
    const [treeData, setTreeData] = useState<TreeDataType[]>([]);
    // const [originPersonData, setOriginPersonData] = useState<PdsApi.UserInfoTypeOfRoleByOrg[]>([]);

    // const handleGetPerson = useCallback(
    //     () => {
    //         const orgDetail = orgList.find((v) => v.id === orgInfo.orgId);
    //         PdsApi.getUserInfoOfRoleByFilterOrg({deptIds: [orgInfo.orgId], orgIds: [orgDetail?.parentId]})
    //             .then((res) => {
    //                 setOriginPersonData(res);
    //                 const allUsers: EasyPerson[] = (res ?? [])
    //                     .map((v) => v.users ?? [])
    //                     .flat()
    //                     .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
    //                 onSetPersons(allUsers);
    //             });
    //     },
    //     [onSetPersons, orgInfo.orgId, orgList]
    // );

    // useEffect(() => {
    //     handleGetPerson();
    // }, [handleGetPerson]);

    useEffect(() => {
        const tempTree = toArr(originPersonData).map((el) => ({
            title: el.role,
            key: `role-${el.role}`,
            originData: {role: el.role ?? ""},
            children: toArr(el.users ?? []).map((e) => ({
                title: `${e.realName ?? e.userName}（${e.userName}）`,
                key: e.userName ?? "",
                disabled: toArr(disabledKeys ?? []).includes(e.userName as string),
                originData: {...e}
            }))
        }));
        setTreeData(tempTree);
    }, [disabledKeys, originPersonData]);

    const handleAllCheckedLeafNode: ComTreeProps["onAllCheckedLeafNode"] = (checkedLeafNodes) => {
        const checkedNodes = checkedLeafNodes as unknown as TreeDataType[];
        const userNames = checkedNodes
            .filter((el) => el.originData.userName !== undefined)
            .map((el) => ({userName: el.originData.userName ?? "", deptId: el.originData.deptIds}));
        setState(userNames);
    };

    return (
        <ComTree
            checkable
            checkedKeys={toArr(state).map((el) => el.userName)}
            onAllCheckedLeafNode={handleAllCheckedLeafNode}
            treeData={treeData}
            searchKey={searchKey}
        />
    );
};

export default PersonTreeRole;
