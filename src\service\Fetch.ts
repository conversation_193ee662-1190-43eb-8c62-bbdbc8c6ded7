/* eslint-disable @typescript-eslint/no-explicit-any */
import {message} from "antd";
import {goToLoginPage} from "../assets/ts/utils";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl: iworksBaseUrl} = (window as any).__IWorksConfig__;

interface FetchProps {
    url: string;
    baseUrl?: string;
    methods?: "get" | "post" | "put" | "delete";
    data?: unknown;
    formData?: FormData;
    needToken?: boolean;
    token?: string;
    requestConfig?: RequestInit;
    isDeal?: boolean; // 是否处理res, 默认true，处理
    responseType?: string;
}

message.config({
    maxCount: 1,
});

const Fetch = async <T = unknown>(props: FetchProps): Promise<T> => {
    const {
        url,
        baseUrl = iworksBaseUrl as string,
        methods = "get",
        data,
        formData,
        token,
        needToken = true,
        requestConfig,
        isDeal = true,
        responseType
    } = props;

    let requestUrl = url.includes("http") ? url : baseUrl + url;
    let requestBody: FormData | string | undefined = formData ?? JSON.stringify(data);
    const requestToken = token ?? localStorage.getItem("token");
    const requestHeader: HeadersInit = new Headers();

    if (Boolean(formData) === false) {
        requestHeader.append("content-type", "application/json;chartset=utf-8");
    }

    if (Boolean(requestToken) && needToken) {
        requestHeader.append("access-token", requestToken ?? "");
    }

    if (methods === "get" && Boolean(data)) {
        requestBody = undefined;
        const getParamsArr: string[] = [];
        Object.entries(data as JSON).forEach(([key, value]) => {
            getParamsArr.push(`${key}=${value}`);
        });
        requestUrl = `${requestUrl}?${getParamsArr.join("&")}`;
    }

    return new Promise((resolve, reject) => {
        fetch(requestUrl, {
            method: methods,
            headers: requestHeader,
            body: requestBody,
            ...requestConfig
        }).then((response) => {
            const contentType = response.headers.get("Content-Type") ?? "";
            // 文件流处理
            // 文档预览接口,返回类型是application/octet-stream,实际类型是text
            if (responseType !== "text" && contentType.includes("application/octet-stream")) {
                response.blob().then((resData) => {
                    resolve(resData as unknown as T);
                });
            } else {
                const responseClone = response.clone();
                response.json().then((res) => {
                    if (isDeal) {
                        if (res.code === 200) {
                            resolve(res);
                        } else if (Boolean(res.code) === false) {
                            resolve(res);
                        } else if (res.code === 1007 || res.code === 1002) {
                            // 跳转登录页
                            // window.location.replace(`${window.location.origin}/#/login`);
                            message.warning(res.msg, 5).then(() => goToLoginPage());
                        } else {
                            message.warning(res.msg);
                            reject(res);
                        }
                    } else {
                        resolve(res);
                    }
                }).catch(() => {
                    responseClone.text().then((cloneData) => {
                        resolve(cloneData as any);
                    });
                });
            }
        }).catch((err) => {
            reject(err);
        });
    });
};

export const postUploadFile = async (option: {
    url: string;
    headers: any;
    file: File;
    onProgress?: (percent: number) => void;
}): Promise<string> => new Promise((res, rej) => {
    const {url, headers, file, onProgress} = option;
    const form = new FormData();
    form.append("file", file as Blob);
    const xhr = new XMLHttpRequest();
    Object.keys(headers).forEach((key) => xhr.setRequestHeader(key, headers[key]));
    xhr.open("post", url);
    xhr.onload = (e) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        if (e.target?.status === 200) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
            // @ts-ignore
            res(e.target.response);
        }
    };
    xhr.onerror = rej;
    if (Boolean(xhr.upload) === true) {
        xhr.upload.onprogress = (e) => {
            const {loaded, total} = e;
            const percent = Math.round(loaded / total * 100);
            if (onProgress instanceof Function) {
                onProgress(percent);
            }
        };
    }
    xhr.send(form);
});

export const fetchFileBlob = async (url: string) => {
    const requestToken = localStorage.getItem("token") ?? "";
    const requestHeader: HeadersInit = new Headers();
    requestHeader.append("access-token", requestToken ?? "");
    return fetch(url, {method: "get", headers: requestHeader}).then(async (res) => res.blob());
};

export default Fetch;
