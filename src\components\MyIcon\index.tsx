import {createFromIconfontCN} from "@ant-design/icons";
import React, {CSSProperties, memo} from "react";
import Color from "../../assets/css/Color";

export const Icon = createFromIconfontCN({
    scriptUrl: "./static/iconfont.js"
});

export interface MyIconProps {
    onClick?: React.MouseEventHandler<HTMLDivElement>;
    style?: CSSProperties;
    type: string;
    color?: false | string;
    fontSize?: number;
}

const styleInit = {
    cursor: "pointer",
};

const MyIcon = (props: MyIconProps) => {
    const {
        style = {},
        onClick,
        type,
        fontSize,
        color = Color["text-3"]
    } = props;
    return (
        <div onClick={onClick} style={{display: "inline-block"}}>
            <Icon style={{color: color === false ? "inherit" : color, fontSize, ...styleInit, ...style}} type={type} />
        </div>
    );
};
export default memo(MyIcon);
