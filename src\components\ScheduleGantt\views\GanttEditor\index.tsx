/* eslint-disable import/extensions */
/* eslint-disable max-lines-per-function */
import React, {useEffect, useState, useCallback, useRef, ReactElement, useMemo, ReactNode} from "react";
import {message, Modal, Spin} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import "@iworks/dhtmlx-gantt/codebase/skins/dhtmlxgantt_material.css";
import "../../../../../static/gantt/api.js";

import {useSelector} from "react-redux";
import clsx from "clsx";
import {createUseStyles} from "react-jss";
import {RootState} from "../../../../store/rootReducer";
import {FromType} from "../../config/interface";
import {getCalendarInfo, getPlanInfoDetail, updatePlanInfo} from "../../../../api/Preparation/index";
import {handleSaveTaskParam, handleServerData} from "../../common/function";
import {GanttTask, stringNumberArray, GanttLink, GanttScale, GanttPerson} from "../../gantt/interface";
import {
    getTotalDuration,
    updateAllProcessInspectionActualTime,
    // updateAllProjectActualTime,
    updateTaskHasPhoto
} from "../../gantt/taskUtils";
import {
    setGanttEditable,
    afterGanttInit,
    globalInit,
    parseGanttData,
} from "../../gantt/ganttConfig";
import createPlanStyles from "../../jss/plan";

import PhotoModal from "../../components/PhotoModal";
import EditTaskModal from "../../components/EditTaskModal";
import GanttHeader from "../../components/GanttHeader/index";
import {GanttButtonType} from "../../components/GanttButton/index";
import GanttToolBar from "../../components/GanttToolBar";
import EditorContext from "./context";
import {buildCalenderInfo, checkHasParentPlan} from "../../tool/planUtils";
import {setGanttAllColumns} from "../../gantt/columnUtils";
import {setGanttCalender} from "../../gantt/calendarUtils";
import {getSearchUsersOfRole} from "../../../../api/pds";
import {getPlanTaskList, getTaskAllColumnList, postSyncProcessInspectionTime, postTaskBindNode, postTaskListBindNode, updatePlanTask} from "../../api/plan/index";
import {WebRes} from "../../../../api/common.type";
import {PlanInfoDetailType} from "../../../../api/Preparation/type";
import {CalendarInfo, ColumnItem, PostTaskListBindNodeItem} from "../../api/plan/type";
import ganttManager from "../../gantt/ganttManager";
import {getTaskEbsNodeList, postTaskBindEbsNodeList, postWbsBindEbs, TaskBindEbsNodeType} from "../../../../api/wbsEbs";
import useModelView from "../../../ModelSelect/useModelView";
import GanttManagerNode from "../../components/GanttManagerNode";
import EditPredecessorsModal from "../../components/EditPredecessorsModal";
import {resourceUnLock} from "../../api/common/index";
import SelectPersonDrawer from "../../components/SelectPersonDrawer";
import {DutyPerson} from "../../components/SelectPersonDrawer/data";

// 甘特图全局配置
globalInit();

const useStyles = createUseStyles({
    pageHeaderSubTitle: {
        width: "56px",
        height: "12px",
        fontSize: "14px",
        fontWeight: "400",
        color: "#444444",
        opacity: "0.85",
    },
    ganttSpinContainer: {
        flexGrow: 1,
        width: "100%",
        "@global": {
            ".ant-spin-nested-loading": {
                height: "100%",
            },
            ".ant-spin-container": {
                height: "100%",
            }
        },
    },
});

export interface ScheduleEditorProps {
    planId: string;
    fromType: FromType; // 页面类型
    enterType: "view" | "edit";
    showHeader?: boolean;
    headerRightButtons?: (ReactElement | GanttButtonType)[];
    toolLeftButtons?: (ReactElement | GanttButtonType)[];
    toolRightButtons?: (ReactElement | GanttButtonType)[];
    tableTitle?: ReactNode;
    onBack?: () => void;
    onLaunchApproval?: (planInfo: PlanInfoDetailType) => void; // 发起审批
}

const GanttEditor = (props: ScheduleEditorProps) => {
    const {planRoot} = createPlanStyles();

    const {ganttSpinContainer} = useStyles();
    const {
        planId,
        fromType,
        enterType,
        showHeader = true,
        headerRightButtons,
        toolLeftButtons,
        toolRightButtons,
        onBack,
        onLaunchApproval,
        tableTitle,
    } = props;
    const {token, orgInfo, curSectionInfo} = useSelector((store: RootState) => store.commonData);
    const [planInfo, setPlanInfo] = useState<Partial<PlanInfoDetailType>>({id: planId});
    const [parentPlanInfo, setParentPlanInfo] = useState<Partial<PlanInfoDetailType>>({});
    // 上次获取任务明细的时间，用于签出任务时判断是否被更新过
    // const lastTime = useRef<number>(0);
    const currentCalendarRef = useRef<CalendarInfo>();
    const refreshTaskRef = useRef<() => void>();
    const checkoutMethodRef = useRef<(checkoutSuccess: boolean) => void>();
    const updatePlanTaskMethodRef = useRef<() => Promise<any>>();
    const saveMethodRef = useRef<(checkinType: 1 | 2) => Promise<any>>();
    const needAutoSave = useRef<boolean>(true);
    const [isWbs, setIsWbs] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(true);
    const saved = useRef<boolean>(false); // 是否保存过
    const [isShowGantt, setIsShowGantt] = useState<boolean>(false); // 是否显示横道图
    const ganttContainerRef = useRef<HTMLDivElement>(null);
    const isCheckIn = useRef<boolean>(true); // 是否签入
    const [checkoutStatus, setCheckoutStatus] = useState<boolean>(false);
    const [scale, setScale] = useState("");
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [dutyPersonValue, setDutyPersonValue] = useState<DutyPerson>();
    const [dutyPersonVisible, setDutyPersonVisible] = useState<boolean>(false);
    const [photoModalVisible, setPhotoModalVisible] = useState<boolean>(false);
    const [photoTaskId, setPhotoTaskId] = useState<string | undefined>(undefined);
    const customColumnsRef = useRef<ColumnItem[]>([]);
    // 自定义任务编辑弹框
    const [editTask, setEditTask] = useState<GanttTask | undefined>(undefined);
    const [editTaskModalVisible, setEditTaskModalVisible] = useState<boolean>(false);
    const [editPredecessorsModalVisible, setEditPredecessorsModalVisible] = useState<boolean>(false);

    // 服务器返回的原始数据，用于保存编辑时判断修改内容
    const originalTaskIds = useRef<stringNumberArray>([]);
    const originalPreTaskIds = useRef<stringNumberArray>([]);
    const originalTasks = useRef<Map<string | number, GanttTask>>(new Map<string | number, GanttTask>());
    const originalLinks = useRef<Map<string | number, GanttLink>>(new Map<string | number, GanttLink>());

    const {openChildTab, sendMessage} = useModelView();

    const projNameMap = useRef<Map<number, string | undefined>>(new Map()); // 保存所有关联的模型ppid和名称
    const modelBindEbsMap = useRef<Map<number, string[]>>(new Map()); // 保存关联构件的模型ppid和构件guid
    const modelBindWbsMap = useRef<Map<number, string[]>>(new Map()); // 保存关联wbs的模型ppid和wbsId

    const hasParentPlan = useMemo(() => checkHasParentPlan(planInfo), [planInfo]);

    useEffect(() => {
        // console.log("fromType", fromType);
        ganttManager.fromType = fromType;
        ganttManager.setLocalColumnsWithFromType(fromType);
    }, [fromType]);

    const resetData = () => {
        originalTaskIds.current = [];
        originalTasks.current.clear();
        originalPreTaskIds.current = [];
        originalLinks.current.clear();
    };

    // 获取所有列信息
    const fetchAllColumnList = useCallback(async () => {
        if (planId === undefined) {
            return;
        }
        try {
            const columnsRes = await getTaskAllColumnList(planId, fromType);
            // console.log("columnsRes", columnsRes);
            if (columnsRes.success) {
                const columnInfos: ColumnItem[] = [];
                const customColumnInfos: ColumnItem[] = [];
                columnsRes.data.forEach((columnInfo) => {
                    if (columnInfo.valueType !== null && columnInfo.valueType !== undefined) {
                        customColumnInfos.push(columnInfo);
                    } else {
                        columnInfos.push(columnInfo);
                    }
                });
                // console.log("columnInfos", columnInfos);
                // console.log("customColumnInfos", customColumnInfos);
                const allColumns = [...columnInfos, ...customColumnInfos];
                setGanttAllColumns(allColumns);
                customColumnsRef.current = customColumnInfos;
            }
        } catch (error) {
            console.log("error", error);
        }
    }, [fromType, planId]);

    const fetchTaskList = useCallback(async () => {
        const container = ganttContainerRef.current;
        if (container === undefined && currentCalendarRef.current === undefined) {
            return;
        }
        try {
            // 获取详细的任务信息
            const planTaskListRes = await getPlanTaskList(planId);
            // console.log("getPlanTaskList", planTaskListRes);
            if (planTaskListRes.data !== null) {
                // lastTime.current = res.data.serverTime;
                const taskData = handleServerData(planTaskListRes.data, currentCalendarRef.current!, customColumnsRef.current);
                resetData();
                let bindWbsNodes: PostTaskListBindNodeItem[] = [];
                let bindEbsNodes: TaskBindEbsNodeType[] = [];
                // 绑定的wbs
                const bindWbsBusinessIds = planTaskListRes.data.taskList.filter((item) => item.bindType === -1).map((item) => item.id);
                try {
                    const bindWbsListRes = await postTaskListBindNode(bindWbsBusinessIds);
                    bindWbsNodes = bindWbsListRes.data ?? [];
                } catch (error) {
                    console.log("error", error);
                }
                // 获取是否存在绑定构件
                const bindEbsBusinessIds = planTaskListRes.data.taskList.filter((item) => item.bindType !== 0).map((item) => item.id);
                try {
                    const bindEbsListRes = await postTaskBindEbsNodeList(bindEbsBusinessIds);
                    bindEbsNodes = bindEbsListRes.data ?? [];
                } catch (error) {
                    console.log("error", error);
                }

                taskData.tasks = taskData.tasks.map((task) => {
                    const wbsNode = bindWbsNodes.find((node) => node.businessId === task.id);
                    const ebsNode = bindEbsNodes.find((node) => node.businessId === task.id);
                    const wbsNodeLevel = wbsNode?.wbsNodes?.[0]?.level;
                    return {
                        ...task,
                        wbsNodeLevel,
                        wbsNodeIds: wbsNode?.wbsNodes.map((node) => node.wbsNodeId),
                        hasEbs: ebsNode?.bindEbs,
                    };
                });
                taskData.tasks.forEach((task: GanttTask) => {
                    originalTaskIds.current.push(task.id);
                    originalTasks.current.set(task.id, task);
                });
                taskData.preTasks.forEach((l: GanttLink) => {
                    originalPreTaskIds.current.push(l.id);
                    originalLinks.current.set(l.id, l);
                });
                // console.log("parseGanttData taskData", taskData);
                parseGanttData({data: taskData.tasks, links: taskData.preTasks});
                // updateAllProjectActualTime();
                if (fromType === "actual") {
                    updateAllProcessInspectionActualTime();
                }
                gantt.render();

                if (fromType === "plan" && isCheckIn.current !== true) {
                    const taskCount = gantt.getTaskCount();
                    if (taskCount === 0) {
                        // 如果没有任务，则解除关联
                        const updatePlanRes = await updatePlanInfo({
                            id: planId,
                            parentPlanTaskIds: [],
                        });
                        if (updatePlanRes.success) {
                            setPlanInfo((prev) => ({...prev, parentId: undefined}));
                        }
                    }
                }
            }
        } catch (error) {
            console.log("error", error);
        }
    }, [fromType, planId]);

    refreshTaskRef.current = fetchTaskList;

    // syncType: 1 | 2 同步类型：1-全覆盖同步，2-仅同步未修改过的
    const doSyncProcess = useCallback(async (syncType: 1 | 2, silence = false) => {
        console.log("同步质量验评 syncType", syncType);
        if (planId !== undefined) {
            try {
                const res = await postSyncProcessInspectionTime({planId, syncType});
                if (res.success) {
                    if (silence !== true) {
                        message.success("同步完成");
                        if (res.data === true) {
                            // data 为true说明需要刷新列表
                            // 同步完成，刷新列表
                            await fetchTaskList();
                        }
                    }
                } else if (silence !== true) {
                    message.error("同步失败");
                }
            } catch (error) {
                console.log("error", error);
                if (silence !== true) {
                    message.error("同步失败");
                }
            }
        }
    }, [fetchTaskList, planId]);

    // 获取负责人列表
    const fetchTaskDutyPersonList = useCallback(async (nodeId: string, nodeType: number) => {
        try {
            const res = await getSearchUsersOfRole({
                deptId: orgInfo.orgId,
                nodeId,
                nodeType
            });
            const allUsers: GanttPerson[] = (res ?? [])
                .map((v) => v.users ?? [])
                .flat()
                .map((v) => ({
                    key: v.userName ?? "",
                    label: v.realName ?? "",
                    userName: v.userName ?? "",
                    realName: v.realName ?? "",
                    email: "",
                }));
            // setAllPerson(allUsers);
            ganttManager.allPersonUserRealNameMap = new Map(allUsers.map((v) => [v.userName, v.realName]));
            ganttManager.allPerson = allUsers;
            // const res = await getTaskOwnerList(orgInfo.orgId);
            // if (res?.data !== null && res?.data !== undefined) {
            //     ganttManager.allPersonUserRealNameMap = new Map(res.data.map((v) => [v.userName, v.realName]));
            //     ganttManager.allPerson = res.data;
            // }
        } catch (error) {
            console.log("error", error);
        }
    }, [orgInfo.orgId]);

    const fetchCalendar = useCallback(async (calendarId: string) => {
        // console.log("fetchCalendar", calendarId);
        const res = await getCalendarInfo(calendarId);
        if (res.success === true && res.data !== undefined) {
            // 设置甘特图工作日历
            currentCalendarRef.current = buildCalenderInfo(res.data);
            setGanttCalender(currentCalendarRef.current);
        }
    }, []);

    // 获取计划数据
    const fetchAllData = useCallback(async () => {
        if (planId === undefined) {
            return;
        }
        setLoading(true);
        try {
            const res: WebRes<PlanInfoDetailType> = await getPlanInfoDetail(planId);
            if (res.success && res.data !== undefined) {
                const {nodeId, nodeType, calendar, startDate, endDate} = res.data;
                setPlanInfo(res.data);
                // 先获取甘特图工作日历
                await fetchCalendar(calendar);
                ganttManager.setPlanStartDate(new Date(startDate));
                ganttManager.setPlanEndDate(new Date(endDate));

                if (fromType === "actual" && ganttManager.isWbs === true) {
                    // 实际计划，先执行一次同步
                    await doSyncProcess(2, true);
                }
                // 再获取详细的任务信息
                await fetchTaskList();
                // 获取责任人列表
                await fetchTaskDutyPersonList(nodeId, nodeType);
            }
        } finally {
            setLoading(false);
        }
    }, [doSyncProcess, fetchCalendar, fetchTaskList, fetchTaskDutyPersonList, fromType, planId]);

    // 获取计划数据
    const fetchParentPlanInfo = useCallback(async () => {
        if (planInfo.parentId === undefined || planInfo.parentId.length === 0) {
            ganttManager.parentPlanStartDate = undefined;
            ganttManager.parentPlanEndDate = undefined;
            return;
        }
        try {
            const res: WebRes<PlanInfoDetailType> = await getPlanInfoDetail(planInfo.parentId);
            if (res.success && res.data !== undefined) {
                setParentPlanInfo(res.data);
                // console.log("setParentPlanInfo", res.data);
                ganttManager.parentPlanStartDate = new Date(res.data.startDate);
                ganttManager.parentPlanEndDate = new Date(res.data.endDate);
                gantt.render();
            }
        } catch (error) {
            console.log("error", error);
        }
    }, [planInfo.parentId]);

    useEffect(() => {
        fetchParentPlanInfo();
    }, [fetchParentPlanInfo]);

    checkoutMethodRef.current = useCallback((checkoutSuccess: boolean) => {
        // console.log("checkoutMethod.current", checkoutSuccess);
        if (checkoutSuccess) {
            isCheckIn.current = false;
            setCheckoutStatus(true);
            // 签出成功后，将gantt设置为可编辑
            setGanttEditable(true);
            gantt.render();
        }
        // // 如果进度计划需要更新，则重新获取数据刷新
        // if (res.result?.updated === true) {
        //     fetchAllData();
        // }
    }, []);

    // 保存过程中操作失败的回调
    const handleFailed = () => {
        needAutoSave.current = true;
        isCheckIn.current = false;
    };

    const fetchCheckIn = useCallback(async () => {
        if (isCheckIn.current === false) {
            const res = await resourceUnLock(fromType === "actual" ? "ACTUAL" : "PLAN", planId);
            if (res.success) {
                // 需要改为签入状态
                isCheckIn.current = true;
                // 仅当删除签出状态才需要更新页面状态
                setCheckoutStatus(false);
                setGanttEditable(false);
                gantt.resetLayout();
                afterGanttInit();
                gantt.render();
            }
        }
    }, [fromType, planId]);

    updatePlanTaskMethodRef.current = useCallback(async () => {
        const {
            addTasks,
            modifyTasks,
            deleteTasks,
            addPreTasks,
            modifyPreTasks,
            deletePreTaskIds,
            bindWbsTasks,
        } = handleSaveTaskParam(
            originalTaskIds.current,
            originalTasks.current,
            originalPreTaskIds.current,
            originalLinks.current,
            customColumnsRef.current
        );
        try {
            const response: WebRes<string> = await updatePlanTask({
                planId,
                addTaskList: addTasks,
                updateTaskList: modifyTasks,
                deleteTaskIdList: deleteTasks,
                addPredTaskRelationList: addPreTasks,
                updatePredTaskRelationList: modifyPreTasks,
                deletePredTaskRelationIdList: deletePreTaskIds,
                opt: fromType === "actual" ? "ACTUAL" : "PLAN",
            });
            if (response.success) {
                resetData();
                saved.current = true;
                gantt.eachTask((task: GanttTask) => {
                    task.order = gantt.getTaskIndex(task.id); // eslint-disable-line no-param-reassign
                    originalTaskIds.current.push(task.id);
                    originalTasks.current.set(task.id, {...task});
                });
                gantt.getLinks().forEach((l: GanttLink) => {
                    originalPreTaskIds.current.push(l.id);
                    originalLinks.current.set(l.id, {...l});
                });
                const totalDuration = getTotalDuration();

                // console.log("bindWbsTasks", bindWbsTasks);
                await postTaskBindNode({
                    deptId: orgInfo.orgId,
                    nodeId: curSectionInfo?.id ?? "",
                    planId,
                    bindType: -1,
                    businessType: "PLAN_TASK",
                    list: bindWbsTasks.filter((task) => task.bindType === -1 && task.wbsNodeIds !== undefined && task.wbsNodeIds.length > 0)
                        .map((task) => ({
                            businessId: String(task.id),
                            wbsNodeIds: task.wbsNodeIds,
                        }))
                });
                if (fromType === "plan") {
                    const parentPlanTaskIds: string[] = [];
                    gantt.eachTask((task: GanttTask) => {
                        if (planInfo.parentId !== undefined && planInfo.parentId.length > 0 && task.planId === planInfo.parentId) {
                            parentPlanTaskIds.push(`${task.id}`);
                        }
                    });
                    // 只有计划编制才会更新计划信息
                    await updatePlanInfo({
                        id: planId,
                        currentTotalDuration: totalDuration,
                        parentId: planInfo.parentId,
                        parentPlanTaskIds,
                    });
                }
                // message.success({content: "保存成功", key: "savePlan"});
                return true;
            }
            throw Error("保存失败");
        } catch (error) {
            throw Error("保存失败");
        }
    }, [curSectionInfo, fromType, orgInfo.orgId, planId, planInfo.parentId]);

    saveMethodRef.current = useCallback(async (checkinType) => {
        if (updatePlanTaskMethodRef.current === undefined) {
            return;
        }
        // message.loading({content: "正在保存...", key: "savePlan"}, 0);
        try {
            setSaveLoading(true);
            const res = await updatePlanTaskMethodRef.current();
            if (checkinType === 1 && res === true) {
                // message.success({content: "保存成功", key: "savePlan"});
                // 签入
                await fetchCheckIn();
            }
            return res;
        } catch (_error) {
            handleFailed();
            message.error({content: "保存失败", key: "savePlan"});
            return false;
        } finally {
            setSaveLoading(false);
        }
    }, [fetchCheckIn]);

    useEffect(() => {
        if (ganttContainerRef.current !== null) {
            gantt.init(ganttContainerRef.current);
            afterGanttInit();
            gantt.clearAll();
            fetchAllColumnList();
            fetchAllData();
        }
    }, [fetchAllData, fetchAllColumnList]);

    // 打开照片对话框
    const handlePhoto = useCallback(() => {
        const selectedId = gantt.getSelectedId();
        if (selectedId === null) {
            message.error("请先选中一条任务");
            return;
        }
        setPhotoTaskId(selectedId);
        setPhotoModalVisible(true);
    }, [setPhotoModalVisible]);

    const tabOpenSuccess = useCallback(() => {
        sendMessage("SetBaseData", {
            deptId: orgInfo.orgId,
            token
        });
        const data: unknown[] = [];
        // eslint-disable-next-line no-restricted-syntax
        for (const kv of projNameMap.current) {
            // console.log("kv", kv);
            const [ppid, projName] = kv;
            const ebsGuids = modelBindEbsMap.current.get(ppid) ?? [];
            const wbsIds = modelBindWbsMap.current.get(ppid) ?? [];
            data.push({
                ppid,
                projectName: projName ?? `${ppid}`,
                selectedGuids: ebsGuids,
                wbsList: wbsIds,
            });
        }
        console.log("SetProjectList", {data});
        sendMessage("SetProjectList", {data});
    }, [orgInfo.orgId, sendMessage, token]);

    // 反查关联模型构件
    const lookUpRelateBim = async (taskId: string) => {
        projNameMap.current.clear();
        modelBindEbsMap.current.clear();
        modelBindWbsMap.current.clear();
        const listEbsRes = await getTaskEbsNodeList(taskId);
        if (listEbsRes.code === 200) {
            const ebsNodes = listEbsRes.data;
            if (ebsNodes !== undefined && ebsNodes !== null && ebsNodes.length > 0) {
                // 保存ebs数据
                ebsNodes.forEach((el) => {
                    if (el.ppid === undefined || el.ebsNodes === undefined) {
                        return;
                    }
                    projNameMap.current.set(el.ppid, el.projName);
                    modelBindEbsMap.current.set(el.ppid, el.ebsNodes.map((ebs) => ebs.handle));
                });
            }
        }
        const taskNode: GanttTask = gantt.getTask(taskId);
        if (taskNode.wbsNodeIds !== undefined) {
            const listWbsRes = await postWbsBindEbs({
                wbsIds: taskNode.wbsNodeIds,
                containChildren: true,
            });
            if (listWbsRes.code === 200) {
                const wbsNodes = listWbsRes.data;
                if (wbsNodes !== undefined && wbsNodes !== null && wbsNodes.length > 0) {
                    // 保存wbs数据
                    wbsNodes.forEach((el) => {
                        if (el.ppid === undefined) {
                            return;
                        }
                        projNameMap.current.set(el.ppid, el.projName);
                        modelBindWbsMap.current.set(el.ppid, el.wbsIds);
                    });
                }
            }
        }
        if (modelBindEbsMap.current.size === 0 && modelBindWbsMap.current.size === 0) {
            message.error({content: "节点未关联模型!"});
            return;
        }
        openChildTab(tabOpenSuccess);
    };

    const handleBackClick = useCallback(async () => {
        const {
            addTasks,
            modifyTasks,
            deleteTasks,
            addPreTasks,
            modifyPreTasks,
            deletePreTaskIds,
        } = handleSaveTaskParam(
            originalTaskIds.current,
            originalTasks.current,
            originalPreTaskIds.current,
            originalLinks.current,
            customColumnsRef.current
        );
        const isChanged = addTasks.length > 0
            || modifyTasks.length > 0
            || deleteTasks.length > 0
            || addPreTasks.length > 0
            || modifyPreTasks.length > 0
            || deletePreTaskIds.length > 0;

        if (isChanged && checkoutStatus) {
            Modal.confirm({
                title: "是否保存已修改任务？",
                okText: "确定",
                cancelText: "取消",
                onOk: async () => {
                    if (checkoutStatus && !isCheckIn.current && saveMethodRef.current !== undefined) {
                        // 签出状态需要先保存数据，并不自动保存
                        needAutoSave.current = false;
                        const res = await saveMethodRef.current(1);
                        if (onBack instanceof Function && res === true) {
                            message.success({content: "保存成功", key: "savePlan"});
                            await fetchCheckIn();
                            onBack();
                        }
                    }
                },
                onCancel: async () => {
                    if (onBack instanceof Function) {
                        await fetchCheckIn();
                        onBack();
                    }
                }
            });
        } else if (onBack instanceof Function) {
            await fetchCheckIn();
            onBack();
        }
    }, [checkoutStatus, fetchCheckIn, onBack]);

    // const autoSaveWithExit = useCallback(async () => {
    //     // 退出当前页面，保存计划
    //     if (!isCheckIn.current && needAutoSave.current && saveMethodRef.current !== undefined) {
    //         await saveMethodRef.current(1);
    //     }
    // }, []);

    const handleChangePerson = useCallback((value?: DutyPerson) => {
        // console.log("handlePersonChange", value);
        if (editTask !== undefined && value !== undefined) {
            const newTask: GanttTask = {
                ...editTask,
                dutyPerson: value
            };
            // 需要更新前置任务link之后再更新任务
            gantt.updateTask(newTask.id, newTask);
        }
    }, [editTask]);

    useEffect(() => {
        if (fromType === "plan") {
            gantt.showLightbox = (id: number) => {
                const task: GanttTask = gantt.getTask(id);
                if (task.readonly !== true) {
                    // if (task.$new === true) {
                    //     task = {
                    //         ...task,
                    //         duration: undefined,
                    //         // eslint-disable-next-line @typescript-eslint/camelcase
                    //         start_date: undefined,
                    //         // eslint-disable-next-line @typescript-eslint/camelcase
                    //         end_date: undefined
                    //     };
                    // }
                    setEditTask(task);
                    setEditTaskModalVisible(true);
                }
            };
            gantt.hideLightbox = () => {
                setEditTaskModalVisible(false);
            };
        } else {
            gantt.showLightbox = () => 0;
            gantt.hideLightbox = () => 0;
        }
    }, [fromType, planInfo.parentId]);

    useEffect(() => {
        gantt.detachEvent("ganttRender");
        gantt.attachEvent("onGanttRender", () => {
            const ganttScale: GanttScale = gantt.getScale();
            if (ganttScale !== null) {
                setScale(ganttScale.unit);
            }
        }, {id: "ganttRender"});

        gantt.detachEvent("buttonClick");
        gantt.attachEvent("onTaskClick", (id, e) => {
            const btn = e.target.closest("[data-action]");
            if (btn !== undefined && btn !== null) {
                const action = btn.getAttribute("data-action");
                switch (action) {
                    case "relateBIM":
                        lookUpRelateBim(id);
                        break;
                    case "relatePhoto":
                        setPhotoTaskId(id);
                        setPhotoModalVisible(true);
                        break;
                    case "editPredecessor": {
                        if (isCheckIn.current !== true) {
                            const task = gantt.getTask(id);
                            if (task.readonly !== true) {
                                setEditTask(task);
                                setEditPredecessorsModalVisible(true);
                            }
                        }
                        break;
                    }
                    case "selectDutyPerson": {
                        if (isCheckIn.current !== true) {
                            const task: GanttTask = gantt.getTask(id);
                            if (task.readonly !== true) {
                                setEditTask(task);
                                setDutyPersonValue(task.dutyPerson);
                                setDutyPersonVisible(true);
                            }
                        }
                        break;
                    }
                    default:
                        console.log("action", action);
                        return true;
                }
            }
            return true;
        }, {id: "buttonClick"});

        return () => {
            document.querySelectorAll(".gantt_tooltip").forEach((el) => {
                el.remove();
            });
            gantt.clearAll();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [planInfo.parentId]);

    return (
        <EditorContext.Provider
            value={{
                planInfo,
                setPlanInfo,
                parentPlanInfo,
                setParentPlanInfo,
                hasParentPlan,
                fromType,
                enterType,
                isWbs,
                setIsWbs,
                scale,
                loading,
                needAutoSave,
                setLoading,
                isShowGantt,
                setIsShowGantt,
                checkoutStatus,
                saveLoading,
                currentCalendarRef,
                onPhoto: handlePhoto,
                customColumnsRef,
                fetchAllData,
                doSyncProcess,
                refreshTaskRef,
                checkoutMethodRef,
                updatePlanTaskMethodRef,
                saveMethodRef,
                onLaunchApproval,
            }}
        >
            {/* 使用GanttManager组件保存editorContext */}
            <GanttManagerNode />
            <div className={clsx(planRoot)}>
                {
                    showHeader && (
                        <GanttHeader
                            showBack={onBack instanceof Function}
                            onBack={handleBackClick}
                            title={planInfo?.name ?? ""}
                            rightButtons={headerRightButtons}
                        />
                    )
                }
                <GanttToolBar
                    // style={{marginBottom: 8}}
                    style={{marginBottom: 12}}
                    leftButtons={toolLeftButtons}
                    rightButtons={toolRightButtons}
                />
                {tableTitle}
                <div className={clsx(ganttSpinContainer)}>
                    <Spin tip="加载中···" size="large" spinning={loading}>
                        <div
                            ref={ganttContainerRef}
                            style={{width: "100%", height: "100%"}}
                        />
                    </Spin>
                </div>
                <EditTaskModal
                    visible={editTaskModalVisible}
                    setVisible={setEditTaskModalVisible}
                    editTask={editTask}
                />
                <EditPredecessorsModal
                    visible={editPredecessorsModalVisible}
                    setVisible={setEditPredecessorsModalVisible}
                    editTask={editTask}
                />
                <SelectPersonDrawer
                    visible={dutyPersonVisible}
                    setVisible={setDutyPersonVisible}
                    value={dutyPersonValue}
                    onChange={handleChangePerson}
                    nodeId={planInfo.nodeId}
                    nodeType={planInfo.nodeType}
                />
                <PhotoModal
                    visible={photoModalVisible}
                    planId={planId}
                    taskId={photoTaskId as string}
                    editable={checkoutStatus}
                    onUpdateHasPhoto={updateTaskHasPhoto}
                    onCancel={() => setPhotoModalVisible(false)}
                    onConfirm={() => setPhotoModalVisible(false)}
                />
            </div>
        </EditorContext.Provider>
    );
};

export default GanttEditor;
