import {createUseStyles} from "react-jss";

const useStyle = createUseStyles({
    virtualizedTreeNode: {
        display: "flex",
        alignItems: "center"
    },
    virtualizedTreeTitle: {
        flexGrow: 1,
        padding: "0 5px",
        display: "flex",
        overflow: "hideen",
        alignItems: "center",
        alignSelf: "stretch",
        "&:hover": {
            transition: "all .3s"
        }
    },
    virtualizedTreeSwitcher: {
        width: "24px",
        height: "24px",
        margin: 0,
        lineHeight: "24px",
        textAlign: "center",
        verticalAlign: "top",
        border: 0,
        outline: "none",
        cursor: "pointer",
        display: "inline-flex",
        justifyContent: "center",
        alignItems: "center",
        "& > svg, img": {
            transition: "transform 0.3s"
        }
    },
    virtualizedTreeSwitcherOpen: {
        "& > svg, img": {
            transform: "rotate(0deg)"
        }
    },
    virtualizedTreeSwitcherClose: {
        "& > svg, img": {
            transform: "rotate(-90deg)"
        }
    }
});

export default useStyle;
