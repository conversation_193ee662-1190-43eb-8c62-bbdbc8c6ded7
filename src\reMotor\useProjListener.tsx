import {useEffect, useRef} from "react";
import MotorContext from "./context";
import {ProjectListener} from "./interface";

const useMotorProjListener = (handler: ProjectListener["notifyProjectChanged"]) => {
    const handlerRef = useRef<ProjectListener>();

    useEffect(
        () => {
            handlerRef.current = {notifyProjectChanged: handler};
            const listener = handlerRef.current;
            MotorContext.addProjectListener(listener);
            return () => {
                MotorContext.removeProjectListener(listener);
            };
        },
        [handler]
    );
};

export default useMotorProjListener;
