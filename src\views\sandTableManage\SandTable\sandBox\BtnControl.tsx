import React, {useCallback, useState, useEffect} from "react";
import {Button, Space, Tooltip, Popover, Form, Radio, Checkbox, message} from "antd";
import moment, {Moment} from "moment";
import {useSelector, useDispatch} from "react-redux";
import Icon from "@ant-design/icons";
import BtnIcon from "../../../../components/MyIcon/BtnIcon";
import useStyle from "../style";
import {RootState} from "../../../../store/rootReducer";
import MemberExhibitor from "../../../../components/MemberExhibitor";
import {setPlayDimension, setPlayPattern, setPlayStatus, setSandTableType, setShowTodayStatus} from "../../../../store/sandManage/action";
import {sandTableFullScreenDomId} from "../helps";
import {CameraSpecAngle, MotorContext} from "../../../../reMotor";
import {CheckedInfoType} from "./index.d";
import {isNotNullOrUndefined} from "../../../../assets/ts/utils";
import {frameMsgCreator} from "../modelView/MotorFrame";
import {ExitFullscreen} from "../../../../assets/icons";
import ModelPlayMgr from "./handler/ModelPlayMgr";
import useFullScreenChange from "../../../../hooks/useFullScreenChange";
import PromiseQueue from "../../../../assets/ts/PromiseQueue";
import {SandDataInitializeBase} from "./handler/dataOrInterface";

interface SettingFormType {
    playbackMode: string[];
    sandTableType: string;
    playDimension: string;
}

interface AngleItem {
    key: CameraSpecAngle;
    icon: string;
}

const viewAngleList: AngleItem[] = [
    {key: CameraSpecAngle.Up, icon: "icon-fushitu"},
    {key: CameraSpecAngle.Down, icon: "icon-yangshitu"},
    {key: CameraSpecAngle.Left, icon: "icon-zuoshitu"},
    {key: CameraSpecAngle.Right, icon: "icon-youshitu"},
    {key: CameraSpecAngle.Front, icon: "icon-zhengshitu"},
    {key: CameraSpecAngle.Behind, icon: "icon-houshitu"},
    {key: CameraSpecAngle.LeftDown, icon: "icon-xinanshitu"},
    {key: CameraSpecAngle.RightDown, icon: "icon-dongnanshitu1"},
    {key: CameraSpecAngle.RgihtUp, icon: "icon-dongbeishitu"},
    {key: CameraSpecAngle.LeftUp, icon: "icon-xibeishitu"},
];

// eslint-disable-next-line max-lines-per-function
const SandBoxBtnControl = (props: {getFormValues: (values: SettingFormType) => void;
    isRenderContrast: boolean;
    checkedInfo?: CheckedInfoType;}) => {
    const cls = useStyle();
    const {getFormValues, isRenderContrast, checkedInfo} = props;
    const [form] = Form.useForm();
    const dispatch = useDispatch();

    const {curSandTable, motorFrame, motorFrameInitStatus} = useSelector((state: RootState) => state.statusData);
    const {playDimension,
        playStatus,
        playPattern, sandTableType, showTodayStatus} = useSelector((state: RootState) => state.sandManageData);
    const {analyzedData, constructionAnalyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const [dateRangeInfo, setDateRangeInfo] = useState<Moment[]>([]);
    const [progress, setProgress] = React.useState<number>(0);
    const [curDate, setCurDate] = React.useState<Moment>();
    const refSandBoxHandler = React.useRef<ModelPlayMgr>(new ModelPlayMgr());
    const refPlayPromiseQueue = React.useRef<PromiseQueue>(new PromiseQueue({intervalNumber: 500, isPopMode: true}));
    const [playSettingChecked, setPlaySettingChecked] = React.useState(false);
    const [viewSettingChecked, setViewSettingChecked] = React.useState(false);
    const {isTargetFullScreen} = useFullScreenChange(sandTableFullScreenDomId);
    const [isDataPrepareReady, setDataPrepareReady] = useState(false);

    const getAnalysedData = React.useCallback((): SandDataInitializeBase | undefined => {
        if (sandTableType === "underConstruction") {
            return constructionAnalyzedData.handler;
        }
        return analyzedData.handler;
    }, [analyzedData, constructionAnalyzedData, sandTableType]);

    const isAnalysedDataReady = React.useCallback((): boolean => {
        if (sandTableType === "underConstruction") {
            return constructionAnalyzedData.isReady;
        }
        return analyzedData.isReady;
    }, [analyzedData, constructionAnalyzedData, sandTableType]);

    const setDateRangeImpl = useCallback(() => {
        const analysedHandler = getAnalysedData();
        const isReady = isAnalysedDataReady();
        if (isReady && isNotNullOrUndefined(analysedHandler)) {
            setDateRangeInfo(analysedHandler.getPlaybackStartEndTimes(playPattern));
        } else {
            setDateRangeInfo([]);
        }
    }, [getAnalysedData, isAnalysedDataReady, playPattern]);

    useEffect(() => {
        setDateRangeImpl();
    }, [setDateRangeImpl]);

    useEffect(() => () => {
        refSandBoxHandler.current.unit();
    }, []);

    useEffect(() => {
        refSandBoxHandler.current.setChildApi(motorFrame);
    }, [motorFrame]);

    useEffect(() => {
        const analysedHandler = getAnalysedData();
        const isReady = isAnalysedDataReady();

        if (isNotNullOrUndefined(checkedInfo)
        && analysedHandler !== undefined
        && isReady) {
            const {ebsList, workProcessList, defaultStatus, warningList} = checkedInfo;
            if (isNotNullOrUndefined(ebsList)) {
                analysedHandler.setFilterInfo("ebs", {
                    isCheckAll: ebsList.isCheckAll,
                    setCheckedKeys: new Set(ebsList.checkedKeys as string[])
                });
            }
            if (isNotNullOrUndefined(workProcessList)) {
                analysedHandler.setFilterInfo("process", {
                    isCheckAll: workProcessList.isCheckAll,
                    setCheckedKeys: new Set(workProcessList.checkedKeys as string[])
                });
            }
            if (isNotNullOrUndefined(defaultStatus)) {
                analysedHandler.setFilterInfo("defaultStatus", {
                    isCheckAll: defaultStatus.isCheckAll,
                    setCheckedKeys: new Set(defaultStatus.checkedKeys as string[])
                });
            }
            if (isNotNullOrUndefined(warningList)) {
                analysedHandler.setFilterInfo("warn", {
                    isCheckAll: warningList.isCheckAll,
                    setCheckedKeys: new Set(warningList.checkedKeys as string[])
                });
            }
        }
    }, [checkedInfo, getAnalysedData, isAnalysedDataReady]);

    const handleFormValueChange = useCallback((changedValues: SettingFormType, values: SettingFormType) => {
        const analysedData = getAnalysedData();
        const isReady = isAnalysedDataReady();

        getFormValues(values);
        if (changedValues.playbackMode !== undefined) {
            if (changedValues.playbackMode.length === 0) {
                message.warning("播放模式不可为空！");
                return;
            }

            if (isReady && isNotNullOrUndefined(analysedData)) {
                setDateRangeInfo(analysedData.getPlaybackStartEndTimes(changedValues.playbackMode));
            } else {
                setDateRangeInfo([]);
            }
            dispatch(setPlayPattern(changedValues.playbackMode));
        } else if (changedValues.playDimension !== undefined) {
            dispatch(setPlayDimension(changedValues.playDimension));
        } else if (changedValues.sandTableType !== undefined) {
            dispatch(setSandTableType(changedValues.sandTableType === "underConstruction" ? "underConstruction" : "custom"));
        }
        setDataPrepareReady(false);
        dispatch(setShowTodayStatus(true));
    }, [dispatch, getAnalysedData, getFormValues, isAnalysedDataReady]);

    const handleViewSetting = useCallback((viewInfo: AngleItem) => {
        MotorContext.setViewPort(viewInfo.key, undefined, undefined, 1);
        if (motorFrame === null) {
            return;
        }
        const frameParam = frameMsgCreator({
            name: "onViewPortChange",
            params: {angle: viewInfo.key, durationTime: 1}
        });
        motorFrame.call(frameParam.name, frameParam);
    }, [motorFrame]);

    const handlePlaySetting = useCallback(() => {
        form.setFieldsValue({
            playDimension,
            sandTableType: sandTableType ?? "underConstruction",
            playbackMode: playPattern ?? ["actual"]
        });
    }, [form, playDimension, playPattern, sandTableType]);

    const getPopupContainer = useCallback(
        () => {
            const target = document.getElementById(sandTableFullScreenDomId);
            return target ?? document.documentElement;
        },
        []
    );

    const handleFullScreen = useCallback(() => {
        const target = document.getElementById(sandTableFullScreenDomId);
        if (target === null) {
            return;
        }
        if (document.fullscreenElement === null) {
            target.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
        // dispatch(setBimFullScreen(document.fullscreenElement === null));
    }, []);

    const clickReset = useCallback(
        () => {
            setProgress(0);
            setCurDate(dateRangeInfo.length > 1 ? dateRangeInfo[0] : undefined);
            refSandBoxHandler.current.reset();
        },
        [dateRangeInfo]
    );

    const refreshPlay = useCallback(() => {
        const analysedHandler = getAnalysedData();
        const isReady = isAnalysedDataReady();
        let playingStatus = false;
        if (curSandTable !== null && isNotNullOrUndefined(analysedHandler) && isReady) {
            if (dateRangeInfo.length === 2) {
                if (playPattern.length === 2) {
                    if (motorFrameInitStatus && motorFrame !== null) {
                        refSandBoxHandler.current
                            .init(playDimension,
                                playPattern,
                                sandTableType,
                                analysedHandler,
                                motorFrame);
                    }
                } else {
                    refSandBoxHandler.current
                        .init(playDimension,
                            playPattern,
                            sandTableType,
                            analysedHandler,
                            motorFrame);
                }

                if (playPattern.length === 1
                    || (playPattern.length === 2 && motorFrameInitStatus && motorFrame !== null)) {
                    setDataPrepareReady(true);
                    playingStatus = true;
                }
            }
        }

        if (!playingStatus) {
            clickReset();
        }
    }, [
        getAnalysedData,
        isAnalysedDataReady,
        clickReset,
        curSandTable,
        dateRangeInfo.length,
        playPattern,
        motorFrameInitStatus,
        motorFrame,
        playDimension,
        sandTableType
    ]);

    useEffect(() => {
        if (showTodayStatus && isDataPrepareReady) {
            if (dateRangeInfo.length === 2) {
                const dateEnd = dateRangeInfo[1].startOf("day");
                const todayTime = moment().startOf("day");
                setCurDate(todayTime < dateEnd ? todayTime : dateEnd);
                setProgress(100);
                dispatch(setPlayStatus("paused"));
                setDataPrepareReady(false);
                dispatch(setShowTodayStatus(false));
            }
        }
    }, [dateRangeInfo, dispatch, isDataPrepareReady, playStatus, showTodayStatus]);

    useEffect(() => {
        refreshPlay();
    }, [refreshPlay]);

    useEffect(() => {
        const analysedHandler = getAnalysedData();
        const isReady = isAnalysedDataReady();

        if (curSandTable !== null && analysedHandler !== undefined && isReady) {
            if (curDate !== undefined && dateRangeInfo.length === 2 && playStatus !== "idle") {
                if (dateRangeInfo[0].isSame(dateRangeInfo[1], "day") || curDate.isSame(dateRangeInfo[1], "day")) {
                    setProgress(100);
                } else {
                    const progressCal = curDate.diff(dateRangeInfo[0], "seconds", true) / dateRangeInfo[1].diff(dateRangeInfo[0], "seconds", true);
                    setProgress(progressCal * 100);
                }
                const dateTrans = curDate.startOf("day")?.toDate();
                refPlayPromiseQueue.current.push(() => {
                    refSandBoxHandler.current.showSandBoxByDate(dateTrans);
                });
            }
        }
    }, [curDate, curSandTable, dateRangeInfo, getAnalysedData, isAnalysedDataReady, playStatus]);

    const handleResetBIMViewport = useCallback(
        () => {
            MotorContext.resetViewport();
            if (motorFrame === null) {
                return;
            }
            const frameParam = frameMsgCreator({
                name: "onResetViewPort",
                params: ""
            });
            motorFrame.call(frameParam.name, frameParam);
        },
        [motorFrame]
    );

    return (
        <>
            <Space className={cls.btnSpace} direction="vertical" size={16}>
                <Tooltip title="视图设置" placement="left" getPopupContainer={getPopupContainer}>
                    <Popover
                        placement="right"
                        trigger="click"
                        onVisibleChange={(visible) => setViewSettingChecked(visible)}
                        visible={viewSettingChecked}
                        overlayClassName={cls.viewSettingWrapper}
                        content={(
                            <Space size={2}>
                                <Button
                                    icon={<BtnIcon type="icon-shouye" style={{fontSize: 18}} />}
                                    onClick={handleResetBIMViewport}
                                />
                                {viewAngleList.map((item) => (
                                    <Button
                                        icon={<BtnIcon type={item.icon} style={{fontSize: 18}} />}
                                        onClick={() => handleViewSetting(item)}
                                        key={item.key}
                                    />
                                ))}
                            </Space>
                        )}
                        getPopupContainer={getPopupContainer}
                    >
                        <Button icon={<BtnIcon type="icon-moxingshijiao" />} type={viewSettingChecked ? "primary" : "default"} />
                    </Popover>
                </Tooltip>
                <Tooltip title="播放设置" placement="left" getPopupContainer={getPopupContainer}>
                    <Popover
                        placement="rightTop"
                        trigger="click"
                        title="播放设置"
                        overlayClassName={cls.playSettingWrapper}
                        getPopupContainer={getPopupContainer}
                        onVisibleChange={(visible) => setPlaySettingChecked(visible)}
                        visible={playSettingChecked}
                        content={(
                            <Form
                                form={form}
                                labelCol={{span: 7}}
                                wrapperCol={{span: 17}}
                                onValuesChange={handleFormValueChange}
                            >
                                {/* <Form.Item
                                    labelAlign="left"
                                    colon={false}
                                    name="playDimension"
                                    label="播放维度"
                                >
                                    <Radio.Group>
                                        <Radio value="procedure">工序级</Radio>
                                        <Radio value="default">构件级</Radio>
                                        <Radio value="wbs">WBS</Radio>
                                    </Radio.Group>
                                </Form.Item> */}
                                <Form.Item
                                    labelAlign="left"
                                    colon={false}
                                    name="sandTableType"
                                    label="沙盘类型"
                                >
                                    <Radio.Group>
                                        <Radio value="underConstruction">施工沙盘</Radio>
                                        <Radio value="custom">自定义沙盘</Radio>
                                    </Radio.Group>
                                </Form.Item>
                                <Form.Item
                                    labelAlign="left"
                                    colon={false}
                                    name="playbackMode"
                                    label="播放模式"
                                >
                                    <Checkbox.Group
                                        options={[{value: "actual", label: "实际时间"}, {value: "plan", label: "计划时间"}]}
                                    />
                                </Form.Item>
                            </Form>
                        )}
                    >
                        <Button
                            icon={<BtnIcon type="icon-bofangshezhi" />}
                            onClick={handlePlaySetting}
                            type={playSettingChecked ? "primary" : "default"}
                        />
                    </Popover>
                </Tooltip>
                <Tooltip title={isTargetFullScreen ? "取消全屏" : "全屏"} placement="left" getPopupContainer={getPopupContainer}>
                    <Button
                        icon={isTargetFullScreen ? <Icon component={ExitFullscreen} /> : <BtnIcon type="icon-quanping" />}
                        onClick={handleFullScreen}
                    />
                </Tooltip>
            </Space>
            <MemberExhibitor
                minDate={dateRangeInfo[0]}
                maxDate={dateRangeInfo[1]}
                progress={progress}
                curDate={curDate}
                setCurDate={setCurDate}
                clickReset={clickReset}
                isRenderContrast={isRenderContrast}
                hasData={sandTableType === "underConstruction" ? constructionAnalyzedData.handler?.hasSandBoxData() : analyzedData.handler?.hasSandBoxData()}
            />
        </>
    );
};

export default SandBoxBtnControl;
