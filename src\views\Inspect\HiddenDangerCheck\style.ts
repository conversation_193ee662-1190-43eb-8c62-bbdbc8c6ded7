import {createUseStyles} from "react-jss";
import Color from "../../../assets/css/Color";

const useStyles = createUseStyles({
    box: {
        position: "relative",
    },
    unfoldIcon: {
        display: "inline-block",
        width: 20,
        height: 20,
        borderRadius: "0 10px 10px 0",
        position: "absolute",
        top: 18,
        left: 0,
        backgroundColor: Color["bg-4"],
        cursor: "pointer",
        zIndex: 1
    },
    foldIcon: {
        display: "inline-block",
        width: 20,
        height: 20,
        borderRadius: "10px 0 0 10px ",
        position: "absolute",
        top: 18,
        left: 238,
        backgroundColor: Color["bg-4"],
        cursor: "pointer",
        zIndex: 1
    }
});

export default useStyles;
