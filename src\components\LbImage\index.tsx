import React, {FC, memo, useCallback, useEffect, useState} from "react";

export interface LbImageProps {
    src: string;
    defaultImg: string;
    style: {};
}

const LbImage: FC<LbImageProps> = memo((props) => {
    const [imgProps, setImgProps] = useState<LbImageProps>(props);
    const errorFunc = useCallback(() => {
        const {...temp} = imgProps;
        temp.src = imgProps.defaultImg;
        setImgProps(temp);
    }, [imgProps]);

    useEffect(() => {
        setImgProps(props);
    }, [props]);
    return (
        <img
            style={imgProps.style}
            src={imgProps.src}
            onError={errorFunc}
        />
    );
});

export default LbImage;
