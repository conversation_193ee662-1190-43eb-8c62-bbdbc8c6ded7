import React from "react";
import {IconFontProps} from "@ant-design/icons/lib/components/IconFont";
import {Icon} from "../MyIcon";

interface MyIconFontProps extends IconFontProps {
    fontSize?: string | number;
}

const MyIconFont = (props: MyIconFontProps) => {
    const {fontSize, style, ...otherProps} = props;

    return <Icon {...otherProps} style={{fontSize, ...style}} />;
};

export default MyIconFont;
