import {Tree, TreeProps} from "antd";
import React, {useEffect, useRef, useState} from "react";

interface ComTreeProps extends TreeProps {
    flex?: boolean;
}

const ComTree = (props: ComTreeProps) => {
    const {flex = false, ...other} = props;
    const treeBoxRef = useRef<HTMLDivElement>(null);
    const [treeHeight, setTreeHeight] = useState<number>(0);

    useEffect(() => {
        if (treeBoxRef.current !== null) {
            setTreeHeight(treeBoxRef.current.offsetHeight);
        }
    }, []);

    const divStyle = () => {
        if (flex) {
            return {flexGrow: 1, height: 0};
        }
        return {height: "100%"};
    };

    return (
        <div ref={treeBoxRef} style={divStyle()}>
            {treeHeight !== 0 && (
                <Tree
                    height={treeHeight}
                    {...other}
                />
            )}
        </div>
    );
};

export default ComTree;
