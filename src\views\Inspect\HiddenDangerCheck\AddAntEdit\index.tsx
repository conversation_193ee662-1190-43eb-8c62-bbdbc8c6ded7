/* eslint-disable max-len */
/* eslint-disable max-nested-callbacks */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-lines-per-function */
import {Button, Collapse, Form, Space, Row, Input, Col, message, Radio, DatePicker, Cascader, Select} from "antd";
import {useForm} from "antd/lib/form/Form";
import moment, {Moment} from "moment";
import React, {Key, useCallback, useEffect, useMemo, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import TextArea from "antd/lib/input/TextArea";
import {isEmpty} from "lodash-es";
import {FileType, WebRes} from "../../../../api/common.type";
import {getHiddenDangerCheckDetail, addHiddenDangerCheck, updateHiddenDangerCheck} from "../../../../api/hiddenDangerCheck";
import ComCollapsePanel from "../../../../components/ComCollapsePanel";
import FileBox, {FileBoxProps} from "../../../../components/FileBox";
import {RootState} from "../../../../store/rootReducer";
import useStyles from "./style";
import {postCheckListSatuteGist} from "../../../../api/center";
import BackIcon from "../../../../components/MyIcon/BackIcon";
import {buildTypeList, businessType} from "../data";
import {getDangerControlDetails} from "../../../../api/danger";
import {ColConfig, formColCss, formColStyle, setFormNodeInfoByNodeId} from "../../../../assets/ts/form";
// import {setLoading} from "../../../../store/common/action";
import confirmLeave from "../../../../assets/ts/confirmLeave";
import useEditedStatus from "../../../../assets/hooks/useEditedStatus";
import DrawerDangerSelect from "../../../../components/DrawerDangerSelect";
import {DangerListType} from "../../../../api/danger/type";
import FormProjectCategory from "../../../../components/CheckSubOption/FormProjectCategory";
import FormCheckSubOption from "../../../../components/CheckSubOption/FormCheckSubOption";
import {CheckSubOptionValueType} from "../../../../components/CheckSubOption/MultipleChoice";
import {groupBy, isObjHasKey} from "../../../../assets/ts/utils";
import SelectPerson from "../../../../components/SelectPerson";
// import RegulatoryBasisView from "../../../../components/RegulatoryBasisView";
import {EBSNodeType, WbsEbsNodeType, handleBindWbsEbs, handleGetWbsEbs2} from "../../../../components/FormWbsEbs/data";
import FormWbsEbs from "../../../../components/FormWbsEbs";
import {getPreparationList} from "../../../../api/Preparation";
import {planTypeList} from "../../../../api/planApproval/type";
import FormSelectProject from "../../../../components/FormSelectProject";
import {SectionListType} from "../../../../store/common/actionTypes";
import PreviewModal from "../../../../components/PreviewModal";

interface Option {
    value: string | number;
    label: string;
    children?: Option[];
}

interface AddAndEditProp {
    type: string;
    id?: string;
    back: (type?: string) => void;
    handleClickZG: (param: any) => void;
    checkType?: number;
    dangerSourceId?: string;
}

const layout = {
    labelCol: {span: 6},
    wrapperCol: {span: 18}
};

export interface AddFormType {
    subOptionContent: any;
    dangerSourceId: string | undefined;
    deptId: string;
    deptName: string;
    nodeId: string;
    checkNodeId: string;
    checkPlanId: [string, string];
    number: string;
    buildType: string;
    // checkUser: {
    //     checkUserName: string;
    //     checkUserId: string;
    // };
    checkUser: any;
    checkType: string;
    optionList: CheckSubOptionValueType[];
    position: string;
    wbsNodeIds?: any;
    wbsNodeNames?: string;
    checkTime: Moment | string;
    // checkResult: string;
    dangerLevel: string;
    checkDesc: any;
    // lawBasis: any;
    moduleType: string;
    attachmentFiles: FileType[];
    photoFiles: FileType[];
    id?: string;
    wbsNodes: any[];
    wbsEbs: WbsEbsNodeType;
    bimBinds: EBSNodeType[];
    bindType: number;
    nodeInfo: SectionListType;
}

const getNodeInfo = (secList: SectionListType[], curSec: SectionListType | null) => {
    const newList = secList.filter((v) => v.isAll !== true);
    const hasAuth = newList.find((v) => v.nodeId === curSec?.nodeId) !== undefined;
    if (hasAuth) {
        return curSec;
    }
    return newList[0] ?? null;
};

const AddAndEdit = (props: AddAndEditProp) => {
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {curSectionInfo, userInfo, sectionList} = useSelector((state: RootState) => state.commonData);
    const cls = useStyles();
    const formCls = formColCss();
    const [form] = useForm();
    const {id, back, type, handleClickZG, dangerSourceId: _dangerSourceId, checkType: _checkType = 0} = props;

    // 是否编辑过,编辑过退出提示
    const [isEdited, setIsEdited] = useState<boolean>(false);
    useEditedStatus(isEdited, back);
    // 附件列表
    const [enclosureList, setEnclosureList] = useState<FileType[]>([]);
    // 影像列表
    const [imageList, setImageList] = useState<FileType[]>([]);
    // const [resData, setResData] = useState<GetDisclosureDetailReturn>();
    // const [curSectionList, setCurSectionList] = useState<LabeledValue[]>(
    //     curSectionInfo !== null && Boolean(curSectionInfo.nodeId)
    //         ? [{label: curSectionInfo.nodeName, value: curSectionInfo.nodeId}]
    //         : []
    // ); // 当前过滤出来的标段
    const [radioChecked, setRadioChecked] = useState<boolean>(true);
    const {drawerCheckedNodes} = useSelector((state: RootState) => state.wbsTree);
    const [selectDangerSource, setSelectDangerSource] = useState<DangerListType>();
    const dispatch = useDispatch();

    const [showDangerLevel] = React.useState<boolean>(true);
    const [showDangerSourceId, setShowDangerSourceId] = React.useState<boolean>(_dangerSourceId !== undefined);
    const [currentCheckType, setCurrentCheckType] = React.useState<number>(_checkType ?? -1);
    const [showDangerDrawer, setShowDangerDrawer] = React.useState<boolean>(false);
    // 检查人的三方
    // const [, setCheckUserBuildTypeArr] = useState<string[]>();
    // 检查人的标段列表
    // const [, setCheckUserSectionIds] = useState<string[]>();
    // 是否来自巡检模块,巡检模块额外有一些字段不允许编辑
    const [isPatrol, setIsPatrol] = useState<boolean>(false);
    // 选中的检查分项Id
    // const [selectOptionList, setSelectOptionList] = useState<string[]>([]);

    // 被检查计划列表
    const [planList, setPlanList] = useState<Option[]>([]);
    const [formSectionInfo, setFormSectionInfo] = useState<SectionListType | null>(curSectionInfo);

    const getPreparationData = useCallback(
        (nodeParam?: SectionListType) => {
            getPreparationList({
                deptId: orgInfo.orgId,
                nodeId: nodeParam?.nodeId ?? curSectionInfo?.id,
                nodeType: nodeParam?.nodeType ?? curSectionInfo?.nodeType,
                pageNum: 1,
                pageSize: 9999
            }).then((res) => {
                const planGroup = groupBy(res.data?.items ?? [], (node) => node.type);
                const tempPlanList: Option[] = planGroup.map((v) => {
                    const label = planTypeList.find((p) => p.value === v[0])?.label ?? "";
                    return {
                        value: v[0],
                        label,
                        children: v[1].map((j) => ({value: j.id, label: j.name}))
                    };
                });
                setPlanList(tempPlanList);
            });
        },
        [curSectionInfo, orgInfo.orgId]
    );

    useEffect(
        () => {
            getPreparationData();
        },
        [getPreparationData]
    );

    /* useEffect(() => {
        if (orgInfo.deptDataType === 1) {
            form.setFieldsValue({checkNodeId: orgInfo.orgName});
        } else {
            getDeptOrgList(orgInfo.orgId ?? "").then(
                (res) => {
                    // 1、标段：下拉选择监理和施工标段；建设方：可下拉选择所有的监理和施工标；监理方：可下拉选择所在监理标和下属的施工标；施工方：可下拉选择所在施工标。
                    const dataList = res.data ?? [];
                    if (curSectionInfo?.classification === 0) {
                        // 施工方
                        const newSectionList = [{label: curSectionInfo?.name ?? "", value: curSectionInfo?.id ?? ""}];
                        setCurSectionList(newSectionList);
                    } else if (curSectionInfo?.classification === 1) {
                        // 监理方
                        const newSectionList = dataList.filter((item) => item.parentId === curSectionInfo?.id)
                            .map((item) => ({label: item.nodeName, value: item.nodeId}));
                        newSectionList.unshift({label: curSectionInfo?.name ?? "", value: curSectionInfo?.id ?? ""});
                        setCurSectionList(newSectionList);
                    } else if (curSectionInfo?.classification === 3) {
                        // 建设方
                        const newSectionList = dataList.map((item) => ({label: item.nodeName, value: item.nodeId}));
                        setCurSectionList(newSectionList);
                    }
                }
            );
        }
    }, [curSectionInfo, form, orgInfo]); */

    useEffect(() => {
        if (_dangerSourceId !== undefined) {
            getDangerControlDetails(_dangerSourceId)
                .then((res: any) => {
                    setSelectDangerSource({id: res.data.id, name: res.data.name} as DangerListType);
                    handleGetWbsEbs2({form, businessId: _dangerSourceId});
                    form.setFieldsValue({dangerSource: res.data.name});
                });
        }
        if (id !== undefined) {
            getHiddenDangerCheckDetail(id).then((res) => {
                if (res.success) {
                    const {
                        deptId,
                        checkNodeId,
                        // checkNodeName,
                        checkPlanId,
                        checkPlanType,
                        number,
                        buildType,
                        checkUser,
                        checkType,
                        position,
                        checkTime,
                        // checkResult,
                        dangerLevel,
                        dangerSourceId,
                        dangerSourceName,
                        moduleType,
                        patrolInspectionTaskId,
                        optionList
                    } = res.data;
                    setIsPatrol(Boolean(patrolInspectionTaskId) === true);
                    form.setFieldsValue({
                        deptId,
                        number,
                        buildType,
                        checkUser: [{userName: checkUser.checkUserId}],
                        checkType,
                        checkPlanId: [checkPlanType, checkPlanId],
                        position,
                        checkTime: moment(checkTime),
                        // checkResult,
                        dangerLevel,
                        checkDesc: res.data.checkDesc,
                        // lawBasis: res.data.lawBasis,
                        moduleType,
                        dangerSource: dangerSourceName,
                        optionList,
                        projectCategoryId: res.data.projectCategoryId
                    });
                    /* if (checkNodeId?.length > 0) {
                        form.setFieldsValue({checkNodeId});
                        setCurSectionList((list) => (list?.length > 0 ? list : [{value: checkNodeId, label: checkNodeName}]));
                    } */
                    setFormNodeInfoByNodeId(checkNodeId, form);
                    // setShowDangerLevel(checkResult === 2);
                    setShowDangerSourceId(checkType === 9);
                    setCurrentCheckType(checkType);
                    setSelectDangerSource({id: dangerSourceId, name: dangerSourceName} as DangerListType);
                    setEnclosureList(res.data.attachmentFiles);
                    setImageList(res.data.photoFiles);
                    // setSelectOptionList(type === "SECURITY" ? optionList.map((el) => el.checkDescId) : optionList.map((el) => el.subOptionId));
                    // setRadioChecked(checkResult === 2);
                }
            });
            handleGetWbsEbs2({form, businessId: id});
        }
    }, [_dangerSourceId, dispatch, form, id, type]);

    const handleBack = () => {
        if (isEdited) {
            confirmLeave(back);
        } else {
            back();
        }
    };


    /* useEffect(() => {
        const arr: string[] = [];
        buildTypeList.forEach((item) => {
            if (item.value === curSectionInfo?.classification) {
                switch (Number(item.value)) {
                    case 0: {
                        arr.push("0");
                        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
                        setCheckUserSectionIds(curSectionInfo?.id !== "" ? [curSectionInfo?.id as string] : undefined);
                        break;
                    }
                    case 1: {
                        arr.push("1");
                        setCheckUserSectionIds(curSectionInfo?.id !== "" ? [curSectionInfo?.id] : undefined);
                        break;
                    }
                    case 3: {
                        arr.push("3");
                        break;
                    }
                    default:
                        break;
                }
            }
        });
        setCheckUserBuildTypeArr(arr);
    }, [curSectionInfo]); */

    const handleEnclosureListChange: FileBoxProps["onChange"] = (val) => {
        setEnclosureList(val);
        setIsEdited(true);
    };

    const handleImageListChange: FileBoxProps["onChange"] = (val) => {
        setImageList(val);
        setIsEdited(true);
    };

    const handleDealLawBasis = (selectedKeys: Key[]) => {
        postCheckListSatuteGist({ids: selectedKeys})
            .then((res) => {
                const tempResData = Array.isArray(res.result) ? res.result.filter((el) => Boolean(el.content) === true) : [];
                form.setFieldsValue({lawBasis: tempResData.map((el) => el.content).join(";").slice(0, 1000)});
            });
    };

    const handleRadioCheck = () => {
        setRadioChecked(!radioChecked);
    };


    const handleSave = () => {
        form.submit();
    };


    const handleFinish = (valParam: AddFormType) => {
        const {checkUser, checkPlanId, nodeInfo, ...val} = valParam;
        const wbsNodes: {wbsNodeId: string; wbsNodeName: string}[] = [];
        drawerCheckedNodes.forEach((item: any) => {
            const obj = {
                wbsNodeId: item.key,
                wbsNodeName: item.title,
            };
            wbsNodes.push(obj);
        });

        const param = {
            ...val,
            checkNodeId: nodeInfo.nodeId,
            nodeId: nodeInfo.nodeId,
            nodeType: nodeInfo.nodeType,
            deptId: orgInfo.orgId,
            moduleType: type,
            dangerSourceId: selectDangerSource?.id,
            attachmentFiles: enclosureList,
            photoFiles: imageList,
            checkTime: moment(val.checkTime).format("x"),
            checkUser: {checkUserId: checkUser[0].userName, checkUserName: checkUser[0].userName},
            bindType: val.wbsEbs === undefined ? 0 : val.wbsEbs.bindType,
            checkPlanId: checkPlanId[1],
            checkPlanType: checkPlanId[0]
        };
        delete param.wbsNodeNames;

        // dispatch(setLoading(true));
        if (id === undefined) {
            addHiddenDangerCheck(param).then((res) => {
                // dispatch(setLoading(false));
                if (res.success) {
                    handleBindWbsEbs(
                        {
                            ...val.wbsEbs,
                            businessType,
                            businessId: res.data,
                            deptId: orgInfo.orgId,
                            nodeId: curSectionInfo?.id,
                        },
                        () => {
                            setIsEdited(false);
                            message.success("操作成功");
                            if (radioChecked) {
                                handleClickZG({id: res.data, buildType: val.buildType, checkType: val.checkType});
                            } else {
                                back("ok");
                            }
                        }
                    );
                }
            });
        } else {
            param.id = id;
            updateHiddenDangerCheck(param).then((res: WebRes) => {
                // dispatch(setLoading(false));
                if (res.success) {
                    handleBindWbsEbs(
                        {
                            ...val.wbsEbs,
                            businessType,
                            businessId: id,
                            deptId: orgInfo.orgId,
                            nodeId: curSectionInfo?.id,
                        },
                        () => {
                            setIsEdited(false);
                            message.success("操作成功");
                            if (radioChecked) {
                                handleClickZG({id, buildType: val.buildType, checkType: val.checkType});
                            } else {
                                back("ok");
                            }
                        }
                    );
                }
            });
        }
    };
    const onFormValueChange = (val: AddFormType) => {
        if (isObjHasKey(val, "optionList")) {
            form.setFieldsValue({checkDesc: val.optionList.map((el) => el.content).join(",").slice(0, 1000)});
            const ids = val.optionList.map((el) => el.checkDescId);
            handleDealLawBasis(ids);
            // setSelectOptionList(type === "SECURITY" ? ids : val.optionList.map((el) => el.subOptionId));
        }
        if (isObjHasKey(val, "projectCategoryId")) {
            form.resetFields(["optionList"]);
        }
        if (isObjHasKey(val, "nodeInfo")) {
            form.setFieldsValue({checkPlanId: undefined, checkUser: undefined});
            getPreparationData(val.nodeInfo);
            setFormSectionInfo(val.nodeInfo);
        }
        // if (isObjHasKey(val, "checkResult")) {
        //     setRadioChecked(Number(val.checkResult) === 2);
        //     setShowDangerLevel(form.getFieldValue("checkResult") === 2);
        // }
        // if (isObjHasKey(val, "checkUser")) {

        // }
        // if (isObjHasKey(val, "buildType")) {
        //     form.resetFields(["checkUser"]);
        //     setCheckUserBuildTypeArr([val.buildType]);
        //     // 建设方检查
        //     if (Number(val.buildType) === 3) {
        //         setCheckUserSectionIds(undefined);
        //         return;
        //     }
        //     const checkSectionInfo = sectionList.find((el) => el.nodeId === form.getFieldValue("checkNodeId"));
        //     if (checkSectionInfo === undefined) {
        //         setCheckUserSectionIds(checkSectionInfo);
        //         return;
        //     }
        //     // 施工方自查
        //     if (Number(val.buildType) === 0) {
        //         setCheckUserSectionIds([checkSectionInfo.nodeId]);
        //     }
        //     // 监理检查
        //     if (Number(val.buildType) === 1) {
        //         setCheckUserSectionIds(checkSectionInfo.parentId === null ? undefined : [checkSectionInfo.parentId]);
        //     }
        // }
        setIsEdited(true);
        setShowDangerSourceId(form.getFieldValue("checkType") === 9);
    };

    useEffect(() => {
        form.setFieldsValue({wbsNodeNames: drawerCheckedNodes.map((el) => el.title).join(",")});
    }, [drawerCheckedNodes, form]);

    useEffect(() => {
        form.setFieldsValue({dangerSource: selectDangerSource?.name});
    }, [selectDangerSource, form]);

    const checkTypeOptions = useMemo(() => {
        const options = [
            {value: 0, label: "日常检查"},
            {value: 1, label: "专项检查"},
            {value: 2, label: "不定期检查"},
            {value: 3, label: "节假日检查"},
            {value: 4, label: "综合性检查"},
            {value: 5, label: "其他"},
        ];
        if ([6, 7, 8, 9].includes(currentCheckType)) {
            // 当值为这四个选项时，选择框禁用，仅用于显示
            options.push({value: 6, label: "按天检查"});
            options.push({value: 7, label: "按周检查"});
            options.push({value: 8, label: "按月检查"});
            options.push({value: 9, label: "风险检查"});
        }
        return options;
    }, [currentCheckType]);

    const getPopupContainer = useCallback(
        (triggerNode: HTMLElement) => (triggerNode.parentNode ?? document.documentElement) as HTMLElement,
        []
    );

    return (
        <div className={`${cls.box} box`}>
            <div className={cls.head}>
                <Space>
                    <BackIcon onClick={handleBack} />
                    <div className="title">{id === undefined ? "新增进度检查" : "编辑进度检查"}</div>
                </Space>
            </div>
            <div className={cls.content}>
                <Form
                    {...layout}
                    form={form}
                    onFinish={handleFinish}
                    onValuesChange={onFormValueChange}
                    initialValues={{
                        // deptName: orgInfo.orgName,
                        // checkNodeId: curSectionInfo?.nodeId ?? undefined,
                        nodeInfo: getNodeInfo(sectionList, curSectionInfo),
                        // buildType: Number(curSectionInfo?.classification),
                        dangerSourceId: _dangerSourceId ?? selectDangerSource?.name,
                        checkType: _checkType,
                        checkTime: moment(),
                        // checkResult: 2,
                        dangerLevel: 0,
                        checkUser: id === undefined ? [{userName: userInfo.username}] : undefined
                    }}
                >
                    <Collapse ghost expandIconPosition="right" defaultActiveKey={["1"]}>
                        <ComCollapsePanel required header="基本信息" key="1">
                            <Row>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="nodeInfo" label="项目" rules={[{required: true, message: "请选择项目"}]}>
                                        <FormSelectProject />
                                    </Form.Item>
                                </Col>
                                {/* <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="deptName" label="项目名称" rules={[{required: true, message: "请输入项目名称"}]}>
                                        <Input placeholder="请输入项目名称" maxLength={20} disabled />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="checkNodeId" label="被检查标段" rules={[{required: true, message: "请选择被检查标段"}]}>
                                        <Select
                                            options={curSectionList}
                                            placeholder="请选择被检查标段"
                                            disabled={orgInfo.deptDataType === 1 || isPatrol}
                                        />
                                    </Form.Item>
                                </Col> */}
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="number" label="检查编号">
                                        <Input placeholder="请输入检查编号" maxLength={20} />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item
                                        className={formCls.item}
                                        {...formColStyle()}
                                        name="checkPlanId"
                                        label="被检查计划"
                                        rules={[{required: true, message: "请选择被检查计划"}]}
                                    >
                                        <Cascader
                                            dropdownClassName="customAntCascader"
                                            options={planList}
                                            allowClear={false}
                                            getPopupContainer={getPopupContainer}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="buildType" label="检查单位" rules={[{required: true, message: "请选择检查单位"}]}>
                                        <Select
                                            disabled={isPatrol}
                                            options={buildTypeList}
                                            placeholder="请选择检查单位"
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item
                                        className={formCls.item}
                                        {...formColStyle()}
                                        name="checkUser"
                                        label="检查人"
                                        rules={[{required: true, message: "请选择检查人"}]}
                                    >
                                        {/* <FormSelectPerson
                                            disabled={isPatrol}
                                            init={id === undefined}
                                            form={form}
                                            formKey="checkUser"
                                            personType={3}
                                            sectionIds={checkUserSectionIds}
                                            title="检查人"
                                            buildTypeValue={checkUserBuildTypeArr}
                                            lubanFlag={1}
                                            onlyShowLubanFlag
                                            onlySelectPerson
                                        /> */}
                                        <SelectPerson
                                            nodeId={formSectionInfo?.nodeId}
                                            nodeType={formSectionInfo?.nodeType}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="projectCategoryId" label="工程类型" rules={[{required: true, message: "请选择工程类型"}]}>
                                        <FormProjectCategory form={form} disabled={isPatrol} itemNameKey="projectCategoryId" valueAuto={id === undefined} moduleType="SECURITY" />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="optionList" label="检查分项" rules={[{required: true, message: "请选择检查分项"}]}>
                                        <FormCheckSubOption disabled={isPatrol} form={form} projectCategoryKey="projectCategoryId" moduleType="SECURITY" />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="checkType" label="检查形式" rules={[{required: true, message: "请选择检查形式"}]}>
                                        <Select
                                            disabled={[6, 7, 8, 9].includes(currentCheckType) || isPatrol}
                                            onChange={(val) => {
                                                if (val === 9) {
                                                    form.setFieldsValue({
                                                        dangerSourceId: undefined
                                                    });
                                                }
                                            }}
                                            options={checkTypeOptions}
                                            placeholder="请选择检查形式"
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig} style={{display: showDangerSourceId ? "" : "none"}}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="dangerSource" label="风险名称" rules={[{required: showDangerSourceId, message: "请选择风险名称"}]}>
                                        <Input
                                            disabled={!isEmpty(_dangerSourceId) || currentCheckType === 9}
                                            placeholder="风险名称"
                                            onClick={() => setShowDangerDrawer(true)}
                                            autoComplete="off"
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="position" label="检查部位" rules={[{required: true, type: "string", max: 50}]}>
                                        <Input placeholder="请输入检查部位" disabled={isPatrol} />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig} style={{display: showDangerLevel ? "" : "none"}}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="dangerLevel" label="异常级别" rules={[{required: showDangerLevel, message: "请选择隐患级别"}]}>
                                        <Select
                                            options={[
                                                {value: 0, label: "一般"},
                                                // {value: 1, label: "较大"},
                                                {value: 2, label: "重大"},
                                                {value: 3, label: "特大"},
                                            ]}
                                            placeholder="请选择异常级别"
                                        />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="checkTime" label="检查日期" rules={[{required: true, message: "请选择检查日期"}]}>
                                        <DatePicker placeholder="请选择检查日期" style={{width: "100%"}} format="YYYY.MM.DD" />
                                    </Form.Item>
                                </Col>
                                {/* <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="checkResult" label="检查结果" rules={[{required: true, message: "请选择检查结果"}]}>
                                        <Select
                                            onChange={(val) => {
                                                if (val !== 2) {
                                                    form.setFieldsValue({
                                                        dangerLevel: undefined
                                                    });
                                                } else {
                                                    setRadioChecked(true);
                                                }
                                            }}
                                            options={[
                                                {value: 0, label: "合格"},
                                                {value: 1, label: "口头警告"},
                                                {value: 2, label: "书面整改"},
                                            ]}
                                            placeholder="请选择检查结果"
                                        />
                                    </Form.Item>
                                </Col> */}
                                <Col {...ColConfig}>
                                    <Form.Item className={formCls.item} {...formColStyle()} name="wbsEbs" label="关联WBS/EBS">
                                        <FormWbsEbs form={form} />
                                    </Form.Item>
                                </Col>
                            </Row>

                            <Row>
                                <Col span={24}>
                                    <Form.Item {...formColStyle()} name="checkDesc" label="检查描述" rules={[{required: true, message: "请输入检查描述"}]}>
                                        <TextArea placeholder="请输入检查描述" showCount maxLength={1000} />
                                    </Form.Item>
                                </Col>
                                {/* <Col span={23}>
                                    <Form.Item {...formColStyle()} name="lawBasis" label="法规依据">
                                        <TextArea placeholder="请输入法规依据" showCount maxLength={1000} />
                                    </Form.Item>
                                </Col> */}
                                {/* <Col span={1} style={{paddingBottom: 15}}>
                                    <RegulatoryBasisView moduleType={type} ids={selectOptionList} />
                                </Col> */}
                            </Row>
                        </ComCollapsePanel>
                    </Collapse>
                    <Row justify="space-between" gutter={[32, 0]}>
                        <Col span={12}>
                            <Collapse ghost expandIconPosition="right" defaultActiveKey={["4"]}>
                                <ComCollapsePanel
                                    header="附件上传"
                                    key="4"
                                >
                                    <FileBox value={enclosureList} onChange={handleEnclosureListChange} />
                                </ComCollapsePanel>
                            </Collapse>
                        </Col>
                        <Col span={12}>
                            <Collapse ghost expandIconPosition="right" defaultActiveKey={["5"]}>
                                <ComCollapsePanel
                                    header="影像资料"
                                    key="5"
                                >
                                    <FileBox
                                        accept="video/*,image/*"
                                        uploadText="上传影像"
                                        value={imageList}
                                        onChange={handleImageListChange}
                                        documentLibEnable={false}
                                    />
                                </ComCollapsePanel>
                            </Collapse>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div className={cls.footer}>
                <Space>
                    <Radio style={{display: showDangerLevel ? "" : "none"}} checked={radioChecked} onClick={() => handleRadioCheck()}>
                        提交后立即发起整改
                    </Radio>
                    <Button onClick={handleBack}>取消</Button>
                    <Button type="primary" onClick={handleSave}>保存</Button>
                </Space>
            </div>
            <DrawerDangerSelect
                selectDanger={selectDangerSource}
                onSelectDanger={setSelectDangerSource}
                visible={showDangerDrawer}
                onClose={() => setShowDangerDrawer(false)}
            />
            <PreviewModal />
        </div>
    );
};

export default AddAndEdit;
