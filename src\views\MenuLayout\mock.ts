/* eslint-disable max-lines */
// 模拟菜单
export const mockMenuList = [
    {
        searchValue: null,
        createBy: null,
        createTime: "2021-01-06 11:48:23",
        updateBy: null,
        updateTime: null,
        remark: "http://192.168.13.20:81/builder-occ/beamfield/#/oldBeamyard\nhttp://192.168.20.83:3000/#/oldBeamyard",
        params: {},
        menuId: 2030,
        menuName: "进度计划",
        parentName: null,
        parentId: 2691,
        orderNum: "1.0",
        path: "/main/schedule",
        component: null,
        isFrame: "0",
        isCache: "0",
        menuType: "M",
        visible: "0",
        status: "0",
        perms: "",
        icon: null,
        children: [
            {
                searchValue: null,
                createBy: null,
                createTime: "2022-08-08 10:10:52",
                updateBy: null,
                updateTime: null,
                remark: "",
                params: {},
                menuId: 12839,
                menuName: "计划编制",
                parentName: null,
                parentId: 2030,
                orderNum: "0.0",
                path: "/main/schedule/list",
                component: null,
                isFrame: "1",
                isCache: "0",
                menuType: "C",
                visible: "0",
                status: "0",
                perms: "ProjectPlatform-Plan-Progress-Establishment",
                icon: null,
                children: [],
                epid: 0,
                productId: 192,
                moduleType: null,
                bgIcon: null,
                attribute: "2",
                isCollect: 0,
                statusMessage: "",
                defaultIcon: null,
                productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                treePath: "/2691/2030/12839/"
            },
            {
                searchValue: null,
                createBy: null,
                createTime: "2022-08-08 10:11:18",
                updateBy: null,
                updateTime: null,
                remark: "",
                params: {},
                menuId: 12841,
                menuName: "计划审批",
                parentName: null,
                parentId: 2030,
                orderNum: "1.0",
                path: "/main/schedule/planApproval",
                component: null,
                isFrame: "1",
                isCache: "0",
                menuType: "C",
                visible: "0",
                status: "0",
                perms: "ProjectPlatform-Plan-Progress-Approval",
                icon: null,
                children: [],
                epid: 0,
                productId: 192,
                moduleType: null,
                bgIcon: null,
                attribute: "2",
                isCollect: 0,
                statusMessage: "",
                defaultIcon: null,
                productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                treePath: "/2691/2030/12841/"
            }
        ],
        epid: 0,
        productId: 192,
        moduleType: null,
        bgIcon: null,
        attribute: "2",
        isCollect: 0,
        statusMessage: "",
        defaultIcon: null,
        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
        treePath: "/2691/2030/"
    },
    {
        searchValue: null,
        createBy: null,
        createTime: "2022-08-08 10:12:10",
        updateBy: null,
        updateTime: null,
        remark: "",
        params: {},
        menuId: 12845,
        menuName: "实际进度",
        parentName: null,
        parentId: 2691,
        orderNum: "1.0",
        path: "/main/actual/list",
        component: null,
        isFrame: "1",
        isCache: "0",
        menuType: "C",
        visible: "0",
        status: "0",
        perms: "ProjectPlatform-Plan-Actual-Progress",
        icon: null,
        children: [],
        epid: 0,
        productId: 192,
        moduleType: null,
        bgIcon: null,
        attribute: "2",
        isCollect: 0,
        statusMessage: "",
        defaultIcon: null,
        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
        treePath: "/2691/12845/"
    },
    {
        searchValue: null,
        createBy: null,
        createTime: "2022-06-23 13:55:36",
        updateBy: null,
        updateTime: null,
        remark: "",
        params: {},
        menuId: 12703,
        menuName: "进度管理",
        parentName: null,
        parentId: 2691,
        orderNum: "3.0",
        path: "/main/scheduleManagement",
        component: null,
        isFrame: "1",
        isCache: "0",
        menuType: "M",
        visible: "0",
        status: "0",
        perms: "",
        icon: null,
        children: [
            {
                searchValue: null,
                createBy: null,
                createTime: "2022-08-08 10:12:41",
                updateBy: null,
                updateTime: null,
                remark: "",
                params: {},
                menuId: 12847,
                menuName: "进度报警",
                parentName: null,
                parentId: 12703,
                orderNum: "0.0",
                path: "/main/scheduleManagement/alarm",
                component: null,
                isFrame: "1",
                isCache: "0",
                menuType: "C",
                visible: "0",
                status: "0",
                perms: "ProjectPlatform-Plan-SandTable-Schedule-Management-Warn",
                icon: null,
                children: [],
                epid: 0,
                productId: 192,
                moduleType: null,
                bgIcon: null,
                attribute: "2",
                isCollect: 0,
                statusMessage: "",
                defaultIcon: null,
                productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                treePath: "/2691/12703/12847/"
            },
            {
                searchValue: null,
                createBy: null,
                createTime: "2022-06-23 14:51:53",
                updateBy: null,
                updateTime: null,
                remark: "",
                params: {},
                menuId: 12705,
                menuName: "沙盘管理",
                parentName: null,
                parentId: 12703,
                orderNum: "1.0",
                path: "/main/scheduleManagement/home",
                component: null,
                isFrame: "1",
                isCache: "0",
                menuType: "C",
                visible: "0",
                status: "0",
                perms: "",
                icon: null,
                children: [
                    {
                        searchValue: null,
                        createBy: null,
                        createTime: "2022-06-23 14:55:00",
                        updateBy: null,
                        updateTime: null,
                        remark: "",
                        params: {},
                        menuId: 12707,
                        menuName: "沙盘驾驶舱",
                        parentName: null,
                        parentId: 12705,
                        orderNum: "1.0",
                        path: "/main/scheduleManagement/sand/sandBox",
                        component: null,
                        isFrame: "1",
                        isCache: "0",
                        menuType: "C",
                        visible: "0",
                        status: "0",
                        perms: "ProjectPlatform-Plan-SandTable-Cockpit",
                        icon: null,
                        children: [],
                        epid: 0,
                        productId: 192,
                        moduleType: null,
                        bgIcon: null,
                        attribute: "2",
                        isCollect: 0,
                        statusMessage: "",
                        defaultIcon: null,
                        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                        treePath: "/2691/12703/12705/12707/"
                    },
                    {
                        searchValue: null,
                        createBy: null,
                        createTime: "2022-06-23 14:56:33",
                        updateBy: null,
                        updateTime: null,
                        remark: "",
                        params: {},
                        menuId: 12709,
                        menuName: "自定义沙盘",
                        parentName: null,
                        parentId: 12705,
                        orderNum: "2.0",
                        path: "/main/scheduleManagement/sand/bim",
                        component: null,
                        isFrame: "1",
                        isCache: "0",
                        menuType: "C",
                        visible: "0",
                        status: "0",
                        perms: "ProjectPlatform-Plan-SandTable-Self-Defining",
                        icon: null,
                        children: [],
                        epid: 0,
                        productId: 192,
                        moduleType: null,
                        bgIcon: null,
                        attribute: "2",
                        isCollect: 0,
                        statusMessage: "",
                        defaultIcon: null,
                        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                        treePath: "/2691/12703/12705/12709/"
                    }
                ],
                epid: 0,
                productId: 192,
                moduleType: null,
                bgIcon: null,
                attribute: "2",
                isCollect: 0,
                statusMessage: "",
                defaultIcon: null,
                productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                treePath: "/2691/12703/12705/"
            },
            {
                searchValue: null,
                createBy: null,
                createTime: "2022-08-08 10:12:59",
                updateBy: null,
                updateTime: null,
                remark: "",
                params: {},
                menuId: 12849,
                menuName: "异常事件",
                parentName: null,
                parentId: 12703,
                orderNum: "3.0",
                path: "异常事件",
                component: null,
                isFrame: "1",
                isCache: "0",
                menuType: "M",
                visible: "0",
                status: "0",
                perms: "",
                icon: null,
                children: [
                    {
                        searchValue: null,
                        createBy: null,
                        createTime: "2022-08-08 10:13:19",
                        updateBy: null,
                        updateTime: null,
                        remark: "",
                        params: {},
                        menuId: 12851,
                        menuName: "进度检查",
                        parentName: null,
                        parentId: 12849,
                        orderNum: "0.0",
                        path: "/main/scheduleManagement/abnormalEvent/inspect",
                        component: null,
                        isFrame: "1",
                        isCache: "0",
                        menuType: "C",
                        visible: "0",
                        status: "0",
                        perms: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check",
                        icon: null,
                        children: [],
                        epid: 0,
                        productId: 192,
                        moduleType: null,
                        bgIcon: null,
                        attribute: "2",
                        isCollect: 0,
                        statusMessage: "",
                        defaultIcon: null,
                        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                        treePath: "/2691/12703/12849/12851/"
                    },
                    {
                        searchValue: null,
                        createBy: null,
                        createTime: "2022-08-08 10:13:33",
                        updateBy: null,
                        updateTime: null,
                        remark: "",
                        params: {},
                        menuId: 12853,
                        menuName: "整改记录",
                        parentName: null,
                        parentId: 12849,
                        orderNum: "1.0",
                        path: "/main/scheduleManagement/abnormalEvent/rectification",
                        component: null,
                        isFrame: "1",
                        isCache: "0",
                        menuType: "C",
                        visible: "0",
                        status: "0",
                        perms: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Reform",
                        icon: null,
                        children: [],
                        epid: 0,
                        productId: 192,
                        moduleType: null,
                        bgIcon: null,
                        attribute: "2",
                        isCollect: 0,
                        statusMessage: "",
                        defaultIcon: null,
                        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                        treePath: "/2691/12703/12849/12853/"
                    }
                ],
                epid: 0,
                productId: 192,
                moduleType: null,
                bgIcon: null,
                attribute: "2",
                isCollect: 0,
                statusMessage: "",
                defaultIcon: null,
                productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
                treePath: "/2691/12703/12849/"
            }
        ],
        epid: 0,
        productId: 192,
        moduleType: null,
        bgIcon: null,
        attribute: "2",
        isCollect: 0,
        statusMessage: "",
        defaultIcon: null,
        productModuleId: "0bbc9aca7f7e4271811fe595222349b3",
        treePath: "/2691/12703/"
    }
];

// 模拟权限码
export const mockAuthGroupList = [
    {
        id: "ProjectPlatform-schedule-manage",
        groupName: "进度管理",
        parentId: "ProjectPlatform-schedule",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Executor-All",
        groupName: "全部",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Executor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor-AllTask",
        groupName: "全部任务",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan",
        groupName: "进度计划",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-manage-actualSchedule",
        groupName: "实际进度",
        parentId: "ProjectPlatform-schedule-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-schedule-manage-actualSchedule:edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-schedule-manage-actualSchedule:export",
                authName: "导出"
            }
        ]
    },
    {
        id: "192:jlzf:sgjl",
        groupName: "施工计量",
        parentId: "192:jlzf",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "192:jlzf:htzqjl:batchapprove",
                authName: "合同中间计量批量审批"
            },
            {
                authCode: "192:jlzf:htzqjl:delete",
                authName: "合同中间计量删除"
            },
            {
                authCode: "192:jlzf:htzqjl:deleteperiod",
                authName: "合同中间计量期次删除"
            },
            {
                authCode: "192:jlzf:htzqjl:fill",
                authName: "合同中间计量填报"
            },
            {
                authCode: "192:jlzf:htzqjl:formapprove",
                authName: "合同中间计量表单审批"
            },
            {
                authCode: "192:jlzf:htzqjl:formattachment",
                authName: "合同中间计量表单附件上传/下载/删除"
            },
            {
                authCode: "192:jlzf:htzqjl:formdelete",
                authName: "合同中间计量表单删除"
            },
            {
                authCode: "192:jlzf:htzqjl:formsign",
                authName: "合同中间计量表单签章"
            },
            {
                authCode: "192:jlzf:htzqjl:formsubmit",
                authName: "合同中间计量表单保存"
            },
            {
                authCode: "192:jlzf:htzqjl:formupdate",
                authName: "合同中间计量表单编辑"
            },
            {
                authCode: "192:jlzf:htzqjl:insert",
                authName: "合同中间计量新增"
            },
            {
                authCode: "192:jlzf:htzqjl:measurement",
                authName: "合同中间计量计量清单"
            },
            {
                authCode: "192:jlzf:htzqjl:modulelist",
                authName: "合同中间计量模型清单"
            },
            {
                authCode: "192:jlzf:htzqjl:nonmodulelist",
                authName: "合同中间计量非模型清单"
            },
            {
                authCode: "192:jlzf:htzqjl:save",
                authName: "合同中间计量保存"
            },
            {
                authCode: "192:jlzf:htzqjlqd:delete",
                authName: "合同中间计量清单删除"
            },
            {
                authCode: "192:jlzf:htzqjlqd:insert",
                authName: "合同中间计量清单新增"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:batchapprove",
                authName: "报表中心批量审批"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:check",
                authName: "报表中心查看"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:delete",
                authName: "报表中心删除计量"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:formapprove",
                authName: "报表中心表单审批"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:formdelete",
                authName: "报表中心表单删除"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:formsubmit",
                authName: "报表中心添加表单"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:formupdate",
                authName: "报表中心表单编辑"
            },
            {
                authCode: "192:jlzf:sgjl:bbzx:insert",
                authName: "报表中心发起计量"
            },
            {
                authCode: "192:jlzf:sgjl:check",
                authName: "施工计量查看"
            },
            {
                authCode: "192:jlzf:sgjl:delete",
                authName: "施工计量删除"
            },
            {
                authCode: "192:jlzf:sgjl:formapprove",
                authName: "施工计量表单审批"
            },
            {
                authCode: "192:jlzf:sgjl:formattachment",
                authName: "施工计量表单附件上传/下载/删除"
            },
            {
                authCode: "192:jlzf:sgjl:formdelete",
                authName: "施工计量表单删除"
            },
            {
                authCode: "192:jlzf:sgjl:formsign",
                authName: "施工计量表单签章"
            },
            {
                authCode: "192:jlzf:sgjl:formsubmit",
                authName: "施工计量表单保存"
            },
            {
                authCode: "192:jlzf:sgjl:formupdate",
                authName: "施工计量表单编辑"
            },
            {
                authCode: "192:jlzf:sgjl:insert",
                authName: "施工计量新增"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:approve",
                authName: "款项计量提交审批"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:approvedetail",
                authName: "款项计量审批详情"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:delete",
                authName: "款项计量删除审批"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:fill",
                authName: "款项计量填报审批"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:insert",
                authName: "款项计量新增审批"
            },
            {
                authCode: "192:jlzf:sgjl:kxjl:save",
                authName: "款项计量保存审批"
            },
            {
                authCode: "192:jlzf:zqjl:check",
                authName: "中间计量查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-Statistics-Sponsor",
        groupName: "发起人统计",
        parentId: "ProjectPlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Statistics-Sponsor",
        groupName: "发起人统计",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-manage-comparison",
        groupName: "进度对比",
        parentId: "ProjectPlatform-schedule-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management-Warn",
        groupName: "进度报警",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement",
        groupName: "环保管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Statistics-OverallTrend",
        groupName: "整体趋势",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-production-list",
        groupName: "生产台账",
        parentId: "beamplan-production",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "beamplan-production-list-export",
                authName: "导出"
            },
            {
                authCode: "beamplan-production-list-status",
                authName: "梁场状态设置"
            }
        ]
    },
    {
        id: "EnterprisePlatform-Main",
        groupName: "主页",
        parentId: "EnterprisePlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Approval",
        groupName: "审批",
        parentId: "EnterprisePlatform-ApprovalManage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Constructor-All",
        groupName: "全部",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Constructor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Statistics-OverallTrend",
        groupName: "整体趋势",
        parentId: "ProjectPlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-production",
        groupName: "生产进度",
        parentId: "ProjectPlanform-beamplan",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-Progress-Establishment",
        groupName: "计划编制",
        parentId: "ProjectPlatform-Plan-Progress",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:change",
                authName: "变更"
            },
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:export",
                authName: "导出"
            },
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:import",
                authName: "导入"
            },
            {
                authCode: "ProjectPlatform-Plan-Progress-Establishment:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:yfk:dyyfk",
        groupName: "动员预付款",
        parentId: "192:yfk",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "192:yfk:dyyfk:check",
                authName: "查看"
            },
            {
                authCode: "192:yfk:dyyfk:delete",
                authName: "删除"
            },
            {
                authCode: "192:yfk:dyyfk:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:yfk:dyyfk:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:yfk:dyyfk:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:yfk:dyyfk:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:yfk:dyyfk:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:yfk:dyyfk:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:yfk:dyyfk:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "EnterprisePlatform-Statistics-Overview",
        groupName: "企业总览",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupName: "人员管理",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ApprovalManage",
        groupName: "审批管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Cockpit",
        groupName: "沙盘驾驶舱",
        parentId: "ProjectPlatform-Plan-SandTable-Management",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-manage-pedestal",
        groupName: "台座布置",
        parentId: "beamplan-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "beamplan-manage-pedestal-add",
                authName: "新增"
            },
            {
                authCode: "beamplan-manage-pedestal-del",
                authName: "删除"
            },
            {
                authCode: "beamplan-manage-pedestal-export",
                authName: "导出"
            },
            {
                authCode: "beamplan-manage-pedestal-update",
                authName: "修改"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-System",
        groupName: "安保体系",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-statistics-analysis-view",
        groupName: "统计看板",
        parentId: "ProjectPlatform-schedule-statistics-analysis",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-schedule-statistics-analysis-view:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "Projectplatform-material",
        groupName: "物料",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-PointsAssessment",
        groupName: "安全考核",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "PersonnelManagement-PointsAssessment:Add",
                authName: "新增"
            },
            {
                authCode: "PersonnelManagement-PointsAssessment:Delete",
                authName: "删除"
            },
            {
                authCode: "PersonnelManagement-PointsAssessment:Edit",
                authName: "编辑"
            },
            {
                authCode: "PersonnelManagement-PointsAssessment:ExportLedger",
                authName: "导出台账"
            }
        ]
    },
    {
        id: "EnterprisePlatform-ApprovalManage",
        groupName: "审批管理",
        parentId: "EnterprisePlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "material-manage-in",
        groupName: "物资入库",
        parentId: "material-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "material-manage-in-add",
                authName: "新增"
            },
            {
                authCode: "material-manage-in-del",
                authName: "删除"
            },
            {
                authCode: "material-manage-in-export",
                authName: "导出"
            },
            {
                authCode: "material-manage-in-update",
                authName: "修改"
            }
        ]
    },
    {
        id: "material-manage-count",
        groupName: "物资统计",
        parentId: "material-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "material-manage-count-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "BIM-Management-System-Model-BIM",
        groupName: "BIM模型",
        parentId: "BIM-Management-System-Model",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-Actual-Progress",
        groupName: "实际进度",
        parentId: "ProjectPlatform-Old-Plan",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-Plan-Actual-Progress:edit",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-Data",
        groupName: "资料管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "92035001",
                authName: "上传资料"
            },
            {
                authCode: "92035003",
                authName: "管理标签"
            },
            {
                authCode: "92035004",
                authName: "管理文件夹"
            },
            {
                authCode: "92035005",
                authName: "删除文件夹"
            },
            {
                authCode: "92035006",
                authName: "资料分享"
            }
        ]
    },
    {
        id: "ProjectPlatform-workbench",
        groupName: "工作台",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:personalset:basicset",
        groupName: "基本设置",
        parentId: "192:personalset",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Reform",
        groupName: "整改记录",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Statistics",
        groupName: "统计",
        parentId: "ProjectPlatform-ApprovalManage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:commconfig",
        groupName: "通用配置",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "material-manage-out",
        groupName: "物资出库",
        parentId: "material-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "material-manage-out-add",
                authName: "新增"
            },
            {
                authCode: "material-manage-out-del",
                authName: "删除"
            },
            {
                authCode: "material-manage-out-export",
                authName: "导出"
            },
            {
                authCode: "material-manage-out-update",
                authName: "修改"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor-Plan",
        groupName: "巡检计划",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "material-manage-room",
        groupName: "库房管理",
        parentId: "material-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "material-manage-room-add",
                authName: "新增"
            },
            {
                authCode: "material-manage-room-del",
                authName: "删除"
            },
            {
                authCode: "material-manage-room-export",
                authName: "导出"
            },
            {
                authCode: "material-manage-room-update",
                authName: "修改"
            }
        ]
    },
    {
        id: "ProjectPlatform-Plan-Progress-Approval",
        groupName: "计划审批",
        parentId: "ProjectPlatform-Plan-Progress",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule",
        groupName: "进度(新)",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System",
        groupName: "BIM管理系统",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-statistics-analysis-schedulePage",
        groupName: "进度台账",
        parentId: "ProjectPlatform-schedule-statistics-analysis",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-schedule-statistics-analysis-schedulePage:SynchronizeData",
                authName: "同步网格数据"
            },
            {
                authCode: "ProjectPlatform-schedule-statistics-analysis-schedulePage:edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-schedule-statistics-analysis-schedulePage:export",
                authName: "导出"
            }
        ]
    },
    {
        id: "EnterprisePlatform-Statistics-Subcontract",
        groupName: "分包总体",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:rwzx",
        groupName: "任务中心",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:meter",
        groupName: "计量",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupName: "进度管理",
        parentId: "ProjectPlatform-Old-Plan",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System-Model-BIM-Operation",
        groupName: "模型操作",
        parentId: "BIM-Management-System-Model-BIM",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "BIM-Management-System-Model-BIM-Operation:share",
                authName: "分享"
            },
            {
                authCode: "BIM-Management-System-Model-BIM-Operation:upload",
                authName: "上传工程"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor-AllTask",
        groupName: "全部任务",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement",
        groupName: "质量管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-Progress",
        groupName: "进度计划",
        parentId: "ProjectPlatform-Old-Plan",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor-AllTask",
        groupName: "全部任务",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Main",
        groupName: "主页",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-Main:Pictures",
                authName: "图片设置"
            }
        ]
    },
    {
        id: "192:qdgl:htqd",
        groupName: "合同清单",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "192:qdgl:htqd:check",
                authName: "查看"
            },
            {
                authCode: "192:qdgl:htqd:import",
                authName: "导入"
            }
        ]
    },
    {
        id: "ProjectPlatform-Model",
        groupName: "模型管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor-Plan",
        groupName: "巡检计划",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:htgl:htxx",
        groupName: "合同信息",
        parentId: "192:htgl",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "192:htgl:htxx:attachment",
                authName: "附件上传/下载/删除"
            },
            {
                authCode: "192:htgl:htxx:check",
                authName: "查看"
            },
            {
                authCode: "192:htgl:htxx:delete",
                authName: "删除"
            },
            {
                authCode: "192:htgl:htxx:insert",
                authName: "新增"
            },
            {
                authCode: "192:htgl:htxx:update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-schedule-statistics-analysis",
        groupName: "统计分析",
        parentId: "ProjectPlatform-schedule",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-System-Manage",
        groupName: "管理制度",
        parentId: "ProjectPlatform-SecurityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "System-Manage:Delete",
                authName: "删除"
            },
            {
                authCode: "System-Manage:Download",
                authName: "下载"
            },
            {
                authCode: "System-Manage:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-schedule-plan",
        groupName: "进度计划",
        parentId: "ProjectPlatform-schedule",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-production-count",
        groupName: "进度统计",
        parentId: "beamplan-production",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "beamplan-production-count-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "192:laboratory",
        groupName: "试验室",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "statistics-duty",
        groupName: "人员值班表",
        parentId: "ProjectPlanform-statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "statistics-duty-crud",
                authName: "人员值班表设置"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Supervisor-All",
        groupName: "全部",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Supervisor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Approval",
        groupName: "审批",
        parentId: "ProjectPlatform-ApprovalManage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-manage",
        groupName: "计划管理",
        parentId: "ProjectPlanform-beamplan",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check",
        groupName: "进度检查",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:detele",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:reform",
                authName: "发起整改"
            }
        ]
    },
    {
        id: "ProjectPlatform-Statistics-Subcontract",
        groupName: "分包总体",
        parentId: "ProjectPlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:rwzx:wddb",
        groupName: "我的待办",
        parentId: "192:rwzx",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "192:rwzx:wddb:batchapprove",
                authName: "批量审批"
            },
            {
                authCode: "192:rwzx:wddb:batchpreview",
                authName: "批量预览"
            },
            {
                authCode: "192:rwzx:wddb:batchsign",
                authName: "批量签章"
            },
            {
                authCode: "192:rwzx:wddb:check",
                authName: "查看"
            },
            {
                authCode: "192:rwzx:wddb:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:rwzx:wddb:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:rwzx:wddb:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:rwzx:wddb:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:rwzx:wddb:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:rwzx:wddb:formupdate",
                authName: "表单编辑"
            }
        ]
    },
    {
        id: "EnterprisePlatform-Model",
        groupName: "模型管理",
        parentId: "EnterprisePlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor-Plan",
        groupName: "巡检计划",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Statistics-Types",
        groupName: "类型统计",
        parentId: "ProjectPlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-manage-plan",
        groupName: "生产计划",
        parentId: "beamplan-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "beamplan-manage-plan-add",
                authName: "新增"
            },
            {
                authCode: "beamplan-manage-plan-del",
                authName: "删除"
            },
            {
                authCode: "beamplan-manage-plan-export",
                authName: "导出"
            },
            {
                authCode: "beamplan-manage-plan-plan",
                authName: "智能排产"
            },
            {
                authCode: "beamplan-manage-plan-update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration",
        groupName: "人员信息",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Old-Plan",
        groupName: "进度(老)",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Self-Defining",
        groupName: "自定义沙盘",
        parentId: "ProjectPlatform-Plan-SandTable-Management",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-Plan-SandTable-Self-Defining:DefineProcess",
                authName: "定义工序"
            },
            {
                authCode: "ProjectPlatform-Plan-SandTable-Self-Defining:ProcessSettings",
                authName: "工序管理"
            },
            {
                authCode: "ProjectPlatform-Plan-SandTable-Self-Defining:ProjectSettings",
                authName: "工程设置"
            }
        ]
    },
    {
        id: "material-manage",
        groupName: "物资管理",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Statistics",
        groupName: "统计",
        parentId: "EnterprisePlatform-ApprovalManage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlanform-statistics",
        groupName: "统计",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlanform-beamplan",
        groupName: "梁场-进度",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement",
        groupName: "巡检管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination",
        groupName: "教育考试",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Management",
        groupName: "沙盘管理",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:qdgl",
        groupName: "清单管理",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:personalset",
        groupName: "个人设置",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event",
        groupName: "异常事件",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Statistics-Types",
        groupName: "类型统计",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement",
        groupName: "安全管理",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "material-statistics",
        groupName: "应耗量统计",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "material-statistics-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "192:commconfig:wbs",
        groupName: "WBS实例",
        parentId: "192:commconfig",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-manage-warn",
        groupName: "进度预警",
        parentId: "ProjectPlatform-schedule-manage",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-schedule-manage-warn:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox",
        groupName: "配电箱",
        parentId: "ProjectPlatform-SecurityManagement-ElectricManagement",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:AddRecord",
                authName: "检查记录-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:DeleteRecord",
                authName: "检查记录-删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:Edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:EditRecord",
                authName: "检查记录-编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:ExportLedger",
                authName: "导出台账"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:ExportQrcode",
                authName: "导出二维码"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:Import",
                authName: "导入"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-ElectricManagement-ElectricBox:Qrcode",
                authName: "二维码"
            }
        ]
    },
    {
        id: "BIM-Management-System-Model",
        groupName: "模型管理",
        parentId: "BIM-Management-System",
        groupLevel: null,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "Projectplatform-material",
        groupName: "物料",
        parentId: "Projectplatform-material",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ApprovalManage",
        groupName: "审批管理",
        parentId: "ProjectPlatform-ApprovalManage",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:laboratory",
        groupName: "试验室",
        parentId: "192:laboratory",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration",
        groupName: "人员信息",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlanform-statistics",
        groupName: "统计",
        parentId: "ProjectPlanform-statistics",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform",
        groupName: "企业平台",
        parentId: "EnterprisePlatform",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "material-manage",
        groupName: "物资管理",
        parentId: "material-manage",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement",
        groupName: "质量管理",
        parentId: "ProjectPlatform-QualityManagement",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-Statistics",
        groupName: "统计",
        parentId: "EnterprisePlatform-Statistics",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination",
        groupName: "教育考试",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:rwzx",
        groupName: "任务中心",
        parentId: "192:rwzx",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupName: "人员管理",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-manage",
        groupName: "进度管理",
        parentId: "ProjectPlatform-schedule-manage",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupName: "进度管理",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System-Model",
        groupName: "模型管理",
        parentId: "BIM-Management-System-Model",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Old-Plan",
        groupName: "进度(老)",
        parentId: "ProjectPlatform-Old-Plan",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Statistics",
        groupName: "统计",
        parentId: "ProjectPlatform-Statistics",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "EnterprisePlatform-ApprovalManage",
        groupName: "审批管理",
        parentId: "EnterprisePlatform-ApprovalManage",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-production",
        groupName: "生产进度",
        parentId: "beamplan-production",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule-statistics-analysis",
        groupName: "统计分析",
        parentId: "ProjectPlatform-schedule-statistics-analysis",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System-Model-BIM",
        groupName: "BIM模型",
        parentId: "BIM-Management-System-Model-BIM",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:qdgl",
        groupName: "清单管理",
        parentId: "192:qdgl",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:personalset",
        groupName: "个人设置",
        parentId: "192:personalset",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement",
        groupName: "巡检管理",
        parentId: "ProjectPlatform-ProgressManagement",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Management",
        groupName: "沙盘管理",
        parentId: "ProjectPlatform-Plan-SandTable-Management",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:meter",
        groupName: "计量",
        parentId: "192:meter",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement",
        groupName: "环保管理",
        parentId: "ProjectPlatform-EnvironmentManagement",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-workbench",
        groupName: "工作台",
        parentId: "ProjectPlatform-workbench",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:processinspection",
        groupName: "工序报验",
        parentId: "192:processinspection",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement",
        groupName: "安全管理",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-Progress",
        groupName: "进度计划",
        parentId: "ProjectPlatform-Plan-Progress",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlanform-beamplan",
        groupName: "梁场-进度",
        parentId: "ProjectPlanform-beamplan",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-System",
        groupName: "安保体系",
        parentId: "ProjectPlatform-SecurityManagement-System",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform",
        groupName: "项目平台",
        parentId: "ProjectPlatform",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:commconfig",
        groupName: "通用配置",
        parentId: "192:commconfig",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:inspection",
        groupName: "web质检（新）",
        parentId: "192:inspection",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System",
        groupName: "BIM管理系统",
        parentId: "BIM-Management-System",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event",
        groupName: "异常事件",
        parentId: "ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-schedule",
        groupName: "进度(新)",
        parentId: "ProjectPlatform-schedule",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "beamplan-manage",
        groupName: "计划管理",
        parentId: "beamplan-manage",
        groupLevel: 0,
        display: 1,
        sort: 0,
        authCodeResultList: []
    },
    {
        id: "192:jlzf:jljl",
        groupName: "监理计量",
        parentId: "192:jlzf",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:jlzf:jljl:check",
                authName: "查看"
            },
            {
                authCode: "192:jlzf:jljl:delete",
                authName: "删除"
            },
            {
                authCode: "192:jlzf:jljl:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:jlzf:jljl:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:jlzf:jljl:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:jlzf:jljl:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:jlzf:jljl:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:jlzf:jljl:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:jlzf:jljl:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Record",
        groupName: "考试记录",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Duty-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-System",
        groupName: "质量体系",
        parentId: "ProjectPlatform-QualityManagement",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:payment_invest",
        groupName: "投资类项目",
        parentId: "192:sunpay:payment",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjjs:wjhs",
        groupName: "文件回收",
        parentId: "192:archives:wjjs",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:wjjs:wjhs:delete",
                authName: "删除"
            },
            {
                authCode: "192:archives:wjjs:wjhs:look",
                authName: "查看"
            },
            {
                authCode: "192:archives:wjjs:wjhs:restore",
                authName: "还原"
            }
        ]
    },
    {
        id: "BIM-Management-System-GIS",
        groupName: "GIS管理",
        parentId: "BIM-Management-System",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-System-OrgStructure-new",
        groupName: "组织机构（无三方）",
        parentId: "ProjectPlatform-SecurityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dabg:crktz",
        groupName: "出入库台账",
        parentId: "192:archives:dabg",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dabg:crktz:batchexport",
                authName: "批量导出"
            },
            {
                authCode: "192:archives:dabg:crktz:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:laboratory:datareport",
        groupName: "数据报告",
        parentId: "192:laboratory",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dazl",
        groupName: "档案整理",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:contract_manage",
        groupName: "合同管理",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Check-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjjs:wjjc",
        groupName: "文件检查",
        parentId: "192:archives:wjjs",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:wjjs:wjjc:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "production-monitor-bind",
        groupName: "台座绑定",
        parentId: "production-monitor",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "production-monitor-bind-del",
                authName: "删除"
            },
            {
                authCode: "production-monitor-bind-detail",
                authName: "查看"
            },
            {
                authCode: "production-monitor-bind-update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "BIM-Management-System-Model-DWG",
        groupName: "DWG模型",
        parentId: "BIM-Management-System-Model",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:contract_manage_invest",
        groupName: "投资类项目",
        parentId: "192:sunpay:contract_manage",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjjs",
        groupName: "文件接收",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:qdgl:hcqd",
        groupName: "核查清单",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:qdgl:hcqd:attachment",
                authName: "附件上传/下载/删除"
            },
            {
                authCode: "192:qdgl:hcqd:check",
                authName: "查看"
            },
            {
                authCode: "192:qdgl:hcqd:delete",
                authName: "删除"
            },
            {
                authCode: "192:qdgl:hcqd:import",
                authName: "导入"
            },
            {
                authCode: "192:qdgl:hcqd:relatelist",
                authName: "关联清单"
            },
            {
                authCode: "192:qdgl:hcqd:save",
                authName: "保存"
            },
            {
                authCode: "192:qdgl:hcqd:sketch",
                authName: "上传草图"
            }
        ]
    },
    {
        id: "192:archives:dabg:dark",
        groupName: "档案入库",
        parentId: "192:archives:dabg",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dabg:dark:instorage",
                authName: "入库"
            },
            {
                authCode: "192:archives:dabg:dark:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:daly:dafw:jy",
        groupName: "档案借阅",
        parentId: "192:archives:daly:dafw",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:daly:dafw:jy:lend",
                authName: "借出"
            },
            {
                authCode: "192:archives:daly:dafw:jy:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:sunpay:pay_manage_bill_making",
        groupName: "制单",
        parentId: "192:sunpay:pay_manage",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-DangerManage-Assess",
        groupName: "辨识评估",
        parentId: "ProjectPlatform-SecurityManagement-DangerManage",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "DangerManage-Assess:Add",
                authName: "新增/发起"
            },
            {
                authCode: "DangerManage-Assess:Delete",
                authName: "删除"
            },
            {
                authCode: "DangerManage-Assess:Import",
                authName: "导入"
            },
            {
                authCode: "DangerManage-Assess:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "production-bind-status",
        groupName: "台座状态",
        parentId: "production-bind",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "production-bind-status-edit",
                authName: "台座状态编辑"
            },
            {
                authCode: "production-bind-status-plan",
                authName: "智能排产"
            }
        ]
    },
    {
        id: "192:archives:jcsz:dasgl",
        groupName: "档案室管理",
        parentId: "192:archives:jcsz",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:jcsz:dasgl:add",
                authName: "新增"
            },
            {
                authCode: "192:archives:jcsz:dasgl:delete",
                authName: "删除"
            },
            {
                authCode: "192:archives:jcsz:dasgl:edit",
                authName: "编辑"
            },
            {
                authCode: "192:archives:jcsz:dasgl:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:dabg:dack",
        groupName: "档案出库",
        parentId: "192:archives:dabg",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dabg:dack:look",
                authName: "查看"
            },
            {
                authCode: "192:archives:dabg:dack:outstorage",
                authName: "出库"
            }
        ]
    },
    {
        id: "192:archives:wjgl:dzwj",
        groupName: "电子文件",
        parentId: "192:archives:wjgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:laboratory:samplemng",
        groupName: "样品管理",
        parentId: "192:laboratory",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dazl:gdgl:dgd",
        groupName: "待归档",
        parentId: "192:archives:dazl:gdgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dazl:gdgl:dgd:confirm",
                authName: "确认/取消归档"
            },
            {
                authCode: "192:archives:dazl:gdgl:dgd:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:jcsz:wjfl",
        groupName: "文件分类",
        parentId: "192:archives:jcsz",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "workbench-notice",
        groupName: "通知公告",
        parentId: "ProjectPlatform-workbench",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "workbench-notice:add",
                authName: "新增"
            },
            {
                authCode: "workbench-notice:delete",
                authName: "删除"
            },
            {
                authCode: "workbench-notice:down",
                authName: "下移"
            },
            {
                authCode: "workbench-notice:edit",
                authName: "编辑"
            },
            {
                authCode: "workbench-notice:top",
                authName: "置顶"
            },
            {
                authCode: "workbench-notice:up",
                authName: "上移"
            }
        ]
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Routing-Plan",
        groupName: "巡检计划",
        parentId: "ProjectPlatform-QualityManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-QualityManagement-inspect-Routing-Plan:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-QualityManagement-inspect-Routing-Plan:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-QualityManagement-inspect-Routing-Plan:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-Device-General",
        groupName: "普通设备",
        parentId: "ProjectPlatform-SecurityManagement-Device",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:AppearanceRecordAdd",
                authName: "出场记录-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:AppearanceRecordUpdate",
                authName: "出场记录-修改"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Download",
                authName: "下载"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Exit",
                authName: "退场"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Import",
                authName: "导出附件"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:InspectionRecordAdd",
                authName: "检查记录-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:InspectionRecordUpdate",
                authName: "检查记录-编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:MaintenanceAdd",
                authName: "维修保养-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:MaintenanceUpdate",
                authName: "维修保养-编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-General:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dazl:yzaj",
        groupName: "预组案卷",
        parentId: "192:archives:dazl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dazl:yzaj:apply",
                authName: "申请归档"
            },
            {
                authCode: "192:archives:dazl:yzaj:catalogue",
                authName: "生成目录"
            },
            {
                authCode: "192:archives:dazl:yzaj:edit",
                authName: "编辑"
            },
            {
                authCode: "192:archives:dazl:yzaj:form",
                authName: "备考表"
            },
            {
                authCode: "192:archives:dazl:yzaj:look",
                authName: "查看"
            },
            {
                authCode: "192:archives:dazl:yzaj:move",
                authName: "上下移动"
            },
            {
                authCode: "192:archives:dazl:yzaj:onekeysort",
                authName: "一键排档"
            },
            {
                authCode: "192:archives:dazl:yzaj:split",
                authName: "拆卷"
            }
        ]
    },
    {
        id: "material-statistics-new",
        groupName: "物资统计",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "material-statistics-new:export",
                authName: "导出"
            }
        ]
    },
    {
        id: "192:cltc:zsftc",
        groupName: "指数法调差",
        parentId: "192:cltc",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:cltc:zsftc:check",
                authName: "查看"
            },
            {
                authCode: "192:cltc:zsftc:delete",
                authName: "删除"
            },
            {
                authCode: "192:cltc:zsftc:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:cltc:zsftc:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:cltc:zsftc:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:cltc:zsftc:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:cltc:zsftc:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:cltc:zsftc:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:cltc:zsftc:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "192:archives:wjgl",
        groupName: "文件管理",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupName: "隐患排查",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System-Model-BIM-WBSMapping",
        groupName: "WBS映射",
        parentId: "BIM-Management-System-Model-BIM",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "BIM-Management-System-Model-BIM-WBSMapping:bind-wbs",
                authName: "关联WBS"
            },
            {
                authCode: "BIM-Management-System-Model-BIM-WBSMapping:del-bind",
                authName: "删除关联"
            },
            {
                authCode: "BIM-Management-System-Model-BIM-WBSMapping:export-excel",
                authName: "导出Excel"
            },
            {
                authCode: "BIM-Management-System-Model-BIM-WBSMapping:import-excel",
                authName: "导入Excel"
            }
        ]
    },
    {
        id: "192:processinspection:sgzj",
        groupName: "施工自检",
        parentId: "192:processinspection",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:processinspection:sgzj:add",
                authName: "新增报验"
            },
            {
                authCode: "192:processinspection:sgzj:delete",
                authName: "删除"
            },
            {
                authCode: "192:processinspection:sgzj:edit",
                authName: "编辑"
            },
            {
                authCode: "192:processinspection:sgzj:editdata",
                authName: "编辑数据"
            },
            {
                authCode: "192:processinspection:sgzj:genform",
                authName: "生成表单"
            },
            {
                authCode: "192:processinspection:sgzj:launchapprove",
                authName: "审批"
            },
            {
                authCode: "192:processinspection:sgzj:launchcheck",
                authName: "发起抽验"
            },
            {
                authCode: "192:processinspection:sgzj:resetprocessinspection",
                authName: "重置报验"
            },
            {
                authCode: "192:processinspection:sgzj:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:processinspection:sgzj:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Responsibility-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration-Operators",
        groupName: "施工作业人员",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "PersonnelRegistration-Operators:Add",
                authName: "新增"
            },
            {
                authCode: "PersonnelRegistration-Operators:BatchExportQRCode",
                authName: "批量导出二维码"
            },
            {
                authCode: "PersonnelRegistration-Operators:CertificateView",
                authName: "证书查看"
            },
            {
                authCode: "PersonnelRegistration-Operators:Delete",
                authName: "删除"
            },
            {
                authCode: "PersonnelRegistration-Operators:DownloadExcelTemplate",
                authName: "下载excel模板"
            },
            {
                authCode: "PersonnelRegistration-Operators:Edit",
                authName: "编辑"
            },
            {
                authCode: "PersonnelRegistration-Operators:Exit",
                authName: "退场"
            },
            {
                authCode: "PersonnelRegistration-Operators:ExportLedger",
                authName: "导出台账"
            },
            {
                authCode: "PersonnelRegistration-Operators:ImportExcel",
                authName: "导入excel"
            },
            {
                authCode: "PersonnelRegistration-Operators:PrivacyView",
                authName: "身份证查看"
            },
            {
                authCode: "PersonnelRegistration-Operators:QRCodeDownload",
                authName: "二维码下载"
            },
            {
                authCode: "PersonnelRegistration-Operators:QRCodeView",
                authName: "二维码查看"
            }
        ]
    },
    {
        id: "192:archives:jcsz",
        groupName: "基础设置",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly:dafw",
        groupName: "档案服务",
        parentId: "192:archives:daly",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-Disclosure-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "production-monitor-vedio",
        groupName: "视频监控",
        parentId: "production-monitor",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "production-monitor-vedio-star",
                authName: "视频关注"
            }
        ]
    },
    {
        id: "192:archives:wjgl:wjsjzx",
        groupName: "文件收集中心",
        parentId: "192:archives:wjgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Routing-Plan",
        groupName: "巡检计划",
        parentId: "ProjectPlatform-SecurityManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-SecurityManagement-inspect-Routing-Plan:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-inspect-Routing-Plan:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-inspect-Routing-Plan:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "production-make-status",
        groupName: "台座状态",
        parentId: "production-make",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "production-make-status-edit",
                authName: "台座状态编辑"
            },
            {
                authCode: "production-make-status-plan",
                authName: "智能排产"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity-Education",
        groupName: "岗前教育",
        parentId: "ProjectPlatform-SecurityManagement-Activity",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "Activity-Education:Add",
                authName: "新增"
            },
            {
                authCode: "Activity-Education:Delete",
                authName: "删除"
            },
            {
                authCode: "Activity-Education:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "production-bind",
        groupName: "钢筋绑扎台座",
        parentId: "ProjectPlatform-production",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly:dafw:gh",
        groupName: "档案归还",
        parentId: "192:archives:daly:dafw",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:daly:dafw:gh:back",
                authName: "归还"
            },
            {
                authCode: "192:archives:daly:dafw:gh:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:bgqz:bggl",
        groupName: "变更管理",
        parentId: "192:bgqz",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dabg",
        groupName: "档案保管",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:jcsz:wjzxgl",
        groupName: "文件中心管理",
        parentId: "192:archives:jcsz",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:processinspection:jlcy",
        groupName: "监理抽验",
        parentId: "192:processinspection",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:processinspection:jlcy:delete",
                authName: "删除"
            },
            {
                authCode: "192:processinspection:jlcy:edit",
                authName: "编辑"
            },
            {
                authCode: "192:processinspection:jlcy:editdata",
                authName: "编辑数据"
            },
            {
                authCode: "192:processinspection:jlcy:genform",
                authName: "生成表单"
            },
            {
                authCode: "192:processinspection:jlcy:launchapprove",
                authName: "审批"
            },
            {
                authCode: "192:processinspection:jlcy:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:processinspection:jlcy:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:yfk:clyfk",
        groupName: "材料预付款",
        parentId: "192:yfk",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:yfk:clyfk:check",
                authName: "查看"
            },
            {
                authCode: "192:yfk:clyfk:delete",
                authName: "删除"
            },
            {
                authCode: "192:yfk:clyfk:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:yfk:clyfk:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:yfk:clyfk:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:yfk:clyfk:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:yfk:clyfk:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:yfk:clyfk:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:yfk:clyfk:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "192:sunpay:financial_invest",
        groupName: "投资类项目",
        parentId: "192:sunpay:financial",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly:dacx",
        groupName: "档案查询",
        parentId: "192:archives:daly",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:daly:dacx:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:ins:tbgd",
        groupName: "同步归档",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:ins:tbgd:apply",
                authName: "提交归档"
            },
            {
                authCode: "192:ins:tbgd:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:tbgd:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:rwzx",
        groupName: "任务中心",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:rwzx:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-production",
        groupName: "生产",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System",
        groupName: "环保体系",
        parentId: "ProjectPlatform-EnvironmentManagement",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-System-Manage",
        groupName: "管理制度",
        parentId: "ProjectPlatform-QualityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "QualityManagement-System-Manage:Add",
                authName: "新增"
            },
            {
                authCode: "QualityManagement-System-Manage:Delete",
                authName: "删除"
            },
            {
                authCode: "QualityManagement-System-Manage:Download",
                authName: "下载"
            },
            {
                authCode: "QualityManagement-System-Manage:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-Homepage-new",
        groupName: "统计分析（无三方）",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:jlzf:dsfjl",
        groupName: "第三方计量",
        parentId: "192:jlzf",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:jlzf:dsfjl:check",
                authName: "查看"
            },
            {
                authCode: "192:jlzf:dsfjl:delete",
                authName: "删除"
            },
            {
                authCode: "192:jlzf:dsfjl:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:jlzf:dsfjl:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:jlzf:dsfjl:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:jlzf:dsfjl:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:jlzf:dsfjl:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:jlzf:dsfjl:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:jlzf:dsfjl:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "ProjectPlatform-ProgressManagement-inspect-Check",
        groupName: "隐患排查",
        parentId: "ProjectPlatform-ProgressManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjgl:wjhs",
        groupName: "文件回收",
        parentId: "192:archives:wjgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly:dafw:tz",
        groupName: "借阅台账",
        parentId: "192:archives:daly:dafw",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:daly:dafw:tz:batchexport",
                authName: "批量导出"
            },
            {
                authCode: "192:archives:daly:dafw:tz:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:wjjs:ysj",
        groupName: "元数据",
        parentId: "192:archives:wjjs",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:wjjs:ysj:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Rectification-All",
        groupName: "全部",
        parentId: "ProjectPlatform-SecurityManagement-inspect-Rectification",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:cltc:xxjftc",
        groupName: "信息价法调差",
        parentId: "192:cltc",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:cltc:xxjftc:check",
                authName: "查看"
            },
            {
                authCode: "192:cltc:xxjftc:delete",
                authName: "删除"
            },
            {
                authCode: "192:cltc:xxjftc:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:cltc:xxjftc:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:cltc:xxjftc:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:cltc:xxjftc:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:cltc:xxjftc:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:cltc:xxjftc:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:cltc:xxjftc:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "192:archives:wjgl:wjjc",
        groupName: "文件检查",
        parentId: "192:archives:wjgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:oversight_review",
        groupName: "发起申诉",
        parentId: "192:sunpay:oversight",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity-Train",
        groupName: "安全培训",
        parentId: "ProjectPlatform-SecurityManagement-Activity",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "Activity-Train:Add",
                authName: "新增"
            },
            {
                authCode: "Activity-Train:Delete",
                authName: "删除"
            },
            {
                authCode: "Activity-Train:Download",
                authName: "下载"
            },
            {
                authCode: "Activity-Train:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:archives:jcsz:zjgz",
        groupName: "组卷规则",
        parentId: "192:archives:jcsz",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:jcsz:zjgz:add",
                authName: "新增"
            },
            {
                authCode: "192:archives:jcsz:zjgz:delete",
                authName: "删除"
            },
            {
                authCode: "192:archives:jcsz:zjgz:edit",
                authName: "编辑"
            },
            {
                authCode: "192:archives:jcsz:zjgz:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Manage-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Check",
        groupName: "隐患排查",
        parentId: "ProjectPlatform-QualityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "QualityManagement-inspect-Check:Add",
                authName: "新增"
            },
            {
                authCode: "QualityManagement-inspect-Check:Delete",
                authName: "删除"
            },
            {
                authCode: "QualityManagement-inspect-Check:Download",
                authName: "下载"
            },
            {
                authCode: "QualityManagement-inspect-Check:InitiateRectification",
                authName: "发起整改"
            },
            {
                authCode: "QualityManagement-inspect-Check:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:archives:wjjs:dzwj",
        groupName: "电子文件",
        parentId: "192:archives:wjjs",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:wjjs:dzwj:addfile",
                authName: "新增文件"
            },
            {
                authCode: "192:archives:wjjs:dzwj:addnode",
                authName: "新增节点"
            },
            {
                authCode: "192:archives:wjjs:dzwj:deletefile",
                authName: "删除文件"
            },
            {
                authCode: "192:archives:wjjs:dzwj:deletenode",
                authName: "删除节点"
            },
            {
                authCode: "192:archives:wjjs:dzwj:deleteorigin",
                authName: "删除原文"
            },
            {
                authCode: "192:archives:wjjs:dzwj:editfile",
                authName: "编辑文件"
            },
            {
                authCode: "192:archives:wjjs:dzwj:editorigin",
                authName: "编辑原文"
            },
            {
                authCode: "192:archives:wjjs:dzwj:electricfile",
                authName: "电子文件"
            },
            {
                authCode: "192:archives:wjjs:dzwj:localdata",
                authName: "本地资料"
            },
            {
                authCode: "192:archives:wjjs:dzwj:look",
                authName: "查看"
            },
            {
                authCode: "192:archives:wjjs:dzwj:move",
                authName: "移动"
            },
            {
                authCode: "192:archives:wjjs:dzwj:movefile",
                authName: "上下移动文件"
            },
            {
                authCode: "192:archives:wjjs:dzwj:movenode",
                authName: "上下移动节点"
            },
            {
                authCode: "192:archives:wjjs:dzwj:moveorigin",
                authName: "上下移动原文"
            },
            {
                authCode: "192:archives:wjjs:dzwj:sign",
                authName: "签章"
            },
            {
                authCode: "192:archives:wjjs:dzwj:volume",
                authName: "组卷"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupName: "管理制度",
        parentId: "ProjectPlatform-EnvironmentManagement-System",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjgl:ysj",
        groupName: "元数据",
        parentId: "192:archives:wjgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:tjfx",
        groupName: "统计分析",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:tjfx:look",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:archives:dazl:gdgl",
        groupName: "归档管理",
        parentId: "192:archives:dazl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly",
        groupName: "档案利用",
        parentId: "192:archives",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:htgl",
        groupName: "合同管理",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Rectification-All",
        groupName: "全部",
        parentId: "ProjectPlatform-QualityManagement-inspect-Rectification",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "production-store-status",
        groupName: "台座状态",
        parentId: "production-store",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "production-store-status-edit",
                authName: "台座状态编辑"
            },
            {
                authCode: "production-store-status-plan",
                authName: "智能排产"
            }
        ]
    },
    {
        id: "192:archives:dazl:gdgl:ygd",
        groupName: "已归档",
        parentId: "192:archives:dazl:gdgl",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:archives:dazl:gdgl:ygd:look",
                authName: "查看"
            },
            {
                authCode: "192:archives:dazl:gdgl:ygd:revolume",
                authName: "重新归档"
            }
        ]
    },
    {
        id: "192:ins:zy",
        groupName: "主页",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 1,
        authCodeResultList: [
            {
                authCode: "192:ins:zy:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:zy:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:zy:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:zy:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:zy:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:zy:upload",
                authName: "上传表单附件"
            },
            {
                authCode: "192:ins:zy:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:sunpay:contract_manage",
        groupName: "合同管理",
        parentId: "192:sunpay:contract_manage",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dazl",
        groupName: "档案整理",
        parentId: "192:archives:dazl",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjgl",
        groupName: "文件管理",
        parentId: "192:archives:wjgl",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dabg",
        groupName: "档案保管",
        parentId: "192:archives:dabg",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "production-bind",
        groupName: "钢筋绑扎台座",
        parentId: "production-bind",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupName: "隐患排查",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:jcsz",
        groupName: "基础设置",
        parentId: "192:archives:jcsz",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly",
        groupName: "档案利用",
        parentId: "192:archives:daly",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:wjjs",
        groupName: "文件接收",
        parentId: "192:archives:wjjs",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-System",
        groupName: "质量体系",
        parentId: "ProjectPlatform-QualityManagement-System",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:dazl:gdgl",
        groupName: "归档管理",
        parentId: "192:archives:dazl:gdgl",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupName: "管理制度",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:archives:daly:dafw",
        groupName: "档案服务",
        parentId: "192:archives:daly:dafw",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Constructor",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System",
        groupName: "环保体系",
        parentId: "ProjectPlatform-EnvironmentManagement-System",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "192:htgl",
        groupName: "合同管理",
        parentId: "192:htgl",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Constructor",
        groupName: "建设方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Constructor",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-production",
        groupName: "生产",
        parentId: "ProjectPlatform-production",
        groupLevel: 0,
        display: 1,
        sort: 1,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-QualityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:pay_manage_pay",
        groupName: "支付",
        parentId: "192:sunpay:pay_manage",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Paper",
        groupName: "考试试卷",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Paper:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Paper:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Paper:Edit",
                authName: "编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-PersonnelManagement-EducationExamination-Paper:Qrcode",
                authName: "二维码"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-Disclosure-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-wbs-bind",
        groupName: "WBS映射",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-OrgStructure",
        groupName: "组织机构",
        parentId: "ProjectPlatform-EnvironmentManagement-System",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:financial",
        groupName: "资金情况",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-ProgressManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:financial_contractor",
        groupName: "总承包项目",
        parentId: "192:sunpay:financial",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect",
        groupName: "环保检查",
        parentId: "ProjectPlatform-EnvironmentManagement",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Routing-AllTask",
        groupName: "全部任务",
        parentId: "ProjectPlatform-SecurityManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:oversight_view",
        groupName: "查看申述",
        parentId: "192:sunpay:oversight",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "192:sunpay:oversight_view:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Responsibility-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:yfk",
        groupName: "预付款",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Check-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "production-store-list",
        groupName: "台座台账",
        parentId: "production-store",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "production-store-list-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration-Management",
        groupName: "管理人员",
        parentId: "ProjectPlatform-SecurityManagement-PersonnelManagement-PersonnelRegistration",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "PersonnelRegistration-Management:Add",
                authName: "新增"
            },
            {
                authCode: "PersonnelRegistration-Management:BatchExportQRCode",
                authName: "批量导出二维码"
            },
            {
                authCode: "PersonnelRegistration-Management:CertificateView",
                authName: "证书查看"
            },
            {
                authCode: "PersonnelRegistration-Management:Delete",
                authName: "删除"
            },
            {
                authCode: "PersonnelRegistration-Management:DownloadExcelTemplate",
                authName: "下载excel模板"
            },
            {
                authCode: "PersonnelRegistration-Management:Edit",
                authName: "编辑"
            },
            {
                authCode: "PersonnelRegistration-Management:Exit",
                authName: "退场"
            },
            {
                authCode: "PersonnelRegistration-Management:ExportLedger",
                authName: "导出台账"
            },
            {
                authCode: "PersonnelRegistration-Management:ImportExcel",
                authName: "导入excel"
            },
            {
                authCode: "PersonnelRegistration-Management:PrivacyView",
                authName: "身份证查看"
            },
            {
                authCode: "PersonnelRegistration-Management:QRCodeDownload",
                authName: "二维码下载"
            },
            {
                authCode: "PersonnelRegistration-Management:QRCodeView",
                authName: "二维码查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-DangerManage-Manage",
        groupName: "分级管控",
        parentId: "ProjectPlatform-SecurityManagement-DangerManage",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "DangerManage-Manage:Check",
                authName: "检查"
            },
            {
                authCode: "DangerManage-Manage:Download",
                authName: "下载"
            },
            {
                authCode: "DangerManage-Manage:QrCode",
                authName: "二维码"
            },
            {
                authCode: "DangerManage-Manage:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:qdgl:dbqd",
        groupName: "对比清单",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "192:qdgl:dbqd:check",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:sunpay:contract_manage_contractor",
        groupName: "总承包项目",
        parentId: "192:sunpay:contract_manage",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:ins:kgbb",
        groupName: "开工报告",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "192:ins:kgbb:add",
                authName: "添加表单"
            },
            {
                authCode: "192:ins:kgbb:back",
                authName: "退回"
            },
            {
                authCode: "192:ins:kgbb:batchdownload",
                authName: "批量导出"
            },
            {
                authCode: "192:ins:kgbb:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:kgbb:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:kgbb:down",
                authName: "下移"
            },
            {
                authCode: "192:ins:kgbb:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:kgbb:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:kgbb:forceback",
                authName: "强制撤回"
            },
            {
                authCode: "192:ins:kgbb:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:kgbb:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:kgbb:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:kgbb:template",
                authName: "应用模板"
            },
            {
                authCode: "192:ins:kgbb:up",
                authName: "上移"
            },
            {
                authCode: "192:ins:kgbb:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:kgbb:uploadtemp",
                authName: "上传模板"
            },
            {
                authCode: "192:ins:kgbb:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Manage-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:bgqz:xzdj",
        groupName: "新增单价",
        parentId: "192:bgqz",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Device",
        groupName: "设备管理",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement-inspect",
        groupName: "巡检检查",
        parentId: "ProjectPlatform-ProgressManagement",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "production-make",
        groupName: "制梁台座",
        parentId: "ProjectPlatform-production",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Duty-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Device-Special",
        groupName: "特种设备",
        parentId: "ProjectPlatform-SecurityManagement-Device",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Add",
                authName: "新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:AppearanceRecordAdd",
                authName: "出场记录-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:AppearanceRecordUpdate",
                authName: "出场记录-修改"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Delete",
                authName: "删除"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Download",
                authName: "下载"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Exit",
                authName: "退场"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Import",
                authName: "导出附件"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:InspectionRecordAdd",
                authName: "检查记录-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:InspectionRecordUpdate",
                authName: "检查记录-编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:MaintenanceAdd",
                authName: "维修保养-新增"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:MaintenanceUpdate",
                authName: "维修保养-编辑"
            },
            {
                authCode: "ProjectPlatform-SecurityManagement-Device-Special:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "material-room-new",
        groupName: "库房管理",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "material-room-new:add",
                authName: "新增"
            },
            {
                authCode: "material-room-new:del",
                authName: "删除"
            },
            {
                authCode: "material-room-new:update",
                authName: "修改"
            },
            {
                authCode: "material-room-new:update",
                authName: "导出"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Check",
        groupName: "隐患排查",
        parentId: "ProjectPlatform-SecurityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "inspect-Check:Add",
                authName: "新增"
            },
            {
                authCode: "inspect-Check:Delete",
                authName: "删除"
            },
            {
                authCode: "inspect-Check:Download",
                authName: "下载"
            },
            {
                authCode: "inspect-Check:InitiateRectification",
                authName: "发起整改"
            },
            {
                authCode: "inspect-Check:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "production-bind-list",
        groupName: "台座台账",
        parentId: "production-bind",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "production-bind-list-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "192:sunpay:payment_contractor",
        groupName: "总承包项目",
        parentId: "192:sunpay:payment",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "BIM-Management-System-Scene",
        groupName: "场景管理",
        parentId: "BIM-Management-System",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Routing-AllTask",
        groupName: "全部计划",
        parentId: "ProjectPlatform-QualityManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:jlzf:zjzxjl",
        groupName: "造价咨询计量",
        parentId: "192:jlzf",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "192:jlzf:zjzxjl:check",
                authName: "查看"
            },
            {
                authCode: "192:jlzf:zjzxjl:delete",
                authName: "删除"
            },
            {
                authCode: "192:jlzf:zjzxjl:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:jlzf:zjzxjl:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:jlzf:zjzxjl:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:jlzf:zjzxjl:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:jlzf:zjzxjl:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:jlzf:zjzxjl:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:jlzf:zjzxjl:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "BIM-Management-System-Model-FBX",
        groupName: "FBX模型",
        parentId: "BIM-Management-System-Model",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect",
        groupName: "质量检查",
        parentId: "ProjectPlatform-QualityManagement",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "workbench-knowledeg",
        groupName: "知识库",
        parentId: "ProjectPlatform-workbench",
        groupLevel: null,
        display: 1,
        sort: 2,
        authCodeResultList: [
            {
                authCode: "workbench-knowledeg:add",
                authName: "新增"
            },
            {
                authCode: "workbench-knowledeg:delete",
                authName: "删除"
            },
            {
                authCode: "workbench-knowledeg:down",
                authName: "下移"
            },
            {
                authCode: "workbench-knowledeg:edit",
                authName: "编辑"
            },
            {
                authCode: "workbench-knowledeg:top",
                authName: "置顶"
            },
            {
                authCode: "workbench-knowledeg:up",
                authName: "上移"
            }
        ]
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect",
        groupName: "质量检查",
        parentId: "ProjectPlatform-QualityManagement-inspect",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Supervisor",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement-inspect",
        groupName: "巡检检查",
        parentId: "ProjectPlatform-ProgressManagement-inspect",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect",
        groupName: "环保检查",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Device",
        groupName: "设备管理",
        parentId: "ProjectPlatform-SecurityManagement-Device",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-QualityManagement-inspect-Rectification",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:yfk",
        groupName: "预付款",
        parentId: "192:yfk",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Supervisor",
        groupName: "监理方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Supervisor",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "production-make",
        groupName: "制梁台座",
        parentId: "production-make",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:financial",
        groupName: "资金情况",
        parentId: "192:sunpay:financial",
        groupLevel: 0,
        display: 1,
        sort: 2,
        authCodeResultList: []
    },
    {
        id: "192:qdgl:hcqdsp",
        groupName: "核查清单审批",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "192:qdgl:hcqdsp:approve",
                authName: "核查清单提交审批"
            },
            {
                authCode: "192:qdgl:hcqdsp:check",
                authName: "核查清单查看审批"
            },
            {
                authCode: "192:qdgl:hcqdsp:delete",
                authName: "核查清单删除审批"
            },
            {
                authCode: "192:qdgl:hcqdsp:fill",
                authName: "核查清单填报审批"
            },
            {
                authCode: "192:qdgl:hcqdsp:insert",
                authName: "核查清单新增审批"
            },
            {
                authCode: "192:qdgl:hcqdsp:save",
                authName: "核查清单保存审批"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "production-store",
        groupName: "存梁台座",
        parentId: "ProjectPlatform-production",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Check-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Check",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-Disclosure-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Responsibility-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "192:processinspection:wddf",
        groupName: "我的待发",
        parentId: "192:processinspection",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Manage-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Manage",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupName: "技术交底",
        parentId: "ProjectPlatform-EnvironmentManagement",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-SecurityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupName: "岗位职责",
        parentId: "ProjectPlatform-EnvironmentManagement-System",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "workbench-recycle",
        groupName: "回收站",
        parentId: "ProjectPlatform-workbench",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "workbench-recycle:delete",
                authName: "彻底删除"
            },
            {
                authCode: "workbench-recycle:rollback",
                authName: "恢复"
            }
        ]
    },
    {
        id: "192:sunpay:payment",
        groupName: "请款管理",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "192:ins:gxjy:sg",
        groupName: "工序检验（施工）",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "192:ins:gxjy:sg:add",
                authName: "添加表单"
            },
            {
                authCode: "192:ins:gxjy:sg:back",
                authName: "退回"
            },
            {
                authCode: "192:ins:gxjy:sg:batchdownload",
                authName: "批量导出"
            },
            {
                authCode: "192:ins:gxjy:sg:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:gxjy:sg:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:gxjy:sg:down",
                authName: "下移"
            },
            {
                authCode: "192:ins:gxjy:sg:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:gxjy:sg:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:gxjy:sg:forceback",
                authName: "强制撤回"
            },
            {
                authCode: "192:ins:gxjy:sg:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:gxjy:sg:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:gxjy:sg:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:gxjy:sg:template",
                authName: "应用模板"
            },
            {
                authCode: "192:ins:gxjy:sg:up",
                authName: "上移"
            },
            {
                authCode: "192:ins:gxjy:sg:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:gxjy:sg:uploadtemp",
                authName: "上传模板"
            },
            {
                authCode: "192:ins:gxjy:sg:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-QualityManagement-System-Duty",
        groupName: "岗位职责",
        parentId: "ProjectPlatform-QualityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "QualityManagement-System-Duty:Add",
                authName: "新增"
            },
            {
                authCode: "QualityManagement-System-Duty:Delete",
                authName: "删除"
            },
            {
                authCode: "QualityManagement-System-Duty:Download",
                authName: "下载"
            },
            {
                authCode: "QualityManagement-System-Duty:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Duty-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "192:cltc",
        groupName: "材料调差",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-System-Duty",
        groupName: "岗位职责",
        parentId: "ProjectPlatform-SecurityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "System-Duty:Add",
                authName: "新增"
            },
            {
                authCode: "System-Duty:Delete",
                authName: "删除"
            },
            {
                authCode: "System-Duty:Download",
                authName: "下载"
            },
            {
                authCode: "System-Duty:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "material-in-new",
        groupName: "物资入库",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "material-in-new:add",
                authName: "新增"
            },
            {
                authCode: "material-in-new:del",
                authName: "删除"
            },
            {
                authCode: "material-in-new:export",
                authName: "导出"
            },
            {
                authCode: "material-in-new:update",
                authName: "修改"
            }
        ]
    },
    {
        id: "ProjectPlatform-QualityManagement-Disclosure",
        groupName: "技术交底",
        parentId: "ProjectPlatform-QualityManagement",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "QualityManagement-Disclosure:Add",
                authName: "新增"
            },
            {
                authCode: "QualityManagement-Disclosure:Delete",
                authName: "删除"
            },
            {
                authCode: "QualityManagement-Disclosure:Download",
                authName: "下载"
            },
            {
                authCode: "QualityManagement-Disclosure:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "production-make-list",
        groupName: "台座台账",
        parentId: "production-make",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "production-make-list-export",
                authName: "导出"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect",
        groupName: "安全检查",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity-meeting",
        groupName: "安全会议",
        parentId: "ProjectPlatform-SecurityManagement-Activity",
        groupLevel: null,
        display: 1,
        sort: 3,
        authCodeResultList: [
            {
                authCode: "Activity-meeting:add",
                authName: "新增"
            },
            {
                authCode: "Activity-meeting:del",
                authName: "删除"
            },
            {
                authCode: "Activity-meeting:update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:cltc",
        groupName: "材料调差",
        parentId: "192:cltc",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Rectification-Executor",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-SecurityManagement-inspect-Routing",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupName: "技术交底",
        parentId: "ProjectPlatform-EnvironmentManagement-Disclosure",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor",
        groupName: "施工方",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing-Executor",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect",
        groupName: "安全检查",
        parentId: "ProjectPlatform-SecurityManagement-inspect",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupName: "岗位职责",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Duty",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "production-store",
        groupName: "存梁台座",
        parentId: "production-store",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:payment",
        groupName: "请款管理",
        parentId: "192:sunpay:payment",
        groupLevel: 0,
        display: 1,
        sort: 3,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-SecurityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:bgqz",
        groupName: "变更签证",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:yfk:aqwmsgf",
        groupName: "安全文明施工费",
        parentId: "192:yfk",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "workbench.report.template",
        groupName: "报表管理",
        parentId: "ProjectPlatform-workbench",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "workbench.report.template:add",
                authName: "新增"
            },
            {
                authCode: "workbench.report.template:del",
                authName: "删除"
            },
            {
                authCode: "workbench.report.template:detail",
                authName: "查看"
            },
            {
                authCode: "workbench.report.template:update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:processinspection:wddb",
        groupName: "我的待办",
        parentId: "192:processinspection",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "material-out-new",
        groupName: "物资出库",
        parentId: "Projectplatform-material",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "material-out-new:add",
                authName: "新增"
            },
            {
                authCode: "material-out-new:export",
                authName: "导出"
            },
            {
                authCode: "material-out-new:update",
                authName: "修改"
            },
            {
                authCode: "material-out-new_del",
                authName: "删除"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-System-Responsibility",
        groupName: "责任书",
        parentId: "ProjectPlatform-SecurityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "SecurityManagement-System-Responsibility:Add",
                authName: "新增"
            },
            {
                authCode: "SecurityManagement-System-Responsibility:Delete",
                authName: "删除"
            },
            {
                authCode: "SecurityManagement-System-Responsibility:Download",
                authName: "下载"
            },
            {
                authCode: "SecurityManagement-System-Responsibility:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity-special",
        groupName: "专项活动",
        parentId: "ProjectPlatform-SecurityManagement-Activity",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "Activity-special:add",
                authName: "新增"
            },
            {
                authCode: "Activity-special:del",
                authName: "删除"
            },
            {
                authCode: "Activity-special:update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupName: "责任书",
        parentId: "ProjectPlatform-EnvironmentManagement-System",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:qdgl:xbgl",
        groupName: "修编管理",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "192:qdgl:xbgl:approve",
                authName: "修编管理提交审批"
            },
            {
                authCode: "192:qdgl:xbgl:check",
                authName: "修编管理查看审批"
            },
            {
                authCode: "192:qdgl:xbgl:delete",
                authName: "修编管理删除审批"
            },
            {
                authCode: "192:qdgl:xbgl:fill",
                authName: "修编管理填报审批"
            },
            {
                authCode: "192:qdgl:xbgl:insert",
                authName: "修编管理新增审批"
            },
            {
                authCode: "192:qdgl:xbgl:save",
                authName: "修编管理保存审批"
            }
        ]
    },
    {
        id: "192:sunpay:pay_manage",
        groupName: "支付管理",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-System-Responsibility",
        groupName: "责任书",
        parentId: "ProjectPlatform-QualityManagement-System",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "QualityManagement-System-Responsibility:Add",
                authName: "新增"
            },
            {
                authCode: "QualityManagement-System-Responsibility:Delete",
                authName: "删除"
            },
            {
                authCode: "QualityManagement-System-Responsibility:Download",
                authName: "下载"
            },
            {
                authCode: "QualityManagement-System-Responsibility:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:ins:jgpd:sg",
        groupName: "交工评定（施工）",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: [
            {
                authCode: "192:ins:jgpd:sg:add",
                authName: "添加表单"
            },
            {
                authCode: "192:ins:jgpd:sg:back",
                authName: "退回"
            },
            {
                authCode: "192:ins:jgpd:sg:batchdownload",
                authName: "批量导出"
            },
            {
                authCode: "192:ins:jgpd:sg:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:jgpd:sg:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:jgpd:sg:down",
                authName: "下移"
            },
            {
                authCode: "192:ins:jgpd:sg:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:jgpd:sg:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:jgpd:sg:forceback",
                authName: "强制撤回"
            },
            {
                authCode: "192:ins:jgpd:sg:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:jgpd:sg:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:jgpd:sg:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:jgpd:sg:template",
                authName: "应用模板"
            },
            {
                authCode: "192:ins:jgpd:sg:up",
                authName: "上移"
            },
            {
                authCode: "192:ins:jgpd:sg:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:jgpd:sg:uploadtemp",
                authName: "上传模板"
            },
            {
                authCode: "192:ins:jgpd:sg:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "production-monitor",
        groupName: "设备监控",
        parentId: "ProjectPlatform-production",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity",
        groupName: "安全活动",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "production-monitor",
        groupName: "设备监控",
        parentId: "production-monitor",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:bgqz",
        groupName: "变更签证",
        parentId: "192:bgqz",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:pay_manage",
        groupName: "支付管理",
        parentId: "192:sunpay:pay_manage",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Rectification",
        groupName: "整改记录",
        parentId: "ProjectPlatform-SecurityManagement-inspect-Rectification",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupName: "责任书",
        parentId: "ProjectPlatform-EnvironmentManagement-System-Responsibility",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-Activity",
        groupName: "安全活动",
        parentId: "ProjectPlatform-SecurityManagement-Activity",
        groupLevel: 0,
        display: 1,
        sort: 4,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:oversight",
        groupName: "监督审查",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "192:ins:gxjy:jl",
        groupName: "工序检验（监理）",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: [
            {
                authCode: "192:ins:gxjy:jl:add",
                authName: "添加表单"
            },
            {
                authCode: "192:ins:gxjy:jl:back",
                authName: "退回"
            },
            {
                authCode: "192:ins:gxjy:jl:batchdownload",
                authName: "批量导出"
            },
            {
                authCode: "192:ins:gxjy:jl:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:gxjy:jl:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:gxjy:jl:down",
                authName: "下移"
            },
            {
                authCode: "192:ins:gxjy:jl:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:gxjy:jl:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:gxjy:jl:forceback",
                authName: "强制撤回"
            },
            {
                authCode: "192:ins:gxjy:jl:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:gxjy:jl:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:gxjy:jl:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:gxjy:jl:template",
                authName: "应用模板"
            },
            {
                authCode: "192:ins:gxjy:jl:up",
                authName: "上移"
            },
            {
                authCode: "192:ins:gxjy:jl:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:gxjy:jl:uploadtemp",
                authName: "上传模板"
            },
            {
                authCode: "192:ins:gxjy:jl:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "192:qtzf:sp",
        groupName: "索赔",
        parentId: "192:qtzf",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: [
            {
                authCode: "192:qtzf:sp:check",
                authName: "查看"
            },
            {
                authCode: "192:qtzf:sp:delete",
                authName: "删除"
            },
            {
                authCode: "192:qtzf:sp:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:qtzf:sp:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:qtzf:sp:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:qtzf:sp:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:qtzf:sp:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:qtzf:sp:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:qtzf:sp:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "192:bgqz:gcqz",
        groupName: "工程签证",
        parentId: "192:bgqz",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: [
            {
                authCode: "192:bgqz:gcqz:attachment",
                authName: "签证附件上传/下载/删除"
            },
            {
                authCode: "192:bgqz:gcqz:check",
                authName: "查看"
            },
            {
                authCode: "192:bgqz:gcqz:delete",
                authName: "删除"
            },
            {
                authCode: "192:bgqz:gcqz:formapprove",
                authName: "表单审批"
            },
            {
                authCode: "192:bgqz:gcqz:formattachment",
                authName: "表单附件上传/下载/删除"
            },
            {
                authCode: "192:bgqz:gcqz:formdelete",
                authName: "表单删除"
            },
            {
                authCode: "192:bgqz:gcqz:formsign",
                authName: "表单签章"
            },
            {
                authCode: "192:bgqz:gcqz:formsubmit",
                authName: "表单保存"
            },
            {
                authCode: "192:bgqz:gcqz:formupdate",
                authName: "表单编辑"
            },
            {
                authCode: "192:bgqz:gcqz:insert",
                authName: "新增"
            }
        ]
    },
    {
        id: "192:jlzf",
        groupName: "计量支付",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-ProgressManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-ProgressManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "workbench.report.fill",
        groupName: "报表填报",
        parentId: "ProjectPlatform-workbench",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: [
            {
                authCode: "workbench.report.fill:add",
                authName: "填报"
            },
            {
                authCode: "workbench.report.fill:detail",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-QualityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-inspect-Score",
        groupName: "安全评分",
        parentId: "ProjectPlatform-SecurityManagement-inspect",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: [
            {
                authCode: "inspect-Score:Add",
                authName: "新增"
            },
            {
                authCode: "inspect-Score:Delete",
                authName: "删除"
            },
            {
                authCode: "inspect-Score:Download",
                authName: "下载"
            },
            {
                authCode: "inspect-Score:InitiateRectification",
                authName: "发起整改"
            },
            {
                authCode: "inspect-Score:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-DangerManage",
        groupName: "危险源管理",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-QualityManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-QualityManagement-inspect-Routing",
        groupLevel: 0,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "192:sunpay:oversight",
        groupName: "监督审查",
        parentId: "192:sunpay:oversight",
        groupLevel: 0,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-DangerManage",
        groupName: "危险源管理",
        parentId: "ProjectPlatform-SecurityManagement-DangerManage",
        groupLevel: 0,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "192:jlzf",
        groupName: "计量支付",
        parentId: "192:jlzf",
        groupLevel: 0,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupName: "巡检任务",
        parentId: "ProjectPlatform-EnvironmentManagement-inspect-Routing",
        groupLevel: 0,
        display: 1,
        sort: 5,
        authCodeResultList: []
    },
    {
        id: "192:ins:jgpd:jl",
        groupName: "交工评定（监理）",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 6,
        authCodeResultList: [
            {
                authCode: "192:ins:jgpd:jl:add",
                authName: "添加表单"
            },
            {
                authCode: "192:ins:jgpd:jl:back",
                authName: "退回"
            },
            {
                authCode: "192:ins:jgpd:jl:batchdownload",
                authName: "批量导出"
            },
            {
                authCode: "192:ins:jgpd:jl:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:jgpd:jl:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:jgpd:jl:down",
                authName: "下移"
            },
            {
                authCode: "192:ins:jgpd:jl:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:jgpd:jl:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:jgpd:jl:forceback",
                authName: "强制撤回"
            },
            {
                authCode: "192:ins:jgpd:jl:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:jgpd:jl:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:jgpd:jl:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:jgpd:jl:template",
                authName: "应用模板"
            },
            {
                authCode: "192:ins:jgpd:jl:up",
                authName: "上移"
            },
            {
                authCode: "192:ins:jgpd:jl:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:jgpd:jl:uploadtemp",
                authName: "上传模板"
            },
            {
                authCode: "192:ins:jgpd:jl:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:qtzf",
        groupName: "其他支付",
        parentId: "192:meter",
        groupLevel: null,
        display: 1,
        sort: 6,
        authCodeResultList: []
    },
    {
        id: "ProjectPlatform-SecurityManagement-RiskProject",
        groupName: "危大工程",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 6,
        authCodeResultList: [
            {
                authCode: "RiskProject:Add",
                authName: "新增"
            },
            {
                authCode: "RiskProject:Delete",
                authName: "删除"
            },
            {
                authCode: "RiskProject:Download",
                authName: "下载"
            },
            {
                authCode: "RiskProject:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "192:qdgl:jrg",
        groupName: "计日工",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 6,
        authCodeResultList: []
    },
    {
        id: "192:qtzf",
        groupName: "其他支付",
        parentId: "192:qtzf",
        groupLevel: 0,
        display: 1,
        sort: 6,
        authCodeResultList: []
    },
    {
        id: "192:qdgl:mxqd",
        groupName: "模型清单",
        parentId: "192:qdgl",
        groupLevel: null,
        display: 1,
        sort: 7,
        authCodeResultList: []
    },
    {
        id: "192:ins:rwzx:df",
        groupName: "任务中心-我的待发",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 7,
        authCodeResultList: [
            {
                authCode: "192:ins:rwzx:df:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:rwzx:df:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:rwzx:df:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:rwzx:df:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:rwzx:df:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:rwzx:df:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:rwzx:df:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:rwzx:df:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:rwzx:df:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-Disclosure",
        groupName: "技术交底",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 7,
        authCodeResultList: [
            {
                authCode: "Disclosure:Add",
                authName: "新增"
            },
            {
                authCode: "Disclosure:Delete",
                authName: "删除"
            },
            {
                authCode: "Disclosure:Download",
                authName: "下载"
            },
            {
                authCode: "Disclosure:Update",
                authName: "编辑"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-ElectricManagement",
        groupName: "临电管理",
        parentId: "ProjectPlatform-SecurityManagement",
        groupLevel: null,
        display: 1,
        sort: 8,
        authCodeResultList: []
    },
    {
        id: "192:ins:rwzx:db",
        groupName: "任务中心-我的待办",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 8,
        authCodeResultList: [
            {
                authCode: "192:ins:rwzx:db:delattach",
                authName: "删除附件"
            },
            {
                authCode: "192:ins:rwzx:db:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:rwzx:db:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:rwzx:db:edit",
                authName: "编辑表单"
            },
            {
                authCode: "192:ins:rwzx:db:labreport",
                authName: "引用试验报告"
            },
            {
                authCode: "192:ins:rwzx:db:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:rwzx:db:sign",
                authName: "签名签章"
            },
            {
                authCode: "192:ins:rwzx:db:upload",
                authName: "上传本地附件"
            },
            {
                authCode: "192:ins:rwzx:db:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "ProjectPlatform-SecurityManagement-ElectricManagement",
        groupName: "临电管理",
        parentId: "ProjectPlatform-SecurityManagement-ElectricManagement",
        groupLevel: 0,
        display: 1,
        sort: 8,
        authCodeResultList: []
    },
    {
        id: "192:ins:rwzx:yb",
        groupName: "任务中心-我的已办",
        parentId: "192:inspection",
        groupLevel: null,
        display: 1,
        sort: 9,
        authCodeResultList: [
            {
                authCode: "192:ins:rwzx:yb:delform",
                authName: "删除表单"
            },
            {
                authCode: "192:ins:rwzx:yb:download",
                authName: "下载附件"
            },
            {
                authCode: "192:ins:rwzx:yb:preview",
                authName: "表单预览"
            },
            {
                authCode: "192:ins:rwzx:yb:view",
                authName: "查看"
            }
        ]
    },
    {
        id: "192:sunpay:counter_account",
        groupName: "统计台账",
        parentId: "192:sunpay",
        groupLevel: null,
        display: 1,
        sort: 10,
        authCodeResultList: []
    },
    {
        id: "192:sunpay",
        groupName: "支付",
        parentId: "EnterprisePlatform",
        groupLevel: null,
        display: 1,
        sort: 20,
        authCodeResultList: []
    },
    {
        id: "192:archives",
        groupName: "档案",
        parentId: "ProjectPlatform",
        groupLevel: null,
        display: 1,
        sort: 20,
        authCodeResultList: []
    },
    {
        id: "192:archives",
        groupName: "档案",
        parentId: "192:archives",
        groupLevel: 0,
        display: 1,
        sort: 20,
        authCodeResultList: []
    },
    {
        id: "192:sunpay",
        groupName: "支付",
        parentId: "192:sunpay",
        groupLevel: 0,
        display: 1,
        sort: 20,
        authCodeResultList: []
    }
];
