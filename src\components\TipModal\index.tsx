import {ExclamationCircleFilled} from "@ant-design/icons";
import {Button, ButtonProps, Modal, ModalFuncProps, ModalProps} from "antd";
import React, {ReactNode, useState} from "react";
import {createUseStyles} from "react-jss";
import Color from "../../assets/css/Color";
import BtnIcon from "../MyIcon/BtnIcon";

interface DeleteModalProps extends ModalProps {
    children?: ReactNode;
    onOk: ModalFuncProps["onOk"];
    content?: ReactNode | string;
    btnText?: string;
    buttonConfig?: ButtonProps;
}


const useStyle = createUseStyles({
    tipIcon: {
        color: Color["yellow-1"],
        marginRight: 16,
        fontSize: 22
    },
    title: {
        display: "flex"
    },
    tipTitle: {
        color: Color["text-2"],
        fontWeight: "bold",
        fontSize: 16,
    },
    tipContent: {
        color: Color["text-2"],
        fontSize: 14,
        margin: "8px 38px"
    },
});

const TipModal = (props: DeleteModalProps) => {
    const cls = useStyle();
    const {children, onOk, buttonConfig, content, btnText = "删除", ...other} = props;
    const [visible, setVisible] = useState(false);

    const handleOk = () => {
        setVisible(false);
        if (onOk !== undefined) {
            onOk();
        }
    };

    const handleClick = () => {
        setVisible(true);
    };

    return (
        <>
            <div onClick={handleClick} style={{display: "inline-block"}}>
                <Button icon={<BtnIcon type="icon-lajitong" />} {...buttonConfig}>{btnText}</Button>
            </div>
            <Modal
                visible={visible}
                onCancel={() => setVisible(false)}
                onOk={handleOk}
                width={416}
                maskClosable={false}
                destroyOnClose
                {...other}
            >
                {children !== undefined
                    ? children
                    : (
                        <>
                            <div className={cls.title}>
                                <ExclamationCircleFilled className={cls.tipIcon} />
                                <div className={cls.tipTitle}>提示</div>
                            </div>
                            <div className={cls.tipContent}>
                                {content !== undefined ? content : "是否确定删除？"}
                            </div>
                        </>
                    )}
            </Modal>
        </>
    );
};

export default TipModal;
