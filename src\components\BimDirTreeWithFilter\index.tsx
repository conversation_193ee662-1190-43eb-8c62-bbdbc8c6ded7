import React, {Key, useCallback, useState} from "react";
import {Col, Row, Select, Tree, TreeProps} from "antd";
import {DownOutlined} from "@ant-design/icons";
import {dfsTree, filterTree, isNonEmptyArray} from "../../assets/ts/utils";
import {DirTreeWithPath} from "../../reMotor";
import MyInputSearch from "../MyInputSearch";

const {Option} = Select;
const {TreeNode} = Tree;

type ProcessBind = "defined" | "all";
interface BimDirTreeWithFilterProps extends TreeProps {
    /** 已定义工序的构件path集合 */
    definedProcessPaths?: string[];
    bimDirTree?: DirTreeWithPath[];
    hasSelector?: boolean;
}

const getParentKey = (key: string, tree: DirTreeWithPath[]): string => {
    let parentKey = "";
    for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (isNonEmptyArray(node.children)) {
            if (node.children.some((item) => item.path === key)) {
                parentKey = node.path;
            } else {
                const newKey = getParentKey(key, node.children);
                if (newKey !== "") {
                    parentKey = newKey;
                }
            }
        }
    }
    return parentKey;
};

const BimDirTreeWithFilter = (props: BimDirTreeWithFilterProps) => {
    const {definedProcessPaths = [], bimDirTree = [], hasSelector = true, ...rest} = props;
    const [processBindType, setProcessBindType] = useState<ProcessBind>("all");
    const [keywords, setKeywords] = useState<string>("");
    const [expandedKeys, setExpandedKeys] = useState<string[]>([bimDirTree[0]?.path ?? ""]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

    const handleProcessBindTypechange = useCallback(
        (val: string) => {
            setProcessBindType(val as ProcessBind);
        },
        []
    );

    const handleSearchChange = useCallback(
        (val: string) => {
            if (val === "") {
                setExpandedKeys([bimDirTree[0]?.path ?? ""]);
                setAutoExpandParent(false);
                setKeywords(val);
            } else {
                const filterBimDirTree = filterTree(bimDirTree, (node) => node.name.includes(val)) ?? [];
                const newExpandedKeys: string[] = [];
                dfsTree(filterBimDirTree, (node) => !isNonEmptyArray(node.children) && newExpandedKeys.push(node.path));
                setExpandedKeys(newExpandedKeys);
                setAutoExpandParent(true);
                setKeywords(val);
            }
        },
        [bimDirTree]
    );

    const handleExpand = useCallback(
        (keys: Key[]) => {
            setExpandedKeys(keys as string[]);
            setAutoExpandParent(false);
        },
        []
    );

    const renderName = useCallback(
        (name: string) => {
            if (keywords !== "" && name.includes(keywords)) {
                const index = name.indexOf(keywords);
                const beforeStr = name.substring(0, index);
                const afterStr = name.slice(index + keywords.length);
                return (
                    <span>
                        {beforeStr}
                        <span style={{background: "#FFFF00"}}>{keywords}</span>
                        {afterStr}
                    </span>
                );
            }
            return (
                <span>{name}</span>
            );
        },
        [keywords]
    );

    const renderTreeNode = useCallback(
        (data: DirTreeWithPath[]) => data.map((v) => {
            if (Array.isArray(v.children) && v.children.length > 0) {
                return (
                    <TreeNode key={v.path} title={renderName(v.name)}>
                        {renderTreeNode(v.children ?? [])}
                    </TreeNode>
                );
            }
            return (
                <TreeNode key={v.path} title={renderName(v.name)} />
            );
        }),
        [renderName]
    );

    /* const memoBimDirTreeData = useMemo(
        () => {
            if (!hasSelector || processBindType === "all") {
                if (keywords === "") {
                    return bimDirTree;
                }
                return filterTree(bimDirTree, (node) => node.name.includes(keywords)) ?? [];
            }

            if (definedProcessPaths.length === 0) {
                return [];
            }

            if (keywords === "") {
                return filterTree(bimDirTree, (node) => definedProcessPaths.includes(node.path)) ?? [];
            }

            const newTree = filterTree(bimDirTree, (node) => definedProcessPaths.includes(node.path)) ?? [];
            return filterTree(newTree, (node) => node.name.includes(keywords)) ?? [];
        },
        [bimDirTree, definedProcessPaths, hasSelector, keywords, processBindType]
    ); */

    return (
        <>
            <Row gutter={8} style={{marginBottom: "16px"}}>
                {hasSelector && (
                    <Col flex="160px">
                        <Select
                            style={{width: "100%"}}
                            value={processBindType}
                            onChange={handleProcessBindTypechange}
                        >
                            <Option key="defined">已定义工序部位</Option>
                            <Option key="all">全部部位</Option>
                        </Select>
                    </Col>
                )}
                <Col flex="auto">
                    <MyInputSearch
                        placeholder="输入部位名称"
                        onSearch={handleSearchChange}
                        allowClear
                        value={keywords}
                    />
                </Col>
            </Row>
            <Tree
                checkable
                blockNode
                switcherIcon={<DownOutlined />}
                expandedKeys={expandedKeys}
                onExpand={handleExpand}
                autoExpandParent={autoExpandParent}
                {...rest}
            >
                {renderTreeNode(bimDirTree)}
            </Tree>
        </>
    );
};

export default BimDirTreeWithFilter;
