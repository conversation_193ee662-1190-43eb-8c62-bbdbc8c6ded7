/* eslint-disable */
import {message} from "antd";
import {Attachment, FileInfo} from "./models/attachment";
import store from "../../store";
import {getSuffix} from "../../assets/ts/utils";
import {FileType} from "../../api/common.type";
import {getListApprovalLineNode} from "../../api/rectification/index";
import {ApprovalRole, Flow, FlowState, Person, RectificationActionType} from "./models/rectification";
import {CustomFieldData, CustomFieldMode, CustomFieldType, OptionType, projectTypeMapping, RefInfo, RelateBimInfo, RelateDocInfo, ServerBimInfo, TreeNodeType} from "./models/custom-field";
import {ExamineRecord, ApprovalCommentVo, ApprovalUserVos, AvatarModel, FileItem, ListApprovalLineNode, OptionDetail, ServerCustomFieldData, ServerCustomFieldPersonData} from "../../api/rectification/models/approval";
import {ApprovalOperationAttachmentSaveParam, ComponentJsonValueSaveParam, ComponentValueDetailWrap, ComponentValueDetail, ApprovalOperationAttachmentVo} from "../../api/rectification/models/process";


export enum ServerCustomFieldAuth {
    Hide = 1,
    Readable = 2,
    Editable = 4
}

export type CustomFieldValueType = string | string[] | number | undefined;

export const transformOptionIds = (j: ServerCustomFieldData) => {
    if (j.type === "PERSON") {
        return (j.optionIds as ServerCustomFieldPersonData[] || []).map((p) => p.userName);
    }
    if (j.type === "ATTACHMENT") {
        return typeof j.attachments === "string" ? j.attachments : JSON.stringify(j.attachments);
    }
    if (j.type === "DROP_DOWN_BOX") {
        if (j.booleanAttributes && j.booleanAttributes.find((b) => b.name === "MULTI_SELECT")!.value) {
            return j.optionIds as string[];
        }

        return j.optionIds ? (j.optionIds as string[])[0] : undefined;
    }

    return j.optionIds as string[];
};

export const transformCustomFieldMap = (
    list?: ComponentValueDetailWrap[][]
): Map<string, string | string[]> => {
    const temp: ServerCustomFieldData[] = (list ?? []).flat().map((v) => JSON.parse(v.componentJson!));
    return new Map(temp.map((v) => [v.id, transformOptionIds(v) ?? v.value ?? ""]));
};

export const transerFileToAttachment = (list: FileType[]): ApprovalOperationAttachmentSaveParam[] => list.map((v) => ({
    md5: v.fileUuid,
    name: v.fileName,
    size: v.fileSize,
    uuid: v.fileUuid,
    extension: getSuffix(v.fileName)
}));

export const getButtonVisiblePolicy = (flowState: FlowState, roles: ApprovalRole[], startFlowNode: boolean) => {
    let policy = 0;
    const hasIp = roles.includes(ApprovalRole.InitialPerson);
    const hasAp = roles.includes(ApprovalRole.ApprovalPerson);
    const hasCp = roles.includes(ApprovalRole.CopyPerson);

    if (hasIp && !hasAp && !hasCp && !startFlowNode
        && (flowState === FlowState.InProgress || flowState === FlowState.Declined)) {
        // approvalRole（发起人），processStatue（进行中）：【撤销】
        policy = 1;
    } else if (hasIp && startFlowNode && !hasCp
        && (flowState === FlowState.Revoked || flowState === FlowState.Declined)) {
        // approvalRole（发起人），processStatue（已退回、已撤销）,退回到的是发起人节点,显示【再次提交】【删除】
        policy = 2;
    } else if (hasIp && hasAp && startFlowNode === false && !hasCp
        && (flowState === FlowState.Revoked || flowState === FlowState.Declined || flowState === FlowState.InProgress)) {
        // approvalRole（发起人+审批人），processStatue（已退回、已撤销）,退回到的是审批人节点，则应显示【提交】【退回】【抄送】【撤销】
        policy = 6;
    } else if (hasAp && !hasIp
        && (flowState === FlowState.InProgress || flowState === FlowState.Declined || flowState === FlowState.Revoked)) {
        // approvalRole（审批人）：【提交】【退回】【抄送】
        policy = 3;
    } else if (roles.length === 0 && flowState !== FlowState.Done) {
        // approvalRole（审批人）：审批人操作过后再查看该审批，中间审批人查看已处理流程
        policy = 4;
    } else if (hasCp && !hasIp && !hasAp && flowState !== FlowState.Done) {
        // approvalRole（抄送人）：【评论】
        policy = 5;
    } else if (hasIp && hasCp && !hasAp && startFlowNode && flowState !== FlowState.Done
        && flowState !== FlowState.Revoked
        && flowState !== FlowState.Declined) {
        policy = 7;
    } else if (hasIp && hasCp && !hasAp && !startFlowNode && flowState !== FlowState.Done) {
        policy = 8;
    } else if (hasIp && hasAp && hasCp && startFlowNode && flowState !== FlowState.Done) {
        policy = 9;
    } else if (hasIp && hasAp && hasCp && !startFlowNode && flowState !== FlowState.Done) {
        policy = 10;
    } else if (flowState === FlowState.Done) {
        policy = 11;
    }

    return policy;
};

/**
 * 当前任务所处状态及对应的可操作类型
 */
export const rules = new Map<number, RectificationActionType[]>([
    [1, [RectificationActionType.Revoke]],
    [2, [RectificationActionType.Submit, RectificationActionType.Delete]],
    [
        3, [
            RectificationActionType.Submit,
            RectificationActionType.Decline,
            RectificationActionType.CopyTo,
            RectificationActionType.HandOver
        ]
    ],
    [4, []],
    [5, [RectificationActionType.Submit]],
    [
        6, [
            RectificationActionType.Submit,
            RectificationActionType.Decline,
            RectificationActionType.CopyTo,
            RectificationActionType.Revoke,
            RectificationActionType.HandOver
        ]
    ],
    [7, [RectificationActionType.Submit, RectificationActionType.Delete]],
    [8, [RectificationActionType.Submit, RectificationActionType.Revoke]],

    [9, [RectificationActionType.Submit, RectificationActionType.Delete]],
    [
        10, [
            RectificationActionType.Submit,
            RectificationActionType.Decline,
            RectificationActionType.CopyTo,
            RectificationActionType.Revoke,
            RectificationActionType.HandOver
        ]
    ],
    [11, [RectificationActionType.Print]]
]);

export const transformJsonValues = (values?: ComponentValueDetailWrap[][]) => {
    if (Array.isArray(values)) {
        return values.flat().map((v) => {
            const temp = JSON.parse(v.componentJson ?? "");
            return transformServerDataToCustomFieldData(temp);
        });
    }
    return [];
};

export const setIsReturnBackFn = (processStatus: string) => processStatus === FlowState.Declined || processStatus === FlowState.Revoked;

export const transformServerDataToCustomFieldData = (j: ComponentValueDetail): CustomFieldData => {
    const requiredAttr = j.booleanAttributes!.find((b) => b.name === "REQUIRED");
    const hasHistory = !!j.hasHistory;
    const placeholderAttr = j.stringAttributes!.find((s) => s.name === "PLACE_HOLDER");
    const titleAttr = j.stringAttributes!.find((a) => a.name === "TITLE");
    const type = getCustomFieldTypeFromServerData(j);
    const getData = () => {
        if (j.optionAttributes!.find((o) => o.name === "OPTION")) {
            return j.optionAttributes!.find((o) => o.name === "OPTION")!.options as OptionType[] || []
        }
        return type === CustomFieldType.Person ? [] : ""

    }
    return {
        id: j.id!,
        name: titleAttr && titleAttr.value ? titleAttr.value : "",
        type: type || CustomFieldType.Input,
        visible: (j.auth! & ServerCustomFieldAuth.Hide) === 0,
        mode: (j.auth! & 0xFE) as CustomFieldMode,
        required: requiredAttr && requiredAttr.value ? requiredAttr.value : false,
        data: getData(),
        hint: placeholderAttr && placeholderAttr.value ? placeholderAttr.value : "",
        maxLength: getMaxLengthFromServerData(j),
        valueId: j.valueId!,
        hasHistory,
        // defaultValue: j.value
    };
};

export const getMaxLengthFromServerData = (serverData: ComponentValueDetail) => {
    if (serverData.type === "PERSON") {
        return Infinity;
    }
    if (!serverData.booleanAttributes || !serverData.stringAttributes) {
        return Infinity;
    }
    const personMulti = serverData.booleanAttributes.find((b) => b.name === "PERSON_MULTI");
    if (personMulti && !personMulti.value) {
        return Infinity;
    }
    if (serverData.type === "DROP_DOWN_BOX") {
        if (serverData.booleanAttributes.find((b) => b.name === "MULTI_SELECT")!.value) {
            return Infinity;
        }

        return 1;
    }
    const maxLen = serverData.stringAttributes.find((s) => s.name === "STR_MAX_LENGTH");
    if (!maxLen || !maxLen.value) {
        return Infinity;
    }
    return parseInt(maxLen.value, 10);
};

// invariance
export const transformCustomFieldType = (type: CustomFieldType) => {
    const data = {
        [CustomFieldType.Person]: "PERSON",
        [CustomFieldType.Input]: "SINGLE_LINE_TEXT",
        [CustomFieldType.TextArea]: "MULTI_LINE_TEXT",
        [CustomFieldType.Select]: "DROP_DOWN_BOX",
        [CustomFieldType.MultiSelect]: "DROP_DOWN_BOX",
        [CustomFieldType.DateInput]: "DATE",
        [CustomFieldType.NumberInput]: "NUMBER",
        [CustomFieldType.Attachment]: "ATTACHMENT",
        [CustomFieldType.REF_BIM]: "REF_BIM",
        [CustomFieldType.REF_DOC]: "REF_DOC",
        [CustomFieldType.REF_PROC]: "REF_PROC"
    };
    return data[type];
};

// covariance
export const getCustomFieldTypeFromServerData = (serverData: ComponentValueDetail) => {
    if (serverData.type === "PERSON") {
        return CustomFieldType.Person;
    }
    if (serverData.type === "SINGLE_LINE_TEXT") {
        return CustomFieldType.Input;
    }
    if (serverData.type === "MULTI_LINE_TEXT") {
        return CustomFieldType.TextArea;
    }
    if (serverData.type === "DROP_DOWN_BOX") {
        if (serverData.booleanAttributes && serverData.booleanAttributes.find((b) => b.name === "MULTI_SELECT")!.value) {
            return CustomFieldType.MultiSelect;
        }

        return CustomFieldType.Select;
    }
    if (serverData.type === "NUMBER") {
        return CustomFieldType.NumberInput;
    }
    if (serverData.type === "DATE") {
        return CustomFieldType.DateInput;
    }
    if (serverData.type === "ATTACHMENT") {
        return CustomFieldType.Attachment;
    }
    if (serverData.type === "REF_BIM") {
        return CustomFieldType.REF_BIM;
    }
    if(serverData.type === "REF_DOC") {
        return CustomFieldType.REF_DOC;
    }
    if(serverData.type === "REF_PROC") {
        return CustomFieldType.REF_PROC;
    }
    return CustomFieldType.Person;
};

/* values类型未知 */
export const transformFormDataToJsonValues = (values: any, customFieldData: CustomFieldData[]) => Object.keys(values).map((key) => {
    const field = customFieldData.find((c) => c.id === key);
    if (!field) {
        return undefined;
    }
    const data: {
        componentId: string;
        optionIds?: string | string[];
        value?: string;
        attachments?: FileInfo[];
        refType?: string;
        valueElements?: RefInfo[];
        docValueElements?: RelateDocInfo[];
    } = {
        componentId: field.id
    };
    if (field.type === CustomFieldType.Person || field.type === CustomFieldType.MultiSelect || field.type === CustomFieldType.Select) {
        data.optionIds = values[key] || [];
    } else if (field.type === CustomFieldType.Attachment && values[key]) {
        const info = JSON.parse(values[key]);
        data.attachments = info;
    } else if (field.type === CustomFieldType.REF_BIM && values[key]) {
        const info: RelateBimInfo = JSON.parse(values[key]);
        const serverInfo = transfromRelateBimInfoToServerInfo(info);
        data.refType = serverInfo.refType;
        data.valueElements = serverInfo.valueElements;
    } else if(field.type === CustomFieldType.REF_DOC && values[key]) {
        const info = JSON.parse(values[key]);
        data.docValueElements = info;
        data.refType = "REF_DOC"
    }
    else {
        data.value = values[key];
    }
    if(!values[key] && (field.type === CustomFieldType.REF_DOC || field.type === CustomFieldType.REF_PROC)) {
        return undefined;
    }
    else {
        return {
            jsonVaule: JSON.stringify(data),
            type: transformCustomFieldType(field.type)
        }; 
    }
    
}).filter((j) => j).map((j) => j!);

export const transformDataToJsonValues = (info: Map<string, string | string[]>,
    customFields: CustomFieldData[]) =>
    Array.from(info.entries()).map(([id, value]) => {
        const field = customFields.find((c) => c.id === id);
        if (!field) {
            return undefined;
        }
        const data: {
            componentId: string;
            valueId: string | null;
            optionIds?: string | string[];
            value?: string | string[];
            attachments?: FileInfo[];
            refType?: string;
            valueElements?: RefInfo[];
            docValueElements?: RelateDocInfo[];
        } = {
            componentId: field.id,
            valueId: null
        };
        if (field.type === CustomFieldType.Person || field.type === CustomFieldType.MultiSelect || field.type === CustomFieldType.Select) {
            data.optionIds = value;
        } else if (field.type === CustomFieldType.Attachment) {
            let info: FileInfo[] = [];
            if(value) {
                info = JSON.parse(value as string);
            } else if (field.data) {
                info = JSON.parse(field.data as string)
            }   
            data.attachments = info;
        } else if (field.type === CustomFieldType.REF_BIM) {
            let info: ServerBimInfo = {refType: '', valueElements: []};
            if(value) {
                info = JSON.parse(value as string);
            } else if (field.data) {
                info = JSON.parse(field.data as string);
            }
            data.refType = info.refType;
            data.valueElements = info.valueElements;
        } else if(field.type === CustomFieldType.REF_DOC){
            let info: RelateDocInfo[] = [];
            info = value ? JSON.parse(value as string) : field.data.length !== 0 ? JSON.parse(field.data as string) : [];
            data.docValueElements = info;
            data.refType = "REF_DOC"
        } else {
            data.value = value;
        }
        data.valueId = field.valueId;
        if(field.type === CustomFieldType.REF_PROC) {
            return undefined;
        }
        return {
            jsonVaule: JSON.stringify(data),
            type: transformCustomFieldType(field.type)
        }!;
    }).filter((j) => j).map((j) => j!);

export const transfromRelateBimInfoToServerInfo = (relateInfo: RelateBimInfo): ServerBimInfo => {
    const getValueElements = (relateInfo: RelateBimInfo) => {
        let refInfo: RefInfo[] = [];
        // 关联工程
        if (relateInfo.bindType === "PROXY_PROJ") {
            refInfo.push({
                refKey: relateInfo.bimInfo.nPPid.toString(),
                refValueDescs: [{
                    descName: "PROJ_NAME",
                    descValue: relateInfo.bimInfo.projName,
                }, {
                    descName: "PROJ_TYPE",
                    descValue: relateInfo.bimInfo.projType.toString()
                }]
            })
        }
        // 关联类别
        else if (relateInfo.bindType === "BIM_CLAS" && relateInfo.binds) {
            refInfo = relateInfo.binds.map(bind => ({
                refKey: bind.subClass || "",
                refValueDescs: [{
                    descName: "PROJ_NAME",
                    descValue: relateInfo.bimInfo.projName,
                }, {
                    descName: "COMP_CLASS",
                    descValue: bind.compClass || ""
                }, {
                    descName: "FLOOR",
                    descValue: bind.floor || ""
                }, {
                    descName: "PPID",
                    descValue: relateInfo.bimInfo.nPPid.toString()
                }, {
                    descName: "PROJ_TYPE",
                    descValue: TreeNodeType.AzModel !== relateInfo.bimInfo.projType ? projTypetoStr(transformProjectTypeBack(relateInfo.bimInfo.projType)) : bind.spec || ""
                }]
            }))
        }
        // 关联构件
        else if(relateInfo.bindType === "BIM_COMP" && relateInfo.binds) {
            refInfo = relateInfo.binds.map(bind => ({
                refKey: bind.handle || "",
                refValueDescs: [{
                    descName: "PROJ_NAME",
                    descValue: relateInfo.bimInfo.projName,
                }, {
                    descName: "COMP_CLASS",
                    descValue: bind.compClass || ""
                }, {
                    descName: "FLOOR",
                    descValue: bind.floor || ""
                }, {
                    descName: "PPID",
                    descValue: relateInfo.bimInfo.nPPid.toString()
                }, {
                    descName: "PROJ_TYPE",
                    descValue: relateInfo.bimInfo.projType.toString()
                }]
            }))
        }
        return refInfo;
    }
    return {
        refType: relateInfo.bindType,
        valueElements: getValueElements(relateInfo)
    }
};

export function transformProjectTypeBack(type: TreeNodeType) {
    return new Map(projectTypeMapping.map(([k, v]) => [v, k])).get(type) || 1;
};

export function transformProjectType(type: number) {
    return new Map(projectTypeMapping).get(type) || TreeNodeType.Project;
};

export function projTypetoStr(iprojType: number) {
    // 设置BIM
    let projType = "";
    switch (iprojType) {
        case 1:
            projType = "土建";
            break;
        case 2:
            projType = "安装";
            break;
        case 3:
            projType = "钢筋";
            break;
        case 4:
            projType = "Revit";
            break;
        case 5:
            projType = "Tekla";
            break;
        case 8:
            projType = "班筑家装";
            break;
        case 9:
            projType = "场布";
            break;
        case 10:
            projType = "Civil3D";
            break;
        case 11:
            projType = "Bentley";
            break;
        case 12:
            projType = "Rhino";
            break;
        case 13:
            projType = "IFC";
            break;
        case 14:
            projType = "CATIA";
            break;
        default:
            throw new TypeError("unexpected type");
    }

    return projType;
};

export const transformUserVosToPersons = (list?: ApprovalUserVos[] | null): Person[] => {
    if (Array.isArray(list)) {
        return list.map((li) => ({
            name: li.userName,
            id: li.userName,
            avatar: li.portraitUuid
        }));
    }
    return [];
};

export const renderPersonIds = (info: ListApprovalLineNode) => {
    const {approverType, approvalUserVos} = info;
    if (approverType === 1 && approvalUserVos !== null) {
        return approvalUserVos.map((v) => v.userName);
    }
    return [];
};

export const renderUrl = (val?: [string | null] | null): string => {
    if (Array.isArray(val)) {
        return val[0] ?? "";
    }
    return "";
};

const transformOptionDetail = (optionDetail?: OptionDetail[]): OptionType[] => (optionDetail ?? []).map((option) => {
    const ret: OptionType = {
        id: option.id ?? "",
        value: option.value ?? ""
    };
    return ret;
});

const getDataInfo = (data: ServerCustomFieldData) => {
    const hasOption = data.optionAttributes.find((o) => o.name === "OPTION");
    if (hasOption !== undefined) {
        return transformOptionDetail(hasOption.options);
    }
    let info: string | OptionType[] = [];
    switch (data.type) {
        case "PERSON":
            info = [];
            break;
        case "ATTACHMENT":
            info = JSON.stringify(data.attachments);
            break;
        case "REF_DOC":
            if (data.valueElements) {
                const docinfo = data.valueElements as unknown as any[];
                const docList: RelateDocInfo[] = docinfo.map((val) => ({
                    docId: val.refId,
                    docName: val.refName,
                    docMd5: val.refMd5,
                    size: val.refSize,
                    extension: val.refExtension,
                    docUuid: val.refUuid,
                    docType: val.refType
                }));
                info = JSON.stringify(docList);
            }
            break;
        case "REF_BIM":
            const relateInfo = {
                refType: data.refType,
                valueElements: data.valueElements
            };
            info = JSON.stringify(relateInfo);
            break;
    }
    return info;
};

const getAvatar = (avatarUrls: AvatarModel[], uuid: string) => {
    const res = avatarUrls.find((u) => u.uuid === uuid);
    return res?.url ?? "";
};

const renderDefaultValue = (val: string | null, type?: string | null, optionIds?: string[] | ServerCustomFieldPersonData[]) => {
    if (typeof val === "string") {
        return val;
    }
    if (!Array.isArray(optionIds)) {
        return "";
    }
    if (type !== "PERSON") {
        return optionIds as string[];
    }
    const list = optionIds as ServerCustomFieldPersonData[];
    return list.map((i) => i.userName);
};

export const renderCustomFields = (list: ServerCustomFieldData[]): CustomFieldData[] => list.map((i) => {
    const hasTitle = i.stringAttributes.find((v) => v.name === "TITLE");
    const hasPlaceHolder = i.stringAttributes.find((v) => v.name === "PLACE_HOLDER");
    const ret: CustomFieldData = {
        id: i.id,
        name: hasTitle?.value ?? "",
        type: getCustomFieldTypeFromServerData(i as ComponentValueDetail),
        /* 只取最低位 */
        visible: (i.auth! & ServerCustomFieldAuth.Hide) === 0,
        /* 忽略最低位 */
        mode: (i.auth! & 0xFE) as CustomFieldMode,
        hasHistory: !!i.hasHistory,
        defaultValue: renderDefaultValue(i.value, i.type, i.optionIds),
        required: !!i.booleanAttributes.find((b) => b.name === "REQUIRED")!.value,
        data: getDataInfo(i),
        hint: hasPlaceHolder?.value ?? "",
        maxLength: getMaxLengthFromServerData(i as ComponentValueDetail),
        valueId: i.valueId ?? ""
    };
    return ret;
});

const renderName = (info: ApprovalCommentVo): string => {
    const {commentator} = info;
    if (commentator === null) {
        return "";
    }
    const {userName, realName, isLeave} = commentator;
    return `${realName ?? userName ?? ""}${isLeave ? "（离职）" : ""}`;
};

const renderAttachments = (list?: FileItem[] | null): Attachment[] => {
    if (Array.isArray(list)) {
        return list.map((a) => {
            const ret: Attachment = {
                name: a.name ?? "",
                uuid: a.uuid ?? "",
                fileType: a.extension ?? "",
                thumbnail: typeof a.thumbnailUuid === "string" ? {
                    name: a.name ?? "",
                    uuid: a.thumbnailUuid,
                    fileType: a.extension ?? "",
                    md5: "",
                    size: 0
                } : undefined
            };
            return ret;
        });
    }
    return [];
};

export const renderExamineRecord = (list: ApprovalCommentVo[], avatarUrls: AvatarModel[]): ExamineRecord[] => list.map((i) => {
    const ret: ExamineRecord = {
        name: renderName(i),
        role: i.flowNodeName ?? "",
        operationType: i.operationType,
        copyToName: i.nextReceivers?.map((v) => v.realName ?? v.userName ?? "").join(", "),
        handOverName: i.transferToUser !== null ? i.transferToUser.realName ?? i.transferToUser.userName ?? "" : undefined,
        date: new Date(i.commentTime),
        avatar: getAvatar(avatarUrls, i.commentator!.portraitUuid!),
        comment: i.commentMsg ?? "",
        attachments: renderAttachments(i.attachments)
    };
    return ret;
});

export const renderPerson = (list?: ApprovalUserVos[] | null) => {
    if (Array.isArray(list)) {
        return list.map((li) => {
            const ret: Person = {
                name: li.userName,
                id: li.userName,
                avatar: li.portraitUuid
            };
            return ret;
        });
    }
    return [];
};

export const loadCurrentFlowAsync = async (
    data: Map<string, string | string[]>,
    customFieldData: CustomFieldData[],
    formTmplId: string,
    procTmplId: string,
    serialNum?: string
) => {
    // const authStr = localStorage.getItem("loginInfo");
    const useInfo = store.getState().commonData.userInfo;
    // const authInfo = JSON.parse(authStr);
    // const auth = authInfo.data.user;
    try {
        const userRes = await getListApprovalLineNode({
            formTmplId,
            procTmplId,
            serialNum,
            jsonValues: transformDataToJsonValues(data, customFieldData)
        });
        const {result, code, msg} = userRes;
        if (code !== 200) {
            message.error(msg ?? "");
            return [];
        }
        const newRes = result ?? [];
        const ret: Flow[] = newRes.map((u, idx) => ({
            id: u.approvalNodeId ?? "",
            name: u.approvalNodeName ?? "",
            type: u.approverType ?? 3,
            positions: (u.approvalPostVos ?? []).map((p) => p.postId ?? "") ?? [],
            roles: (u.approvalRoleVos ?? []).map((r) => r?.roleId ?? "") ?? [],
            canSet: u.approverType === 3 && idx !== 0,
            persons: u.approverType === 3 ? renderPerson(u.approvalUserVos) : [],
            disableReason: idx === 0 ? useInfo.realName : "后台已指定",
            defaultPersonIds: idx === 0 ? [useInfo.username] : renderPersonIds(u)
        }));
        return ret;
    } catch (err) {
        // message.error(err);
        return []
    }
};

// 已知分叉的模板需要jsonValues这个参数
export const loadCurrentFlowWithoutJsonValues = async (
    formTmplId: string,
    procTmplId: string,
    serialNum?: string,
    jsonValues?: ComponentJsonValueSaveParam[],
) => {
    const useInfo = store.getState().commonData.userInfo;
    try {
        const {result} = await getListApprovalLineNode({formTmplId, procTmplId, serialNum, jsonValues});
        const ret: Flow[] = (result ?? []).map((u, idx) => ({
            id: u.approvalNodeId ?? "",
            name: u.approvalNodeName ?? "",
            type: u.approverType ?? 3,
            positions: u.approvalPostVos?.map((p) => p.postId ?? "") ?? [],
            roles: u.approvalRoleVos?.map((r) => r.roleId ?? "") ?? [],
            canSet: u.approverType === 3 && idx !== 0,
            persons: u.approverType === 3 ? renderPerson(u.approvalUserVos) : [],
            disableReason: idx === 0 ? useInfo.username : "后台已指定",
            defaultPersonIds: idx === 0 ? [useInfo.username] : renderPersonIds(u)
        }));
        return ret;
    } catch (err) {
        return [];
    }
    
};

export const transferAttachmentVoToFileType = (list?: ApprovalOperationAttachmentVo[]): FileType[] => {
    if (Array.isArray(list)) {
        return list.map((v) => {
            const item: FileType = {
                fileUuid: v.uuid ?? "",
                fileSize: v.size ?? 0,
                fileName: v.name ?? ""
            };
            return item;
        });
    }
    return [];
};
