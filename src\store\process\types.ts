import {PickObject} from "@motor/core";
import {Action} from "redux";
import {ProcessTmplTreeNode} from "../../api/processManager/type";

type BimSelectType = "single" | "multiple" | "all";

/** 工序信息相关 */
export interface ProcessInfo {
    /** 当前工程对应的工序模板id */
    curTmplId?: string;
    /** 当前工程对应的工序模板树 */
    tmplTree: ProcessTmplTreeNode[];
    /** 定义工序时，可以对模型进行单选、多选、全选操作，将选中高亮的构件存入store */
    selectedComps: PickObject[];
    /** 自定义沙盘右侧的EBS树可以控制模型的显隐，将勾选的EBS树的叶子节点的key存入store */
    visibleCompPaths: Set<string>;
    selectType?: BimSelectType;
}

export const SET_PROCESS_TMPL_TREE = "SET_PROCESS_TMPL_TREE";
export const SET_PROCESS_TMPL_ID = "SET_PROCESS_TMPL_ID";

export const SET_SELECTED_COMPS = "SET_SELECTED_COMPS";
export const SET_VISIBLE_COMPS = "SET_VISIBLE_COMPS";

export const SET_PROCESS_SELECT_TYPE = "SET_PROCESS_SELECT_TYPE";

export interface SetProcessTmplTree extends Action<typeof SET_PROCESS_TMPL_TREE> {
    payload: ProcessTmplTreeNode[];
}

export interface SetProcessTmplId extends Action<typeof SET_PROCESS_TMPL_ID> {
    payload: string;
}

export interface SetSelectedComps extends Action<typeof SET_SELECTED_COMPS> {
    payload: PickObject[];
}

export interface SetVisibleCompPaths extends Action<typeof SET_VISIBLE_COMPS> {
    payload: ProcessInfo["visibleCompPaths"];
}

export interface SetProcessSelectType extends Action<typeof SET_PROCESS_SELECT_TYPE> {
    payload: ProcessInfo["selectType"];
}

export type ProcessActions = SetProcessTmplTree | SetProcessTmplId | SetSelectedComps | SetVisibleCompPaths | SetProcessSelectType;
