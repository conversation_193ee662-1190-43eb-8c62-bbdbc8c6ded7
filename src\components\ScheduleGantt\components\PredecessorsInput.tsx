import React, {useContext, useState, useEffect, useRef, useCallback} from "react";
import {Table, Button, Form, Select, InputNumber} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import {FormInstance} from "antd/lib/form";
import {createUseStyles} from "react-jss";
import clsx from "clsx";
import {GanttTask} from "../gantt/interface";
import MyIconFont from "../../MyIconFont";
import ganttManager from "../gantt/ganttManager";

const useStyles = createUseStyles({
    predecessorsTable: {
        "@global": {
            ".ant-table-placeholder": {
                "& .ant-table-cell": {
                    borderBottom: "none !important",
                }
            },
            ".ant-table-container": {
                border: "1px solid #f0f0f0 !important",

                "& .ant-table-body": {
                    height: "150px",
                    overflow: "overlay !important"
                },
                "& .ant-table-cell": {
                    height: "32px !important",
                    padding: "4px !important",

                    "&:last-child": {
                        borderRightWidth: "0 !important",
                    },
                },
            },
        },
    },
});

const EditableContext = React.createContext<FormInstance | null>(null);

// finish_to_start":"0", "start_to_start":"1", "finish_to_finish":"2", "start_to_finish":"3"
const linkTypeMap: any = {// eslint-disable-line
    FS: "完成-开始 (FS)",
    SS: "开始-开始 (SS)",
    FF: "完成-完成 (FF)",
    SF: "开始-完成 (SF)",
};

interface LinkItem {
    key: string;
    preTaskWBS?: string;
    preTaskType?: string;
    dateSpan?: number;
}

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({index, ...props}) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    children: React.ReactNode;
    dataIndex: string;
    record: LinkItem;
    taskList: GanttTask[];
    linkList: LinkItem[];
    handleSave: (record: LinkItem) => void;
}

const EditableCell: React.FC<EditableCellProps> = (props) => {
    const {
        title,
        editable,
        children,
        dataIndex,
        record,
        taskList,
        linkList,
        handleSave,
        ...restProps
    } = props;
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<any>(undefined);// eslint-disable-line
    const form = useContext(EditableContext) as FormInstance;
    const [canUseTaskList, setCanUseTaskList] = useState<GanttTask[]>([]);

    useEffect(() => {
        if (taskList !== undefined && taskList.length > 0) {
            setCanUseTaskList(taskList.filter((task) => {
                const linkNotExist = linkList.findIndex((link) => link.preTaskWBS === task.$wbs) < 0;
                if (linkNotExist || task.$wbs === record.preTaskWBS) {
                    return true;
                }
                return false;
            }));
        }
    }, [taskList, linkList, record]);

    useEffect(() => {
        if (editing === true && inputRef.current !== undefined) {
            inputRef.current.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        if (editing === false && record !== undefined && record !== null) {
            let value = (record as any)[dataIndex];// eslint-disable-line 
            if (dataIndex === "dateSpan") {
                value = Number.parseFloat(ganttManager.durationFormatter.format(value ?? 0));
            }
            // console.log("dateSpan:", value);
            form.setFieldsValue({[dataIndex]: value});
        }
        setEditing(!editing);
    };

    const save = async () => {
        try {
            const values = await form.validateFields();
            if (values[dataIndex] !== undefined) {
                // console.log("values[dataIndex]:", values[dataIndex]);
                if (dataIndex === "dateSpan") {
                    values[dataIndex] = ganttManager.durationFormatter.parse(`${values[dataIndex]}`);
                }
                // console.log("values[dataIndex]:", values[dataIndex]);
                handleSave({...record, ...values});
            }
            toggleEdit();
        } catch (errInfo) {
            // console.log("Save failed:", errInfo);
        }
    };

    let childNode = children;

    if (editable) {
        if (editing) {
            if (title === "序号" || title === "任务名称") {
                childNode = (
                    <Form.Item
                        style={{margin: 0, width: "100%"}}
                        name={dataIndex}
                    >
                        <Select
                            showSearch
                            style={{width: "100%"}}
                            ref={inputRef}
                            onSelect={save}
                            onBlur={save}
                            filterOption={(input: any, option) => option?.children?.includes(input) === true}
                        >
                            {canUseTaskList.map((task: GanttTask) => (
                                <Select.Option key={task.$wbs} value={String(task.$wbs)}>
                                    {title === "序号" ? task.$wbs : task.text}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                );
            }
            if (title === "时间关系") {
                childNode = (
                    <Form.Item
                        style={{margin: 0, width: "100%"}}
                        name={dataIndex}
                    >
                        <Select style={{width: "100%"}} ref={inputRef} onSelect={save} onBlur={save}>
                            {Object.keys(linkTypeMap).map((key) => <Select.Option key={key} value={key}>{linkTypeMap[key]}</Select.Option>)}
                        </Select>
                    </Form.Item>
                );
            }
            if (title === "时间间隔") {
                childNode = (
                    <Form.Item
                        style={{margin: 0, width: "100%"}}
                        name={dataIndex}
                    >
                        <InputNumber
                            style={{width: "100%"}}
                            ref={inputRef}
                            precision={0}
                            onBlur={save}
                        />
                    </Form.Item>
                );
            }
        }
    }

    return (
        <td
            {...restProps}
            onClick={() => editing === false && toggleEdit()}
        >
            {childNode}
        </td>
    );
};

interface PredecessorsInputProps {
    label?: string;
    value?: string;
    editTask?: GanttTask;
    onChange?: (predecessors: string) => void;
}

const PredecessorsInput: React.FC<PredecessorsInputProps> = (props) => {
    const {label, value, editTask, onChange} = props;
    const {predecessorsTable} = useStyles();
    const [count, setCount] = useState<number>(1);
    const [allTaskList, setAllTaskList] = useState<GanttTask[]>([]);
    const [taskList, setTaskList] = useState<GanttTask[]>([]);
    const [linkList, setLinkList] = useState<LinkItem[]>([]);

    useEffect(() => {
        if (value !== undefined && value.length > 0) {
            const links: LinkItem[] = value.split(",")
                .filter((linkStr) => {
                    if (ganttManager.linksFormatter.canParse(linkStr.trim())) {
                        const link = ganttManager.linksFormatter.parse(linkStr.trim());
                        if (gantt.isTaskExists(link.source)) {
                            return true;
                        }
                    }
                    return false;
                })
                .map((linkStr) => {
                    const link = ganttManager.linksFormatter.parse(linkStr.trim());
                    const preTask: GanttTask = gantt.getTask(link.source);
                    return {
                        key: preTask.$wbs as string,
                        preTaskWBS: preTask.$wbs as string,
                        preTaskType: Object.keys(linkTypeMap)[Number(link.type)],
                        dateSpan: link.lag,
                    };
                });
            setLinkList(links);
        }
    }, [value]);

    useEffect(() => {
        const list: GanttTask[] = [];
        gantt.eachTask((task) => {
            list.push(task);
        });
        setAllTaskList(list);
    }, []);

    useEffect(() => {
        if (editTask !== undefined) {
            if (allTaskList?.length > 0) {
                setTaskList(allTaskList.filter((task) => {
                    if (editTask?.id !== task.id
                        && gantt.isChildOf(editTask?.id, task.id) === false
                        && gantt.isChildOf(task.id, editTask?.id) === false) {
                        // 过滤自己和父子节点
                        return true;
                    }
                    return false;
                }));
            }
        }
    }, [editTask, allTaskList]);

    const triggerChange = (newLinkList: LinkItem[]) => {
        if (onChange instanceof Function) {
            const predecessors = newLinkList
                .map((link) => {
                    const type = link.preTaskType ?? "FS";
                    let dateSpan = "";
                    const linkDateSpan = Number.parseFloat(ganttManager.durationFormatter.format(link.dateSpan));
                    if (link.dateSpan !== undefined) {
                        if (link.dateSpan > 0) {
                            dateSpan = `+${linkDateSpan}`;
                        } else if (link.dateSpan < 0) {
                            dateSpan = `${linkDateSpan}`;
                        }
                    }
                    return `${link.preTaskWBS}${type}${dateSpan}`;
                })
                .filter((linkStr) => ganttManager.linksFormatter.canParse(linkStr))
                .join(",");
            onChange(predecessors);
        }
    };

    const handleDelete = (key: string) => {
        const newLinkList = linkList.filter((item) => item.key !== key);
        setLinkList(linkList.filter((item) => item.key !== key));
        triggerChange(newLinkList);
    };

    const handleAdd = useCallback(() => {
        const newData: LinkItem = {key: `temp_${count}`};// eslint-disable-line 
        setLinkList([...linkList, newData]);
        setCount(count + 1);
    }, [count, linkList]);

    const handleSave = (row: LinkItem) => {
        const newLinkList: LinkItem[] = [...linkList];
        const index = newLinkList.findIndex((item: LinkItem) => row.key === item.key);
        const item = newLinkList[index];
        newLinkList.splice(index, 1, {
            ...item,
            ...row,
        });
        setLinkList([...newLinkList]);
        triggerChange(newLinkList);
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const columns = [
        {
            title: "序号",
            dataIndex: "preTaskWBS",
            editable: true,
            width: "20%",
            align: "center" as "center",
        },
        {
            title: "任务名称",
            dataIndex: "preTaskWBS",
            editable: true,
            width: "25%",
            render: (preTaskWBS: string) => allTaskList.find((task) => task.$wbs === preTaskWBS)?.text ?? ""
        },
        {
            title: "时间关系",
            dataIndex: "preTaskType",
            editable: true,
            width: "25%",
            render: (preTaskType: string) => linkTypeMap[preTaskType]
        },
        {
            title: "时间间隔",
            dataIndex: "dateSpan",
            width: "15%",
            editable: true,
            align: "center" as "center",
            render: (dateSpan: string) => (
                <span>
                    {dateSpan !== undefined ? `${Number.parseFloat(ganttManager.durationFormatter.format(dateSpan))} 天` : ""}
                </span>
            )
        },
        {
            title: "操作",
            dataIndex: "operation",
            width: "15%",
            align: "center" as "center",
            render: (_: unknown, record: LinkItem) => <a onClick={() => handleDelete(record.key)}>删除</a>
        }
    ].map((col) => {
        if (col.editable !== true) {
            return col;
        }
        return {
            ...col,
            onCell: (record: LinkItem) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                taskList,
                linkList,
                handleSave,
            }),
        };
    });

    return (
        <div>
            <div style={{display: "flex", justifyContent: "space-between", marginBottom: 16}}>
                <span>{label}</span>
                <Button
                    style={{display: "flex", alignItems: "center", padding: "0 4px"}}
                    type="text"
                    icon={<MyIconFont type="icon-tianjia" />}
                    onClick={handleAdd}
                >
                    添加
                </Button>
            </div>
            <Table
                className={clsx(predecessorsTable)}
                rowKey={(record) => record.key}
                bordered
                components={components}
                dataSource={linkList}
                columns={columns}
                scroll={{y: 150}}
                pagination={false}
            />
        </div>
    );
};

export default PredecessorsInput;
