import {TreeProps} from "antd";
import {DataNode} from "antd/lib/tree";
import {isEqual} from "lodash-es";
import React, {useEffect, useState} from "react";
import {WBSType} from "../../api/common.type";
import {emptyFun} from "../../assets/ts/utils";
import {SectionListType} from "../../store/common/actionTypes";
import ComDrawer, {ComDrawerProps} from "../ComDrawer";
import {DataNodeEx} from "./FilterWBSTreeHandler";
import ImportWBSTree from "./ImportWBSTree";

export const getLeafNode = (nodeData: DataNode) => {
    const leafNodes: DataNode[] = [];
    const traverTree = (val: DataNode) => {
        if (Array.isArray(val.children) && val.children.length > 0) {
            val.children.forEach((item) => traverTree(item));
        } else {
            leafNodes.push(val);
        }
    };
    traverTree(nodeData);
    return leafNodes;
};

export interface ImportWBSDrawerProps extends TreeProps {
    sectionInfo?: SectionListType;
    selectedWbs?: string;
    value?: WBSType[] | null;
    wbsLevels?: number[];
    limitWbsLevel?: number;
    onChange?: (value: WBSType[]) => void;
    isAppend?: boolean; // 是否追加导入
    onOk?: (value: DataNodeEx[]) => void;
    drawerVisible: boolean;
    setDrawerVisible: (value: boolean) => void;
    drawerConfig?: ComDrawerProps;
}

const ImportWBSDrawer = (props: ImportWBSDrawerProps) => {
    const {drawerVisible, setDrawerVisible, value, onChange = emptyFun, onOk, drawerConfig, ...other} = props;
    // const cls = useStyle();

    const [saveValue, setSaveValue] = useState<WBSType[]>([]);

    const [treeData, setTreeData] = useState<DataNode[]>([]);

    const [loaded, setLoaded] = useState(0);

    useEffect(() => {
        if (!isEqual(value, saveValue)) {
            setSaveValue(value ?? []);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    // const openDrawer = () => setDrawerVisible(true);
    const closeDrawer = () => setDrawerVisible(false);

    const getParentIds = (treeDataParam: DataNodeEx[], mapParentIds: Map<string, string>) => {
        for (let i = 0; i < treeDataParam.length; ++i) {
            const elem = treeDataParam[i];
            const parentId = typeof elem.parentId !== "undefined" ? elem.parentId : "";
            mapParentIds.set(elem.key.toString(), parentId);
            getParentIds(elem.children as DataNodeEx[], mapParentIds);
        }
    };

    const getCheckedNodeIds = (checkedLeafList: WBSType[], mapParentIds: Map<string, string>, setAllCheckedItems: Set<string>) => {
        for (let i = 0; i < checkedLeafList.length; ++i) {
            const elem = checkedLeafList[i];
            setAllCheckedItems.add(elem.wbsNodeId);

            let elemId = elem.wbsNodeId;
            let findParent = mapParentIds.get(elemId);
            while (typeof findParent !== "undefined" && findParent !== "") {
                setAllCheckedItems.add(findParent);
                if (elemId !== findParent) {
                    elemId = findParent;
                    findParent = mapParentIds.get(elemId);
                } else {
                    break;
                }
            }
        }
    };

    const getCheckedItems = (treeDataParam: DataNodeEx[], setAllCheckedItems: Set<string>, checkedItems: DataNodeEx[]) => {
        for (let i = 0; i < treeDataParam.length; ++i) {
            const elem = treeDataParam[i];
            const findElem = setAllCheckedItems.has(elem.key.toString());
            if (findElem) {
                checkedItems.push(elem);

                getCheckedItems(elem.children as DataNodeEx[], setAllCheckedItems, checkedItems);
            }
        }
    };

    const handleOk = () => {
        onChange(saveValue);
        closeDrawer();

        const mapParentIds = new Map<string, string>();
        getParentIds(treeData as DataNodeEx[], mapParentIds);

        const setAllCheckedItems = new Set<string>();
        getCheckedNodeIds(saveValue, mapParentIds, setAllCheckedItems);

        const checkedItems: DataNodeEx[] = [];
        getCheckedItems(treeData as DataNodeEx[], setAllCheckedItems, checkedItems);

        console.log("checkedItems", checkedItems);
        if (onOk instanceof Function) {
            onOk(checkedItems);
        }
    };

    const handleCancel = () => {
        setSaveValue(value ?? []);
        closeDrawer();
    };

    const handleWBSChange: TreeProps["onCheck"] = (_val, info) => {
        setSaveValue(info.checkedNodes.map((el) => ({
            wbsNodeId: `${el.key}`,
            wbsNodeName: `${el.title}`
        })));
    };

    const setTreeNodes = React.useCallback((dataList: DataNode[]) => {
        setTreeData(dataList);
        setSaveValue((preSaveValue) => {
            const wbsIdMap = new Map<string, WBSType>();
            preSaveValue.forEach((el) => {
                wbsIdMap.set(el.wbsNodeId, el);
            });
            const findTaskChildren = (wbs: any) => {
                // 查找需要选中的任务节点
                if (wbsIdMap.has(wbs.parentId)) {
                    wbsIdMap.set(wbs.key, {
                        level: wbs.businessType,
                        wbsNodeId: wbs.key,
                        wbsNodeName: wbs.title,
                    });
                }
                if (wbs.children !== undefined) {
                    wbs.children.forEach((child: any) => findTaskChildren(child));
                }
            };
            dataList.forEach((el) => findTaskChildren(el));
            return [...wbsIdMap.values()];
        });
        setLoaded(1);
    }, []);

    return (
        <ComDrawer
            title="导入项目划分"
            visible={drawerVisible}
            width={720}
            {...drawerConfig}
            onOk={handleOk}
            onCancel={handleCancel}
        >
            <div style={{padding: 24, height: "100%"}}>
                <ImportWBSTree
                    {...other}
                    key={loaded}
                    checkable
                    checkedKeys={saveValue.map((el) => el.wbsNodeId)}
                    onCheck={handleWBSChange}
                    setTreeNodesFunc={setTreeNodes}
                />
            </div>
        </ComDrawer>
    );
};

export default ImportWBSDrawer;
