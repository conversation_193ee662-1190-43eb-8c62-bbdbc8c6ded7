import {createUseStyles} from "react-jss";
import Color from "../../../assets/css/Color";

const useStyle = createUseStyles({
    root: {
        display: "flex",
        height: "calc(100% - 50px)",
        flexDirection: "column",
    },
    contentBox: {
        flex: "auto",
        overflow: "hidden",
        background: "#fff"
    },
    modelViewSpin: {
        height: "100%",
        "& .ant-spin-container": {
            height: "100%",
        }
    },
    topTab: {
        "& .ant-tabs-tab": {
            padding: "16px 0",
            fontSize: "16px",
        },
        "& .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn": {
            color: "#061127",
            fontWeight: "bold"
        },
        "& .ant-tabs-ink-bar": {
            background: "#061127"
        },
        "& .ant-tabs": {
            color: "#33394D"
        },
        "& .ant-tabs-tab:hover": {
            color: "#061127"
        }
    },
    left: {
        width: 480,
        height: "100%",
        // transition: "margin 0.2s",
        zIndex: 2,
        padding: "16px 24px",
        // marginLeft: -480
    },
    leftSwitch: {
        position: "absolute",
        width: 18,
        height: 52,
        lineHeight: "52px",
        // color: `${Color.primary} !important`,
        right: -18,
        top: "50%",
        transform: "translateY(-50%)",
        background: "rgba(255,255,255,.8)",
        cursor: "pointer"
    },
    btnSpace: {
        position: "absolute",
        left: 24,
        top: 24,
        zIndex: 11,
        "& .ant-btn": {
            "&:hover, &:focus": {
                color: Color["text-1"],
                borderColor: "#E1E2E5",
                backgroundColor: "#F5F5F6"
            },
            "&:active": {
                color: Color["text-1"],
                borderColor: "#E1E2E5",
                backgroundColor: "#E1E2E5"
            }
        },
        "& button.ant-popover-open.ant-btn-primary": {
            color: Color["bg-1"],
            backgroundColor: Color.primary,
        },
        "& .ant-btn-icon-only": {
            width: "40px",
            height: "40px",
            padding: "4.9px 0",
            fontSize: "18px",
            borderRadius: "2px",
        }
    },
    focusBtn: {
        "&:focus": {
            color: "#fff !important",
            borderColor: `${Color.primary} !important`,
            backgroundColor: `${Color.primary} !important`
        }
    },
    modelBox: {
        width: "100%",
        height: "100%"
    },
    btnTopRight: {
        position: "absolute",
        right: 24,
        top: 24
    },
    modelIcon: {
        width: 40,
        height: 40,
        background: "#fff",
        border: "1px solid #E1E2E5",
        color: "#061127",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        cursor: "pointer"
    },
    tabBox: {
        padding: "0 24px !important",
        "& div.ant-tabs-nav": {
            marginBottom: 0
        }
    },
    antTreeWrapper: {
        display: "flex",
        flexDirection: "column",
        height: "100%",
        "& .ant-tree": {
            flexGrow: 1,
            overflow: "auto"
        },
        "& .ant-col-24": {
            height: "100%",
            "& .ant-tree": {
                height: "calc(100% - 56px)",
                "& .ant-tree-list": {
                    overflowY: "scroll"
                }
            },
        }
    },
    bimSignBox: {
        color: "#fff",
        background: "rgba(6,17,39,0.2)",
        padding: "6px 16px",
        position: "absolute",
        right: 24,
        top: 24,
        zIndex: 1
    },
    motorFrameBox: {
        width: "50%",
        height: "calc(100% - 46px)",
        borderLeft: "1px solid #061127",
        background: "#EDF2F5"
    },
    radioBox: {
        "&.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)": {
            background: "#061127",
            borderColor: "#061127",
            color: "#fff",
            "&:hover": {
                background: "#061127",
                borderColor: "#061127"
            }
        },
        "& .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before": {
            background: "#061127 !important"
        },
        "& .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover::before": {
            background: "#061127 !important"
        },
        "& label.ant-radio-button-wrapper": {
            color: "#33394D",
            background: "#F5F5F6",
            width: 88,
            fontSize: 14,
            textAlign: "center",
            "&:hover": {
                color: "#33394D"
            }
        }
    },
    playSettingWrapper: {
        width: 340,
        "& .ant-popover-title": {
            borderBottom: 0,
            padding: 16,
            paddingBottom: 0,
            fontWeight: "bold"
        },
        "& .ant-form .ant-form-item": {
            marginBottom: 0
        }
    },
    viewSettingWrapper: {
        "& .ant-popover-inner-content": {
            padding: 0,
            "& .ant-btn": {
                border: "none !important",
                "&:hover, &:focus": {
                    color: Color.primary
                }
            }
        }
    }
});

export default useStyle;
