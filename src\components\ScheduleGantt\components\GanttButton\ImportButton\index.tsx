import React, {useContext} from "react";
import {<PERSON>ton, Dropdown, <PERSON>u, message, Modal} from "antd";
import {useSelector} from "react-redux";
import {gantt} from "@iworks/dhtmlx-gantt";
import saveAs from "file-saver";
import EditorContext from "../../../views/GanttEditor/context";
import {importFromExcel, importFromProject, importFromWBS, importFromWBSByParentPlan} from "../../../gantt/importExportUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";
import {CalendarInfo} from "../../../api/plan/type";
import {updatePlanInfo} from "../../../../../api/Preparation";
import Color from "../../../../../assets/css/Color";
import ImportWBSDrawer from "../../../../WBS/ImportWBSDrawer";
import {DataNodeEx} from "../../../../WBS/FilterWBSTreeHandler";
import PermissionCode from "../../../../PermissionCode";
import {WBSType} from "../../../../../api/common.type";
import {GanttTask} from "../../../gantt/interface";
import {RootState} from "../../../../../store/rootReducer";
import {SectionListType} from "../../../../../store/common/actionTypes";
import {getTemplateUrl, TemplateType} from "../../../../../assets/ts/templateManager";

const ImportButton = () => {
    const cls = useStyles();
    const {sectionList} = useSelector((store: RootState) => store.commonData);
    const {planInfo, hasParentPlan, fromType, isWbs, checkoutStatus, setLoading, currentCalendarRef} = useContext(EditorContext);
    const [drawerVisible, setDrawerVisible] = React.useState(false);
    const [planSectionInfo, setPlanSectionInfo] = React.useState<SectionListType>();
    const [selectedWbs, setSelectedWbs] = React.useState<string>();
    const [wbsValue, setWbsValue] = React.useState<WBSType[]>([]);
    const [wbsLevels, setWbsLevels] = React.useState<number[]>([]);
    const [limitWbsLevel, setLimitWbsLevel] = React.useState<number>();


    const handleWBSOk = (value: DataNodeEx[]) => {
        // console.log("handleWBSOk", value);
        // console.log("gantt.getTaskCount", gantt.getTaskCount());
        if (hasParentPlan === false) {
            if (gantt.getTaskCount() > 0) {
                const tip = isWbs
                    ? `导入后会更新原有任务项，${planInfo.hasChildren === true ? "且调整了任务会清除已有子计划任务，" : ""}是否导入？`
                    : "导入后会覆盖原有数据，是否导入？";
                Modal.confirm({
                    title: tip,
                    okText: "确定",
                    cancelText: "取消",
                    onOk: () => {
                        importFromWBS(value, setLoading);
                    }
                });
            } else {
                importFromWBS(value, setLoading);
            }
        } else {
            importFromWBSByParentPlan(value, setLoading);
        }
    };

    const importWBS = () => {
        const wbsList: WBSType[] = [];
        let level = 0;
        const levelSet = new Set<number>();
        gantt.eachTask((task: GanttTask) => {
            if (task.type === gantt.config.types.task && task.wbsNodeIds !== undefined) {
                task.wbsNodeIds.forEach((wbsId) => wbsList.push({
                    level: task.wbsNodeLevel,
                    wbsNodeId: wbsId,
                    wbsNodeName: task.text,
                }));
            }
            if (hasParentPlan && task.planId === planInfo.parentId && task.wbsNodeLevel !== undefined && task.wbsNodeLevel !== null) {
                // 上级计划的任务层级
                level = Math.max(task.wbsNodeLevel, level);
                levelSet.add(task.wbsNodeLevel);
            }
        });
        const sectionInfo = sectionList.find((section) => section.id === planInfo.nodeId);
        setPlanSectionInfo(sectionInfo);
        setWbsValue(wbsList);
        setLimitWbsLevel(level);
        setWbsLevels(Array.from(levelSet).sort((a, b) => a - b));
        const selectedId = gantt.getSelectedId();
        if (selectedId !== null) {
            const task = gantt.getTask(selectedId);
            setSelectedWbs(task?.wbsNodeIds?.[0]);
        }
        setDrawerVisible(true);
    };

    const importExcelFile = () => {
        const fileInput = document.getElementById("excelFile") as HTMLInputElement;
        const callback = () => {
            fileInput.value = "";
        };
        if (fileInput !== null && fileInput.files !== null && fileInput.files.length !== 0) {
            importFromExcel(fileInput.files[0], currentCalendarRef.current!, callback, setLoading);
        }
    };

    const importProjectFile = () => {
        const fileInput = document.getElementById("projectFile") as HTMLInputElement;
        const callback = (newCalendarInfo: CalendarInfo) => {
            fileInput.value = "";
            if (planInfo.id !== undefined && newCalendarInfo.calendarType !== currentCalendarRef.current?.calendarType) {
                // 更新日历
                updatePlanInfo({
                    id: planInfo.id,
                    parentId: planInfo.parentId,
                    calendar: newCalendarInfo.ctid,
                }).then((res) => {
                    if (res.success) {
                        currentCalendarRef.current = newCalendarInfo;
                    } else {
                        message.info({content: res.msg});
                    }
                });
            }
        };
        if (fileInput !== null && fileInput.files !== null && fileInput.files.length !== 0) {
            importFromProject(fileInput.files[0], currentCalendarRef.current!, callback, setLoading);
        }
    };

    const handleDownloadExcel = () => {
        if (fromType === "plan") {
            saveAs(getTemplateUrl(TemplateType.SCHEDULE_TASK_DETAILS_IMPORT), "计划编制");
        } else {
            saveAs(getTemplateUrl(TemplateType.ACTUAL_PROGRESS_TASK_DETAILS_IMPORT), "实际进度");
        }
    };

    const importExportMenus = (
        <Menu>
            <Menu.Item key="template" className={cls.dropDown} onClick={handleDownloadExcel}>下载模板</Menu.Item>
            {fromType === "plan" && <Menu.Item key="import_wbs" className={cls.dropDown} onClick={importWBS}>导入项目划分</Menu.Item>}
            <Menu.Item key="import_excel" className={cls.dropDown} disabled={hasParentPlan}>
                <label form="excelFile">
                    导入Excel
                    <input
                        hidden
                        type="file"
                        id="excelFile"
                        name="file"
                        accept=".xlsx,.xls"
                        disabled={hasParentPlan}
                        onChange={importExcelFile}
                    />
                </label>
            </Menu.Item>
            <Menu.Item key="import_project" className={cls.dropDown} disabled={hasParentPlan}>
                <label form="projectFile">
                    导入Project
                    <input
                        hidden
                        type="file"
                        id="projectFile"
                        name="file"
                        accept=".mpp,.xml"
                        disabled={hasParentPlan}
                        onChange={importProjectFile}
                    />
                </label>
            </Menu.Item>
        </Menu>
    );

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <PermissionCode
            authcode="ProjectPlatform-Plan-Progress-Establishment:import"
        >
            <>
                <Dropdown overlay={importExportMenus} placement="bottomCenter">
                    <Button
                        className={cls.textButton}
                        type="text"
                        icon={<MyIconFont type="icon-shangchuan" fontSize={18} />}
                        style={{color: Color["primary-1"]}}
                    >
                        导入
                    </Button>
                </Dropdown>
                <ImportWBSDrawer
                    sectionInfo={planSectionInfo}
                    selectedWbs={selectedWbs}
                    value={wbsValue}
                    wbsLevels={wbsLevels}
                    limitWbsLevel={limitWbsLevel}
                    drawerVisible={drawerVisible}
                    setDrawerVisible={setDrawerVisible}
                    onOk={handleWBSOk}
                    isAppend={hasParentPlan}
                />
            </>
        </PermissionCode>
    );
};

export default ImportButton;
