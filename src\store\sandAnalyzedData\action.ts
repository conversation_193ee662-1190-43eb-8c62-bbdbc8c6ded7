import {createSFAPayloadAction} from "../utils";
import ActionTypes from "./actionTypes";
import {StateType} from "./reducer";

export const setAnalyzedData = createSFAPayloadAction(ActionTypes.SET_SAND_ANALYZEDDATA, (payload: StateType["analyzedData"]) => payload);

export const setConstructionAnalyzedData = createSFAPayloadAction(ActionTypes.SET_CONSTRUCTION_SAND_ANALYZEDDATA, (payload: StateType["constructionAnalyzedData"]) => payload);
