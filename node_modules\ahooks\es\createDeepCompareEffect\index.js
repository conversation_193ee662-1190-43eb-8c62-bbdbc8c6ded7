import { useRef } from 'react';
import isEqual from 'lodash/isEqual';

var depsEqual = function depsEqual(aDeps, bDeps) {
  if (aDeps === void 0) {
    aDeps = [];
  }

  if (bDeps === void 0) {
    bDeps = [];
  }

  return isEqual(aDeps, bDeps);
};

export var createDeepCompareEffect = function createDeepCompareEffect(hook) {
  return function (effect, deps) {
    var ref = useRef();
    var signalRef = useRef(0);

    if (deps === undefined || !depsEqual(deps, ref.current)) {
      ref.current = deps;
      signalRef.current += 1;
    }

    hook(effect, [signalRef.current]);
  };
};