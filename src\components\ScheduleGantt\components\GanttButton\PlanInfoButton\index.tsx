import React, {useCallback, useContext, useState} from "react";
import {But<PERSON>} from "antd";
import moment, {Moment} from "moment";
import {gantt} from "@iworks/dhtmlx-gantt";
import PlanInfoModal from "../../../../PlanInfoModal";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";
import {setGanttCalender} from "../../../gantt/calendarUtils";
import {CalendarInfo} from "../../../api/plan/type";
import {getPlanInfoDetail} from "../../../../../api/Preparation";
import ganttManager from "../../../gantt/ganttManager";
import {GanttTask} from "../../../gantt/interface";
import {updateGanttConfig} from "../../../gantt/ganttConfig";

const PlanInfoButton = () => {
    const cls = useStyles();
    const {fromType, planInfo, setPlanInfo, checkoutStatus, updatePlanTaskMethodRef, currentCalendarRef} = useContext(EditorContext);
    const [planInfoVisible, setPlanInfoVisible] = useState(false);
    const [taskStartDate, setTaskStartDate] = useState<Date>();
    const [taskEndDate, setTaskEndDate] = useState<Date>();

    const handlePlanInfoBack = useCallback(async (planId, calendar?: CalendarInfo) => {
        console.log("handlePlanInfoBack", calendar);
        setPlanInfoVisible(false);
        if (calendar !== undefined) {
            // 保存当前日历并更新甘特图
            currentCalendarRef.current = calendar;
            setGanttCalender(calendar);
            gantt.render();
            // 仅保存数据，不删除签出状态
            if (updatePlanTaskMethodRef.current instanceof Function) {
                updatePlanTaskMethodRef.current();
            }
        }
        if (planId !== undefined) {
            const res = await getPlanInfoDetail(planId);
            if (res.success) {
                setPlanInfo(res.data);
                ganttManager.setPlanStartDate(new Date(res.data.startDate));
                ganttManager.setPlanEndDate(new Date(res.data.endDate));
                updateGanttConfig();
            }
        }
    }, [currentCalendarRef, updatePlanTaskMethodRef, setPlanInfo]);

    const handleClick = useCallback(() => {
        setPlanInfoVisible(true);
        let tmpMinDate: Moment | undefined;
        let tmpMaxDate: Moment | undefined;
        gantt.eachTask((task: GanttTask) => {
            if (tmpMinDate === undefined) {
                tmpMinDate = moment(task.start_date);
            } else {
                tmpMinDate = moment.min(moment(task.start_date), tmpMinDate);
            }

            if (tmpMaxDate === undefined) {
                tmpMaxDate = moment(task.end_date);
            } else {
                tmpMaxDate = moment.max(moment(task.end_date), tmpMaxDate);
            }
        });
        if (tmpMinDate !== undefined && tmpMaxDate !== undefined) {
            setTaskStartDate(tmpMinDate.toDate());
            setTaskEndDate(tmpMaxDate.toDate());
        }
    }, []);

    return (
        <>
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-shuoming" fontSize={18} />}
                onClick={handleClick}
            >
                计划信息
            </Button>
            {
                planInfoVisible && (
                    <PlanInfoModal
                        visible={planInfoVisible}
                        setVisible={setPlanInfoVisible}
                        taskStartDate={taskStartDate}
                        taskEndDate={taskEndDate}
                        planId={planInfo.id}
                        planInfoBack={handlePlanInfoBack}
                        readonly={checkoutStatus !== true || fromType !== "plan"}
                    />
                )
            }
        </>
    );
};

export default PlanInfoButton;
