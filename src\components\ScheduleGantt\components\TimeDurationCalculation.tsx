/* eslint-disable @typescript-eslint/camelcase */
import React, {useCallback, useEffect, useState} from "react";
import {Col, DatePicker, Form, InputNumber, Row} from "antd";
import moment, {Moment} from "moment";
import {FormInstance} from "antd/lib/form";
import {DurationDateValue, GanttTask} from "../gantt/interface";
import ganttManager from "../gantt/ganttManager";
import {calculateTaskChangeDurationDate} from "../gantt/calendarUtils";

interface TimeDurationCalculationProps {
    defaultvalue: DurationDateValue;
    disabled?: boolean;
    form: FormInstance;
    editTask: GanttTask | undefined;
    autoInit?: boolean; // 初始化时是否自动进行填充
    onSetType?: Function;
}

const TimeDurationCalculation: React.FC<TimeDurationCalculationProps> = (props) => {
    const {defaultvalue, disabled, form, editTask} = props;

    const [durationValue, setDurationValue] = useState<DurationDateValue>();

    useEffect(() => {
        // 只在第一次初始化时使用
        if (editTask !== undefined && durationValue === undefined && defaultvalue !== undefined) {
            const editInitValue = {
                duration: typeof defaultvalue.duration === "number" && editTask?.duration !== undefined
                    ? defaultvalue.duration
                    : undefined,
                startDate: editTask?.duration !== undefined ? defaultvalue.startDate : undefined,
                endDate: editTask?.duration !== undefined ? defaultvalue.endDate : undefined
            };
            setDurationValue(editInitValue);
        }
    }, [defaultvalue, durationValue, editTask]);

    useEffect(() => {
        let fieldsValue: {plan_duration?: number | string; plan_start_date?: Moment; plan_end_date?: Moment} = {};

        fieldsValue = {
            plan_duration: durationValue?.duration !== undefined ? Number.parseFloat(ganttManager.durationFormatter.format(`${Math.ceil(durationValue.duration)}`)) : undefined,
            plan_start_date: durationValue?.startDate !== undefined ? moment(durationValue?.startDate) : undefined,
            plan_end_date: durationValue?.endDate !== undefined ? moment(durationValue?.endDate) : undefined,
        };
        form.setFieldsValue(fieldsValue);
    }, [durationValue, editTask, form]);

    /**
     * 修改工期
     */
    const handleDurationChange = useCallback((value: number | undefined) => {
        if (value === undefined) {
            return;
        }
        const duration = ganttManager.durationFormatter.parse(value?.toString());
        if (durationValue?.startDate !== undefined || durationValue?.endDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {duration});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, duration}));
        }
    }, [durationValue]);

    /**
     * 修改开始时间
     */
    const handleStartDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        if (durationValue?.duration !== undefined || durationValue?.endDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {startDate: date.toDate()});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, startDate: date.toDate()}));
        }
    }, [durationValue]);

    /**
     * 修改结束时间
     */
    const handleEndDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        if (durationValue?.duration !== undefined || durationValue?.startDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {endDate: date.toDate()});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, endDate: date.toDate()}));
        }
    }, [durationValue]);

    return (
        <Row>
            <Col span={12}>
                <Form.Item
                    label="计划开始"
                    name="plan_start_date"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={disabled}
                        onChange={handleStartDateChange}
                        placeholder="请选择开始时间"
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    label="计划完成"
                    name="plan_end_date"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={disabled}
                        onChange={handleEndDateChange}
                        placeholder="请选择完成时间"
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    style={{position: "relative"}}
                    label="计划工期"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 14}}
                >
                    <Form.Item
                        noStyle
                        name="plan_duration"
                    >
                        <InputNumber
                            style={{width: "100%"}}
                            onChange={handleDurationChange}
                            min={1}
                            precision={0}
                            disabled={disabled}
                            placeholder="请输入计划工期"
                        />
                    </Form.Item>
                    <span style={{position: "absolute", right: "-20px", lineHeight: "32px"}}>天</span>
                </Form.Item>
            </Col>
        </Row>
    );
};

export default TimeDurationCalculation;
