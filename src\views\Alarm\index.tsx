import {Col, message, Popover, Row, Space, Typography} from "antd";
import React, {FC, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {useForm} from "antd/lib/form/Form";
import {useSelector} from "react-redux";
import {planTypeList} from "../../api/planApproval/type";
import {getPreparationList} from "../../api/Preparation";
import {getWarnTypeList} from "../../api/sandManager";
import useComState from "../../assets/hooks/useComState";
import {groupBy, isDefined, isNonEmptyArray} from "../../assets/ts/utils";
import TableLayout from "../../components/TableLayout";
import {RootState} from "../../store/rootReducer";
import {QueryFormType, queryItemList, trnsformWarnTypeAndLevel, ComColumnsType, columnsInit} from "./data";
import ComTable from "../../components/ComTable";
import {exportWarnList, getActualWarnList} from "../../api/actualProgress";
import {ActualWarnItem} from "../../api/actualProgress/type";
import {WarnTypeListResult} from "../../api/sandManager/type";
import MyIcon from "../../components/MyIcon";
import ComModalExport from "../../components/ComModal/ComModalExport";
import {getState} from "../../store";
import {WarnExportParam} from "../../api/hiddenDangerCheck/type";
import {PlanPreparationItemType} from "../../api/Preparation/type";
import QueryFormSingle from "../../components/QueryFormSingle";
import updateWorkGuideTask from "../../components/CompleteWorkGuideTask";

const {Text} = Typography;

interface Option {
    value: string | number;
    label: string;
    children?: Option[];
}

const renameWbsNo = (list: ActualWarnItem[], order = "") => list.forEach((v, i) => {
    const curNo = order === "" ? (i + 1).toString() : `${order}.${i + 1}`;
    // eslint-disable-next-line no-param-reassign
    v.wbsNo = curNo;
    if (isNonEmptyArray(v.children)) {
        renameWbsNo(v.children, curNo);
    }
});

export const listToTree = (list: ActualWarnItem[]): ActualWarnItem[] => {
    const listMap = new Map(list.map((v) => [v.id, v]));
    const root: ActualWarnItem[] = [];
    list.forEach((v) => {
        const parent = listMap.get(v.parentTaskId ?? "");
        if (parent !== undefined && v.id !== v.parentTaskId) {
            (parent.children ?? (parent.children = [])).push(v);
        } else {
            root.push(v);
        }
    });
    renameWbsNo(root);
    return root;
};

// 判断有没标段信息
const isHasSectionInfo = () => {
    if (getState().commonData.curSectionInfo === null) {
        message.warning("请先选择标段");
        return false;
    }
    return true;
};

const Alarm: FC = () => {
    const {curSectionInfo, orgInfo, leafMenuId} = useSelector((state: RootState) => state.commonData);
    const [state, setState] = useComState({queryFormInit: queryItemList});
    const [form] = useForm();
    const [queryFormData, setQueryFormData] = useState<QueryFormType>({});
    const [tableData, setTableData] = useState<ActualWarnItem[]>([]);
    const [ready, setReady] = useState<boolean>(false);
    const [warnTypeList, setWarnTypeList] = useState<WarnTypeListResult[]>();
    const [columns, setColumns] = useState<ComColumnsType[]>(columnsInit);
    const sectionIdRef = useRef<string>(curSectionInfo?.id ?? "");
    const [planList, setPlanList] = useState<PlanPreparationItemType[]>([]);

    const refreshQueryItemList = useCallback(
        (planOptions: Option[], warnOptions: WarnTypeListResult[], tempPlanList: PlanPreparationItemType[]) => {
            const arr = queryItemList.map((item) => {
                if (item.key === "warnLevel") {
                    return {
                        ...item,
                        typeConfig: {
                            options: warnOptions.map((v) => ({label: v.name, value: v.type})),
                            placeholder: "全部"
                        }
                    };
                }
                if (item.key === "checkPlanId") {
                    return {
                        ...item,
                        typeConfig: {options: planOptions, placeholder: "全部", dropdownClassName: "customAntCascader"}
                    };
                }
                return item;
            });
            setState.setQueryFormList(arr);
            setReady(true);
            setWarnTypeList(warnOptions);
            setPlanList(tempPlanList);
        },
        [setState]
    );

    const init = useCallback(
        async () => {
            if (!isDefined(curSectionInfo) || Boolean(orgInfo.orgId) === false) {
                return;
            }
            setReady(false);
            const warningListRes = await getWarnTypeList();
            const planListRes = await getPreparationList({
                deptId: orgInfo.orgId,
                nodeId: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.id,
                nodeType: curSectionInfo?.isAll === true ? undefined : curSectionInfo?.nodeType,
                pageNum: 1,
                pageSize: 9999,
                filterNoWarn: 1
            });

            // 只展示审批完成或者无需审批的计划
            const allPlan = (planListRes.data?.items ?? []).filter((v) => v.status === 3 || v.status === 0);
            const planGroup = groupBy(allPlan, (node) => node.type);
            const tempPlanList: Option[] = planGroup.map((v) => {
                const label = planTypeList.find((p) => p.value === v[0])?.label ?? "";
                return {
                    value: v[0],
                    label,
                    children: v[1].map((j) => ({value: j.id, label: j.name}))
                };
            });
            if (allPlan.length > 0) {
                const firstPlan = allPlan[0];
                form.setFieldsValue({checkPlanId: [firstPlan.type, firstPlan.id]});
                setQueryFormData((prev) => ({...prev, checkPlanId: [firstPlan.type, firstPlan.id]}));
            } else {
                form.setFieldsValue({checkPlanId: undefined});
                setQueryFormData((prev) => ({...prev, checkPlanId: undefined}));
            }
            sectionIdRef.current = curSectionInfo?.id ?? "";
            refreshQueryItemList(tempPlanList, warningListRes.data ?? [], allPlan);
        },
        [curSectionInfo, form, orgInfo.orgId, refreshQueryItemList]
    );

    useEffect(
        () => {
            init();
        },
        [init]
    );

    const getTableData = useCallback(
        () => {
            if (!isDefined(curSectionInfo) || !ready) {
                return;
            }
            if (curSectionInfo.id !== sectionIdRef.current) {
                return;
            }
            const {checkPlanId = [], warnLevel} = queryFormData;
            const [type, planId] = checkPlanId;
            getActualWarnList({
                deptId: orgInfo.orgId,
                nodeId: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeId,
                nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeType,
                planId,
                type,
                warnLevel: trnsformWarnTypeAndLevel(warnLevel)
            }).then((res) => {
                const tempList = res?.data ?? [];
                setTableData(listToTree(tempList));
                setState.setTotal(tempList.length);
            }).catch(() => setTableData([]));
        },
        [curSectionInfo, orgInfo.orgId, queryFormData, ready, setState]
    );

    useEffect(
        () => {
            getTableData();
        },
        [getTableData]
    );

    useEffect(() => {
        // 组件挂载时执行
        // 查看成功，调用updateWorkGuideTask更新任务完成状态
        if (orgInfo?.orgId !== "" && curSectionInfo?.id !== undefined && leafMenuId !== "") {
            updateWorkGuideTask(orgInfo?.orgId, curSectionInfo.id, leafMenuId, "VIEW");
        }
    }, [curSectionInfo, leafMenuId, orgInfo]);

    const handleReset = useCallback(() => {
        form.resetFields();
        if (planList.length > 0) {
            const firstPlan = planList[0];
            form.setFieldsValue({checkPlanId: [firstPlan.type, firstPlan.id]});
        } else {
            form.setFieldsValue({checkPlanId: undefined});
        }
        const param = form.getFieldsValue();
        setQueryFormData(param);
    }, [form, planList]);

    const handleFormFinished = (queryParams: QueryFormType) => {
        setQueryFormData(queryParams);
    };

    const rowSelection = useMemo(() => ({
        selectedRowKeys: state.selectIds,
        onChange: setState.setSelectIds,
        columnWidth: 60
    }), [setState.setSelectIds, state.selectIds]);

    const warnTypePopover = useMemo(() => {
        if (!Array.isArray(warnTypeList)) {
            return;
        }
        const warnListDom = (
            <>
                <Text strong>报警级别说明</Text>
                {warnTypeList.map((v) => (
                    <Row justify="space-between" style={{width: 200}} key={v.id}>
                        <Col span={10}>{v.name}</Col>
                        <Col span={14}>{v.desc}</Col>
                    </Row>
                ))}
            </>
        );
        return (
            <>
                报警级别
                <Popover content={warnListDom}>
                    <QuestionCircleOutlined style={{marginLeft: 5}} />
                </Popover>
            </>
        );
    }, [warnTypeList]);

    const updateColumns = useCallback(
        () => {
            if (!Array.isArray(warnTypeList)) {
                return;
            }
            setColumns((prev) => prev.map((v) => {
                if (v.key === "warnLevel") {
                    return {
                        ...v,
                        title: warnTypePopover,
                        render: (t: number) => {
                            const type = trnsformWarnTypeAndLevel(t);
                            // eslint-disable-next-line max-nested-callbacks
                            const warnType = warnTypeList.find((j) => j.type === type);
                            return (
                                <>
                                    <MyIcon type="icon-jinggao_mianxing" color={warnType?.color} />
                                    {warnType?.name}
                                </>
                            );
                        }
                    };
                }
                return v;
            }));
        },
        [warnTypeList, warnTypePopover]
    );

    useEffect(
        () => {
            updateColumns();
        },
        [updateColumns]
    );

    const handleExport = useCallback(() => {
        if (!isHasSectionInfo()) {
            return;
        }
        if (!isDefined(curSectionInfo)) {
            return;
        }
        const {checkPlanId = [], warnLevel} = queryFormData;
        const [type, planId] = checkPlanId;
        let params: {} = {
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeId,
            nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeType,
            planId,
            type,
            warnLevel: trnsformWarnTypeAndLevel(warnLevel)
        };
        if (state.selectIds.length > 0) {
            params = {
                ...params,
                baseIds: state.selectIds
            };
        } else {
            params = {...params};
        }
        /* const param: WarnExportParam = {
            baseIds: state.selectIds as string[],
            nodeId: curSectionInfo?.nodeId ?? "",
            nodeType: curSectionInfo?.nodeType ?? 2
        }; */
        exportWarnList(params as WarnExportParam, "进度报警.xlsx");
    }, [curSectionInfo, orgInfo.orgId, queryFormData, state.selectIds]);

    return (
        <TableLayout>
            <Row className="box">
                <Col flex="auto" className="safety-quality-table" style={{paddingTop: 8}}>
                    <Row align="middle" justify="space-between">
                        <Col span={2}>
                            <Row justify="space-between">
                                <Col>
                                    <Space>
                                        <ComModalExport
                                            totalNum={state.total}
                                            selectedNum={state.selectIds.length}
                                            onOk={handleExport}
                                        />
                                    </Space>
                                </Col>
                            </Row>
                        </Col>
                        <Col span={22}>
                            <QueryFormSingle<QueryFormType>
                                queryItemList={state.queryFormList}
                                onFormFinish={handleFormFinished}
                                onFormClear={handleReset}
                                form={form}
                                formRow={{justify: "end"}}
                            />
                        </Col>
                    </Row>
                    <ComTable
                        columns={columns.filter((el) => el.show || el.mustShow)}
                        dataSource={tableData}
                        pagination={false}
                        rowKey="id"
                        rowSelection={rowSelection}
                        indentSize={5}
                    />
                </Col>
            </Row>
        </TableLayout>
    );
};

export default Alarm;
