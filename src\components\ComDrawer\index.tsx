import {<PERSON><PERSON>, Drawer, DrawerP<PERSON>, <PERSON>, Col} from "antd";
import React, {ReactNode} from "react";
import {CloseOutlined} from "@ant-design/icons";
import {createUseStyles} from "react-jss";

export interface ComDrawerProps extends DrawerProps {
    onOk?: () => void;
    onCancel?: () => void;
    onOkText?: string;
    okLoading?: boolean;
    rightClose?: boolean;
    children: ReactNode;
}
export const useStyle = createUseStyles({
    comDrawer: {
        "& .ant-drawer-content": {
            overflow: "inherit",
            "& .ant-drawer-header .ant-drawer-title": {
                color: "#061127",
                fontWeight: 700
            },
            "& .ant-drawer-body": {
                padding: 0,
                overflow: "auto"
            },
            "& .ant-drawer-footer": {
                borderTop: "none",
                padding: 0
            }
        },
    },
    drawerFooter: {
        "& .ant-btn": {
            borderRadius: 0,
            height: 48,
            width: "100%",
            color: "#fff"
        }
    }
});
const ComDrawer = (props: ComDrawerProps) => {
    const {
        onOk,
        onCancel,
        onOkText = "确定",
        okLoading = false,
        closable = false,
        width = 520,
        keyboard = false,
        maskClosable = false,
        children,
        destroyOnClose = true,
        footerStyle,
        rightClose = true,
        className,
        ...other
    } = props;
    const cls = useStyle();

    const renderFooter = React.useMemo(() => (
        // <Space>
        //     {onCancel !== undefined && <Button onClick={onCancel}>取消</Button>}
        //     {onOk !== undefined && <Button type="primary" onClick={onOk}>{onOkText}</Button>}
        // </Space>
        <Row gutter={1} className={cls.drawerFooter}>
            <Col span={12}>
                <Button style={{background: "#33394D", borderColor: "#33394D", fontSize: 14}} onClick={onCancel}>
                    取消
                </Button>
            </Col>
            <Col span={12}>
                <Button type="primary" onClick={onOk} style={{fontSize: 14}} loading={okLoading}>
                    {onOkText}
                </Button>
            </Col>
        </Row>
    ), [cls.drawerFooter, okLoading, onCancel, onOk, onOkText]);

    return (
        <Drawer
            closable={closable}
            width={width}
            className={`${cls.comDrawer} ${className}`}
            keyboard={keyboard}
            destroyOnClose={destroyOnClose}
            maskClosable={maskClosable}
            footer={renderFooter}
            onClose={onCancel}
            extra={<CloseOutlined onClick={onCancel} />}
            {...other}
        >
            {children}
        </Drawer>
    );
};

export default ComDrawer;
