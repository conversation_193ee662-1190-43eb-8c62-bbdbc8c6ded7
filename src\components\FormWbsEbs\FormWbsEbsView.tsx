import React, {useEffect} from "react";
import {Form, Space, Typography, FormItemProps} from "antd";
import {EyeOutlined} from "@ant-design/icons";
import {sortedUniq} from "lodash-es";
import {WbsEbsNodeType} from "./data";
import {toArr} from "../../assets/ts/utils";
import {useViewFormStyle} from "../../assets/ts/form";
import useModelView, {ProjectType} from "../ModelSelect/useModelView";
import {useAppSelector} from "../../store";



const {Paragraph} = Typography;

interface FormWbsEbsViewProps {
    value?: WbsEbsNodeType;
    itemConfig?: FormItemProps;
    deptId?: string;
    deptName?: string;
    sectionId?: string;
    moduleType?: string;
}

const FormWbsEbsView = (props: FormWbsEbsViewProps) => {
    const {
        value,
        itemConfig,
        deptId,
        deptName,
        sectionId,
        moduleType
    } = props;
    const cls = useViewFormStyle();
    const {token, orgInfo, curSectionInfo} = useAppSelector((state) => state.commonData);
    const {openChildTab, sendMessage, loaded} = useModelView();

    useEffect(() => {
        if (loaded && value !== undefined) {
            sendMessage("SetBaseData", {token});
            const objPpid: {[key: string]: ProjectType} = {};
            toArr(value.ebsNodes ?? []).forEach((el) => {
                const bindEbs = objPpid[el.ppid];
                if (bindEbs !== undefined) {
                    if (Array.isArray(bindEbs.selectedGuids) && typeof el.handle === "string") {
                        bindEbs.selectedGuids.push(el.handle);
                    }
                } else {
                    objPpid[el.ppid] = {
                        ppid: el.ppid,
                        projectName: el.projName ?? "",
                        selectedGuids: typeof el.handle === "string" ? [el.handle] : []
                    };
                }
            });
            sendMessage("SetProjectList", {data: Object.values(objPpid)});
            // TODO moduleType修改
            sendMessage("SetSafetyQuality", {
                deptId: deptId ?? orgInfo.orgId,
                deptName: deptName ?? orgInfo.orgName,
                sectionId: sectionId ?? curSectionInfo?.nodeId,
                // moduleType: moduleType ?? storeModuleType
                moduleType
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [loaded]);

    const renderLabel = () => (
        <Space>
            {value !== undefined && value.bindType === 2 && <EyeOutlined onClick={() => openChildTab()} />}
            <div>关联WBS/EBS</div>
        </Space>
    );

    const renderText = () => {
        if (value === undefined) {
            return "";
        }
        if (value.bindType === -1) {
            return toArr(value.wbsNodes ?? []).map((el) => el.wbsNodeName).join(",");
        }
        if (value.bindType === 1) {
            return toArr(value.ebsNodes ?? []).map((el) => el.projName).join(",");
        }
        if (value.bindType === 2 || value.bindType === 3) {
            const textArr = toArr(value.ebsNodes ?? []).map((el) => {
                const newArrs = [el.projName, ...el.paths ?? []];
                return newArrs.join(">");
            });
            return sortedUniq(textArr).join(",");
        }
        return "";
    };

    return (
        <Form.Item className={cls.item} style={{marginBottom: 0}} {...itemConfig} label={renderLabel()}>
            <Paragraph ellipsis={{tooltip: renderText(), rows: 3}}>
                {renderText()}
            </Paragraph>
        </Form.Item>
    );
};

export default FormWbsEbsView;
