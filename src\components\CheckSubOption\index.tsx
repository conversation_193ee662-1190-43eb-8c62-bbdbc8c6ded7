import {But<PERSON>, Drawer, Input, Space, Tree, Typography} from "antd";
import {DataNode, EventDataNode, TreeProps} from "antd/lib/tree";
import React, {Key, memo, useCallback, useEffect, useRef, useState} from "react";
import {cloneDeep, debounce} from "lodash-es";
import {getCheckQualityListChoose, getCheckSecurityListChoose} from "../../api/center";
import {GetCheckQualityListChooseType, GetCheckSecurityListChooseType} from "../../api/center/type";
import {filterTreeNew, generateList, ModuleType} from "../../assets/ts/utils";
import {CheckSubOptionValueType} from "./MultipleChoice";

export interface CheckSubOptionProps {
    isVisible: boolean; // 控制是否显示
    moduleType: string; // security(安全) quality（质量）
    onClose: () => void;
    projectCategoryId: string;
    value?: CheckSubOptionValueType;
    onChange?: (val: CheckSubOptionValueType | undefined) => void;
}

const CheckSubOption = (props: CheckSubOptionProps) => {
    // 房建 读的是房建的安全 和 质量
    // 基建 读的 是公路的安全 和 质量
    const inputRef = useRef<Input>(null);
    const {isVisible, onClose, moduleType, projectCategoryId, value: propsValue, onChange} = props;
    const [treeData, setTreeData] = useState<DataNode[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [copyTreeData, setCopyTreeData] = useState<DataNode[]>([]); // 备份一份数据
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [treeValue, setTreeValue] = useState<CheckSubOptionValueType>();

    useEffect(() => {
        setTreeValue(propsValue);
    }, [propsValue]);

    // 获取安全 的原始数据
    const getSecurityData = useCallback(async (id: string) => {
        let dataList: GetCheckSecurityListChooseType[] = [];
        try {
            dataList = (await getCheckSecurityListChoose(id)).result ?? [];
        } catch (error) {
            dataList = [];
        }
        return dataList;
    }, []);
    // 转换安全 的原始数据 tree
    const getSecurityDataTree = useCallback((securityDataList: GetCheckSecurityListChooseType[]) => {
        const newSecurityDataList: GetCheckSecurityListChooseType[] = JSON.parse(JSON.stringify(securityDataList));
        newSecurityDataList.forEach((item) => {
            const newItem = item;
            newItem.key = item.id;
            newItem.title = item.content;
            newItem.children = newItem.checkSubentries;
            newItem.disabled = true;
            newItem.checkSubentries.forEach((item2) => {
                const newItem2 = item2;
                newItem2.key = item2.id;
                newItem2.title = item2.content;
                newItem2.parentId = item.id;
                newItem2.parentName = item.content;
                newItem2.children = newItem2.hiddenDangerPoints;
                newItem2.disabled = true;
                // eslint-disable-next-line max-nested-callbacks
                item2.hiddenDangerPoints.forEach((item3) => {
                    const newItem3 = item3;
                    newItem3.key = item3.id;
                    newItem3.title = item3.content;
                    newItem3.parentId = item2.id;
                    newItem3.parentName = item2.content;
                    // 最后一层节点标志
                    newItem3.lastNode = true;
                });
            });
        });
        return newSecurityDataList;
    }, []);
    // 获取质量 的原始数据
    const getQualityData = useCallback(async (id: string) => {
        let dataList: GetCheckQualityListChooseType[] = [];
        try {
            dataList = (await getCheckQualityListChoose(id)).result ?? [];
        } catch (error) {
            dataList = [];
        }
        return dataList;
    }, []);
    // 转换质量 的原始数据 tree
    const getQualityDataTree = useCallback((dataList: GetCheckQualityListChooseType[]) => {
        const newDataList: GetCheckQualityListChooseType[] = JSON.parse(JSON.stringify(dataList));
        newDataList.forEach((item) => {
            const newItem = item;
            newItem.key = item.id;
            newItem.title = item.content;
            newItem.children = newItem.checkContents;
            newItem.disabled = true;
            newItem.checkContents.forEach((item2) => {
                const newItem2 = item2;
                newItem2.key = item2.id;
                newItem2.title = item2.content;
                newItem2.parentId = item.id;
                newItem2.parentName = item.content;
                // 最后一层节点标志
                newItem2.lastNode = true;
            });
        });
        return newDataList;
    }, []);

    const init = useCallback(
        async () => {
            if (projectCategoryId === "") {
                return;
            }
            if (moduleType === ModuleType.security) {
                // 安全
                const securityDataList = await getSecurityData(projectCategoryId);
                setTreeData(getSecurityDataTree(securityDataList));
                setCopyTreeData(getSecurityDataTree(securityDataList));
            } else if (moduleType === ModuleType.quality) {
                // 质量
                const qualityDataList = await getQualityData(projectCategoryId);
                setTreeData(getQualityDataTree(qualityDataList));
                setCopyTreeData(getQualityDataTree(qualityDataList));
            }
        },
        [getQualityData, getQualityDataTree, getSecurityData, getSecurityDataTree, moduleType, projectCategoryId],
    );

    useEffect(() => {
        init();
    }, [init]);

    const onSelectTree = (_selectedKeys: Key[], e: {selectedNodes: DataNode[]; node: EventDataNode}) => {
        if (e.selectedNodes.length === 0) {
            setTreeValue(undefined);
        } else {
            const tempValue = e.selectedNodes[0] as unknown as GetCheckQualityListChooseType;
            setTreeValue({
                checkDescId: tempValue.id,
                subOptionContent: tempValue.parentName,
                subOptionId: tempValue.parentId,
                content: tempValue.content
            });
        }
    };
    const handleOk = () => {
        if (onChange !== undefined) {
            onChange(treeValue);
        }
        onClose();
    };

    const defaultMatcher = (filterText: string, node: DataNode) => {
        if (typeof node?.title === "string") {
            return node?.title?.includes(filterText);
        }
        return false;
    };

    const onSearch = debounce(
        () => {
            let value = "";
            if (inputRef !== null && inputRef.current !== null) {
                // console.log(inputRef.current.state.value, "333");
                value = inputRef.current.state.value;
            }
            // const {value} = e.target;
            const newTreeData = cloneDeep(treeData);
            if (value === "") {
                setAutoExpandParent(false);
                setExpandedKeys([]);
                setCopyTreeData(treeData);
                return;
            }
            const filtered = newTreeData.map((item) => filterTreeNew(item, value, defaultMatcher))
                .filter((item) => Array.isArray(item.children) && item.children.length > 0);
            setCopyTreeData(filtered);
            const newExpandedKeys = generateList(filtered, [])
                .map((item: DataNode) => item.key);
            setAutoExpandParent(true);
            setExpandedKeys(newExpandedKeys);
        },
        1000
    );
    const onExpand = (newExpandedKeys: React.Key[]) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
    };
    // 搜索结束

    const handleClose = () => {
        setTreeValue(propsValue);
        onClose();
    };

    const renderNode: TreeProps["titleRender"] = (nodeData) => <Typography.Text style={{maxWidth: "400px"}} ellipsis>{nodeData.title}</Typography.Text>;
    const renderBox = () => (
        <Drawer
            onClose={onClose}
            visible={isVisible}
            title="检查分项"
            width={520}
            bodyStyle={{padding: 15}}
            maskClosable={false}
            closable={false}
            keyboard={false}
            footerStyle={{textAlign: "right"}}
            footer={(
                <Space>
                    <Button onClick={handleClose}>取消</Button>
                    <Button type="primary" onClick={handleOk}>确定</Button>
                </Space>
            )}
        >
            <div>
                <Input.Search ref={inputRef} onChange={onSearch} placeholder="请输入名称" style={{marginBottom: 10}} />
                <Tree
                    defaultExpandAll
                    showLine
                    treeData={copyTreeData}
                    onSelect={onSelectTree}
                    onExpand={onExpand}
                    autoExpandParent={autoExpandParent}
                    expandedKeys={expandedKeys}
                    titleRender={renderNode}
                    selectedKeys={[treeValue?.checkDescId ?? ""]}
                />
            </div>
        </Drawer>
    );
    return <>{renderBox()}</>;
};

export default memo(CheckSubOption);
