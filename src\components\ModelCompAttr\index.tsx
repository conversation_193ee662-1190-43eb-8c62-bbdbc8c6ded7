import React, {useMemo} from "react";
import {Col, Collapse, List, Row} from "antd";
import {CompAttrResult} from "../../api/comAttr/type";
import {CompAttrItem, CompInfoNode} from "./interface";
import parserCompInfo from "./compInfoParser";
import {getDocUrl} from "../../api/common.api";
import TextEllipsis from "../TextEllipsis";

interface ModelCompAttrProps {
    attrInfo?: CompAttrResult;
    deptId: string;
}

const getValue = (subItem: CompAttrItem) => {
    if (subItem.it === 1) { // 文件
        if (subItem.docInfo !== null && subItem.docInfo !== undefined) {
            return subItem.docInfo.filename;
        }
        return "";
    }
    return subItem.iv;
};
const checkIsDoc = (subItem: CompAttrItem) => subItem.it === 1 && subItem.docInfo !== null && subItem.docInfo !== undefined;

/** 模型的构件信息 */
const ModelCompAttr = (props: ModelCompAttrProps) => {
    const {attrInfo, deptId} = props;

    const attrData = useMemo<CompInfoNode[]>(() => parserCompInfo(attrInfo), [attrInfo]);

    const onClickPreview = (subItem: CompAttrItem) => {
        if (subItem.docInfo !== null && subItem.docInfo !== undefined) {
            getDocUrl(subItem.docInfo.fileuuid, subItem.docInfo.filename, deptId).then((res) => {
                if (res.success === true) {
                    window.open(res.result);
                }
            });
        }
    };

    const renderAttrList = (item: CompInfoNode, attrList: CompAttrItem[]) => {
        const renderAttrItem = (subItem: CompAttrItem) => {
            let valueNode = <TextEllipsis text={getValue(subItem)} style={{color: "#061127"}} />;
            if (checkIsDoc(subItem)) {
                valueNode = (
                    <div
                        style={{cursor: "pointer", color: "#0497FE"}}
                        onClick={() => onClickPreview(subItem)}
                    >
                        <TextEllipsis text={getValue(subItem)} />
                    </div>
                );
            }
            const attrItem = (
                <List.Item style={{padding: 0, flexDirection: "column", marginBottom: 16}}>
                    <Row gutter={20}>
                        <Col span={6}>
                            <TextEllipsis
                                style={{color: "#717784"}}
                                text={item.it !== -1 ? `*${subItem.ik}` : subItem.ik}
                            />
                        </Col>
                        <Col span={18}>
                            {valueNode}
                        </Col>
                    </Row>
                </List.Item>
            );
            if (subItem !== undefined && subItem.attrList !== undefined && subItem.attrList?.length > 0) {
                return (
                    <Collapse ghost>
                        <Collapse.Panel key={subItem.ik} header={attrItem}>
                            {renderAttrList(item, subItem.attrList)}
                        </Collapse.Panel>
                    </Collapse>
                );
            }
            return attrItem;
        };

        if (Array.isArray(attrList) && attrList.length > 0) {
            return (
                <List
                    style={{padding: 0}}
                    itemLayout="horizontal"
                    dataSource={attrList}
                    size="small"
                    split={false}
                    renderItem={(child) => renderAttrItem(child)}
                />
            );
        }
        return null;
    };

    return (
        <List
            itemLayout="vertical"
            dataSource={attrData}
            size="small"
            split={false}
            style={{padding: 0}}
            renderItem={(item) => (
                <List.Item style={{padding: 0}}>
                    <div style={{padding: 0, marginBottom: 24}}>
                        <TextEllipsis
                            text={item.it !== -1 ? `*${item.groupName}` : item.groupName}
                            style={{
                                color: "#061127",
                                fontSize: 14,
                                fontWeight: 700
                            }}
                        />
                    </div>
                    {renderAttrList(item, item.attrList)}
                </List.Item>
            )}
        />
    );
};

export default ModelCompAttr;
