import React, {forwardRef, useImper<PERSON><PERSON><PERSON>le} from "react";
import {Col, Form, Row} from "antd";
import {ApprovalType, Flow} from "../../../components/Rectification/models/rectification";
import TextEllipsis from "../../../components/TextEllipsis";
import {chunkArray} from "../../../assets/ts/utils";
import {ApprovalNodeUserParam} from "../../../api/rectification/models/process";
import {ValueType} from "../../../components/SelectPerson/data";
import SelectPerson from "../../../components/SelectPerson";

export interface PersonFormRef {
    // nodeUsers: ApprovalNodeUserParam[];
    getNodeUsers: () => Promise<ApprovalNodeUserParam[]>;
}

interface PersonFormProps {
    flowList: Flow[];
    nodeId?: string;
    nodeType?: number;
}

const getPersonInitVal = (list: Flow[]) => {
    const obj: {[key: string]: ValueType[]} = Object.create(null);
    list.forEach((v) => {
        obj[v.id] = v.persons.map((p) => ({userName: p.name}));
    });
    return obj;
};

const PersonForm = forwardRef<PersonFormRef, PersonFormProps>((props, ref) => {
    // const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {flowList, nodeId, nodeType} = props;
    const [personFormInstance] = Form.useForm<{[key: string]: ValueType[]}>();
    // const [curFlowNodeId, setCurFlowNodeId] = useState<string>();
    // eslint-disable-next-line max-len
    // const [checkedPersonMap, setCheckedPersonMap] = useState<Map<string, string[]>>(new Map(flowList.map((v) => [v.id, v.persons.map((p) => p.name)])));
    // const [originPersonList, setOriginPersonList] = useState<TreeType[]>([]);

    // useEffect(() => {
    //     getLoadPersonTree(0, orgInfo.orgId, 1).then((res) => setOriginPersonList(
    //         // eslint-disable-next-line max-nested-callbacks
    //         bfsTree(res.data as unknown as TreeType).filter((v) => v.type === 5)
    //     ));
    // }, [orgInfo]);

    useImperativeHandle(ref, () => ({
        /* nodeUsers: flowList.filter((f) => f.type === ApprovalType.InitiatorSpecify).map((f) => ({
            approvalNodeId: f.id,
            approvalUsers: (checkedPersonMap.get(f.id) ?? []).filter((v) => typeof v === "string")
        })), */
        getNodeUsers: async () => {
            const userObj = await personFormInstance.validateFields();
            return flowList
                .filter((f) => f.type === ApprovalType.InitiatorSpecify)
                .map((f) => ({
                    approvalNodeId: f.id,
                    // eslint-disable-next-line max-nested-callbacks
                    approvalUsers: (userObj[f.id] ?? []).map((v) => v.userName)
                }));
        }
    }), [flowList, personFormInstance]);

    // const handlePersonInputClick = useCallback(
    //     (id: string) => {
    //         setCurFlowNodeId(id);
    //     },
    //     []
    // );

    // const handlePersonSelectBoxChange = useCallback(
    //     (val: TreeType[]) => {
    //         if (curFlowNodeId === undefined) {
    //             return;
    //         }
    //         const centerNameList = val.filter((v) => typeof v.centerUserName === "string").map((v) => v.centerUserName!);
    //         personFormInstance.setFieldsValue({
    //             [curFlowNodeId]: centerNameList.join(", ")
    //         });
    //         setCheckedPersonMap(new Map(checkedPersonMap).set(curFlowNodeId, centerNameList));
    //     },
    //     [personFormInstance, curFlowNodeId, checkedPersonMap]
    // );

    /* const handleFlowAddPerson = (id: string, checkedKeys: string[]) => {
        const newFlowAdditionalPerson = new Map<string, string[]>();
        checkedPersonMap.set(id, checkedKeys);
        // eslint-disable-next-line no-restricted-syntax
        for (const [k, v] of checkedPersonMap) {
            newFlowAdditionalPerson.set(k, v);
        }
        setCheckedPersonMap(newFlowAdditionalPerson);
    }; */

    if (flowList.length === 0) {
        return null;
    }

    return (
        <Form
            labelCol={{span: 3}}
            wrapperCol={{span: 18}}
            labelAlign="right"
            form={personFormInstance}
            style={{padding: "0 20px"}}
            initialValues={getPersonInitVal(flowList)}
        >
            {
                chunkArray(flowList).map((dv, dvx) => (
                    <Row key={dvx.toString()}>
                        {
                            dv.map((f) => (
                                <Col span={12} key={f.id}>
                                    <Form.Item
                                        label={<TextEllipsis text={f.name} />}
                                        rules={[{required: f.canSet, message: "必填项"}]}
                                        name={f.id}
                                        initialValue={!f.canSet ? f.disableReason : f.persons.map((v) => v.name).join(", ")}
                                    >
                                        {/* <ConnectedPersonSelector
                                            onPopup={() => null}
                                            disabled={!f.canSet}
                                            disableReason={f.disableReason}
                                            mustCheckedPersonIds={f.defaultPersonIds}
                                            selectedKeys={checkedPersonMap.get(f.id)}
                                            onChange={(c) => handleFlowAddPerson(f.id, c)}
                                        /> */}
                                        <SelectPerson
                                            inputDisabled={!f.canSet}
                                            disableReason={f.disableReason}
                                            nodeId={nodeId}
                                            nodeType={nodeType}
                                        />
                                    </Form.Item>
                                </Col>
                            ))
                        }
                    </Row>
                ))
            }
        </Form>
    );
});

export default PersonForm;
