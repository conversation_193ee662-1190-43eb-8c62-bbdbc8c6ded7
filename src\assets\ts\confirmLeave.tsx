import {ExclamationCircleFilled} from "@ant-design/icons";
import {Modal, ModalFuncProps} from "antd";
import React from "react";
// import {useSelector} from "react-redux";
import Color from "../css/Color";
import {getState} from "../../store";

const confirmLeave = async (handle?: () => void): Promise<boolean> => new Promise((resolve) => {
    const {iframeEditStatus} = getState().commonData;
    if (iframeEditStatus === true) {
        let clickOk = false;
        Modal.confirm({
            title: "提示",
            icon: <ExclamationCircleFilled style={{color: Color["yellow-1"], marginRight: 8}} />,
            content: "当前表单尚未保存,确定离开吗?",
            onOk() {
                clickOk = true;
                resolve(true);
            },
            onCancel() {
                resolve(false);
            },
            afterClose() {
                if (clickOk && handle !== undefined) {
                    handle();
                }
            }
        });
    } else {
        resolve(true);
        if (handle !== undefined) {
            handle();
        }
    }
});

export const confirmOk = async (props: ModalFuncProps) => new Promise((resolve) => {
    Modal.confirm({
        title: "提示",
        icon: <ExclamationCircleFilled style={{color: Color["yellow-1"]}} />,
        content: "确定删除吗?",
        ...props,
        onOk() {
            resolve(true);
        },
        onCancel() {
            resolve(false);
        }
    });
});

export default confirmLeave;
