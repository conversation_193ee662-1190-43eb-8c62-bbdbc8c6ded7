const fs = require("fs");
const path = require("path");

//筛选目录下已经存在的打包部署文件
const findExitFileZip = ({rootPath, reg}) => {
    const list = [];
    const fileList = fs.readdirSync(rootPath);
    fileList.map((val, key) => { 
        const fPath = path.join(rootPath, val);
        const fileStat = fs.statSync(fPath);
        if (reg.test(val) && fileStat.isFile) { 
            list.push(fPath);
        }
    })
    return list;
}

//删除已经存在的压缩文件
const deleteFileAll = ({rootPath,reg})=> {
    let files = findExitFileZip({rootPath, reg});
    files.forEach((filePath, index)=> {
        if(fs.statSync(filePath).isDirectory()) { // 文件夹就循环删掉下面的文件
            deleteall(filePath);
        } else { // delete file
            // fs.unlinkSync(filePath);
            fs.unlink(filePath, (err) => { 
                console.log(err);
            });
        }
    });
};
const deleteall = (path)=> {
    let files = [];
    if(fs.existsSync(path)) {
        files = fs.readdirSync(path);
        files.forEach(function(file, index) {
            let curPath = path + "/" + file;
            // curPath.log(filePath,"filePath")
            // curPath.log(filePath,"filePath")
            if(fs.statSync(curPath).isDirectory()) { // recurse
                deleteall(curPath);
            } else { // delete file
                fs.unlinkSync(curPath);
            }
        });
        fs.rmdirSync(path);
    }
 };
exports.deleteFileAll = deleteFileAll;
exports.findExitFileZip = findExitFileZip;
exports.deleteall = deleteall;
