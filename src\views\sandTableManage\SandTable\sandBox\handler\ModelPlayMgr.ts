import Motor from "@motor/core";
import moment from "moment";
import {ParentAPI} from "postmate";
import MotorUtils from "../../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
import {MotorContext} from "../../../../../reMotor";
import WarningMarkerHandler from "../markerHandler";
import ConstructionActualSandBoxHandler from "./ConstructionActualSandBoxHandler";
import ConstructionPlanSandBoxHandler from "./ConstructionPlanSandBoxHandler";
import ConstructionPlanSandBoxHandlerForMotorFrame from "./ConstructionPlanSandBoxHandlerForMotorFrame";
import {SandBoxHandler, SandBoxHandlerForMotorFrame, SandDataInitializeBase} from "./dataOrInterface";
import ProcessActualSandBoxHandler from "./ProcessActualSandBoxHandler";
import ProcessPlanSandBoxHandler from "./ProcessPlanSandBoxHandler";
import ProcessPlanSandBoxHandlerForMotorFrame from "./ProcessPlanSandBoxHandlerForMotorFrame";
import StatusActualSandBoxHandler from "./StatusActualSandBoxHandler";
import StatusPlanSandBoxHandler from "./StatusPlanSandBoxHandler";
import StatusPlanSandBoxHandlerForMotorFrame from "./StatusPlanSandBoxHandlerForMotorFrame";

export default class ModelPlayMgr {
    private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    private sandBoxHandler: SandBoxHandler | undefined = undefined;

    private sandBoxHandlerForMotorFrame: SandBoxHandlerForMotorFrame | undefined = undefined;

    private warningMarkerHandler: WarningMarkerHandler | undefined = undefined;

    private playingTime: Date | null = null;

    constructor() {
        this.bimProject = MotorContext.getCurBIMProject();
    }

    public init(
        playDimension: string,
        playPattern: string[],
        sandTableType: string,
        dataContainer: SandDataInitializeBase,
        motorFrame: ParentAPI | null
    ): void {
        this.dataContainer = dataContainer;
        if (typeof this.warningMarkerHandler !== "undefined") {
            this.warningMarkerHandler.unInit();
        }

        this.bimProject = MotorContext.getCurBIMProject();
        switch (sandTableType) {
            case "underConstruction":
                if (playPattern.includes("actual")) {
                    this.sandBoxHandler = new ConstructionActualSandBoxHandler(dataContainer);
                } else {
                    this.sandBoxHandler = new ConstructionPlanSandBoxHandler(dataContainer);
                }

                if (playPattern.length === 2) {
                    const newItem = new ConstructionPlanSandBoxHandlerForMotorFrame(dataContainer);
                    this.sandBoxHandlerForMotorFrame = newItem;
                } else {
                    this.sandBoxHandlerForMotorFrame = undefined;
                }
                break;
            case "custom":
            default: {
                if (playDimension === "default" && playPattern.includes("actual")) {
                    this.sandBoxHandler = new StatusActualSandBoxHandler(dataContainer);
                } else if (playDimension === "default" && playPattern.includes("plan")) {
                    this.sandBoxHandler = new StatusPlanSandBoxHandler(dataContainer);
                } else if (playDimension === "procedure" && playPattern.includes("actual")) {
                    this.sandBoxHandler = new ProcessActualSandBoxHandler(dataContainer);
                } else {
                    this.sandBoxHandler = new ProcessPlanSandBoxHandler(dataContainer);
                }
                if (playPattern.length === 2) {
                    const newItem = playDimension === "default"
                        ? new StatusPlanSandBoxHandlerForMotorFrame(dataContainer)
                        : new ProcessPlanSandBoxHandlerForMotorFrame(dataContainer);
                    this.sandBoxHandlerForMotorFrame = newItem;
                } else {
                    this.sandBoxHandlerForMotorFrame = undefined;
                }
            }
        }

        if (isNotNullOrUndefined(this.sandBoxHandlerForMotorFrame)) {
            this.sandBoxHandlerForMotorFrame.setChildApi(motorFrame);
            this.sandBoxHandlerForMotorFrame.reset();
        }

        this.sandBoxHandler.reset();
        const warningMarkerHandler = new WarningMarkerHandler(playDimension === "procedure" && playPattern.includes("actual"));
        warningMarkerHandler.init(dataContainer);
        this.warningMarkerHandler = warningMarkerHandler;
    }

    unit() {
        const {warningMarkerHandler} = this;
        if (isNotNullOrUndefined(warningMarkerHandler)) {
            warningMarkerHandler.unInit();
        }
    }

    setChildApi(motorFrame: ParentAPI | null) {
        const {sandBoxHandlerForMotorFrame} = this;
        if (isNotNullOrUndefined(sandBoxHandlerForMotorFrame)) {
            sandBoxHandlerForMotorFrame.setChildApi(motorFrame);
        }
    }

    reset() {
        this.resetModel();
        const {sandBoxHandler, sandBoxHandlerForMotorFrame, warningMarkerHandler} = this;
        if (isNotNullOrUndefined(sandBoxHandler)) {
            sandBoxHandler.reset();
        }

        if (isNotNullOrUndefined(warningMarkerHandler)) {
            warningMarkerHandler.reset();
        }

        if (isNotNullOrUndefined(sandBoxHandlerForMotorFrame)) {
            sandBoxHandlerForMotorFrame.reset();
        }
    }

    showSandBoxByDate(curTime: Date): void {
        const {playingTime} = this;

        const momentEnd = moment(curTime.getTime()).startOf("day");
        const momentStart = playingTime !== null ? moment(playingTime).startOf("day") : momentEnd;
        const offset = momentEnd.diff(momentStart, "days");
        if (offset === 1) {
            this.showByDate(curTime);
        } else {
            this.jumpToDate(curTime);
        }

        this.playingTime = curTime;
    }

    private showByDate(curTime: Date) {
        const {sandBoxHandler, sandBoxHandlerForMotorFrame, warningMarkerHandler} = this;
        if (isNotNullOrUndefined(sandBoxHandler)) {
            sandBoxHandler.showSandBoxByDate(curTime);
        }

        if (isNotNullOrUndefined(warningMarkerHandler)) {
            warningMarkerHandler.showWarningMarkerByDate(curTime);
        }

        if (isNotNullOrUndefined(sandBoxHandlerForMotorFrame)) {
            sandBoxHandlerForMotorFrame.showSandBoxByDate(curTime);
        }
    }

    private jumpToDate(curTime: Date) {
        const {sandBoxHandler, sandBoxHandlerForMotorFrame, warningMarkerHandler} = this;
        if (isNotNullOrUndefined(sandBoxHandler)) {
            sandBoxHandler.jumpToDate(curTime);
        }

        if (isNotNullOrUndefined(warningMarkerHandler)) {
            warningMarkerHandler.jumpToDate(curTime);
        }

        if (isNotNullOrUndefined(sandBoxHandlerForMotorFrame)) {
            sandBoxHandlerForMotorFrame.jumpToDate(curTime);
        }
    }

    private resetModel() {
        if (isNotNullOrUndefined(this.dataContainer)
            && isNotNullOrUndefined(this.bimProject)) {
            const {bimProject} = this;
            MotorUtils.resestBIMProject(bimProject);
            bimProject.setVisibility(false);
        }

        this.playingTime = null;
    }
}
