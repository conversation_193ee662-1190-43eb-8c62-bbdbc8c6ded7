import React, {useEffect, useRef, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import Postmate from "postmate";
import moment from "moment";
import {RootState} from "../../../../../store/rootReducer";
import {InitViewerParam, OpenProjectParam} from "../../../../../reMotor/motorFrame/messageParam.type";
import {FrameMessageType} from "../../../../../reMotor/motorFrame/interface";
import {isNotNullOrUndefined, uuid} from "../../../../../assets/ts/utils";
import useStyle from "./style";
import {setMotorFrame, setMotorFrameInitStatus} from "../../../../../store/status/action";

const {motorFrameUrl} = window;
// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = window.__IWorksConfig__ as unknown as {baseUrl: string};

const emptyFrameMsg: FrameMessageType = {
    id: uuid(),
    name: "demo",
    params: "demo",
    version: 1,
    timeStamp: moment().valueOf(),
    type: "request"
};

export const frameMsgCreator = (msg: Partial<FrameMessageType>): FrameMessageType => ({...emptyFrameMsg, ...msg});

const MotorFrame = () => {
    const cls = useStyle();
    const iframeUrl = motorFrameUrl;
    const dispatch = useDispatch();
    const {token} = useSelector((state: RootState) => state.commonData);
    const {motorFrame} = useSelector((state: RootState) => state.statusData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const personRef = useRef<HTMLDivElement>(null);
    const [initViewerSuccess, setInitViewerSuccess] = useState<boolean>(false);
    // const [openProjectSuccess, setOpenProjectSuccess] = useState<boolean>(false);

    /** 组件卸载前清空frame内容 */
    useEffect(
        () => () => {
            if (motorFrame !== null) {
                motorFrame.destroy();
                dispatch(setMotorFrame(null));
                dispatch(setMotorFrameInitStatus(false));
            }
        },
        [dispatch, motorFrame]
    );

    useEffect(() => {
        const init = () => {
            if (typeof iframeUrl !== "string" || iframeUrl === "") {
                return;
            }
            const handshake = new Postmate({
                container: personRef.current,
                url: iframeUrl,
                name: "motor-frame",
                classListArray: ["motor-frame"],
                model: {
                    version: 1
                }
            });

            handshake.then((children) => {
                console.log("children", children);
                dispatch(setMotorFrame(children));
            }).catch((e) => {
                console.log("error", e);
            });
        };

        init();
    }, [dispatch, iframeUrl]);

    useEffect(() => {
        if (motorFrame === null) {
            return;
        }
        if (token !== null && token !== "") {
            // eslint-disable-next-line no-useless-catch
            try {
                const paramCmd: InitViewerParam = {
                    token,
                    baseUrl: `${baseUrl}/editor`,
                };
                const param = frameMsgCreator({name: "initViewer", params: paramCmd});
                motorFrame.call(param.name, param);
            } catch (error) {
                throw error;
            }
        }

        motorFrame.on("initViewerResponse", (val: FrameMessageType) => {
            if (typeof val !== "undefined" && typeof val.responseInfo !== "undefined") {
                setInitViewerSuccess(val.params as boolean);
            }
        });

        motorFrame.on("openProjectResponse", (val: FrameMessageType) => {
            if (typeof val !== "undefined" && typeof val.params !== "undefined") {
                dispatch(setMotorFrameInitStatus(true));
            }
        });
    }, [dispatch, motorFrame, token]);

    React.useEffect(() => {
        if (motorFrame === null) {
            return;
        }

        if (initViewerSuccess && isNotNullOrUndefined(curSandTable)) {
            // eslint-disable-next-line no-useless-catch
            try {
                const paramCmd: OpenProjectParam = {
                    id: curSandTable.motor3dId,
                    isBIM: true,
                };
                const param = frameMsgCreator({name: "openProject", params: paramCmd});
                motorFrame.call(param.name, param);
            } catch (error) {
                throw error;
            }
        }
    }, [motorFrame, curSandTable, initViewerSuccess]);


    if (typeof iframeUrl !== "string" || iframeUrl === "") {
        return null;
    }

    return (
        <div className={cls.frame} ref={personRef} />
    );
};

export default MotorFrame;
