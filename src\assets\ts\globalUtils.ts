import {message} from "antd";
import {getState} from "../../store";


// 判断有没标段信息
export const isHasSectionInfo = () => {
    if (getState().commonData.curSectionInfo === null) {
        message.warning("请先选择标段");
        return false;
    }
    return true;
};
// 判断有没标段信息 只返回boolean
export const isHasSectionInfoStatus = () => {
    if (getState().commonData.curSectionInfo === null) {
        return false;
    }
    return true;
};
