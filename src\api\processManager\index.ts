/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import {PageRes, WebRes, WebResResult} from "../common.type";
import {CompProcessItem, CompProcessListParam, CompProcessSaveParam, IworksProcessTemplate, ProcessTemplateListRes, ProcessTemplateTreeNode} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

/** 获取工序模板列表 */
export const getProcessTemplateList = async (
    params: {orgType: number; orgid: string}
): Promise<WebResResult<ProcessTemplateListRes>> => Fetch({
    methods: "post",
    url: `${baseUrl}/builder/appconfig/general/process/template/getProcessTemplateInfoWrapper`,
    data: {...params, pageParam: {orders: [], page: 1, size: 9999}}
});

/** 查询工序模板详情 */
export const getProcessTemplateTreeInfo = async (
    templateId: string
): Promise<WebResResult<ProcessTemplateTreeNode>> => Fetch({
    methods: "get",
    url: `${baseUrl}/builder/appconfig/general/process/template/getProcessTemplateTreeInfo/${templateId}`
});

/** 获取默认工序模板树 */
export const getDefaultProcessTemplateTreeInfo = async (): Promise<WebResResult<ProcessTemplateTreeNode>> => Fetch({
    methods: "get",
    url: `${baseUrl}/builder/appconfig/general/process/template/getDefaultProcessTemplateTreeInfo`
});

/** 新增-更新绑定工序模板树 */
export const saveProjProcessTmpl = async (param: IworksProcessTemplate): Promise<WebRes<string>> => Fetch({
    methods: "post",
    url: `${baseUrl}/iworks/sand-table/proj-states-tree/save-or-update`,
    data: param
});

/** 根据ppid获取当前工程关联的工序模板树 */
export const getProjProcessTmplByPpid = async (ppid: number): Promise<WebRes<IworksProcessTemplate>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/sand-table/proj-states-tree/${ppid}`
});

/** 获取构件绑定的工序列表 */
export const getProcessListByComps = async (
    param: CompProcessListParam
): Promise<WebRes<PageRes<CompProcessItem>>> => Fetch({
    methods: "post",
    url: `${baseUrl}/iworks/sand-table/search-componentState`,
    data: param
});

/** 更新构件绑定的工序列表 */
export const saveCompProcessList = async (param: CompProcessSaveParam): Promise<WebRes<string>> => Fetch({
    methods: "post",
    url: `${baseUrl}/iworks/sand-table/proj-states/save-or-update`,
    data: param
});
