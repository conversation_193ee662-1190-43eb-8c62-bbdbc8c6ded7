import {createSelector} from "reselect";
import {WBSType} from "../../api/common.type";
import {EBSNodeType} from "../../components/FormWbsEbs/data";
import {RootState} from "../rootReducer";

const getQueryCheckedNodes = (state: RootState) => state.wbsTree.queryCheckedNodes;
const getIsShow = (state: RootState) => state.wbsTree.isShow;

interface QueryEbsNodeType extends EBSNodeType {
    mergePath: string[];
}

const selectQueryWbsEbsParams = createSelector(
    [getQueryCheckedNodes, getIsShow],
    (queryCheckedNodes, isShow) => {
        const tempEbsNodes: QueryEbsNodeType[] = [];
        const tempWbsNodes: WBSType[] = [];
        if (isShow === false) {
            return undefined;
        }

        // 没有选中
        if (queryCheckedNodes.length === 0) {
            return {
                noSelect: true
            };
        }

        // 全部处理
        const nodeAll = queryCheckedNodes.find((el) => el.key === "all");
        if (nodeAll !== undefined) {
            return undefined;
        }
        queryCheckedNodes.forEach((item) => {
            if (item.type === "ebs") {
                if (item.children === undefined || item.children.length === 0) {
                    let projNode = tempEbsNodes.find((ebs) => ebs.ppid === item.ppid);
                    if (projNode === undefined) {
                        projNode = {
                            ppid: item.ppid as number,
                            projName: item.title as string,
                            mergePath: [],
                        };
                        tempEbsNodes.push(projNode);
                    }
                    if (projNode !== undefined && Array.isArray(projNode.mergePath) && item.path !== undefined) {
                        projNode.mergePath.push(item.path);
                    }
                }
            } else if (item.key !== "noRelevance") {
                tempWbsNodes.push({wbsNodeId: item.key as string, wbsNodeName: item.title as string});
            }
        });
        const tempBimBinds = tempEbsNodes.map((item) => ({
            ppid: item.ppid,
            mergePath: item.mergePath,
        }));
        const tempWbsIds = tempWbsNodes.map((item) => item.wbsNodeId);
        return {
            bindTypes: queryCheckedNodes.find((el) => el.key === "noRelevance") === undefined ? undefined : [0],
            bimBinds: tempBimBinds.length === 0 ? undefined : tempBimBinds,
            wbsIds: tempWbsIds.length === 0 ? undefined : tempWbsIds,
        };
    }
);

export default selectQueryWbsEbsParams;
