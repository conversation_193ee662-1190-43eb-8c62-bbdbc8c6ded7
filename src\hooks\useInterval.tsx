/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";

export default function useInterval(handle: any, run: boolean, ms = 5 * 60 * 1000): void {
    const handleRef = React.useRef(handle);
    const intervalRef = React.useRef<any>(null);

    React.useEffect(() => {
        handleRef.current = handle;
    });

    React.useEffect(() => {
        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
        if (run && !intervalRef.current) {
            intervalRef.current = setInterval(() => {
                handleRef.current();
            }, ms);
        }
        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
        if (!run && intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }

        return () => {
            if (intervalRef.current !== null && intervalRef.current !== undefined) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [ms, run]);
}
