/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable import/no-cycle */
import {gantt} from "@iworks/dhtmlx-gantt";
import {ColumnItem, CalendarInfo} from "../api/plan/type";
import {FromType} from "../config/interface";
import {EditorContextType, initialContext} from "../views/GanttEditor/context";
import {getPlanEndDate, getPlanStartDate} from "./calendarUtils";
import {GanttColumnItem} from "./columnUtils";
import {updateGanttConfig} from "./ganttConfig";
import {DurationFormatter, GanttDataset, LinksFormatter, GanttPerson, GanttTask} from "./interface";

class GanttManager {
    /** 用于存放编辑器context，供全局调用 */
    editorContext: EditorContextType;

    fromType: FromType;

    /** 是否是导入wbs的计划 */
    isWbs: boolean;

    /** 是否显示横道图 */
    isShowGantt: boolean;

    /** 计划开始时间 */
    planStartDate: Date;

    /** 计划完成时间 */
    planEndDate: Date;

    /** 上级计划开始时间 */
    parentPlanStartDate: Date | undefined;

    /** 上级计划完成时间 */
    parentPlanEndDate: Date | undefined;

    /** 所有列 */
    ganttAllColumnList: ColumnItem[];

    /** 本地内置列 */
    ganttLocalColumnList: GanttColumnItem[];

    /** gantt工期格式化 */
    durationFormatter: DurationFormatter;

    /** gantt前置任务格式化 */
    linksFormatter: LinksFormatter;

    /** 当前日历信息 */
    currentCalendarInfo: CalendarInfo;

    /** 当前所有人员账号-名称集合 */
    allPersonUserRealNameMap: Map<string, string>;

    /** 当前所有人员集合 */
    allPerson: GanttPerson[];

    /** 当前任务-要求时间任务缓存 */
    requestTaskCacheMap: Map<string, GanttTask | undefined>;

    constructor() {
        this.editorContext = initialContext;
        this.fromType = "plan";
        this.isWbs = false;
        this.isShowGantt = false;
        this.planStartDate = new Date(2000, 0, 1);
        this.planEndDate = new Date(2100, 11, 31);
        this.ganttAllColumnList = [];
        this.ganttLocalColumnList = [];
        this.durationFormatter = gantt.ext.formatters.durationFormatter({
            enter: "day",
            store: "day",
            format: "day",
            hoursPerDay: 24,
            short: true
        });
        this.linksFormatter = gantt.ext.formatters.linkFormatter({durationFormatter: this.durationFormatter});
        this.currentCalendarInfo = {
            ctid: "0",
            ctName: "24小时日历",
            calendarType: 0,
        } as unknown as CalendarInfo;
        this.allPersonUserRealNameMap = new Map();
        this.allPerson = [];
        this.requestTaskCacheMap = new Map();
    }

    setPlanStartDate(startDate: Date) {
        const newStartDate = getPlanStartDate(startDate);
        if (newStartDate !== undefined) {
            this.planStartDate = newStartDate;
        }
    }

    setPlanEndDate(endDate: Date) {
        const newEndDate = getPlanEndDate(endDate);
        if (newEndDate !== undefined) {
            this.planEndDate = newEndDate;
        }
    }

    // 根据页面类型设置列是否可编辑
    setLocalColumnsWithFromType(from?: FromType) {
        let columns: GanttColumnItem[] = [];
        switch (from) {
            case "plan":
                columns = [
                    {columnId: "text", editable: true},
                    {columnId: "duration", editable: true},
                    {columnId: "start_date", editable: true},
                    {columnId: "end_date", editable: true},
                    {columnId: "request_duration", editable: true},
                    {columnId: "request_start_date", editable: true},
                    {columnId: "request_end_date", editable: true},
                    {columnId: "predecessors", editable: true},
                    {columnId: "dutyUnit", editable: true},
                    {columnId: "dutyPerson", editable: true},
                ];
                break;
            case "approval":
                columns = [
                    {columnId: "text"},
                    {columnId: "duration"},
                    {columnId: "start_date"},
                    {columnId: "end_date"},
                    {columnId: "request_duration"},
                    {columnId: "request_start_date"},
                    {columnId: "request_end_date"},
                    {columnId: "predecessors"},
                    {columnId: "dutyUnit"},
                    {columnId: "dutyPerson"},
                ];
                break;
            case "actual":
                columns = [
                    {columnId: "text"},
                    {columnId: "duration"},
                    {columnId: "start_date"},
                    {columnId: "end_date"},
                    {columnId: "predecessors"},
                    {columnId: "dutyUnit"},
                    {columnId: "dutyPerson"},
                    {columnId: "taskStatus"},
                    {columnId: "actual_duration", editable: true},
                    {columnId: "actual_start", editable: true},
                    {columnId: "actual_end", editable: true},
                    {columnId: "actualPlan"},
                    {columnId: "lagDeviation"},
                    {columnId: "hasRelateModel"},
                    {columnId: "hasRelatePhoto"},
                ];
                break;
            default:
                break;
        }
        this.ganttLocalColumnList = columns;
    }

    // // 根据上级计划的开始结束时间去判断是否冲突
    // checkParentPlanDateConfict() {
    //     let isConflict = false;
    //     gantt.eachTask((task) => {
    //         if (isConflict === false) {
    //             if (this.parentPlanStartDate !== undefined && this.parentPlanEndDate !== undefined) {
    //                 if (task.start_date < this.parentPlanStartDate || task.start_date > this.parentPlanEndDate) {
    //                     isConflict = true;
    //                 }
    //                 if (task.end_date < this.parentPlanStartDate || task.end_date > this.parentPlanEndDate) {
    //                     isConflict = true;
    //                 }
    //             }
    //         }
    //     });
    //     return isConflict;
    // }

    // 根据上级计划中的任务去判断是否冲突
    checkTaskRequestDateConfict() {
        let isConflict = false;
        gantt.eachTask((task) => {
            if (isConflict === false) {
                const requestTask = this.getRequestTaskWithTask(task);
                if (requestTask !== undefined) {
                    const {request_duration, request_start_date, request_end_date} = requestTask;
                    if (request_duration !== undefined && request_start_date !== undefined && request_end_date !== undefined) {
                        if (task.duration > request_duration || task.end_date > request_end_date) {
                            isConflict = true;
                        }
                    }
                }
            }
        });
        return isConflict;
    }

    /**
     * 根据是否是wbs任务，更新横道图配置，设置当前是否是wbs数据
     */
    updateGanttConfigWithWbs(ganttData: GanttDataset) {
        const {data} = ganttData;
        this.isWbs = false;
        if (data !== undefined) {
            this.isWbs = data.some((task) => task.bindType === -1);
        }
        this.editorContext.setIsWbs(this.isWbs);
        updateGanttConfig();
    }

    /**
     * 上级计划，获取当前任务对应的要求任务，用要求任务的要求时间限制当前任务
     */
    getRequestTask(task: GanttTask) {
        // 上级计划查找要求时间逻辑
        if (
            task.request_duration !== undefined
            && task.request_start_date !== undefined
            && task.request_end_date !== undefined
        ) {
            return task;
        }
        if (task.parent === undefined || task.parent === "0" || task.parent === 0) {
            return undefined;
        }
        let parentTask: GanttTask = gantt.getTask(task.parent);
        while (parentTask !== undefined) {
            if (
                parentTask.request_duration !== undefined
                && parentTask.request_start_date !== undefined
                && parentTask.request_end_date !== undefined
            ) {
                return parentTask;
            }
            if (parentTask.parent === undefined || parentTask.parent === "0" || parentTask.parent === 0) {
                return undefined;
            }
            parentTask = gantt.getTask(parentTask.parent);
        }
        return undefined;
    }

    /**
     * 下级计划，获取最近的上级计划导入任务，用要求时间限制当前任务
     */
    getRequestTaskWithParentPlan(task: GanttTask) {
        // 下级计划查找要求时间逻辑
        if (task.planId === this.editorContext.planInfo.parentId) {
            return task;
        }
        if (task.parent === undefined || task.parent === "0" || task.parent === 0) {
            return undefined;
        }
        let parentTask: GanttTask = gantt.getTask(task.parent);
        while (parentTask !== undefined) {
            if (parentTask.planId === this.editorContext.planInfo.parentId) {
                // 如果是上级计划的任务则直接返回
                return parentTask;
            }
            if (parentTask.parent === undefined || parentTask.parent === "0" || parentTask.parent === 0) {
                return undefined;
            }
            parentTask = gantt.getTask(parentTask.parent);
        }
        return undefined;
    }

    /**
     * 获取当前任务对应的要求任务，用要求任务的要求时间限制当前任务
     */
    getRequestTaskWithTask(task: GanttTask) {
        // if (this.hasParentPlan() && this.requestTaskCacheMap.has(task.id)) {
        //     // 下级计划才走缓存
        //     return this.requestTaskCacheMap.get(task.id);
        // }
        const requestTask = this.editorContext.hasParentPlan
            ? this.getRequestTaskWithParentPlan(task)
            : this.getRequestTask(task);

        // if (this.hasParentPlan()) {
        //     this.requestTaskCacheMap.set(task.id, requestTask);
        // }
        return requestTask;
    }
}

const ganttManager = new GanttManager();

export default ganttManager;
