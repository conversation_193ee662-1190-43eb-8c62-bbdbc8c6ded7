import React from "react";
import {AutoSizer, List, ListRowProps} from "react-virtualized";
import TreeNode from "./virtualized-tree-node";

export interface TreeData<T extends TreeData<T>> {
    key: string;
    children: T[];
}

export interface VirtualizedTreeEvent {
    nodeKey: string;
    nativeEvent: MouseEvent;
    isLeaf: boolean;
}

export interface VirtualizedTreeNodeCheckedEvent extends VirtualizedTreeEvent {
    event: "check";
    checked?: boolean;
}

export interface VirtualizedTreeClickEvent extends VirtualizedTreeEvent {
    event: "click";
    mouseEvent: React.MouseEvent<HTMLElement>;
}

export interface VirtualizedTreeProps<T extends TreeData<T>> {
    data: T[];
    containerWidth?: number;
    indent?: number;
    nodeTitle: (data: T) => React.ReactNode;
    checkable?: boolean | ((data: T) => boolean);
    defaultExpandAll?: boolean;
    onExpand?: (keys: string[]) => void;
    expandedKeys?: string[];
    onCheck?: (keys: string[], e: VirtualizedTreeNodeCheckedEvent) => void;
    checkedKeys?: string[];
    expander?: React.ReactNode | ((data: T, isExpanded: boolean) => React.ReactNode);
    nodeClassName?: string | ((data: T) => string);
    rowHeight: number;
    onNodeClick?: (e: VirtualizedTreeClickEvent) => void;
}

type InternalTreeData<T extends TreeData<T>> = T & {
    depth: number;
};

export default class VirtualizedTree<T extends TreeData<T>> extends React.PureComponent<VirtualizedTreeProps<T>> {
    private maxAutoWidth = -1;

    render() {// eslint-disable-line 
        const renderedNodes = this.getRenderedNodes();
        const {rowHeight, expandedKeys, checkedKeys, indent, nodeClassName, expander, checkable, nodeTitle, containerWidth} = this.props;
        return (
            <AutoSizer>
                {({height, width: autoWidth}) => {
                    this.maxAutoWidth = Math.max(autoWidth, this.maxAutoWidth);
                    return (
                        <List
                            className="virtualized-tree"
                            height={height}
                            rowCount={renderedNodes.length}
                            rowHeight={rowHeight}
                            rowRenderer={(props: ListRowProps) => {
                                const {index, style} = props;
                                const node = renderedNodes[index];
                                const expanded = expandedKeys?.includes(node.key) ?? false;
                                const checked = checkedKeys?.includes(node.key) ?? false;
                                const isLeaf = node.children.length === 0;
                                const renderedStyle = {...style, marginLeft: node.depth * indent!};
                                return (
                                    <TreeNode<T>
                                        style={renderedStyle}
                                        key={index}
                                        nodeClassName={nodeClassName}
                                        onExpand={() => this.handleExpand(
                                            !expanded ? [...expandedKeys!, node.key] : expandedKeys!.filter((k) => k !== node.key)
                                        )}
                                        data={node}
                                        expandable={node.children.length !== 0}
                                        expander={expander}
                                        expanded={expanded}
                                        checkable={checkable}
                                        checked={checked}
                                        onClick={(e) => this.handleClick(e, node.key, isLeaf)}
                                        onCheck={(e, c) => this.handleCheck(
                                            !expanded ? [...checkedKeys ?? [], node.key] : checkedKeys!.filter((k) => k !== node.key),
                                            {event: "check", nodeKey: node.key, nativeEvent: e, checked: c, isLeaf}

                                        )}
                                        title={nodeTitle(node)}
                                    />
                                );
                            }}
                            width={containerWidth ?? this.maxAutoWidth}
                        />
                    );
                }}
            </AutoSizer>
        );
    }

    private getNodes(treeData: T[]): T[] {
        const nodes = treeData.filter((t) => t.children.length !== 0);
        return nodes.concat(nodes.map((n) => this.getNodes(n.children)).flat());
    }

    private getRenderedNodes(): InternalTreeData<T>[] {
        const {expandedKeys, data} = this.props;
        const isExpanded = (key: string) => expandedKeys?.includes(key) ?? false;

        const getChildren = (dataInner: T, depth = 1): InternalTreeData<T>[] => {
            if (isExpanded(dataInner.key)) {
                const children: InternalTreeData<T>[] = [];
                dataInner.children.forEach((i) => children.push({...i, depth}, ...getChildren(i, depth + 1)));
                return children;
            }
            return [];
        };
        return data.map((d) => [{...d, depth: 0}, ...getChildren(d)]).flat();
    }

    private handleClick(e: React.MouseEvent<HTMLDivElement, MouseEvent>, nodeKey: string, isLeaf: boolean) {
        const {onNodeClick} = this.props;
        if (onNodeClick !== undefined) {
            onNodeClick({
                event: "click",
                mouseEvent: e,
                nodeKey,
                isLeaf,
                nativeEvent: e.nativeEvent
            });
        }
    }

    private handleCheck(checkedKeys: string[], e: VirtualizedTreeNodeCheckedEvent) {
        const {onCheck} = this.props;
        if (onCheck !== undefined) {
            onCheck(checkedKeys, e);
        }
    }

    private handleExpand(expandedKeys: string[]) {
        const {onExpand} = this.props;
        if (onExpand !== undefined) {
            onExpand(expandedKeys);
        }
    }
}
