import {Button} from "antd";
import React, {ReactNode} from "react";
import FileBox, {FileBoxProps} from "./index";
import {packageDownload} from "../../api/common.api";
import {DownFileListType, FileType} from "../../api/common.type";
import ComCollapsePanel, {ComCollapsePanelProps} from "../ComCollapsePanel";
import PermissionCode, {includesCodeStatus} from "../PermissionCode";

type IComCollapsePanelProps = Omit<ComCollapsePanelProps, "children" | "header">;
export interface FileCollapsePanelProps extends IComCollapsePanelProps {
    key: React.Key;
    fileList: {name: string; value: FileType[]}[];
    header?: string;
    packName?: string;
    downloadBtnText?: string;
    fileBoxConfig?: FileBoxProps;
    children?: ReactNode;
}

const FileCollapsePanel = (props: FileCollapsePanelProps) => {
    const {
        header = "附件信息",
        downloadBtnText = "下载全部附件",
        key,
        children,
        fileList,
        packName = undefined,
        fileBoxConfig,
        ...other
    } = props;
    const [isLoading, setIsLoading] = React.useState(false);

    const handleExportFiles = React.useCallback((e) => {
        setIsLoading(true);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        const files: DownFileListType[] = fileList.map((item, index, arr) => ({
            dir: true,
            name: arr.length > 1 ? `${index + 1}.${item.name}_${header}` : `${item.name}_${header}`,
            fileList: item.value.map((i) => ({fileUuid: i.fileUuid, name: i.fileName, dir: false})),
        }));
        packageDownload({packDownParam: {fileList: files, packName: `${packName ?? files[0].name}.zip`}, fun: () => setIsLoading(false)});
    }, [fileList, header, packName]);

    return (
        <ComCollapsePanel
            header={header}
            key={key}
            operationBtn={(
                <PermissionCode authcode="Download">
                    <Button
                        type="text"
                        onClick={(e) => handleExportFiles(e)}
                        loading={isLoading}
                        disabled={fileList.every((item) => item.value.length === 0)}
                    >
                        {downloadBtnText}
                    </Button>
                </PermissionCode>
            )}
            {...other}
        >
            {children !== undefined
                ? children
                : (
                    <FileBox
                        value={fileList[0].value}
                        isDownload={includesCodeStatus("Download")}
                        isUpload={false}
                        isDelete={false}
                        isEditName={false}
                        emptyConfig={{description: `未上传${fileList[0].name}`}}
                        {...fileBoxConfig}
                    />
                )}
        </ComCollapsePanel>
    );
};

export default FileCollapsePanel;
