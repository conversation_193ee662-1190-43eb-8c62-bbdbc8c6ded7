import {jss, createUseStyles} from "react-jss";
import gaiPNG from "../../../../static/image/gai.png";

const createPlanStyles = createUseStyles({
    box: {
        display: "flex",
        flexDirection: "column",
        height: "100%",
        width: "100%",
    },
    row: {
        display: "flex",
        flexDirection: "row",
        marginBottom: 20,

        "&:last-child": {
            marginBottom: 0,
        }
    },
    title: {
        width: 130,
        marginLeft: 0,
        marginTop: 4,
        textAlign: "right",
    },
    asterisk: {
        color: "red",
        marginTop: 8,
        marginRight: 8,
    },
    button: {
        width: 350,
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        marginTop: 20,
    },
    planRoot: {
        display: "flex",
        flexDirection: "column",
        height: "100%",
        background: "#fff",
    },
});

// 覆盖gantt全局样式
jss.createStyleSheet({
    "@global": {
        ".weekend": {
            background: "#f4f7f4"
        },
        // 修改表格边框 start
        ".gantt_grid_data .gantt_row": {
            borderBottom: "1px solid #d7d7d7"
        },
        // 表格头部
        ".gantt_grid .gantt_grid_scale div.gantt_grid_head_cell": {
            color: "#061127",
            fontWeight: 700,
            textAlign: "left",
            padding: "0px 12px !important ",
            boxSizing: "border-box !important",
        },
        ".gantt_grid .gantt_grid_scale div.gantt_grid_head_lagDeviation": {
            textAlign: "right",
        },
        ".gantt_grid .gantt_grid_scale div.gantt_grid_head_text": {
            padding: "0 30px !important",
        },
        ".gantt_grid .gantt_grid_scale div.gantt_grid_head_cell, .gantt_grid .gantt_grid_data .gantt_cell": {
            borderRight: "1px solid #E1E2E5",
            padding: 0
        },
        ".gantt_grid_data .gantt_last_cell, .gantt_grid_scale .gantt_last_cell, .gantt_task .gantt_task_scale .gantt_scale_cell.gantt_last_cell, .gantt_task_bg .gantt_last_cell": {
            borderRightWidth: "0 !important"
        },
        // eslint-disable-next-line max-len
        // ".gantt_grid_editor_placeholder[data-column-name='actual_duration']::after, .gantt_grid_editor_placeholder[data-column-name='actual_start']::after, .gantt_grid_editor_placeholder[data-column-name='actual_end']::after": {
        //     display: "inline-block",
        //     position: "absolute",
        //     top: 0,
        //     width: 40,
        //     height: "100%",
        //     background: `url(${gaiPNG}) 0 0 no-repeat`,
        //     content: "''",
        // },
        ".gantt_grid_scale": {
            position: "relative"
        },
        // 修改表格边框 end
        // 修改选中颜色 start
        ".gantt_task_row.gantt_selected .gantt_task_cell": {
            borderRight: "1px solid #d7d7d7"
        },
        ".gantt_row.gantt_selected, .gantt_task_row.gantt_selected, .gantt_row:hover, .gantt_task_row:hover": {
            backgroundColor: "#f5f5f5 !important"
        },
        // 修改选中颜色 end
        ".gantt_grid_editor_placeholder > div, .gantt_grid_editor_placeholder input, .gantt_grid_editor_placeholder select": {
            width: "100% !important"
        },
        ".gantt_task_line, .gantt_line_wrapper": {
            marginTop: "-9px"
        },
        ".gantt_side_content": {
            marginBottom: "7px"
        },
        ".gantt_task_link .gantt_link_arrow": {
            marginTop: "-12px"
        },
        ".gantt_task_line": {
            backgroundColor: "#5393ea",
            border: "1px solid #5393ea",
        },
        ".gantt_task_line.gantt_critical_task": {
            backgroundColor: "#f47557",
            border: "1px solid #f47557",
        },
        ".gantt_critical_link .gantt_line_wrapper>div": {
            backgroundColor: "#f47557",
        },
        ".gantt_critical_link .gantt_link_arrow": {
            borderColor: "#f47557",
        },
        ".gantt_side_content.gantt_right": {
            bottom: 0
        },
        ".gantt_container_hide_timeline": {
            "& .timeline_cell": {
                display: "none",
            },
            // "& .gantt_layout_y.gantt_layout_cell_border_right": {
            //     borderRight: "none",
            // },
        },
        ".baseline": {
            position: "absolute",
            borderRadius: "2px",
            opacity: 0.6,
            marginTop: "-7px",
            height: "12px",
            background: "#ffd180",
            border: "1px solid rgb(255,153,0)"
        },
        ".gantt_grid_ellipsis_text": {
            display: "inline-block",
            width: "100%",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
        },
        ".fa": {
            cursor: "pointer",
            fontSize: "14px",
            textAlign: "center",
            opacity: 0.2,
            padding: "5px",
        },
        ".fa:hover": {
            opacity: 1
        },
        ".fa-pencil": {
            color: "#ffa011"
        },
        ".fa-plus": {
            color: "#328EA0"
        },
        ".fa-times": {
            color: "red"
        },
        ".gantt_modal_box": {
            width: "300px",

            "& .gantt_tooltip": {
                maxWidth: "none",
            },

            "&.gantt-excel-form": {
                width: "802px",
                maxWidth: "802px",
                "& table": {
                    // width: "1100px",
                    minWidth: "750px",
                    textAlign: "left",
                    borderCollapse: "collapse",
                    // "& tr": {height: 48},
                    "& th": {
                        textAlign: "center",
                        whiteSpace: "nowrap",
                    },
                    // "& th:nth-of-type(3)": {
                    //     textAlign: "right",
                    //     // whiteSpace: "nowrap",
                    // },

                    "& td": {
                        border: "1px solid #aeaeae",
                        whiteSpace: "nowrap",
                        padding: "5px",
                        "& select": {
                            width: "100%",
                            minWidth: "87px",
                            padding: "5px",
                        }
                    }
                },
                "& .dhtmlx_popup_text": {
                    maxHeight: "600px",
                    overflow: "auto",
                }
            },
            "& .gantt_popup_title": {
                fontWeight: "bold",
                textAlign: "center",
            },
            "& .gantt_popup_button": {
                minWidth: "100px",
                border: "1px solid #cecece",
                height: "30px",
                lineHeight: "30px",
                display: "inline-block",
                margin: "0 5px",
                borderRadius: "4px",
                background: "#ffffff",
            },
            "& .gantt_ant_button": {
                lineHeight: "1.5715",
                position: "relative",
                display: "inline-block",
                fontWeight: "400",
                whiteSpace: "nowrap",
                textAlign: "center",
                backgroundImage: "none",
                boxShadow: "0 2px 0 rgb(0 0 0 / 2%)",
                cursor: "pointer",
                transition: "all .3s cubic-bezier(.645,.045,.355,1)",
                userSelect: "none",
                touchAction: "manipulation",
                height: "32px",
                padding: "4px 15px",
                fontSize: "14px",
                borderRadius: "2px",
                color: "rgba(0,0,0,.85)",
                background: "#fff",
                border: "1px solid #d9d9d9",
            },
            "& .gantt_ant_primary_button": {
                lineHeight: "1.5715",
                position: "relative",
                display: "inline-block",
                fontWeight: "400",
                whiteSpace: "nowrap",
                textAlign: "center",
                backgroundImage: "none",
                boxShadow: "0 2px 0 rgb(0 0 0 / 5%)",
                cursor: "pointer",
                transition: "all .3s cubic-bezier(.645,.045,.355,1)",
                userSelect: "none",
                touchAction: "manipulation",
                height: "32px",
                padding: "4px 15px",
                fontSize: "14px",
                borderRadius: "2px",
                color: "#fff",
                background: "#545B99",
                border: "1px solid #545B99",
                borderColor: "#545B99",
                textShadow: "0 -1px 0 rgb(0 0 0 / 12%)",
            },
        },
        ".gantt_input_number": {
            "-moz-appearance": "textfield",

            "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
                "-webkit-appearance": "none",
            }
        },
        ".gantt_input_custom_number": {
            "-moz-appearance": "textfield",
            "pointer-events": "all",

            "&::-webkit-outer-spin-button, &::-webkit-inner-spin-button": {
                "-webkit-appearance": "none",
                "pointer-events": "all",
            }
        },
        ".gantt-project-wrapper": {
            position: "absolute",
            height: "16px",
            overflowX: "hidden",
            color: "#ffffff",
        },
        ".gantt-project-wrapper div": {
            position: "absolute",
        },
        ".project-bar": {
            height: "10px",
            width: "100%",
            backgroundColor: "#26d19f",
        },
        ".project-left, .project-right": {
            top: "10px",
            backgroundColor: "transparent",
            borderStyle: "solid",
            width: "0px",
            height: "0px",
        },
        ".project-left": {
            left: "0px",
            borderWidth: "0px 0px 6px 6px",
            borderTopColor: "transparent",
            borderRightColor: "transparent !important",
            borderBottomColor: "transparent !important",
            borderLeftColor: "#26d19f !important",
        },
        ".project-right": {
            right: "0px",
            borderWidth: "0px 6px 6px 0px",
            borderTopColor: "transparent",
            borderRightColor: "#26d19f",
            borderBottomColor: "transparent !important",
            borderLeftColor: "transparent",
        },
        ".custom-gantt-project-baseline": {
            position: "absolute",
            borderRadius: "2px",
            opacity: 0.6,
            height: "10px",
            background: "transparent",
            borderWidth: "2px 2px 0px 2px",
            borderStyle: "solid",
            borderColor: "black",
            // borderTopColor: "black",
            // borderRightColor: "black",
            // borderBottomColor: "transparent",
            // borderLeftColor: "black",
        },
        ".gantt_column_gai": {
            marginLeft: "-12px !important",
            textIndent: "12px !important",
            background: `url(${gaiPNG}) 0 0 no-repeat`
        },
        ".gantt_tree_content:not(:nth-child(2))": {
            padding: "0px 12px",
            boxSizing: "border-box !important",
        },
        // ".gantt_tree_icon.gantt_blank": {
        //     display: "none !important"
        // }
    }
}).attach();

export default createPlanStyles;
