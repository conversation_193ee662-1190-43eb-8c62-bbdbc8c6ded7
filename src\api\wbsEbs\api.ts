import {WebRes} from "../common.type";
import Fetch from "../../service/Fetch";
import {BindWbsEbsType, EbsBindWbsType, EbsType, PostListWbsEbsNodeRes, WbsBindEbsType, PostTaskEbsNodeWithTimeListRes, TaskBindEbsNodeType, ListEbsNodeResponse, PostWbsBindEbsParams, PostWbsBindEbsNode} from "./type";

/**
 * 获取已绑定的ebs
 */
export const getBindEbs = async (params: {}) => Fetch<WebRes<EbsType[]>>({
    url: "/sphere/plan/wbs/bind-ebs-list/v1",
    methods: "post",
    data: params
});

/**
 * 绑定wbs或ebs节点
 */
export const bindWbsEbs = async (params: {}) => Fetch<WebRes<string>>({
    url: "/sphere/plan/wbs/bind-node",
    methods: "post",
    data: params,
});

/**
 * 根据业务id获取绑定的wbs或ebs节点
 */
export const getBindWbsEbsById = async (params: {}) => Fetch<WebRes<BindWbsEbsType[]>>({
    url: "/sphere/plan/wbs/list-bind-node",
    methods: "post",
    data: params
});

/**
 * 获取EBS关联的wbs
 */
export const getEbsBindWbs = async (params: {}) => Fetch<WebRes<EbsBindWbsType[]>>({
    url: "/sphere/plan/wbs/path-ebs-list-by-wbs",
    methods: "post",
    data: params
});

/**
 * 获取Wbs关联的Ebs
 */
export const getWbsBindEbs = async (params: {}) => Fetch<WebRes<WbsBindEbsType[]>>({
    url: "/sphere/plan/wbs/path-wbs-list-by-ebs",
    methods: "post",
    data: params
});

// 获取已绑定的wbs
export const getBindWBSList = async (params: {}) => Fetch<WebRes<string[]>>({
    methods: "post",
    url: "/sphere/plan/wbs/bind-wbs-list/v1",
    data: params,
});

// 根据业务id查询绑定的ebs节点
export const postWbsEbsNodeList = async (businessIds: string[], _ppid?: string) => Fetch<WebRes<PostListWbsEbsNodeRes>>({
    url: "/sphere/plan/wbs/list-ebs-node",
    methods: "post",
    data: businessIds,
});

// 根据业务id查询是否绑定了ebs节点
export const postTaskBindEbsNodeList = async (businessIds: string[], _ppid?: string) => Fetch<WebRes<TaskBindEbsNodeType[]>>({
    url: "/sphere/plan/wbs/list-ifbind-ebs",
    methods: "post",
    data: businessIds,
});

// 查询任务及其子任务绑定的ebs
export const getTaskEbsNodeList = async (businessId: string) => Fetch<WebRes<ListEbsNodeResponse[]>>({
    url: `/sphere/plan/wbs/list-ebs-node?taskId=${businessId}`,
    methods: "get",
});

// 根据wbsId查询wbs绑定关系(根据ppid分组)
export const postWbsBindEbs = async (params: PostWbsBindEbsParams) => Fetch<WebRes<PostWbsBindEbsNode[]>>({
    url: "/luban-infrastructure-center/wbs-bind-ebs/query-bind-ppid",
    methods: "post",
    data: params,
});

// 根据业务id查询绑定的ebs节点和变更时间
export const postTaskEbsNodeWithTimeList = async (businessIds: string[], _ppid?: string) => Fetch<WebRes<PostTaskEbsNodeWithTimeListRes>>({
    url: "/sphere/plan/task/list-ebs-node",
    methods: "post",
    data: businessIds,
});

/**
 * 获取我的任务已绑定的ebs
 */
export const getMyTaskBIndEBSList = async (params: {}) => Fetch<WebRes<EbsType[]>>({
    methods: "post",
    url: "/sphere/inspectionTask/bind-ebs-list",
    data: params
});

// 获取我的任务已绑定的wbs
export const getMyTaskBindWBSList = async (params: {}) => Fetch<WebRes<string[]>>({
    methods: "post",
    url: "/sphere/inspectionTask/bind-wbs-list",
    data: params
});

// 获取整改的已绑定的wbs节点列表
export const getReformBindWBSList = async (params: {}) => Fetch<WebRes<string[]>>({
    methods: "post",
    url: "/process/reform/instance/bind-wbs-list",
    data: params,
});
