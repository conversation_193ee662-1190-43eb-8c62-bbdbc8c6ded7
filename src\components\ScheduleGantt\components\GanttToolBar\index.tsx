import React, {CSSProperties, ReactElement} from "react";
import {Row} from "antd";
import {GanttButtonType} from "../GanttButton";
import ButtonList from "../GanttButton/ButtonList";

interface GanttToolBarProps {
    leftButtons?: (ReactElement | GanttButtonType)[];
    rightButtons?: (ReactElement | GanttButtonType)[];
    style?: CSSProperties;
}

const GanttToolBar = (props: GanttToolBarProps) => {
    const {leftButtons, rightButtons, style} = props;
    return (
        <Row justify="space-between" align="middle" style={style}>
            <ButtonList buttons={leftButtons} />
            <ButtonList buttons={rightButtons} />
        </Row>
    );
};

export default GanttToolBar;
