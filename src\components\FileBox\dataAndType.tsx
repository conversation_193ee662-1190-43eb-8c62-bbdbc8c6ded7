import React, {ReactNode} from "react";
import MyIcon from "../MyIcon";

export const iconStyle = {
    fontSize: 44
};

export const Icons: {
    [k: string]: ReactNode;
} = {
    txt: <MyIcon type="icon-wenbengeshi" style={iconStyle} />,
    pdf: <MyIcon type="icon-pdfgeshi" style={iconStyle} />,
    xlsx: <MyIcon type="icon-excelgeshi" style={iconStyle} />,
    xls: <MyIcon type="icon-excelgeshi" style={iconStyle} />,
    pic: <MyIcon type="icon-pptgeshi" style={iconStyle} />,
    ppt: <MyIcon type="icon-pptgeshi" style={iconStyle} />,
    word: <MyIcon type="icon-wordgeshi" style={iconStyle} />,
    rar: <MyIcon type="icon-rargeshi" style={iconStyle} />,
    video: <MyIcon type="icon-shipingeshi" style={iconStyle} />,
    zip: <MyIcon type="icon-zipgeshi" style={iconStyle} />,
    file: <MyIcon type="icon-wenjian" style={iconStyle} />,
    unknown: <MyIcon type="icon-weizhigeshi" style={iconStyle} />
};
