export interface ProjectDurationResult {
    /** 是否自动完工：1：自动设置；0：手动设置 */
    autoFinish: 1 | 0;
    startDate?: number;// 工程实际开始时间
    endDate?: number;// 工程实际竣工时间
    planStartDate: number;// 工程计划开始时间
    planEndDate: number;// 工程计划竣工时间
    ppid: number;
    /** 是否启用报警 1：启用；0：未启用 */
    warnStatus: 1 | 0;
}

export interface ProjectInfoType {
    handle: string;
    path: string;
}

export interface SandMemberListParams {
    page: number;
    ppid: number;
    size: number;
    projectInfos?: ProjectInfoType[]; // 构件信息集合 为空表示查询全部
}

export interface ComponentStateLifeCycle {
    planEndDate: number;
    planStartDate: number;
    startDate: number;
    endDate: number;
}

export interface SandMemberInfo {
    earlyWarn: boolean; // 提前报警 true:是，false：否
    lifeCycles: ComponentStateLifeCycle; // 构件状态的生命周期信息
    stateColor: string;
    stateKey: string;
    stateName: string;
}

export interface SandMemberListItem {
    finishEndDate: number; // 完工时间
    finishStateKey: string; // 完工工序id，最晚一条完工工序的id
    id: number;
    infos: number; // 工序集合,为空代表删除改记录
    ppid: number; // 工程id
    projectInfo: ProjectInfoType; // 构件信息
    stateMd5: string; // 工序md5，用于判断工序集合内容是否一致
}

export interface SandMemberListResult {
    items: SandMemberListItem[];
    totalCount: number;
    totalPage: number;
}

export interface WarnTypeListResult {
    color: string; // 预警颜色
    desc: string; // 预警条件描述
    id: string;
    maxCompare: number | null; // 小于：3；小于等于：4 integer(int32)
    maxDay: number | null; // 小于多少天
    minCompare: number | null; // 大于：1；大于等于2；
    minDay: number | null; // 大于多少天
    name: string; // 预警名称
    /** 流程分类（1：蓝色预警；2：黄色预警；3：橙色预警；4：红色预警） */
    type: number;
    updatedBy: string; // 更新人
    updatedOn: number; // 更新时间
}

export interface WbsComponentStateParams {
    page: number;
    ppid: number;
    size: number;
    sort?: string;
}

export interface ComponentStateItem {
    businessType: number; // 节点业务类型(1单位、2子单位、3分部、4子分部、5分项、6子分项、7构件、8工序、9检验批)

    handle: string;
    path: string;
    startDate?: number;// 工程实际开始时间
    endDate?: number;// 工程实际竣工时间
    planStartDate: number;// 工程计划开始时间
    planEndDate: number;// 工程计划竣工时间
}

export interface ComponentStateResult {
    items: ComponentStateItem[];

    totalCount: number;
    totalPage: number;
}
