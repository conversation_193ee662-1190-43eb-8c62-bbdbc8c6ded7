/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-explicit-any */
import Motor from "@motor/core";
import moment, {Moment} from "moment";
import {isEmpty, isNotNullOrUndefined} from "../../../../../assets/ts/utils";

import {CompNecInfo, CompProcessItem, CompStateParam, StateWarningPeriodInfo} from "../../../../../api/processManager/type";
import {getProjectDuration, getWarnTypeList} from "../../../../../api/sandManager";
import {WarnTypeListResult} from "../../../../../api/sandManager/type";

import {MotorContext} from "../../../../../reMotor";
import {BIMSandTableInfo} from "../../../../../store/status/types";
import {CompSandBoxItem, CompSandBoxItemOfSingleTimePeriod, CompStatusChangedInfo, SandBoxFilterInfo, SandBoxFilterItem, SandBoxFilterType, SandBoxTimeScope, SandDataInitializeBase, StateInfoOfTimePeriod, WarningTypeExpiredInfo, WarningTypeInfo} from "./dataOrInterface";
import {getAllProcessList} from "../../helps";

export const compSeparator = "✉";
export const pathSeparator = "☎";

// const PAGESIZEMAX = 1000;

const PAGESIZEMAX_COMPBIND = 5000;

export default class BimSandDataInitialize implements SandDataInitializeBase {
    private bimProject: Motor.Model | null = null;

    private bimInfo: BIMSandTableInfo | undefined = undefined;

    private projectTimeScope: SandBoxTimeScope | undefined = undefined;

    private sandBoxTimeScope: SandBoxTimeScope | undefined = undefined;

    private mapWarnInfo: Map<string, WarnTypeListResult> = new Map();

    private warnExpiredConfig: WarningTypeExpiredInfo | undefined = undefined;

    private mapCompInfo = new Map<string, CompSandBoxItem>();

    private mapPlanChangedInfoByTime = new Map<number, CompSandBoxItemOfSingleTimePeriod[]>(); // 进度工序变化

    private mapActualChangedInfoByTime = new Map<number, CompSandBoxItemOfSingleTimePeriod[]>(); // 实际工序变化

    private mapPlanStatusChangedInfoByTime = new Map<number, CompStatusChangedInfo[]>();// 进度默认模式状态变化

    private mapWarningInfoByTime = new Map<number, WarningTypeInfo>();

    private mapPathAndComp = new Map<string, string[]>();// EBS 筛选

    private mapStateAndComp = new Map<string, string[]>();// 工序列表 筛选

    private mapHandleList = new Map<string, Motor.Element>();

    private filterInfo: SandBoxFilterInfo = {
        ebsInfo: {isCheckAll: true, setCheckedKeys: new Set()},
        processInfo: {isCheckAll: true, setCheckedKeys: new Set()},
        defaultStatusInfo: {isCheckAll: true, setCheckedKeys: new Set()},
        warnInfo: {isCheckAll: true, setCheckedKeys: new Set()},
    };

    constructor(bimInfo: BIMSandTableInfo) {
        this.bimProject = MotorContext.getCurBIMProject();
        this.bimInfo = bimInfo;
    }

    async refresh(): Promise<void> {
        this.resetData();

        if (isNotNullOrUndefined(this.bimProject)) {
            await this.loadBase();
        }
    }

    setFilterInfo(filterType: SandBoxFilterType, filterInfo: SandBoxFilterItem) {
        switch (filterType) {
            case "ebs":
                this.filterInfo.ebsInfo = {...filterInfo};
                break;
            case "process":
                this.filterInfo.processInfo = {...filterInfo};
                break;
            case "defaultStatus":
                this.filterInfo.defaultStatusInfo = {...filterInfo};
                break;
            case "warn":
                this.filterInfo.warnInfo = {...filterInfo};
                break;
            default:
                break;
        }
    }

    getFilterInfo(filterType: SandBoxFilterType): SandBoxFilterItem {
        const {ebsInfo, processInfo, defaultStatusInfo, warnInfo} = this.filterInfo;
        switch (filterType) {
            case "ebs":
                return ebsInfo;
            case "process":
                return processInfo;
            case "defaultStatus":
                return defaultStatusInfo;
            case "warn":
            default:
                return warnInfo;
        }
    }

    queryAllBindHandleList(): string[] {
        const ids: string[] = [];
        this.mapHandleList.forEach((item, key) => {
            const isBind = this.mapCompInfo.has(key);
            if (isBind) {
                ids.push(item.id ?? "");
            }
        });
        return ids;
    }

    // 查询当前ebs树已勾选的构件
    queryVisibilityByEbsFilter() {
        const {filterInfo, mapPathAndComp} = this;
        if (filterInfo.ebsInfo.isCheckAll) {
            return {isCheckAll: true, comps: []};
        }
        let compIdList: string[] = [];
        const checkedList = Array.from(filterInfo.ebsInfo.setCheckedKeys);
        for (let i = 0; i < checkedList.length; ++i) {
            const find = mapPathAndComp.get(checkedList[i]);
            if (typeof find !== "undefined") {
                compIdList = compIdList.concat(find);
            }
        }
        const comps = this.queryComponentsByCompKeyFromCache(compIdList);
        return {isCheckAll: false, comps};
    }

    setvisibilityByEbsFilter() {
        const {filterInfo, mapPathAndComp, bimProject} = this;
        let compIdList: string[] = [];
        if (!filterInfo.ebsInfo.isCheckAll) {
            const checkedList = Array.from(filterInfo.ebsInfo.setCheckedKeys);
            for (let i = 0; i < checkedList.length; ++i) {
                const find = mapPathAndComp.get(checkedList[i]);
                if (typeof find !== "undefined") {
                    compIdList = compIdList.concat(find);
                }
            }

            if (isNotNullOrUndefined(bimProject)) {
                bimProject.setVisibility(false);
                const comps = this.queryComponentsByCompKeyFromCache(compIdList);
                if (comps.length > 0) {
                    bimProject.setVisibility(true, comps);
                }
            }
        }
    }

    hasSandBoxData(): boolean {
        return this.mapCompInfo.size > 0;
    }

    getPlaybackStartEndTimes(playPattern: string[]): Moment[] {
        const {projectTimeScope} = this;
        let timeList: Moment[] = [];
        if (isNotNullOrUndefined(projectTimeScope)) {
            if (playPattern.includes("plan")) {
                timeList = [...timeList, moment(projectTimeScope.planStartTime), moment(projectTimeScope.planEndTime)];
            }
            if (playPattern.includes("actual")) {
                if (isNotNullOrUndefined(projectTimeScope.actualStartTime) && !isNotNullOrUndefined(projectTimeScope.actualEndTime)
                    && projectTimeScope.actualStartTime.getTime() < moment().endOf("day").valueOf()) {
                    timeList = [...timeList, moment(projectTimeScope.actualStartTime), moment()];
                } else {
                    timeList = [...timeList, moment(projectTimeScope.actualStartTime), moment(projectTimeScope.actualEndTime)];
                }
            }
            const validTimeList = timeList.filter((i) => i.isValid());
            if (validTimeList.length >= 2) {
                timeList = [moment.min(validTimeList).startOf("day"), moment.max([...validTimeList, moment()]).endOf("day")];
            } else {
                timeList = validTimeList;
            }
        }

        return timeList;
    }

    getCompSandBoxList(): CompSandBoxItem[] {
        return Array.from(this.mapCompInfo.values());
    }

    setAllBimCompVisible(visible: boolean): void {
        if (isNotNullOrUndefined(this.bimProject)) {
            this.bimProject.setVisibility(visible);
        }
    }

    queryCompListByProcessIdS(ids: string[]): string[] {
        let reusltList: string[] = [];
        for (let i = 0; i < ids.length; ++i) {
            const find = this.mapStateAndComp.get(ids[i]);
            if (isNotNullOrUndefined(find)) {
                const findComp = this.queryComponentsByCompKeyFromCache(find);
                if (isNotNullOrUndefined(findComp)) {
                    reusltList = reusltList.concat(findComp.map((el) => el.id ?? ""));
                }
            }
        }

        return reusltList;
    }

    queryComponentsByCompKeyFromCache(compKeyList: string[]): Motor.Element[] {
        const comps: any[] = [];
        compKeyList.forEach((el: string) => {
            const findComp = this.mapHandleList.get(el);
            if (isNotNullOrUndefined(findComp)) {
                comps.push(findComp);
            }
        });

        return comps;
    }

    queryComponentNameFromCache(compKey: string): string {
        const findComp = this.mapHandleList.get(compKey);
        if (isNotNullOrUndefined(findComp)) {
            return findComp.name;
        }

        return "";
    }

    getProjectSandTimeScope(): SandBoxTimeScope | undefined {
        return this.projectTimeScope;
    }

    getWarnIdByType(warnType: number): string | undefined {
        const warnTypeList = Array.from(this.mapWarnInfo.entries());
        for (let i = 0; i < warnTypeList.length; ++i) {
            const {id, type} = warnTypeList[i][1];
            if (warnType === type) {
                return id;
            }
        }

        return undefined;
    }

    getWarnNameById(warnId: string): string {
        const findName = this.mapWarnInfo.get(warnId);
        if (typeof findName !== "undefined") {
            return findName.name;
        }

        return "";
    }

    getWarnTypeById(warnId: string): number {
        const findName = this.mapWarnInfo.get(warnId);
        if (typeof findName !== "undefined") {
            return findName.type;
        }

        return 0;
    }

    queryWarningListByTime(date: Date): WarningTypeInfo | undefined {
        const find = this.mapWarningInfoByTime.get(date.getTime());
        return find;
    }

    queryAccumulatedWarningListByTime(date: Date): WarningTypeInfo | undefined {
        const result: WarningTypeInfo = {
            warningType1CompList: [],
            warningType2CompList: [],
            warningType3CompList: [],
            warningType4CompList: [],
            disappearedCompList: [],
        };

        const dateValue = date.getTime();
        let type1List: string[] = [];
        let type2List: string[] = [];
        let type3List: string[] = [];
        let type4List: string[] = [];
        const itemsList = Array.from(this.mapWarningInfoByTime);
        for (let i = 0; i < itemsList.length; ++i) {
            const item = itemsList[i];
            if (item[0] <= dateValue) {
                const setDisappearedComp = new Set<string>(item[1].disappearedCompList);
                type1List = type1List.concat(item[1].warningType1CompList).filter((el) => !setDisappearedComp.has(el));
                type2List = type2List.concat(item[1].warningType2CompList).filter((el) => !setDisappearedComp.has(el));
                type3List = type3List.concat(item[1].warningType3CompList).filter((el) => !setDisappearedComp.has(el));
                type4List = type4List.concat(item[1].warningType4CompList).filter((el) => !setDisappearedComp.has(el));
            } else {
                break;
            }
        }

        type4List = Array.from(new Set<string>(type4List));
        type3List = Array.from(new Set<string>(type3List));
        type2List = Array.from(new Set<string>(type2List));
        type1List = Array.from(new Set<string>(type1List));

        type3List = type3List.filter((x) => !type4List.some((item) => x === item));

        type2List = type2List.filter((x) => !type4List.some((item) => x === item) && !type3List.some((item) => x === item));

        type1List = type1List.filter((x) => !type4List.some((item) => x === item)
            && !type3List.some((item) => x === item) && !type2List.some((item) => x === item));

        result.warningType1CompList = type1List;
        result.warningType2CompList = type2List;
        result.warningType3CompList = type3List;
        result.warningType4CompList = type4List;
        result.disappearedCompList = [];
        return result;
    }

    queryCompStatusListByPlanTime(date: Date): CompStatusChangedInfo[] {
        const resultList: CompStatusChangedInfo[] = [];
        const find = this.mapPlanStatusChangedInfoByTime.get(date.getTime());
        if (isNotNullOrUndefined(find)) {
            const itemsList = find;
            for (let i = 0; i < itemsList.length; ++i) {
                if (!this.isInFilter(itemsList[i].compInfo)) {
                    resultList.push(itemsList[i]);
                }
            }
        }
        return resultList;
    }

    queryAccumulatedCompStatusListByPlanTime(date: Date): CompStatusChangedInfo[] {
        let resultList: CompStatusChangedInfo[] = [];

        const dateValue = date.getTime();
        const mapResult: Map<string, CompStatusChangedInfo> = new Map();
        const itemsList = Array.from(this.mapPlanStatusChangedInfoByTime);
        for (let i = 0; i < itemsList.length; ++i) {
            const item = itemsList[i];
            if (item[0] <= dateValue) {
                for (let j = 0; j < item[1].length; ++j) {
                    const elem = item[1][j];
                    if (!this.isInFilter(elem.compInfo)) {
                        mapResult.set(elem.compKey, elem);
                    }
                }
            } else {
                break;
            }
        }

        const dataList = Array.from(mapResult);
        resultList = dataList.map((el) => el[1]);

        return resultList;
    }

    queryCompSandBoxListByPlanTime(date: Date): CompSandBoxItemOfSingleTimePeriod[] {
        const resultList: CompSandBoxItemOfSingleTimePeriod[] = [];
        const find = this.mapPlanChangedInfoByTime.get(date.getTime());
        if (isNotNullOrUndefined(find)) {
            const itemsList = find;
            for (let i = 0; i < itemsList.length; ++i) {
                if (!this.isInFilter(itemsList[i].compInfo)) {
                    resultList.push(itemsList[i]);
                }
            }
        }
        return resultList;
    }

    queryAccumulatedCompSandBoxListByPlanTime(date: Date): CompSandBoxItemOfSingleTimePeriod[] {
        let resultList: CompSandBoxItemOfSingleTimePeriod[] = [];

        const dateValue = date.getTime();
        const mapResult: Map<string, CompSandBoxItemOfSingleTimePeriod> = new Map();
        const itemsList = Array.from(this.mapPlanChangedInfoByTime);
        for (let i = 0; i < itemsList.length; ++i) {
            const item = itemsList[i];
            if (item[0] <= dateValue) {
                for (let j = 0; j < item[1].length; ++j) {
                    const elem = item[1][j];
                    if (!this.isInFilter(elem.compInfo)) {
                        mapResult.set(elem.compKey, elem);
                    }
                }
            } else {
                break;
            }
        }

        const dataList = Array.from(mapResult);
        resultList = dataList.map((el) => el[1]);

        return resultList;
    }

    queryCompSandBoxListByActualTime(date: Date): CompSandBoxItemOfSingleTimePeriod[] {
        const resultList: CompSandBoxItemOfSingleTimePeriod[] = [];
        const find = this.mapActualChangedInfoByTime.get(date.getTime());
        if (isNotNullOrUndefined(find)) {
            const itemsList = find;
            for (let i = 0; i < itemsList.length; ++i) {
                if (!this.isInFilter(itemsList[i].compInfo)) {
                    resultList.push(itemsList[i]);
                }
            }
        }
        return resultList;
    }

    queryAccumulatedCompTimePeriodListByActualTime(date: Date): CompSandBoxItemOfSingleTimePeriod[] {
        let resultList: CompSandBoxItemOfSingleTimePeriod[] = [];

        const dateValue = date.getTime();
        const mapResult: Map<string, CompSandBoxItemOfSingleTimePeriod> = new Map();
        const itemsList = Array.from(this.mapActualChangedInfoByTime);
        for (let i = 0; i < itemsList.length; ++i) {
            const item = itemsList[i];
            if (item[0] <= dateValue) {
                for (let j = 0; j < item[1].length; ++j) {
                    const elem = item[1][j];
                    if (!this.isInFilter(elem.compInfo)) {
                        mapResult.set(elem.compKey, elem);
                    }
                }
            } else {
                break;
            }
        }

        const dataList = Array.from(mapResult);
        resultList = dataList.map((el) => el[1]);

        return resultList;
    }

    queryAccumulatedCompListByActualTime(date: Date): string[] {
        const items = this.queryAccumulatedCompTimePeriodListByActualTime(date).map((el) => el.compKey);
        return items;
    }

    queryCompStatusListByActualTime(_date: Date): CompStatusChangedInfo[] {
        return [];
    }

    queryAccumulatedCompStatusListByActualTime(_date: Date): CompStatusChangedInfo[] {
        return [];
    }

    public getHandleFromCompKey(compKey: string): string | undefined {
        const array = compKey.split(compSeparator);
        if (array.length === 2) {
            return array[0];
        }

        return undefined;
    }

    public getPathFromCompKey(compKey: string): string | undefined {
        const array = compKey.split(compSeparator);
        if (array.length === 2) {
            return array[1];
        }

        return undefined;
    }

    private isInFilter(compInfo: CompNecInfo): boolean {
        const {filterInfo} = this;
        const {ebsInfo} = filterInfo;
        if (!ebsInfo.isCheckAll) {
            if (ebsInfo.setCheckedKeys.size === 0) {
                return true;
            }

            return !ebsInfo.setCheckedKeys.has(compInfo.path);
        }

        return false;
    }

    private getProjectSandboxEndTime(): Date {
        let time = 0;
        if (isNotNullOrUndefined(this.projectTimeScope)) {
            const {planEndTime, actualEndTime} = this.projectTimeScope;
            if (isNotNullOrUndefined(planEndTime)) {
                if (isNotNullOrUndefined(actualEndTime)) {
                    time = planEndTime < actualEndTime ? actualEndTime.getTime() : planEndTime.getTime();
                } else {
                    time = planEndTime.getTime();
                }
            }
        }

        if (time > 0) {
            return new Date(moment(Math.max(moment().valueOf(), time)).add(1, "days").startOf("day").valueOf());
        }

        return new Date(moment().add(1, "days").startOf("day").valueOf());
    }

    private generateCompKey(handle: string, path: string) {
        return handle + compSeparator + path;
    }

    private getMachedWarningItem(startTime: number, endTime: number, warningList: StateWarningPeriodInfo[]): string {
        for (let i = 0; i < warningList.length; ++i) {
            const elem = warningList[i];
            if (startTime >= elem.planEndTime && endTime <= elem.actualEndTime) {
                return elem.periodKey;
            }
        }
        return "";
    }

    private getMachedPlanStateItem(startTime: number, endTime: number, stateItems: CompStateParam[]): CompStateParam | undefined {
        for (let i = stateItems.length - 1; i >= 0; --i) {
            const elem = stateItems[i];
            if (startTime >= elem.lifeCycles.planStartDate && endTime <= elem.lifeCycles.planEndDate) {
                return elem;
            }
        }
        return undefined;
    }

    private getMachedActualStateItem(startTime: number, endTime: number, stateItems: CompStateParam[]): CompStateParam | undefined {
        for (let i = stateItems.length - 1; i >= 0; --i) {
            const elem = stateItems[i];
            const {startDate, endDate} = elem.lifeCycles;
            if (isNotNullOrUndefined(startDate)) {
                if (startTime >= startDate && (!isNotNullOrUndefined(endDate) || endTime <= endDate)) {
                    return elem;
                }
            }
        }
        return undefined;
    }

    private mergeWarningTimePeriodList(periodList: StateWarningPeriodInfo[]): StateWarningPeriodInfo[] {
        const resultList: StateWarningPeriodInfo[] = [];

        if (periodList.length > 0) {
            let prevItem: StateWarningPeriodInfo = periodList[0];
            resultList.push(prevItem);
            for (let i = 1; i < periodList.length; ++i) {
                const elem = periodList[i];
                if (elem.periodKey === prevItem.periodKey) {
                    prevItem.actualEndTime = elem.actualEndTime;
                } else {
                    resultList.push(elem);
                    prevItem = elem;
                }
            }
        }

        return resultList;
    }

    private mergeTimePeriodList(periodList: StateInfoOfTimePeriod[]): StateInfoOfTimePeriod[] {
        const resultList: StateInfoOfTimePeriod[] = [];

        if (periodList.length > 0) {
            let prevItem: StateInfoOfTimePeriod = periodList[0];
            resultList.push(prevItem);
            for (let i = 1; i < periodList.length; ++i) {
                const elem = periodList[i];
                if (elem.stateKey === prevItem.stateKey
                    && elem.isInterval === prevItem.isInterval
                    && elem.isVirtualTail === prevItem.isVirtualTail) {
                    prevItem.endTime = elem.endTime;
                } else {
                    resultList.push(elem);
                    prevItem = elem;
                }
            }
        }

        return resultList;
    }

    private generatePlanStatusChangedInfo(item: CompSandBoxItem): void {
        const {mapPlanStatusChangedInfoByTime} = this;
        const planTimeStart = item.planStartTime.getTime();
        const planTimeEnd = moment(item.planEndTime.getTime()).startOf("day").valueOf();
        const firstItem: CompStatusChangedInfo = {
            compKey: item.compKey,
            compInfo: item.compInfo,
            status: planTimeStart === planTimeEnd ? "complete" : "ongoing"
        };
        const find = mapPlanStatusChangedInfoByTime.get(planTimeStart);
        if (typeof find !== "undefined") {
            find.push(firstItem);
        } else {
            mapPlanStatusChangedInfoByTime.set(planTimeStart, [firstItem]);
        }

        if (planTimeStart !== planTimeEnd) {
            const completeItem: CompStatusChangedInfo = {
                compKey: item.compKey,
                compInfo: item.compInfo,
                status: "complete"
            };

            const findCompleteItem = mapPlanStatusChangedInfoByTime.get(planTimeEnd);
            if (typeof findCompleteItem !== "undefined") {
                findCompleteItem.push(completeItem);
            } else {
                mapPlanStatusChangedInfoByTime.set(planTimeEnd, [completeItem]);
            }
        }
    }

    private generatePlanTimePeriodList(item: CompSandBoxItem): void {
        const {planStartTime, planEndTime} = item;

        const setStartTime: Set<number> = new Set<number>();
        const setEndTime: Set<number> = new Set<number>();
        for (let i = 0; i < item.stateItems.length; ++i) {
            const stateItem = item.stateItems[i];
            setStartTime.add(stateItem.lifeCycles.planStartDate);
            if (stateItem.lifeCycles.planStartDate > planStartTime.getTime()) {
                setEndTime.add(moment(stateItem.lifeCycles.planStartDate).add(-1, "days").endOf("day").valueOf());
            }

            setEndTime.add(stateItem.lifeCycles.planEndDate);
            if (stateItem.lifeCycles.planEndDate < planEndTime.getTime()) {
                setStartTime.add(moment(stateItem.lifeCycles.planEndDate).add(1, "days").startOf("day").valueOf());
            }
        }

        if (setStartTime.size === setEndTime.size) {
            const startTimeList = Array.from(setStartTime).sort((a, b) => a - b);
            const endTimeList = Array.from(setEndTime).sort((a, b) => a - b);

            const planTimePeriodList: StateInfoOfTimePeriod[] = startTimeList.map((el, index) => ({
                startTime: new Date(el),
                endTime: new Date(endTimeList[index]),
                stateColor: "",
                stateKey: "",
                stateName: "",
                isInterval: false,
                isVirtualTail: false,
            }));

            let prevStateItem: StateInfoOfTimePeriod | undefined;
            for (let i = 0; i < planTimePeriodList.length; ++i) {
                const elem = planTimePeriodList[i];
                const matcheStateItem = this.getMachedPlanStateItem(elem.startTime.getTime(), elem.endTime.getTime(), item.stateItems);
                if (typeof matcheStateItem !== "undefined") {
                    elem.stateKey = matcheStateItem.stateKey;
                    elem.stateName = matcheStateItem.stateName;
                    elem.stateColor = matcheStateItem.stateColor;
                    prevStateItem = elem;
                } else if (typeof prevStateItem !== "undefined") {
                    elem.stateKey = prevStateItem.stateKey;
                    elem.stateName = prevStateItem.stateName;
                    elem.stateColor = prevStateItem.stateColor;
                    elem.isInterval = true;
                }
            }

            const mergePlanTimePeriodList = this.mergeTimePeriodList(planTimePeriodList);
            for (let i = 0; i < mergePlanTimePeriodList.length; ++i) {
                const elem = mergePlanTimePeriodList[i];
                const newItem: CompSandBoxItemOfSingleTimePeriod = {
                    finishEndDate: item.finishEndDate,
                    compInfo: item.compInfo,
                    timePeriodInfo: elem,
                    compKey: item.compKey,
                    compName: this.queryComponentNameFromCache(item.compKey),
                    earliestPlanEndTime: item.earliestPlanEndTime.getTime(),
                    latestPlanEndTime: item.planEndTime.getTime(),
                    completeTime: item.planEndTime.getTime(),
                };

                const find = this.mapPlanChangedInfoByTime.get(elem.startTime.getTime());
                if (isNotNullOrUndefined(find)) {
                    find.push(newItem);
                } else {
                    this.mapPlanChangedInfoByTime.set(elem.startTime.getTime(), [newItem]);
                }
            }

            const {projectTimeScope} = this;
            if (mergePlanTimePeriodList.length > 0 && isNotNullOrUndefined(projectTimeScope)) {
                const projectActualEndTime = projectTimeScope.actualEndTime;
                const projectPlanEndTime = projectTimeScope.planEndTime;
                const [tail] = mergePlanTimePeriodList.slice(-1);

                let endTime: Date | null = null;
                if (isNotNullOrUndefined(projectActualEndTime)) {
                    endTime = projectActualEndTime;
                    if (isNotNullOrUndefined(projectPlanEndTime) && endTime < projectPlanEndTime) {
                        endTime = projectPlanEndTime;
                    }
                } else if (isNotNullOrUndefined(projectPlanEndTime)) {
                    endTime = projectPlanEndTime;
                    if (projectActualEndTime !== null && endTime < projectActualEndTime) {
                        endTime = projectActualEndTime;
                    }
                }

                if (isNotNullOrUndefined(projectPlanEndTime) && isNotNullOrUndefined(endTime) && tail.endTime < endTime) {
                    const tailCopy = {
                        ...tail,
                        startTime: new Date(moment(tail.endTime).add(1, "days").startOf("day").valueOf()),
                        endTime,
                        isVirtualTail: true
                    };
                    const newItem: CompSandBoxItemOfSingleTimePeriod = {
                        finishEndDate: item.finishEndDate,
                        compInfo: item.compInfo,
                        timePeriodInfo: tailCopy,
                        compKey: item.compKey,
                        compName: this.queryComponentNameFromCache(item.compKey),
                        earliestPlanEndTime: item.earliestPlanEndTime.getTime(),
                        latestPlanEndTime: item.planEndTime.getTime(),
                        completeTime: isNotNullOrUndefined(item.planEndTime) ? item.planEndTime.getTime() : 0,
                    };

                    const find = this.mapPlanChangedInfoByTime.get(tailCopy.startTime.getTime());
                    if (isNotNullOrUndefined(find)) {
                        find.push(newItem);
                    } else {
                        this.mapPlanChangedInfoByTime.set(tailCopy.startTime.getTime(), [newItem]);
                    }
                }
            }

            // eslint-disable-next-line no-param-reassign
            item.planTimePeriodList = mergePlanTimePeriodList;
        }
    }

    private generateActualTimePeriodList(item: CompSandBoxItem): void {
        const {actualStartTime, actualEndTime} = item;
        if (isNotNullOrUndefined(actualStartTime) && isNotNullOrUndefined(actualEndTime)) {
            const endTimeCal = isNotNullOrUndefined(this.projectTimeScope) && isNotNullOrUndefined(this.projectTimeScope.actualEndTime)
                ? this.projectTimeScope.actualEndTime
                : this.getProjectSandboxEndTime();

            const setStartTime: Set<number> = new Set<number>();
            const setEndTime: Set<number> = new Set<number>();
            for (let i = 0; i < item.stateItems.length; ++i) {
                const stateItem = item.stateItems[i];
                const {startDate, endDate} = stateItem.lifeCycles;

                if (isNotNullOrUndefined(startDate) || isNotNullOrUndefined(endDate)) {
                    if (isNotNullOrUndefined(startDate)) {
                        setStartTime.add(startDate);
                        if (startDate > actualStartTime.getTime()) {
                            setEndTime.add(moment(startDate).add(-1, "days").endOf("day").valueOf());
                        }
                    }

                    if (isNotNullOrUndefined(endDate)) {
                        setEndTime.add(endDate);
                        if (endDate < actualEndTime.getTime()) {
                            setStartTime.add(moment(endDate).add(1, "days").startOf("day").valueOf());
                        }
                    } else if (isNotNullOrUndefined(startDate)
                        && endTimeCal.getTime() > startDate) {
                        setEndTime.add(endTimeCal.getTime());
                    }
                }
            }

            if (setStartTime.size === setEndTime.size) {
                const startTimeList = Array.from(setStartTime).sort((a, b) => a - b);
                const endTimeList = Array.from(setEndTime).sort((a, b) => a - b);

                const actualTimePeriodList: StateInfoOfTimePeriod[] = startTimeList.map((el, index) => ({
                    startTime: new Date(el),
                    endTime: new Date(endTimeList[index]),
                    stateColor: "",
                    stateKey: "",
                    stateName: "",
                    isInterval: false,
                    isVirtualTail: false,
                }));

                let prevStateItem: StateInfoOfTimePeriod | undefined;
                for (let i = 0; i < actualTimePeriodList.length; ++i) {
                    const elem = actualTimePeriodList[i];
                    const matcheStateItem = this.getMachedActualStateItem(elem.startTime.getTime(),
                        elem.endTime.getTime(),
                        item.stateItems);
                    if (typeof matcheStateItem !== "undefined") {
                        elem.stateKey = matcheStateItem.stateKey;
                        elem.stateName = matcheStateItem.stateName;
                        elem.stateColor = matcheStateItem.stateColor;
                        prevStateItem = elem;
                    } else if (typeof prevStateItem !== "undefined") {
                        elem.stateKey = prevStateItem.stateKey;
                        elem.stateName = prevStateItem.stateName;
                        elem.stateColor = prevStateItem.stateColor;
                        elem.isInterval = true;
                    }
                }

                const mergeActualTimePeriodList = this.mergeTimePeriodList(actualTimePeriodList);

                for (let i = 0; i < mergeActualTimePeriodList.length; ++i) {
                    const elem = mergeActualTimePeriodList[i];
                    const newItem: CompSandBoxItemOfSingleTimePeriod = {
                        finishEndDate: item.finishEndDate,
                        compInfo: item.compInfo,
                        timePeriodInfo: elem,
                        compKey: item.compKey,
                        compName: this.queryComponentNameFromCache(item.compKey),
                        earliestPlanEndTime: item.earliestPlanEndTime.getTime(),
                        latestPlanEndTime: item.planEndTime.getTime(),
                        completeTime: isNotNullOrUndefined(item.actualEndTime) ? item.actualEndTime.getTime() : 0,
                    };

                    const find = this.mapActualChangedInfoByTime.get(elem.startTime.getTime());
                    if (isNotNullOrUndefined(find)) {
                        find.push(newItem);
                    } else {
                        this.mapActualChangedInfoByTime.set(elem.startTime.getTime(), [newItem]);
                    }
                }

                const {projectTimeScope} = this;
                if (mergeActualTimePeriodList.length > 0 && isNotNullOrUndefined(projectTimeScope)) {
                    const projectActualEndTime = projectTimeScope.actualEndTime;
                    const projectPlanEndTime = projectTimeScope.planEndTime;
                    const [tail] = mergeActualTimePeriodList.slice(-1);

                    let endTime: Date | null = null;
                    if (isNotNullOrUndefined(projectActualEndTime)) {
                        endTime = projectActualEndTime;
                        if (isNotNullOrUndefined(projectPlanEndTime) && endTime < projectPlanEndTime) {
                            endTime = projectPlanEndTime;
                        }
                    } else if (isNotNullOrUndefined(projectPlanEndTime)) {
                        endTime = projectPlanEndTime;
                        if (projectActualEndTime !== null && endTime < projectActualEndTime) {
                            endTime = projectActualEndTime;
                        }
                    }

                    if (isNotNullOrUndefined(projectActualEndTime) && isNotNullOrUndefined(endTime) && tail.endTime < endTime) {
                        const tailCopy = {
                            ...tail,
                            startTime: new Date(moment(tail.endTime).add(1, "days").startOf("day").valueOf()),
                            endTime,
                            isVirtualTail: true
                        };
                        const newItem: CompSandBoxItemOfSingleTimePeriod = {
                            finishEndDate: item.finishEndDate,
                            compInfo: item.compInfo,
                            timePeriodInfo: tailCopy,
                            compKey: item.compKey,
                            compName: this.queryComponentNameFromCache(item.compKey),
                            earliestPlanEndTime: item.earliestPlanEndTime.getTime(),
                            latestPlanEndTime: item.planEndTime.getTime(),
                            completeTime: isNotNullOrUndefined(item.actualEndTime) ? item.actualEndTime.getTime() : 0,
                        };

                        const find = this.mapActualChangedInfoByTime.get(tailCopy.startTime.getTime());
                        if (isNotNullOrUndefined(find)) {
                            find.push(newItem);
                        } else {
                            this.mapActualChangedInfoByTime.set(tailCopy.startTime.getTime(), [newItem]);
                        }
                    }
                }

                // eslint-disable-next-line no-param-reassign
                item.actualTimePeriodList = mergeActualTimePeriodList;
            }
        }
    }

    private addWarningInfoMap(planEndTime: number, compKey: string, modifyType: "1" | "2" | "3" | "4" | "disappeared"): void {
        const {mapWarningInfoByTime} = this;

        const DefaultWarningTypeData: WarningTypeInfo = {
            warningType1CompList: [],
            warningType2CompList: [],
            warningType3CompList: [],
            warningType4CompList: [],
            disappearedCompList: [],
        };

        const find = mapWarningInfoByTime.get(planEndTime);
        if (typeof find !== "undefined") {
            switch (modifyType) {
                case "1":
                    find.warningType1CompList.push(compKey);
                    break;
                case "2":
                    find.warningType2CompList.push(compKey);
                    break;
                case "3":
                    find.warningType3CompList.push(compKey);
                    break;
                case "4":
                    find.warningType4CompList.push(compKey);
                    break;
                case "disappeared":
                    find.disappearedCompList.push(compKey);
                    break;
                default:
                    break;
            }
        } else {
            switch (modifyType) {
                case "1":
                    mapWarningInfoByTime.set(planEndTime, {...DefaultWarningTypeData, warningType1CompList: [compKey]});
                    break;
                case "2":
                    mapWarningInfoByTime.set(planEndTime, {...DefaultWarningTypeData, warningType2CompList: [compKey]});
                    break;
                case "3":
                    mapWarningInfoByTime.set(planEndTime, {...DefaultWarningTypeData, warningType3CompList: [compKey]});
                    break;
                case "4":
                    mapWarningInfoByTime.set(planEndTime, {...DefaultWarningTypeData, warningType4CompList: [compKey]});
                    break;
                case "disappeared":
                    mapWarningInfoByTime.set(planEndTime, {...DefaultWarningTypeData, disappearedCompList: [compKey]});
                    break;
                default:
                    break;
            }
        }
    }

    private generateWarningListInfo(item: CompSandBoxItem): void {
        const {warnExpiredConfig} = this;
        const periodList: StateWarningPeriodInfo[] = [];
        let earliestPlanTime = 0;
        let latestActualTime = 0;
        const endTimeCal = this.getProjectSandboxEndTime().getTime();
        if (isNotNullOrUndefined(warnExpiredConfig) && item.finishEndDate === 0) {
            const {stateItems} = item;
            for (let i = 0; i < stateItems.length; ++i) {
                const {endDate, planEndDate} = stateItems[i].lifeCycles;
                if (!isNotNullOrUndefined(endDate) || endDate > planEndDate) {
                    const stateWarningInfo: StateWarningPeriodInfo = {
                        planEndTime: moment(planEndDate).startOf("day").valueOf(),
                        actualEndTime: endDate ?? endTimeCal,
                        periodKey: ""
                    };
                    stateWarningInfo.periodKey = `${stateWarningInfo.planEndTime.toString()}_${stateWarningInfo.actualEndTime.toString()}`;
                    periodList.push(stateWarningInfo);
                    if (periodList.length === 1) {
                        earliestPlanTime = stateWarningInfo.planEndTime;
                        latestActualTime = stateWarningInfo.actualEndTime;
                    } else {
                        earliestPlanTime = Math.min(stateWarningInfo.planEndTime, earliestPlanTime);
                        latestActualTime = Math.max(stateWarningInfo.actualEndTime, latestActualTime);
                    }
                }
            }

            periodList.sort((l, r) => {
                if (l.planEndTime !== r.planEndTime) {
                    return l.planEndTime - r.planEndTime;
                }
                if (l.actualEndTime !== r.actualEndTime) {
                    return l.actualEndTime - r.actualEndTime;
                }

                return l.periodKey.localeCompare(r.periodKey);
            });

            const setPlanTime: Set<number> = new Set<number>();
            const setActualTime: Set<number> = new Set<number>();
            for (let i = 0; i < periodList.length; ++i) {
                const periodItem = periodList[i];
                setPlanTime.add(periodItem.planEndTime);
                if (periodItem.planEndTime > earliestPlanTime) {
                    setActualTime.add(moment(periodItem.planEndTime).add(-1, "days").endOf("day").valueOf());
                }
                setActualTime.add(periodItem.actualEndTime);
                if (periodItem.actualEndTime < latestActualTime) {
                    setPlanTime.add(moment(periodItem.actualEndTime).add(1, "days").startOf("day").valueOf());
                }
            }

            if (setPlanTime.size !== setActualTime.size) {
                return;
            }

            const startTimeList = Array.from(setPlanTime).sort((a, b) => a - b);
            const endTimeList = Array.from(setActualTime).sort((a, b) => a - b);

            const timePeriodList: StateWarningPeriodInfo[] = startTimeList.map((el, index) => ({
                planEndTime: el,
                actualEndTime: endTimeList[index],
                periodKey: this.getMachedWarningItem(el, endTimeList[index], periodList),
            }));

            const [tail] = endTimeList.slice(-1);
            if (tail < endTimeCal) {
                timePeriodList.push({
                    planEndTime: moment(tail).add(1, "days").startOf("day").valueOf(),
                    actualEndTime: endTimeCal,
                    periodKey: "",
                });
            }

            const mergedTimePeriodList = this.mergeWarningTimePeriodList(timePeriodList);

            for (let i = 0; i < mergedTimePeriodList.length; ++i) {
                const elem = mergedTimePeriodList[i];
                const {planEndTime, actualEndTime, periodKey} = elem;

                let hasPushCompKey = false;
                if (isEmpty(periodKey)) {
                    this.addWarningInfoMap(elem.planEndTime, item.compKey, "disappeared");
                } else {
                    const planEndTimeStart = planEndTime;
                    const endDateStart = moment(actualEndTime).startOf("day").valueOf();
                    if (warnExpiredConfig.warningType1ExpiredDays > 0) {
                        const time1 = moment(planEndTimeStart).add(warnExpiredConfig.warningType1ExpiredDays, "days").valueOf();
                        if (time1 <= endDateStart) {
                            this.addWarningInfoMap(time1, item.compKey, "1");
                        }
                    }

                    if (warnExpiredConfig.warningType2ExpiredDays > 0) {
                        const time2 = moment(planEndTimeStart).add(warnExpiredConfig.warningType2ExpiredDays, "days").valueOf();
                        if (time2 <= endDateStart) {
                            this.addWarningInfoMap(time2, item.compKey, "2");
                        } else if (!hasPushCompKey) {
                            this.addWarningInfoMap(time2, item.compKey, "disappeared");
                            hasPushCompKey = true;
                        }
                    }

                    if (warnExpiredConfig.warningType3ExpiredDays > 0) {
                        const time3 = moment(planEndTimeStart).add(warnExpiredConfig.warningType3ExpiredDays, "days").valueOf();
                        if (time3 <= endDateStart) {
                            this.addWarningInfoMap(time3, item.compKey, "3");
                        } else if (!hasPushCompKey) {
                            this.addWarningInfoMap(time3, item.compKey, "disappeared");
                            hasPushCompKey = true;
                        }
                    }

                    if (warnExpiredConfig.warningType4ExpiredDays > 0) {
                        const time4 = moment(planEndTimeStart).add(warnExpiredConfig.warningType4ExpiredDays, "days").valueOf();
                        if (time4 <= endDateStart) {
                            this.addWarningInfoMap(time4, item.compKey, "4");
                        } else if (!hasPushCompKey) {
                            this.addWarningInfoMap(time4, item.compKey, "disappeared");
                            hasPushCompKey = true;
                        }
                    }
                }
            }
        }
    }

    private generateCompSandBoxItem(item: CompProcessItem): CompSandBoxItem {
        const newDate = new Date(moment().startOf("day").valueOf());
        const result: CompSandBoxItem = {
            finishEndDate: item.finishEndDate ?? 0,
            stateItems: item.infos ?? [],
            compInfo: item.projectInfo,
            compKey: this.generateCompKey(item.projectInfo.handle, item.projectInfo.path),
            planStartTime: newDate,
            planEndTime: newDate,
            actualStartTime: null,
            actualEndTime: null,
            planTimePeriodList: [],
            actualTimePeriodList: [],
            earliestPlanEndTime: newDate,
        };

        if (result.stateItems.length > 0) {
            const [firstItem] = result.stateItems;
            result.planStartTime = new Date(moment(firstItem.lifeCycles.planStartDate).startOf("day").valueOf());
            result.planEndTime = new Date(moment(firstItem.lifeCycles.planEndDate).endOf("day").valueOf());
            result.earliestPlanEndTime = new Date(moment(firstItem.lifeCycles.planEndDate).endOf("day").valueOf());
        }

        const existOngoingStateItem = result.stateItems.find((el) => isNotNullOrUndefined(el.lifeCycles.startDate)
            && !isNotNullOrUndefined(el.lifeCycles.endDate));
        if (typeof existOngoingStateItem !== "undefined") {
            result.actualEndTime = this.getProjectSandboxEndTime();
        }

        for (let i = 0; i < result.stateItems.length; ++i) {
            const stateItem = result.stateItems[i];
            const {planStartDate, planEndDate, startDate, endDate} = stateItem.lifeCycles;
            if (planStartDate < result.planStartTime.getTime()) {
                result.planStartTime = new Date(moment(planStartDate).startOf("day").valueOf());
            }

            if (planEndDate > result.planEndTime.getTime()) {
                result.planEndTime = new Date(moment(planEndDate).endOf("day").valueOf());
            }

            if (planEndDate < result.earliestPlanEndTime.getTime()) {
                result.earliestPlanEndTime = new Date(moment(planEndDate).endOf("day").valueOf());
            }

            if (isNotNullOrUndefined(startDate) || isNotNullOrUndefined(endDate)) {
                if (result.actualStartTime === null) {
                    result.actualStartTime = isNotNullOrUndefined(startDate) ? new Date(moment(startDate).startOf("day").valueOf()) : null;
                } else if (isNotNullOrUndefined(startDate) && startDate < result.actualStartTime.getTime()) {
                    result.actualStartTime = new Date(moment(startDate).startOf("day").valueOf());
                }

                if (result.actualEndTime === null) {
                    result.actualEndTime = isNotNullOrUndefined(endDate) ? new Date(moment(endDate).endOf("day").valueOf()) : null;
                } else if (isNotNullOrUndefined(endDate) && endDate > result.actualEndTime.getTime()) {
                    result.actualEndTime = new Date(moment(endDate).endOf("day").valueOf());
                }
            }
        }

        this.generatePlanStatusChangedInfo(result);
        this.generatePlanTimePeriodList(result);
        this.generateActualTimePeriodList(result);
        this.generateWarningListInfo(result);

        return result;
    }

    private updateTimeRange(item: CompSandBoxItem): void {
        if (isNotNullOrUndefined(this.sandBoxTimeScope)) {
            const {sandBoxTimeScope} = this;
            if (sandBoxTimeScope.planStartTime === null) {
                sandBoxTimeScope.planStartTime = item.planStartTime;
            } else if (item.planStartTime < sandBoxTimeScope.planStartTime) {
                sandBoxTimeScope.planStartTime = item.planStartTime;
            }

            if (sandBoxTimeScope.planEndTime === null) {
                sandBoxTimeScope.planEndTime = item.planEndTime;
            } else if (item.planEndTime > sandBoxTimeScope.planEndTime) {
                sandBoxTimeScope.planEndTime = item.planEndTime;
            }

            if (sandBoxTimeScope.actualStartTime === null) {
                sandBoxTimeScope.actualStartTime = item.actualStartTime !== null ? item.actualStartTime : null;
            } else if (item.actualStartTime !== null && item.actualStartTime < sandBoxTimeScope.actualStartTime) {
                sandBoxTimeScope.actualStartTime = item.actualStartTime;
            }

            if (sandBoxTimeScope.actualEndTime === null) {
                sandBoxTimeScope.actualEndTime = item.actualEndTime !== null ? item.actualEndTime : null;
            } else if (item.actualEndTime !== null && item.actualEndTime > sandBoxTimeScope.actualEndTime) {
                sandBoxTimeScope.actualEndTime = item.actualEndTime;
            }
        }
    }


    private async loadBase() {
        if (isNotNullOrUndefined(this.bimInfo)) {
            await this.queryProjectDuration();
            await this.queryWarnTypeList();
            const bindCompList = await this.queryAllBindComponent();
            this.initBindCompCache(bindCompList);
            await this.loadHandleList();
        }
    }

    private handleCompatible(stateList: CompStateParam[]) {
        stateList.reverse();
        for (let i = 0; i < stateList.length; ++i) {
            const {lifeCycles} = stateList[i];
            if (isNotNullOrUndefined(lifeCycles.startDate) && lifeCycles.startDate === 0) {
                lifeCycles.startDate = undefined;
            }

            if (isNotNullOrUndefined(lifeCycles.endDate) && lifeCycles.endDate === -1) {
                lifeCycles.endDate = undefined;
            }
        }
    }

    private initBindCompCache(bindCompList: CompProcessItem[]) {
        this.mapCompInfo.clear();
        this.mapPathAndComp.clear();
        this.mapStateAndComp.clear();
        for (let i = 0; i < bindCompList.length; ++i) {
            const bindComp = bindCompList[i];
            if (Array.isArray(bindComp.infos)) {
                this.handleCompatible(bindComp.infos);
            }
            const sandBoxItem = this.generateCompSandBoxItem(bindComp);
            const {path} = sandBoxItem.compInfo;
            const key = sandBoxItem.compKey;
            this.updateTimeRange(sandBoxItem);
            this.mapCompInfo.set(key, sandBoxItem);

            const findPath = this.mapPathAndComp.get(path);
            if (typeof findPath !== "undefined") {
                findPath.push(key);
            } else {
                this.mapPathAndComp.set(path, [key]);
            }

            for (let j = 0; j < sandBoxItem.stateItems.length; ++j) {
                const elem = sandBoxItem.stateItems[j];
                const findState = this.mapStateAndComp.get(elem.stateKey);
                if (typeof findState !== "undefined") {
                    findState.push(key);
                } else {
                    this.mapStateAndComp.set(elem.stateKey, [key]);
                }
            }
        }

        const arrayObj = Array.from(this.mapPlanChangedInfoByTime);
        arrayObj.sort((val1, val2) => val1[0] - val2[0]);
        this.mapPlanChangedInfoByTime = new Map(arrayObj.map((i) => [i[0], i[1]]));

        const arrayObjActual = Array.from(this.mapActualChangedInfoByTime);
        arrayObjActual.sort((val1, val2) => val1[0] - val2[0]);
        this.mapActualChangedInfoByTime = new Map(arrayObjActual.map((i) => [i[0], i[1]]));

        const arrayObjStatus = Array.from(this.mapPlanStatusChangedInfoByTime);
        arrayObjStatus.sort((val1, val2) => val1[0] - val2[0]);
        this.mapPlanStatusChangedInfoByTime = new Map(arrayObjStatus.map((i) => [i[0], i[1]]));

        const arrayObjWarning = Array.from(this.mapWarningInfoByTime);
        arrayObjWarning.sort((val1, val2) => val1[0] - val2[0]);
        this.mapWarningInfoByTime = new Map(arrayObjWarning.map((i) => [i[0], i[1]]));
    }

    private async queryProjectDuration(): Promise<void> {
        if (isNotNullOrUndefined(this.bimInfo) && isNotNullOrUndefined(this.projectTimeScope)) {
            const res = await getProjectDuration(this.bimInfo.ppid);
            if (isNotNullOrUndefined(res) && isNotNullOrUndefined(res.data)) {
                this.projectTimeScope.planStartTime = new Date(res.data.planStartDate);
                this.projectTimeScope.planEndTime = new Date(res.data.planEndDate);
                this.projectTimeScope.actualStartTime = isNotNullOrUndefined(res.data.startDate) ? new Date(res.data.startDate) : null;
                this.projectTimeScope.actualEndTime = isNotNullOrUndefined(res.data.endDate) ? new Date(res.data.endDate) : null;
            }
        }
    }

    private getExpiredDays(findType: WarnTypeListResult | undefined): number {
        if (isNotNullOrUndefined(findType)) {
            const {minDay, minCompare, maxDay} = findType;
            // 大于：1；大于等于2；
            if (isNotNullOrUndefined(minDay)) {
                if (minCompare === 1) {
                    return minDay + 1;
                }
                if (minCompare === 2) {
                    return minDay;
                }

                return minDay;
            }

            // // 小于：3；小于等于：4 integer(int32)
            if (isNotNullOrUndefined(maxDay)) {
                return 1;
                // if (maxCompare === 4) {
                //     return maxDay;
                // }
                // if (findType.maxCompare === 3) {
                //     return maxDay - 1;
                // }

                // return maxDay;
            }
        }

        return 0;
    }

    private async queryWarnTypeList(): Promise<void> {
        const res = await getWarnTypeList();
        const warnTypeList = Array.isArray(res.data) ? res.data : [];
        this.mapWarnInfo = new Map(warnTypeList.map((el) => [el.id, el]));

        const findType1 = warnTypeList.find((el) => el.type === 1);
        const findType2 = warnTypeList.find((el) => el.type === 2);
        const findType3 = warnTypeList.find((el) => el.type === 3);
        const findType4 = warnTypeList.find((el) => el.type === 4);

        const warnExpiredConfig = {
            warningType1ExpiredDays: this.getExpiredDays(findType1),
            warningType2ExpiredDays: this.getExpiredDays(findType2),
            warningType3ExpiredDays: this.getExpiredDays(findType3),
            warningType4ExpiredDays: this.getExpiredDays(findType4),
            warningType1Key: typeof findType1 !== "undefined" ? findType1.id : "",
            warningType2Key: typeof findType2 !== "undefined" ? findType2.id : "",
            warningType3Key: typeof findType3 !== "undefined" ? findType3.id : "",
            warningType4Key: typeof findType4 !== "undefined" ? findType4.id : "",
        };

        this.warnExpiredConfig = warnExpiredConfig;
    }

    private async queryAllBindComponent(): Promise<CompProcessItem[]> {
        let resultList: CompProcessItem[] = [];
        if (isNotNullOrUndefined(this.bimInfo)) {
            const input = {
                ppid: this.bimInfo.ppid,
                projectInfos: [],
                size: PAGESIZEMAX_COMPBIND,
            };
            resultList = await getAllProcessList(input);
        }
        return Promise.resolve(resultList);
    }

    private splitArray(arr: string[], pageSize = 1000) {
        const len = arr.length;
        const result: string[][] = [];
        for (let i = 0; i < len; i += pageSize) {
            result.push(arr.slice(i, i + pageSize));
        }
        return result;
    }

    private async loadHandleList() {
        const handleList: string[] = [];
        this.mapCompInfo.forEach((comp) => {
            handleList.push(comp.compInfo.handle);
        });

        this.mapHandleList.clear();
        const {bimProject} = this;
        if (isNotNullOrUndefined(bimProject) && handleList.length > 0) {
            const splitSmallArray = this.splitArray(handleList);
            for (let i = 0; i < splitSmallArray.length; ++i) {
                const elemHandleList = splitSmallArray[i];
                await bimProject.queryElementByBimIds(elemHandleList)
                    .then((comps) => {
                        if (Array.isArray(comps)) {
                            for (let j = 0; j < comps.length; ++j) {
                                const el = comps[j];
                                const bimId = el.bimId ?? "";
                                const path = Array.isArray(el.dir) ? el.dir.join(pathSeparator) : "";
                                this.mapHandleList.set(this.generateCompKey(bimId, path), el);
                            }
                        }
                    });
            }
        }
    }

    private resetData(): void {
        this.projectTimeScope = {
            planStartTime: null,
            planEndTime: null,
            actualStartTime: null,
            actualEndTime: null,
        };

        this.sandBoxTimeScope = {
            planStartTime: null,
            planEndTime: null,
            actualStartTime: null,
            actualEndTime: null,
        };

        this.filterInfo = {
            ebsInfo: {isCheckAll: true, setCheckedKeys: new Set()},
            processInfo: {isCheckAll: true, setCheckedKeys: new Set()},
            defaultStatusInfo: {isCheckAll: true, setCheckedKeys: new Set()},
            warnInfo: {isCheckAll: true, setCheckedKeys: new Set()},
        };

        this.mapWarnInfo.clear();
        this.mapWarningInfoByTime.clear();

        this.mapPlanChangedInfoByTime.clear();
        this.mapActualChangedInfoByTime.clear();
    }
}
