import {PageInfo} from "./common.type";

export interface AddProjectParams {
    name: string;
    deptId: string;
    loc: string;
}

export interface GetProjectListParams extends PageInfo {
    name?: string;
    deptIdList?: string[];
}

export interface ProjectInfo {
    deptId: string;
    deptName: string;
    id: string;
    loc: string;
    name: string;
    sceneId: string;
    updateAt: number;
    updateBy: string;
}

export interface GetProjectListRes {
    content: ProjectInfo[];
    total: number;
}
