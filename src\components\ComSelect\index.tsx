import React, {CSSProperties} from "react";
import {createUseStyles} from "react-jss";
import {Select} from "antd";
import {SelectProps} from "antd/lib/select";


const useStyle = createUseStyles({
    box: {
        display: "inline-flex",
        border: "1px solid #E1E2E5",
    },
    label: {
        color: "#717784",
        padding: "5px 0 0 7px",
    },
    select: {
        flexGrow: 1,
        width: 0
    }
});

type RawValueType = string | number;
export interface LabelValueType {
    key?: string;
    value: RawValueType;
    label: React.ReactNode;
    isCacheable?: boolean;
}
export declare type DefaultValueType = RawValueType | RawValueType[] | LabelValueType | LabelValueType[];

export interface ComSelectProps<T = DefaultValueType> extends SelectProps<T> {
    label: string;
    boxStyle?: CSSProperties;
    colon?: boolean;
}

const ComSelect = (props: ComSelectProps) => {
    const {label, boxStyle, colon = true, ...other} = props;
    const cls = useStyle();

    return (
        <div className={cls.box} style={boxStyle}>
            <div className={cls.label}>{`${label} :`}</div>
            <Select bordered={false} className={cls.select} {...other} />
        </div>
    );
};

export default ComSelect;
