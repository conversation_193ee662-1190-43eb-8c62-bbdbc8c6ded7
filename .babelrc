{"presets": [["@babel/preset-env", {"modules": false, "targets": {"browsers": ["> 1%", "last 2 versions", "ie >= 8"]}}], "@babel/preset-react", "@babel/preset-typescript"], "plugins": [["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-transform-react-jsx", ["@babel/plugin-proposal-class-properties"], ["@babel/plugin-transform-runtime", {"corejs": 3}], ["@babel/plugin-transform-typescript", {"allowNamespaces": true}], ["@babel/plugin-syntax-dynamic-import"]]}