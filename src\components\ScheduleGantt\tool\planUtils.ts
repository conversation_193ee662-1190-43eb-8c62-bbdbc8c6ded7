import moment from "moment";
import {CalendarInfoRes, PlanInfoDetailType} from "../../../api/Preparation/type";
import {CalendarInfo} from "../api/plan/type";
import {Project, OrgItem} from "../config/interface";


export const headquartersId = function headquartersId(items: OrgItem[]) {
    if (items === undefined) {
        return null;
    }
    const x = items.find((item) => item.id === item.parentId);
    if (x !== undefined) {
        return x.id;
    }
    return null;
};

export const projWithOrgItem = (orgItem: OrgItem, projs: Project[]): Project | undefined => {
    const proj1 = projs.find((item) => item.id === orgItem.id);
    if (proj1 === undefined) {
        const proj2 = projs.find((item) => item.children !== undefined && projWithOrgItem(orgItem, item.children) !== undefined);
        return proj2;
    }
    return proj1;
};

export const isEqualProjs = (projs: Project[], orgList: OrgItem[]): boolean => {
    let status = true;
    orgList.forEach((item) => {
        if (projWithOrgItem(item, projs) === undefined) {
            status = false;
        }
    });
    return status;
};

export const buildCalenderInfo = (calendarRes: CalendarInfoRes): CalendarInfo => {
    // console.log("buildCalenderInfo calendarRes", calendarRes);
    const {
        ctid,
        ctName,
        calendarFlag,
        calendarFalg,
        copyid,
        startDate,
        endDate,
        restDates,
        workDates,
    } = calendarRes;
    let calendarType = calendarFlag ?? calendarFalg;
    if (ctid === "0" || ctid === "1") {
        calendarType = Number(ctid);
    }
    if (copyid !== null && copyid.length > 0) {
        calendarType += 2;
    }
    const calendarInfo = {
        ctid,
        ctName,
        copyid,
        calendarType,
        restDayName: ctName,
        startDate: startDate !== undefined && startDate !== null ? moment(startDate).valueOf() : null,
        endDate: endDate !== undefined && endDate !== null ? moment(endDate).valueOf() : null,
        restDays: restDates?.map((date) => moment(date).valueOf()) ?? [],
        workDays: workDates?.map((date) => moment(date).valueOf()) ?? [],
        isTemplate: false,
    };
    // console.log("buildCalenderInfo", calendarInfo);
    return calendarInfo;
};

export const checkHasParentPlan = (planInfo?: Partial<PlanInfoDetailType>): boolean => {
    const hasParentPlan = planInfo !== undefined && planInfo.parentId !== undefined && planInfo.parentId.length !== 0;
    return hasParentPlan;
};
