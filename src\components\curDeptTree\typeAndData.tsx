import {DataNode} from "antd/lib/tree";

export interface CurDeptTreeType {
    key: string;
    id: string;
    type: number;
    title: string;
    value: string;
    parentId: string;
    children: CurDeptTreeType[];
}

// 树搜索时会用到

// matcher的原因是因为 判断条件会不一样
export const findNode = (node: DataNode, filter: string, matcher: (filterText: string, node: DataNode) => boolean) => {
    if (matcher(filter, node) === true) {
        return matcher(filter, node);
    }
    if (Array.isArray(node.children)
        && node.children.length > 0
        && node.children.some((child: DataNode) => findNode(child, filter, matcher))
    ) {
        return true;
    }
    return false;
};
export const filterTree = (node: DataNode, filter: string, matcher: (filterText: string, node: DataNode) => boolean) => {
    // 如果 只有一层，就直接返回
    if (matcher(filter, node) || !Array.isArray(node.children)) {
        return node;
    }
    // 如果不是，则只保留匹配或具有匹配后代的那些
    const filtered: DataNode[] = node.children
        .filter((child: DataNode) => findNode(child, filter, matcher))
        .map((child: DataNode) => filterTree(child, filter, matcher));
    return {...node, children: filtered};
};

// 将树形节点改为一维数组
export const generateList = (data: DataNode[], dataList: DataNode[]) => {
    for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const {key, title} = node;
        dataList.push({key, title});
        if (Array.isArray(node.children) && node.children.length > 0) {
            generateList(node.children, dataList);
        }
    }
    return dataList;
};
