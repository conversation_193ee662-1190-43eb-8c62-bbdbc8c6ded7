import React, {useCallback, useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {TreeProps} from "antd";
import {Defined, dfsTree, isDefined, isNonEmptyArray} from "../../../../../assets/ts/utils";
import BimDirTreeWithFilter from "../../../../../components/BimDirTreeWithFilter";
import {DirTreeWithPath, MotorContext} from "../../../../../reMotor";
import {RootState} from "../../../../../store/rootReducer";
import {pathSeparator} from "../../helps";
import {dispatch} from "../../../../../store";
import {setProcessSelectType, setSelectedComps, setVisibleCompPaths} from "../../../../../store/process/action";

// interface LeftCombineTreeProps {
//     curTab?: string;
// }

/**
 * 自定义沙盘左侧的树，目前包含模型的部位树和工序模板树
 *
 * 两棵树不会同时展示，是根据tab的切换动态展示，但是树的状态不能随着tab的切换而重置，所以有必要将两棵树的状态放在一个组件里面
 *
 * 对于部位树来说，要默认展示已定义了工序的部位
 *
 * 对于工序模板树来说，默认展示全部
 *
 * 两颗树都和模型有联动，但是两棵树之间并不会联动
 */
const LeftCombineTree = () => {
    // const {curTab} = props;
    // const {tmplTree} = useSelector((state: RootState) => state.processData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    // const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const [bimDirTree, setBimDirTree] = useState<DirTreeWithPath[]>();
    const [bimCheckedKeys, setBimCheckedKeys] = useState<string[]>();
    // const [processTmplCheckedKeys, setProcessTmplCheckedKeys] = useState<string[]>([]);
    // const [bindProcessBimKeys, setBindProcessBimKeys] = useState<string[]>([]); // 已绑定工序的构件path集合

    const initBimDirTree = useCallback(
        async () => {
            const curProj = MotorContext.getCurBIMProject();
            if (!isDefined(curSandTable) || !isDefined(curProj)) {
                return;
            }
            // const processRes = await getProcessListByComps({ppid: curSandTable.ppid, page: 1, size: 5000});
            const tempBimDirTree = await MotorContext.getOpenedCompTree();
            const allCompPath: string[] = [];
            const leafNodeCompPaths: Set<string> = new Set();
            dfsTree(tempBimDirTree, (item) => {
                allCompPath.push(item.path);
                if (!isNonEmptyArray(item.children)) {
                    leafNodeCompPaths.add(item.path);
                }
            });
            setBimCheckedKeys(allCompPath);
            setBimDirTree(tempBimDirTree);
            dispatch(setVisibleCompPaths(leafNodeCompPaths));
            // setBindProcessBimKeys(allProcessData.map((v) => v.projectInfo?.path ?? ""));
        },
        [curSandTable]
    );

    /* const initProcessTmplTree = useCallback(
        () => {
            const allTmplKeys: string[] = [];
            dfsTree(tmplTree, (item) => allTmplKeys.push(item.key));
            setProcessTmplCheckedKeys(allTmplKeys);
        },
        [tmplTree]
    ); */

    useEffect(
        () => {
            initBimDirTree();
        },
        [initBimDirTree]
    );

    /* useEffect(
        () => {
            initProcessTmplTree();
        },
        [initProcessTmplTree]
    ); */

    const handleBimDirTreeCheck = useCallback<Defined<TreeProps["onCheck"]>>(
        (_checkedKeys, info) => {
            const curProj = MotorContext.getCurBIMProject();
            if (curProj === null) {
                return;
            }
            // 在EBS树的check事件发生变化时，清空模型已选中的构件
            curProj.deselect();
            dispatch(setSelectedComps([]));
            dispatch(setProcessSelectType(undefined));
            const {node: {key, checked}, checkedNodes} = info;
            /** 当前ebs树已选中构件的path集合 */
            const checkedCompPathSet: Set<string> = new Set();
            const leafNodeCompPaths: Set<string> = new Set();
            dfsTree(checkedNodes, (item) => {
                checkedCompPathSet.add(item.key as string);
                if (!isNonEmptyArray(item.children)) {
                    leafNodeCompPaths.add(item.key as string);
                }
            });
            const checkedCompPath = Array.from(checkedCompPathSet);
            setBimCheckedKeys(checkedCompPath);
            if (key === "root") {
                curProj.setVisibility(!checked);
            } else {
                const pathDirList = (key as string).split(pathSeparator);
                curProj.setVisibility(!checked, [pathDirList]);
            }
            dispatch(setVisibleCompPaths(leafNodeCompPaths));
        },
        []
    );

    // const handleProcessTreeCheck = useCallback<Defined<TreeProps["onCheck"]>>(
    //     (_checkedKeys, info) => {
    //         const curProj = MotorContext.getProject();

    //         if (curProj === null) {
    //             return;
    //         }

    //         if (analyzedData !== undefined && analyzedData.hasInitialized && typeof analyzedData.handler !== "undefined") {
    //             const {node: {key, checked, title}, checkedNodes} = info;
    //             /** 当前ebs树已选中构件的path集合 */
    //             const checkedCompPathSet: Set<string> = new Set();
    //             dfsTree(checkedNodes, (item) => !isNonEmptyArray(item.children) && checkedCompPathSet.add(item.key as string));

    //             /** 当前ebs树已选中构件的path集合 */
    //             const curCheckedCompPathSet: Set<string> = new Set();
    //             dfsTree([info.node] as DataNode[], (item) => !isNonEmptyArray(item.children)
    //             && curCheckedCompPathSet.add(item.key as string));

    //             const checkedProcessIds = Array.from(checkedCompPathSet);
    //             setProcessTmplCheckedKeys(checkedProcessIds);
    //             if (key === "root" && title === "全部") {
    //                 curProj.setVisibility(!checked);
    //             } else {
    //                 const idList = analyzedData.handler.queryCompListByProcessIdS(Array.from(curCheckedCompPathSet));
    //                 curProj.setVisibility(!checked, idList);
    //             }
    //         }
    //     },
    //     [analyzedData]
    // );

    // const bimDirTreeView = useMemo(
    //     () => {
    //         return (
    //             <BimDirTreeWithFilter
    //                 bimDirTree={bimDirTree}
    //                 definedProcessPaths={bindProcessBimKeys}
    //                 checkedKeys={bimCheckedKeys}
    //                 onCheck={handleBimDirTreeCheck}
    //                 expandedKeys={expandedKeys}
    //                 onExpand={handleExpand}
    //             />
    //         );
    //     },
    //     [bimDirTree, bimCheckedKeys, handleBimDirTreeCheck, bindProcessBimKeys, expandedKeys, handleExpand]
    // );

    /* const proceeTmplTreeView = useMemo(
        () => (
            <ProcessTmplTreeWithFilter
                checkable
                tmplTree={tmplTree}
                checkedKeys={processTmplCheckedKeys}
                onCheck={handleProcessTreeCheck}
            />
        ),
        [processTmplCheckedKeys, tmplTree, handleProcessTreeCheck]
    ); */

    if (bimDirTree === undefined) {
        return null;
    }

    return (
        <BimDirTreeWithFilter
            bimDirTree={bimDirTree}
            checkedKeys={bimCheckedKeys}
            onCheck={handleBimDirTreeCheck}
            hasSelector={false}
        />
    );
};

export default LeftCombineTree;
