/* eslint-disable max-nested-callbacks */
import React, {FC, memo, useState, useEffect, useCallback, ReactText, useRef, useContext} from "react";
import {Modal, message, Table, Tabs} from "antd";
import {isEqual} from "lodash-es";
import clsx from "clsx";
import {createUseStyles} from "react-jss";
import CustomColumnTable from "./CustomColumnTable";
import {ColumnItem, PostCustomColumnItem} from "../api/plan/type";
import ganttManager from "../gantt/ganttManager";
import {ganttColumnNameMap, ServerGanttColumnType, setGanttAllColumns} from "../gantt/columnUtils";
import EditorContext from "../views/GanttEditor/context";
import {postTaskCustomColumnList, putTaskHiddenColumnList} from "../api/plan";
import {CustomValueType} from "../common/constant";

const useStyles = createUseStyles({
    defaultColumnTable: {
        "@global": {
            ".ant-table-body": {
                height: "320px",
            },
        },
    },
    customColumnTableShow: {
        "@global": {
            ".ant-table-body": {
                height: "320px",
            },
        },
    },
    customColumnTableEdit: {
        "@global": {
            ".ant-table-body": {
                height: "280px",
            },
        },
    },
    columnSettingTable: {
        "@global": {
            ".ant-table-placeholder": {
                "& .ant-table-cell": {
                    borderBottom: "none !important",
                }
            },
            ".ant-table-container": {
                border: "1px solid #f0f0f0 !important",

                "& .ant-table-body": {
                    overflow: "overlay !important"
                },
                "& .ant-table-cell": {
                    height: "40px !important",
                    padding: "4px !important",

                    "&:last-child": {
                        borderRightWidth: "0 !important",
                    },
                },
            },
        },
    },
});

interface ColumnSettingModalProps {
    planId: string;
    visible: boolean;
    setVisible: (visible: boolean) => void;
    editable: boolean;
}

// 列设置对话框组件
const ColumnSettingModal: FC<ColumnSettingModalProps> = memo((props) => {
    const {visible, setVisible, editable, planId} = props;
    const {fromType, customColumnsRef} = useContext(EditorContext);
    const {defaultColumnTable, customColumnTableShow, customColumnTableEdit, columnSettingTable} = useStyles();
    const [modalVisible, setModalVisible] = useState<boolean>(visible);
    const [selectedColumnKeys, setSelectedColumnKeys] = useState<ReactText[]>([]);
    const [columnList, setColumnList] = useState<ColumnItem[]>([]);
    const [allColumnInfoList, setAllColumnInfoList] = useState<ColumnItem[]>([]);
    const [customColumnList, setCustomColumnList] = useState<ColumnItem[]>([]);
    const [selectedCustomColumnKeys, setSelectedCustomColumnKeys] = useState<ReactText[]>([]);
    const customColumnChanged = useRef(false);

    useEffect(() => {
        if (visible) {
            setAllColumnInfoList(ganttManager.ganttAllColumnList);
        }
    }, [visible]);

    const resetColumnInfos = useCallback(() => {
        const columnInfos: ColumnItem[] = [];
        const customColumnInfos: ColumnItem[] = [];
        allColumnInfoList.forEach((column) => {
            const localColumnId = ganttColumnNameMap.get(column.columnId as ServerGanttColumnType);
            if (column.valueType !== null && column.valueType !== undefined) {
                customColumnInfos.push(column);
            } else if (ganttManager.ganttLocalColumnList.some((item) => item.columnId === localColumnId)) {
                columnInfos.push(column);
            }
        });
        /* console.log("columnInfos", columnInfos);
        console.log("customColumnInfos", customColumnInfos); */
        setColumnList(columnInfos);
        setCustomColumnList(customColumnInfos);
        setSelectedColumnKeys(columnInfos
            .filter((item: ColumnItem) => item.hidden === false)
            .map((item: ColumnItem) => item.columnId));
        setSelectedCustomColumnKeys(customColumnInfos
            .filter((item: ColumnItem) => item.hidden === false)
            .map((item: ColumnItem) => item.columnId));
    }, [allColumnInfoList]);

    useEffect(() => {
        setModalVisible(visible);
        if (visible) {
            resetColumnInfos();
        }
    }, [visible, resetColumnInfos]);

    const handleCancel = useCallback(() => {
        setVisible(false);
    }, [setVisible]);

    // 保存隐藏列
    const doPostHiddenColumnCodes = useCallback(async () => {
        const newColumnInfoList = columnList.map((item: ColumnItem) => ({
            ...item,
            hidden: selectedColumnKeys.includes(item.columnId) === false
        }));
        const newCustomColumnInfoList = customColumnList.map((item: ColumnItem) => ({
            ...item,
            hidden: selectedCustomColumnKeys.includes(item.columnId) === false
        }));
        const hiddenColumnCodes = newColumnInfoList
            .filter((item: ColumnItem) => item.hidden === true)
            .map((item: ColumnItem) => item.columnId);

        const response = await putTaskHiddenColumnList(planId, fromType, hiddenColumnCodes);
        if (response.success) {
            setVisible(false);
            const allColumnInfos = [...newColumnInfoList, ...newCustomColumnInfoList];
            setGanttAllColumns(allColumnInfos);
            customColumnsRef.current = newCustomColumnInfoList;
        } else {
            message.error({content: "设置失败"});
        }
    }, [columnList, customColumnList, customColumnsRef, fromType, planId, selectedColumnKeys, selectedCustomColumnKeys, setVisible]);

    // 保存自定义列
    const doPostCustomColumns = useCallback(async () => {
        const toServerCustomColumns: PostCustomColumnItem[] = customColumnList.map((item: ColumnItem) => ({
            planId,
            columnId: item.columnId ?? "",
            name: item.name ?? "",
            valueType: item.valueType as CustomValueType,
            hidden: selectedCustomColumnKeys.includes(item.columnId) === false ?? false,
        }));

        const response = await postTaskCustomColumnList(planId, toServerCustomColumns);
        if (response.success) {
            customColumnChanged.current = false;
            await doPostHiddenColumnCodes();
        } else {
            message.error({content: "设置失败"});
        }
    }, [customColumnList, planId, selectedCustomColumnKeys, doPostHiddenColumnCodes]);


    const handleUpdateColumnControl = useCallback(async () => {
        if ((selectedColumnKeys.length + selectedCustomColumnKeys.length) < 5) {
            message.info("显示列不能少于5列！");
            return;
        }
        try {
            if (fromType === "actual" && customColumnChanged.current) {
                // 自定义列变化，先保存自定义列再保存隐藏列
                await doPostCustomColumns();
            } else {
                await doPostHiddenColumnCodes();
            }
        } catch (error) {
            console.log("error", error);
        }
    }, [selectedColumnKeys.length, selectedCustomColumnKeys.length, fromType, doPostCustomColumns, doPostHiddenColumnCodes]);

    const handleColumnSelect = (selectedKeys: ReactText[]) => {
        setSelectedColumnKeys(selectedKeys);
    };

    const handleCustomColumnSelect = (selectedKeys: ReactText[]) => {
        customColumnChanged.current = true;
        setSelectedCustomColumnKeys(selectedKeys);
    };

    return (
        <>
            <Modal
                title={<span style={{fontWeight: "bold"}}>列设置</span>}
                visible={modalVisible}
                bodyStyle={{paddingTop: 0}}
                width={600}
                onCancel={handleCancel}
                onOk={handleUpdateColumnControl}
                destroyOnClose
            >
                <div style={{height: "430px"}}>
                    <Tabs>
                        <Tabs.TabPane key="default" tab="内置列">
                            <Table
                                className={clsx(columnSettingTable, defaultColumnTable)}
                                scroll={{y: 320}}
                                rowKey="columnId"
                                rowSelection={{
                                    selectedRowKeys: selectedColumnKeys,
                                    onChange: handleColumnSelect,
                                }}
                                columns={[
                                    {
                                        title: "列名称",
                                        dataIndex: "name",
                                    }
                                ]}
                                dataSource={columnList}
                                pagination={false}
                            />
                        </Tabs.TabPane>
                        {
                            fromType === "actual" && (
                                <Tabs.TabPane key="custom" tab="自定义列">
                                    <CustomColumnTable
                                        className={clsx(columnSettingTable, editable ? customColumnTableEdit : customColumnTableShow)}
                                        scroll={{y: editable ? 280 : 320}}
                                        editable={editable}
                                        value={customColumnList}
                                        onColumnChange={(list) => {
                                            if (isEqual(list, columnList) === false) {
                                                customColumnChanged.current = true;
                                                setCustomColumnList(list);
                                            }
                                        }}
                                        rowSelection={{
                                            selectedRowKeys: selectedCustomColumnKeys,
                                            onChange: handleCustomColumnSelect,
                                        }}
                                    />
                                </Tabs.TabPane>
                            )
                        }
                    </Tabs>
                </div>
            </Modal>
        </>
    );
});

export default ColumnSettingModal;
