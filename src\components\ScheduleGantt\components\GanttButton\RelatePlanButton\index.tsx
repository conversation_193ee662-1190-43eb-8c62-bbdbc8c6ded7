import React, {useCallback, useContext, useEffect, useState} from "react";
import {<PERSON><PERSON>, <PERSON>, Typography} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import {PlanInfoDetailType, PlanPreparationItemType} from "../../../../../api/Preparation/type";
import SelectTaskDrawer from "./SelectTaskDrawer";
import {getListAssociativeParentPlan} from "../../../../../api/Preparation";

const RelatePlanButton = () => {
    const cls = useStyles();
    const {planInfo, parentPlanInfo, setParentPlanInfo, hasParentPlan, fetchAllData, checkoutStatus} = useContext(EditorContext);
    const [planList, setPlanList] = useState<PlanPreparationItemType[]>([]);
    const [visible, setVisible] = useState(false);

    const getParentPlanList = useCallback(async () => {
        if (hasParentPlan === false) {
            setParentPlanInfo({});
        }
        if (planInfo.id !== undefined) {
            const res = await getListAssociativeParentPlan(planInfo.id);
            if (res.success) {
                const wbsPlanList = res.data.filter((el) => el.bindWbs);
                setPlanList(wbsPlanList);
                if (hasParentPlan) {
                    // 设置上级计划信息
                    setParentPlanInfo(wbsPlanList.find((plan) => plan.id === planInfo.parentId) as Partial<PlanInfoDetailType> ?? {});
                }
            }
        }
    }, [hasParentPlan, planInfo.id, planInfo.parentId, setParentPlanInfo]);

    useEffect(() => {
        getParentPlanList();
    }, [getParentPlanList]);

    const handleClick = useCallback(() => {
        setVisible(true);
    }, []);

    const handleSelect = useCallback((plan?: Partial<PlanInfoDetailType>) => {
        setVisible(false);
        setParentPlanInfo(plan ?? {});
        if (fetchAllData !== undefined) {
            fetchAllData();
        }
    }, [setParentPlanInfo, fetchAllData]);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Row className={cls.textButton}>
            <span>关联上级任务:</span>
            <Button
                style={{width: 150, marginLeft: 8}}
                onClick={handleClick}
            >
                <Typography.Text ellipsis={{tooltip: parentPlanInfo?.name}}>
                    {parentPlanInfo?.name ?? "关联上级任务"}
                </Typography.Text>
            </Button>
            {
                planInfo.id !== undefined && (
                    <SelectTaskDrawer
                        planId={planInfo.id}
                        planList={planList}
                        visible={visible}
                        setVisible={setVisible}
                        onOk={handleSelect}
                    />
                )
            }
        </Row>
    );
};

export default RelatePlanButton;
