@import '~antd/lib/style/themes/default.less';
@import '~antd/dist/antd.less'; // 引入官方提供的 less 样式入口文件
@import "./globalColor.less";

@primary-color: #1f54c5;
@primary-color-dark: #061127;
@select-item-selected-bg: @bg-4;
@tree-node-selected-bg: @bg-4;
@table-selected-row-bg: @bg-4;
@picker-basic-cell-active-with-range-color: @bg-4;
@picker-basic-cell-hover-with-range-color: @bg-3;

// .ant-checkbox-checked span.ant-checkbox-inner, .ant-tree-checkbox-checked span.ant-tree-checkbox-inner{
//     background-color: #1f54c5;
//     border-color: #1f54c5;
// }
.ant-radio-group {
    // .ant-radio-checked span.ant-radio-inner {
    //     border-color: #1f54c5;
    // }
    // .ant-radio-wrapper:hover .ant-radio, .ant-radio:hover .ant-radio-inner, .ant-radio-input:focus + .ant-radio-inner {
    //     border-color: #1f54c5;
    // }
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
        box-shadow: none;
    }
}

// .ant-slider {
//     div.ant-slider-track {
//         background-color: #1f54c5;
//     }
//     &:hover div.ant-slider-track {
//         background-color: #1f54c5;
//     }
//     &:hover div.ant-slider-handle:not(.ant-tooltip-open), .ant-slider-handle{
//         border-color: #1f54c5
//     }
// }

/* @radio-button-color: @primary-color-dark !important;
@radio-button-hover-color: #fff !important;
@radio-button-active-color: #fff !important;
@radio-button-bg: #F5F5F6 !important;
@radio-button-checked-bg: @primary-color-dark !important;
@radio-solid-checked-color: @primary-color-dark !important; */
