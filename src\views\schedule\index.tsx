// import React from "react";
// import {Schedule} from "@iworks/plan";
// import {useSelector} from "react-redux";
// import {RootState} from "../../store/rootReducer";

// const TheSchedule: React.FC = () => {
//     const commonData = useSelector((state: RootState) => state.commonData);
//     return (
//         <>
//             {/* <PlanRouter /> */}
//             {/* <Router>{renderRoutes(PlanRouter)}</Router> */}
//             <Schedule {...commonData} />
//         </>
//     );
// };
// export default TheSchedule;
