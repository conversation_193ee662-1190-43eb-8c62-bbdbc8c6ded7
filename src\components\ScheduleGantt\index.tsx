import React, {useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import GanttEditor, {ScheduleEditorProps} from "./views/GanttEditor";
import {RootState} from "../../store/rootReducer";
import {setToken} from "./api/commonFetch";

// const root = document.getElementById("schedule");
// const load = () => render(
//     // eslint-disable-next-line @typescript-eslint/no-explicit-any
//     <Provider store={store as any}>
//         <Router>{renderRoutes(routes)}</Router>
//     </Provider>,
//     root
// );

const ScheduleGantt = (props: ScheduleEditorProps) => {
    const dispatch = useDispatch();
    const {token, orgInfo} = useSelector((store: RootState) => store.commonData);

    useEffect(() => {
        setToken(token ?? "");
    }, [dispatch, token]);
    if ((orgInfo?.orgId ?? "").length === 0) {
        return null;
    }

    return (
        <GanttEditor {...props} />
    );
};

export default ScheduleGantt;
