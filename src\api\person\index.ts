import Fetch from "../../service/Fetch";
import {DownloadUrlRes} from "./type";
import {OrgRes, RoleRes} from "../../store/personSelect/interface";

export const getUsersOfOrg = async <T = OrgRes[] | null>(searchKey: string, deptId: string) => Fetch<T>({
    url: "pdscommon/rs/userInfo/searchUsersOfOrg",
    methods: "post",
    data: {searchKey, deptId}
});

export const getUsersOfRole = async <T = RoleRes[] | null>(searchKey: string, deptId: string) => Fetch<T>({
    url: "pdscommon/rs/userInfo/searchUsersOfRole",
    methods: "post",
    data: {searchKey, deptId}
});

export const getUrlByUuid = async <T = DownloadUrlRes[]>(fileUUIDList: string[]) => Fetch<T>({
    url: "pdscommon/rs/fileaddress/downloadURLs",
    methods: "post",
    data: {fileUUIDList}
});
