import React, {useCallback, useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import _ from "lodash-es";
import {useHistory, useLocation} from "react-router-dom";
import {RouteConfigComponentProps} from "react-router-config";
import IworksFrame from "@iworks/iworksframe";
import {MenuItem} from "@iworks/iworksframe/dist/components/navMenu/interface";
import {HeaderConfig} from "@iworks/iworksframe/dist/interface";
import {getAuthCode, getMenuList, putAuthEnterprise, getOrgList} from "../../api/common.api";
import {dealMenuData, goToLoginPage} from "../../assets/ts/utils";
import {RootState} from "../../store/rootReducer";
import {setAuthCodeList, setLastSelectedMenu, setMenuList, setOrgInfo, setOriginAuthCode<PERSON>ist, setToken, setUserInfo, setOrgList, setServerInfo, setCurSectionInfo, setSectionList} from "../../store/common/action";
import {AuthCodeList} from "../../store/common/actionTypes";
import {MenuListProps} from "../../api/common.type";
import {setAuthorityTabs} from "../../store/sandManage/action";
import {mockAuthGroupList, mockMenuList} from "./mock";
import {getState} from "../../store";
import confirmLeave from "../../assets/ts/confirmLeave";
import RenderRoutesPage from "./RenderRoutesPage";
import {changeLeftBoxVisible, resetLeftBoxVisible} from "../../store/no-persist/action";
import IframeBox from "./IframeBox";

const {productId, baseUrl, basePds, shellDownUrl, motorViewUrl} = _.get(window, "__IWorksConfig__");

const isMock = false;
const MenuLayout = (props: RouteConfigComponentProps) => {
    const dispatch = useDispatch();
    const history = useHistory();
    const {pathname} = useLocation();
    const lastMenu: MenuListProps | undefined = localStorage.getItem("persist:plan") !== null ? JSON.parse(JSON.parse(localStorage.getItem("persist:plan") ?? "").commonData).lastSelectedMenu : undefined;

    const {
        from,
        menuId,
        menuList,
        lastSelectedMenu,
        hideProjectTreeStatus,
        orgInfo,
    } = useSelector((state: RootState) => state.commonData);
    const [selectedTreeKey, setSelectedTreeKey] = useState<string>();
    const [selectedMenuKey, setSelectedMenuKey] = useState<string>(lastMenu?.key ?? lastSelectedMenu?.path ?? "");
    const [headerFrameStatus, setHeaderFrameStatus] = useState<"visible" | "hidden" | undefined>(undefined);

    useEffect(() => {
        dispatch(changeLeftBoxVisible({[pathname]: orgInfo.deptDataType !== 1}));
    }, [dispatch, orgInfo.deptDataType, pathname]);

    useEffect(
        () => () => {
            dispatch(resetLeftBoxVisible());
        },
        [dispatch]
    );

    React.useLayoutEffect(() => {
        const url = new URL(window.location.href);
        if (url.searchParams.get("from") === "newStandard" && (localStorage.getItem("viewType") ?? "").length === 0) {
            localStorage.setItem("viewType", "main");
            // setVisible(true);
        } else {
            // setVisible(true);
        }
    }, []);

    React.useEffect(() => {
        // eslint-disable-next-line no-console
        console.log("MenuLayout", from);
        if (from === "frameWithMenuShell") {
            setHeaderFrameStatus("hidden");
        } else {
            setHeaderFrameStatus("visible");
        }
    }, [from, history]);

    useEffect(() => {
        const token = localStorage.getItem("token");
        dispatch(setToken(token ?? ""));
    }, [dispatch]);

    const handleMenuSelect: HeaderConfig["menuSelectedCallback"] = useCallback((selectKey, selectItem) => {
        if (!((lastSelectedMenu ?? "") !== "" && lastSelectedMenu?.path === selectKey)) {
            // 防止重复跳转路由
            history.push(selectKey);
        }
        // dispatch(setTreeData([]));
        // dispatch(setEbsTreeData([]));
        dispatch(setLastSelectedMenu(selectItem));
        if (selectKey !== "/main" && pathname !== "/main") {
            setSelectedMenuKey(pathname);
        } else {
            setSelectedMenuKey(selectKey);
        }
        // setSelectedMenuKey(selectKey === "/main" || pathname === undefined || selectKey !== "/main" ? pathname : selectKey);
        // setSelectedMenuKey(selectKey);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch, history]);

    // 获取菜单
    const getMenuDataList = useCallback(() => {
        getMenuList(menuId)
            .then(
                (res) => {
                    if (res.code === 200) {
                        let resData: MenuListProps[] = [];
                        resData = res.data;
                        // TODO: mock记得去掉
                        const newResData = isMock ? dealMenuData(mockMenuList as unknown as MenuListProps[]) : dealMenuData(resData);
                        // const newResData = dealMenuData(resData);
                        const allMenuList = newResData;
                        dispatch(setMenuList(allMenuList));
                        const manageMenu = allMenuList.find((i) => i.label === "进度管理");
                        if (manageMenu !== undefined && manageMenu.children.length > 0) {
                            const children = manageMenu.children.find((el) => el.key === "/main/scheduleManagement/home");
                            const tabList = children !== undefined ? children.children.map((j) => ({...j, key: j.label === "沙盘驾驶舱" ? "sandBox" : "bim"})) : [];
                            dispatch(setAuthorityTabs(tabList));
                        }
                        if (lastMenu !== undefined) {
                            handleMenuSelect(lastMenu.path, lastMenu);
                            return;
                        }
                        setSelectedMenuKey(allMenuList[0].path);
                    }
                }
            )
            .catch(() => {
                dispatch(setMenuList([]));
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch, menuId]);
    // 获取数据
    useEffect(() => {
        try {
            getOrgList()
                .then((res) => {
                    dispatch(setOrgList(res.data));
                });
        } catch (error) {
            console.log(error);
        }
    }, [dispatch]);
    const setServer = useCallback(() => {
        const list = [
            {serverName: "baseUrl", serverURL: baseUrl},
            {serverName: "basePds", serverURL: basePds},
            {serverName: "shellDownUrl", serverURL: shellDownUrl}
        ];
        list.push({serverName: "lbmotor", serverURL: motorViewUrl});
        const serviceList = _.mapValues(_.keyBy(list, "serverName"), "serverURL");
        dispatch(setServerInfo(serviceList));
    }, [dispatch]);
    useEffect(() => {
        setServer();
    }, [setServer]);
    // 用户信息
    const setAuthEnterprise = useCallback(() => {
        const epid = localStorage.getItem("epid");
        putAuthEnterprise(Number(epid) ?? 0).then(
            (authEnterprise) => {
                if (authEnterprise.data !== undefined) {
                    getMenuDataList();
                    dispatch(setUserInfo({
                        realName: authEnterprise.data.user.realName ?? "",
                        roleId: authEnterprise.data.user.roleId ?? "",
                        roleName: authEnterprise.data.user.roleName ?? "",
                        username: authEnterprise.data.user.username ?? "",
                        epid: `${authEnterprise.data.epid ?? ""}`,
                        enterpriseName: authEnterprise.data.nameCn ?? ""
                    }));
                }
            }
        );
    }, [dispatch, getMenuDataList]);

    // 权限码
    const setAuthCodeData = useCallback(async () => {
        try {
            const res = await getAuthCode(Number(productId));
            if (Array.isArray(res.data)) {
                let resData: AuthCodeList[] = [];
                resData = isMock ? mockAuthGroupList : res.data;
                const list = (resData ?? []).map((el) => el.authCodeResultList.map((item) => item.authCode));
                const hasAuthCodeList = (resData ?? []).filter((el) => el.authCodeResultList.length > 0);
                dispatch(setAuthCodeList(list.flat(2)));
                dispatch(setOriginAuthCodeList(hasAuthCodeList));
            }
        } catch (err) {
            console.log(err);
        }
    }, [dispatch]);

    useEffect(() => {
        const token = localStorage.getItem("token");
        if (token === "") {
            return;
        }
        setAuthEnterprise();
        setAuthCodeData();
    }, [setAuthCodeData, setAuthEnterprise]);
    useEffect(() => {
        if ((orgInfo?.orgId ?? "").length > 0) {
            setSelectedTreeKey(orgInfo.orgId);
        }
    }, [orgInfo]);
    const handleProjectChange: HeaderConfig["changeProjectCallback"] = useCallback(
        async (value) => {
            const isLeave = await confirmLeave();
            if (!isLeave) {
                return;
            }
            const tempOrgId = getState().commonData.orgInfo.orgId;
            if (value.id !== tempOrgId) {
                // console.log("MenuLayout setOrgInfo");
                setSelectedTreeKey(value.id);
                dispatch(setCurSectionInfo(null));
                dispatch(setSectionList([]));
                dispatch(setOrgInfo({
                    orgId: value.id,
                    orgName: value.name,
                    orgType: value.type,
                    deptDataType: value.deptDataType
                }));
            }
        },
        [dispatch]
    );
    useEffect(() => {
        if ((orgInfo.orgId ?? "").length === 0) {
            const oldOrgInfo = localStorage.getItem("persist:plan") !== null ? JSON.parse(JSON.parse(localStorage.getItem("persist:plan") ?? "").commonData).orgInfo : undefined;
            if (oldOrgInfo !== undefined) {
                setSelectedTreeKey(oldOrgInfo.orgId);
            }
        }
    }, [dispatch, orgInfo, orgInfo.orgId]);

    return (
        headerFrameStatus !== undefined
            ? (
                <>
                    {
                        headerFrameStatus === "visible" && (
                            <IworksFrame
                                platformName={window?.systemNameConfig?.planWeb}
                                hideProjectTree={hideProjectTreeStatus}
                                selectedTreeKey={selectedTreeKey}
                                selectedMenuKey={selectedMenuKey}
                                goToLoginPage={goToLoginPage}
                                menusConfig={menuList as unknown as MenuItem[]}
                                menuSelectedCallback={handleMenuSelect}
                                changeProjectCallback={handleProjectChange}
                            >
                                <RenderRoutesPage {...props} />
                            </IworksFrame>
                        )
                    }
                    {
                        headerFrameStatus === "hidden"
                        && (
                            <IframeBox>
                                <div style={{
                                    height: "100vh",
                                    background: "#fff",
                                    fontSize: " 14px !important",
                                    minWidth: "960px",
                                    overflowX: "auto",
                                }}
                                >
                                    <RenderRoutesPage {...props} />
                                </div>
                            </IframeBox>
                        )
                    }
                </>
            )
            : null
    );
};

export default MenuLayout;
