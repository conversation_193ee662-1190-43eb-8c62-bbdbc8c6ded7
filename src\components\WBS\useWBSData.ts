import {DataNode} from "antd/lib/tree";
import {useCallback, useEffect} from "react";
import {useDispatch, useSelector} from "react-redux";
import {getWBSAllTree} from "../../api/center";
import {WBSNodeType} from "../../api/center/type";
import {NodeTypeEnum} from "../../store/common/actionTypes";
import {RootState} from "../../store/rootReducer";
import {setOrginData, setTreeData, setTreeDataObj} from "./store/action";

const toTree = (list: WBSNodeType[]): DataNode[] => {
    const {
        keyField = "id",
        childField = "children",
        parentField = "parentId"
    } = {};

    const tree = [];
    const record: {[key: string]: WBSNodeType[]} = {};

    for (let i = 0, len = list.length; i < len; i++) {
        const item = list[i];
        const id = item[keyField];

        if (record[id] !== undefined) {
            item[childField] = record[id];
        } else {
            record[id] = [];
            item[childField] = record[id];
        }

        if (item[parentField] === item[keyField] || item.parentId === undefined || Number(item.businessType) === 1) {
            tree.push(item);
        } else {
            const parentId = item[parentField];
            if (record[parentId] === undefined) {
                record[parentId] = [];
            }
            record[parentId].push(item);
        }
    }

    return tree;
};

const useWBSData = () => {
    const dispatch = useDispatch();
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {sectionList} = useSelector((state: RootState) => state.commonData);
    const getTreeData = useCallback(
        async () => {
            if (orgInfo.orgId === "") {
                return;
            }
            // 房建并且没有标段的
            if (Number(orgInfo.deptDataType) === 1 && sectionList.filter((el) => el.nodeType !== NodeTypeEnum.项目).length === 0) {
                getWBSAllTree(orgInfo.orgId)
                    .then((res) => {
                        if (res.success) {
                            const tempTreeData = [
                                {
                                    key: orgInfo.orgId,
                                    title: orgInfo.orgName,
                                    children: toTree(res.data.map((el) => ({...el, title: el.name, key: el.id})))
                                }
                            ];
                            dispatch(setOrginData({[orgInfo.orgId]: res.data}));
                            dispatch(setTreeDataObj({[orgInfo.orgId]: tempTreeData}));
                        } else {
                            dispatch(setTreeData([]));
                            dispatch(setTreeDataObj({}));
                        }
                    })
                    .catch(() => {
                        dispatch(setTreeData([]));
                        dispatch(setTreeDataObj({}));
                    });
            } else {
                const promiseList: Promise<void>[] = [];
                const tempTreeData: {[key: string]: DataNode[]} = {};
                const tempOriginData: {[key: string]: WBSNodeType[]} = {};
                sectionList.filter((e) => e.classification === 0).forEach((el) => {
                    promiseList.push(
                        getWBSAllTree(orgInfo.orgId, el.id)
                            .then((res) => {
                                if (res.success === true) {
                                    tempOriginData[el.id] = res.data;
                                    tempTreeData[el.id] = [
                                        {
                                            key: el.id,
                                            title: el.name,
                                            // eslint-disable-next-line max-nested-callbacks
                                            children: toTree(res.data.map((e) => ({...e, title: e.name, key: e.id})))
                                        }
                                    ];
                                }
                            })
                            .catch((err) => {
                                console.log(err);
                            })
                    );
                });
                await Promise.all(promiseList);
                dispatch(setOrginData(tempOriginData));
                dispatch(setTreeDataObj(tempTreeData));
            }
        },
        [dispatch, orgInfo.deptDataType, orgInfo.orgId, orgInfo.orgName, sectionList],
    );

    useEffect(() => {
        getTreeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [sectionList]);
};

export default useWBSData;
