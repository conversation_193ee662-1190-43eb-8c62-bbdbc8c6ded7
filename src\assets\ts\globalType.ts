export interface AppUrlSearchType {
    from?: string; // 来源，固定为newStandard，说明来自新平台
    menuId?: string; // 子系统产品Id;
    orgId?: string; // 当前选择的项目Id
    token?: string;
    epid?: string; // 企业号
    menuPath?: string;
}
// url账号密码登录传参
export interface ParamsInfo {
    username: string;
    password: string;
    enterpriseId: number;
}

export type IworksProjectType = "personal" | "enterprise"; // 项目的身份，分为个人和企业
export type BackType = "cancel" | "ok";
