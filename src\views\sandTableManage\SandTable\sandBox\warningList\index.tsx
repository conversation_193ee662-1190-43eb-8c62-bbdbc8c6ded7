import {Col, Row, Space, Spin, Tree, TreeProps, Typography} from "antd";
import React, {memo, useState, useCallback, useRef} from "react";
import {DataNode} from "antd/lib/tree";
import Icon from "@ant-design/icons";
import {getWarnTypeList} from "../../../../../api/sandManager";
import {CheckedInfoType, CheckInfo, WarningTreeDataType} from "../index.d";
import MyInputSearch from "../../../../../components/MyInputSearch";
import {AlarmBubbleIcon} from "../../../../../assets/icons";

const {Text} = Typography;
const colorList = ["#FAD20C", "#ED6A0C", "#8A3FD4", "#D40000"];
const WarningList = (props: {setCheckedInfo: React.Dispatch<React.SetStateAction<CheckedInfoType>>}) => {
    const {setCheckedInfo} = props;
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(["all", "first", "second", "third", "forth"]);
    const [treeData, setTreeData] = useState<WarningTreeDataType[]>([]);
    const [expandedKey, setExpandedKey] = useState<React.Key[]>(["all"]);
    const [search, setSearch] = useState("");
    const allTree = useRef<WarningTreeDataType[]>([]);

    const getData = useCallback(() => {
        getWarnTypeList().then((res) => {
            const allTreeData = [
                {
                    title: "全部",
                    key: "all",
                    children: res.data.map((i) => ({...i, title: i.name, key: i.id}))
                }
            ];
            allTree.current = allTreeData;
            setTreeData(allTreeData);
        // eslint-disable-next-line no-console
        }).catch((err) => console.log(err));
    }, []);
    React.useEffect(() => {
        getData();
    }, [getData]);
    const handleSearch = useCallback((value) => {
        setSearch(value);
        if ((value ?? "").length > 0) {
            setTreeData([
                {
                    title: "全部",
                    key: "all",
                    children: allTree.current[0].children?.filter((i) => i.title.includes(value))
                }
            ]);
        } else {
            setTreeData(allTree.current);
        }
    }, []);

    const handleChecked: TreeProps["onCheck"] = (val, info: CheckInfo<DataNode>) => {
        const {checkedNodes, checked, halfCheckedKeys} = info;
        setCheckedKeys(val as unknown as React.Key[]);
        setCheckedInfo((oldInfo) => ({
            ...oldInfo,
            warningList: {
                checkedKeys: checkedNodes.filter((i) => i.children === undefined).map((j) => j.key),
                isCheckAll: checked && (halfCheckedKeys ?? []).length === 0
            }
        }));
    };

    const handleExpand: TreeProps["onExpand"] = (expandedVal) => {
        setExpandedKey(expandedVal);
    };

    const renderTreeNode = React.useCallback((list: WarningTreeDataType[]) => list.map((item, index) => (
        <Tree.TreeNode
            key={item.key}
            data-value={item}
            title={(
                <div>
                    <Space>
                        {item.key !== "all" && <Icon component={AlarmBubbleIcon} style={{color: colorList[index] ?? item.color ?? ""}} /> }
                        <Text
                            ellipsis={{tooltip: item.title}}
                            style={{wordBreak: "break-all", width: 120}}
                        >
                            {item.title}
                        </Text>
                    </Space>
                </div>
            )}
        >
            {item.children !== undefined && item.children.length > 0 && renderTreeNode(item.children)}
        </Tree.TreeNode>
    )), []);

    return (
        <div style={{height: "100%"}}>
            <Row>
                <Col flex="auto">
                    <MyInputSearch placeholder="输入报警名称" onSearch={handleSearch} allowClear value={search} />
                </Col>
            </Row>
            {treeData.length === 0
                ? <Spin />
                : (
                    <Tree
                        defaultExpandAll
                        autoExpandParent
                        style={{margin: "16px 0", height: "calc( 100% - 40px )"}}
                        checkedKeys={checkedKeys}
                        onCheck={handleChecked}
                        expandedKeys={expandedKey}
                        checkable
                        onExpand={handleExpand}
                    >
                        {renderTreeNode(treeData)}
                    </Tree>
                )}
        </div>
    );
};

export default memo(WarningList);
