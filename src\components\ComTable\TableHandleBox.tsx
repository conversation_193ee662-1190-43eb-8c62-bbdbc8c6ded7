import {EllipsisOutlined} from "@ant-design/icons";
import {Dropdown, Menu, Space} from "antd";
import React, {ReactNode} from "react";
import {createUseStyles} from "react-jss";
import Color from "../../assets/css/Color";

const useStyle = createUseStyles({
    box: {},
    deviceMenu: {
        "& .ant-dropdown-menu-item:hover, & .ant-dropdown-menu-submenu-title:hover": {
            backgroundColor: "revert"
        },
        "& .ant-btn-text:hover": {
            backgroundColor: Color["bg-4"]
        }
    },
});

interface TableHandleBoxProps {
    children: ReactNode[];
}

const TableHandleBox = (props: TableHandleBoxProps) => {
    const {children} = props;
    const cls = useStyle();

    if (children.length <= 3) {
        return (
            <div className={cls.box}>
                <Space>
                    {children}
                </Space>
            </div>
        );
    }

    return (
        <div className={cls.box}>
            <Space>
                {children.slice(0, 2)}
            </Space>
            <Dropdown
                overlay={(
                    <Menu className={cls.deviceMenu}>
                        {children.slice(2).map((el, index) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <Menu.Item key={index}>
                                {el}
                            </Menu.Item>
                        ))}
                    </Menu>
                )}
            >
                <EllipsisOutlined style={{color: Color.primary, padding: "0 8px"}} />
            </Dropdown>
        </div>
    );
};

export default TableHandleBox;
