// import Motor from "@motor/core";
import {ParentAPI} from "postmate";
// import MotorUtils from "../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
// import {MotorContext} from "../../../../reMotor";
import {frameMsgCreator} from "../../modelView/MotorFrame";
import StatusColor, {SandBoxHandlerForMotorFrame, SandDataInitializeBase} from "./dataOrInterface";

export default class StatusPlanSandBoxHandlerForMotorFrame implements SandBoxHandlerForMotorFrame {
    // private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    private curDate: Date | null = null;

    private motorFrame: ParentAPI | null = null;

    constructor(dataContainer: SandDataInitializeBase) {
        // this.bimProject = MotorContext.getCurBIMProject();
        this.dataContainer = dataContainer;
    }

    reset() {
        this.curDate = null;
        this.resetModel();
    }

    refresh() {
        if (isNotNullOrUndefined(this.curDate)) {
            this.showSandBoxByDate(this.curDate);
        }
    }

    setChildApi(motorFrame: ParentAPI | null) {
        this.motorFrame = motorFrame;
    }

    showSandBoxByDate(curTime: Date): void {
        const {motorFrame, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(motorFrame)) {
            const statusConfig = dataContainer.getFilterInfo("defaultStatus");
            if (!statusConfig.isCheckAll && statusConfig.setCheckedKeys.size === 0) {
                return;
            }

            const compItems = dataContainer.queryCompStatusListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = compItem.status === "complete" ? StatusColor.completeColor : StatusColor.ongoingColor;
                if (statusConfig.isCheckAll
                    || (colorString === StatusColor.ongoingColor && statusConfig.setCheckedKeys.has("ongoing"))
                    || (colorString === StatusColor.completeColor && statusConfig.setCheckedKeys.has("finished"))) {
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            const colorList = Array.from(mapColorComp);
            const colorInfo: {color: string; ids: string[]}[] = [];
            for (let i = 0; i < colorList.length; ++i) {
                const item = colorList[i];
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    colorInfo.push({
                        color: item[0],
                        ids: comps.map((el) => el.id ?? "")
                    });
                }
            }
            const param = frameMsgCreator({
                name: "onSandPlay",
                params: {colorInfo}
            });
            motorFrame.call(param.name, param);
        }
    }

    jumpToDate(curTime: Date) {
        const {motorFrame, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(motorFrame)) {
            const statusConfig = dataContainer.getFilterInfo("defaultStatus");
            if (!statusConfig.isCheckAll && statusConfig.setCheckedKeys.size === 0) {
                return;
            }

            const compItems = dataContainer.queryAccumulatedCompStatusListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = compItem.status === "complete" ? StatusColor.completeColor : StatusColor.ongoingColor;
                if (statusConfig.isCheckAll
                    || (colorString === StatusColor.ongoingColor && statusConfig.setCheckedKeys.has("ongoing"))
                    || (colorString === StatusColor.completeColor && statusConfig.setCheckedKeys.has("finished"))) {
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            this.resetModel();
            const colorList = Array.from(mapColorComp);
            const colorInfo: {color: string; ids: string[]}[] = [];
            for (let i = 0; i < colorList.length; ++i) {
                const item = colorList[i];
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    colorInfo.push({
                        color: item[0],
                        ids: comps.map((el) => el.id ?? "")
                    });
                }
            }
            const param = frameMsgCreator({
                name: "onSandPlay",
                params: {colorInfo}
            });
            motorFrame.call(param.name, param);
        }
    }

    private resetModel() {
        const {motorFrame, dataContainer} = this;
        if (isNotNullOrUndefined(motorFrame) && isNotNullOrUndefined(dataContainer)) {
            const {isCheckAll, comps} = dataContainer.queryVisibilityByEbsFilter();
            const param = frameMsgCreator({
                name: "resetProject",
                params: {visible: isCheckAll, color: StatusColor.notStartColor}
            });
            motorFrame.call(param.name, param);
            if (!isCheckAll) {
                const visibleParam = frameMsgCreator({
                    name: "setVisible",
                    params: {visible: true, ids: comps}
                });
                motorFrame.call(visibleParam.name, visibleParam);
            }
        }
    }
}
