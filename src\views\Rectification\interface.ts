import moment, {Moment} from "moment";
import {TreeType, FileType} from "../../api/common.type";
import {GetReportFormTemplateType, QuerySignByNodeReturn, LaunchReformType} from "../../api/rectification/models/flowLine";
import {ReformDetailVo} from "../../api/rectification/models/process";

export interface CheckSubOptionValueType {
    checkDescId: string;
    subOptionContent: string;
    subOptionId: string;
    content?: string;
}

export type RectifyTaskStatus = "add" | "detail" | "list";

export const expireStatusList = [
    // {
    //     label: "全部",
    //     value: 0
    // },
    {
        label: "已逾期",
        value: 1
    },
    {
        label: "未逾期",
        value: 2
    }
];

export const checkTypeListEasy = [
    {value: 0, label: "日常检查"},
    {value: 1, label: "专项检查"},
    {value: 2, label: "不定期检查"},
    {value: 3, label: "节假日检查"},
    {value: 4, label: "综合性检查"},
    {value: 5, label: "其他"},
];

export const checkTypeList = [
    {
        label: "日常检查",
        value: 0
    },
    {
        label: "专项检查",
        value: 1
    },
    {
        label: "节假日检查",
        value: 2
    },
    {
        label: "不定期检查",
        value: 3
    },
    {
        label: "综合性检查",
        value: 4
    },
    {
        label: "其他",
        value: 5
    },
    {
        label: "日检查",
        value: 6
    },
    {
        label: "周检查",
        value: 7
    },
    {
        label: "月检查",
        value: 8
    },
    {
        label: "风险检查",
        value: 9
    },
    {
        label: "安全大检查",
        value: 10
    }
];

export const reformStatusList = [
    // {
    //     label: "全部",
    //     value: 0
    // },
    {
        label: "整改中",
        value: 1
    },
    {
        label: "已整改",
        value: 2
    }
];

export interface TabPaneListType {
    label: string;
    value: number;
    key: string;
}
export const processTypeList: TabPaneListType[] = [
    {
        label: "待处理的",
        value: 2,
        key: "",
    },
    {
        label: "我发起的",
        value: 1,
        key: "",
    },
    {
        label: "已处理",
        value: 3,
        key: "",
    },
    {
        label: "抄送我的",
        value: 4,
        key: "",
    },
    // {
    //     label: "全部",
    //     value: 5,
    //     key: "",
    // }
];

export const nodeTypeList = [
    {
        label: "分公司",
        value: 1
    },
    {
        label: "项目部",
        value: 2
    },
    {
        label: "标段",
        value: 3
    },
    {
        label: "单项",
        value: 4
    },
    {
        label: "单位",
        value: 5
    },
    {
        label: "工程",
        value: 6
    }
];
export const unitTypeList = [
    {
        label: "业主检查",
        value: 3
    },
    {
        label: "监理检查",
        value: 1
    },
    {
        label: "施工自查",
        value: 0
    }
];

export const processStatusTypeList = [
    {
        label: "进行中",
        value: "in-progress"
    },
    {
        label: "撤销",
        value: "canceled"
    },
    {
        label: "退回",
        value: "backed"
    },
    {
        label: "已通过",
        value: "passed"
    }
];

export const queryFormInit: RectifyQueryFormModel = {
};


export interface RectifyQueryFormModel {
    /** 检查分项 */
    checkSubOption?: string;
    /** 检查类型筛选条件 */
    checkTypeId?: number;
    /** 整改期限 筛选条件 */
    time?: [Moment, Moment];
    /** 逾期状态: 0-全部 1-已逾期 2-未逾期 */
    expireStatus?: number;
    /** 整改状态: 0-全部 1-整改中 2-已整改 */
    reformStatus?: number;
    /** 搜索关键字 */
    searchKey?: string;
    /** 检查标段 */
    checkNodeIds?: string[];
    optionsList?: CheckSubOptionValueType;
    projectCategoryId?: string;
}

export interface RectifyAddFormModel {
    /** 整改批注 */
    comment: string;
    /** 整改期限 */
    deadline: Moment;
    /** 表单名称 */
    name: string;
}

export interface RectifySubmitFormModel {
    message?: string;
}

export const renderSubmitFormValues = (info: ReformDetailVo): RectifyAddFormModel => ({
    comment: info.reformComment,
    name: info.name,
    deadline: moment(info.reformDeadline)
});

export const getPersonByname = (names: string[], list: TreeType[]) => list.filter((v) => typeof v.centerUserName === "string").filter((v) => names.includes(v.centerUserName!));


export interface LaunchReformParams extends LaunchReformType {
    currentFlowNodeId: string;
}
export interface SignSuccessInfoType {
    fileId: string;
    md5: string;
    name: string;
    size: number;
}
export interface RectificationSheetSignProps {
    handleSubmit: (fileInfo?: SignSuccessInfoType) => void;
    reformDetail?: ReformDetailVo | undefined; // 发起人之后的节点需传  用以获取预览表单数据
    launchReform?: LaunchReformParams | undefined; // 发起人节点需传  用以获取预览表单数据
    replyReportFormTemplate?: GetReportFormTemplateType;
    noticeReportFormTemplate?: GetReportFormTemplateType;
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    defaultAnnotation?: string; // 默认批注 整改人以后节点需传
    operateMsg?: string; // 整改意见
    fileList?: FileType[];
    formPdfInfo?: QuerySignByNodeReturn;
}

export interface FormInfoType extends GetReportFormTemplateType{
    urls: string[];
}
export interface PreviewInfoType {
    previewUrl: string;
    fileId: string;
    fileName: string;
    formType: string;
    formName: string;
}
