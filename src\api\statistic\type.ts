import {WebRes} from "../common.type";

export interface WbsStatistic {
    sectionId: string;
    children?: WbsStatistic[];
    /** 实际结束时间 */
    actualEndDate: string;
    /** 实际开始时间 */
    actualStartDate: string;
    /** 执行桩体 */
    executionStatus: string;
    /** 实际结束时间 */
    id: string;
    /** wbs节点名称 */
    name: string;
    /** 父节点id */
    pId: string;
    /** 计划完成时间 */
    planEndDate: string;
    /** 计划开始时间 */
    planStartDate: string;
    /** 是否存在子节点 */
    hasChild: boolean;
}

export type WbsStatisticListResult = WebRes<WbsStatistic[]>;

export interface GetWbsStatisticBody {
    /** 项目id */
    deptId: string;
    /** 标段id */
    sectionId: string;
    /** wbsPId(传空则查询第一层) */
    wbsPId?: string;
}

export interface GetWbsStatisticByNameBody {
    /** 项目id */
    deptId: string;
    /** 标段id */
    sectionId: string;
    /** wbs节点名称 */
    name: string;
}

export interface Rule {
    color: string;
    description: string;
    id: string;
    rules: RuleItem[];
    sort: number;
    edit: boolean;
    updateAt?: string;
    updateBy?: string;
}

interface RuleItem {
    includes: boolean;
    val: number;
}

export type GetRulesResult = WebRes<Rule[]>;
