import {DataNode} from "antd/lib/tree";

export interface WebRes<T = unknown> {
    success: boolean;
    msg: string;
    code: number;
    data: T;
}

export interface TableRes<T = unknown> extends WebRes {
    data: {
        totalPage: number;
        totalCount: number;
        items: T;
    };
}

export interface WebResResult<T = unknown> {
    success: boolean;
    msg: string;
    code: number;
    result: T;
}

export interface PageRes<T> {
    items?: T[];
    totalCount?: number;
    totalPage?: number;
}

export interface PageInfo {
    currentPage?: number;
    pageSize?: number;
}

export interface OrgNode extends DataNode {
    cityCoordinate: string | null;
    proviceCoordinate: string | null;
    countyCoordinate: string | null;
    deptCode: string | null;
    cityId: string | number | null;
    provinceId: string | number | null;
    countyId: string | number | null;
    latitude: string | null;
    longitude: string | null;
    id: string;
    name: string;
    parentId: string;
    type: number;
    cityName: string | null;
    sortOrder: number;
    children?: OrgNode[];
    key: string | number;
    deptDataType: number;
}

export interface RegionDataType {
    value: string;
    label: string;
    lat: number;
    lon: number;
    children: RegionDataType[];
}

export interface RegionInfo {
    provinceId: string;
    areaId: string;
    center: [number, number, number];
}

export interface LoginInfoDataType {
    epid: number;
    logoUrl: string;
    welcomeBackgroundUrl: string;
    platformName: string;
    authCodes: string;
    nameCn: string;
    user: {
        admin: boolean;
        realName: string;
        roleId: string;
        roleName: string;
        username: string;
    };
}

export interface TokenUserInfo {
    /** @description 邮箱 */
    email?: string;
    /**
     * Format: int32
     * @description 企业id
     */
    epid?: number;
    /** @description 身份证号 */
    idCard?: string;
    /** @description 手机号 */
    mobile?: string;
    /** @description 组织id */
    orgId?: string;
    /** @description 组织名称 */
    orgName?: string;
    /** @description 用户头像uuid */
    portraitUuid?: string;
    /** @description 岗位id */
    posts?: {
        /** @description 岗位id */
        postId?: string;
        /** @description 岗位名称 */
        postName?: string;
    };
    /** @description 真实姓名 */
    realName?: string;
    /** @description 备注 */
    remarks?: string;
    /** @description 角色id */
    roleId?: string;
    /** @description 角色名称 */
    roleName?: string;
    /**
     * Format: int32
     * @description 添加用户的来源:0:默认内部 1：外部人员
     */
    sourceType?: number;
    /** @description 来源系统id */
    systemId?: string;
    /** @description 用户id */
    userId?: string;
    /** @description 用户名 */
    userName?: string;
    /**
     * Format: int32
     * @description 用户类型:1 子管理员, 2 普通用户
     */
    userType?: number;
}

export interface MenuListProps {
    menuId: number; // 产品id
    menuName: string; // 名称
    icon: string | null; // 图标
    iconName: string | undefined;
    bgIcon: string | null; // 背景图
    path: string; // 路由
    isCollect: number; // 是否默认收藏 0否1是
    favorites: boolean; // 是否收藏
    type: string;
    remark: string; // 描述,
    isFrame: string; // 外链地址
    menuType: "M" | "C" | "F";
    children: MenuListProps[];
    status: string;
    statusMessage: string;
    defaultIcon: string | null;
    parentId: number;
    key: string;
    perms: string;
    label: string;
}

export interface GetDeptOrgListReturn {
    authFlag: boolean;
    /* 可以替换之前的curBuildInfo.value */
    classification: number;
    nodeId: string;
    nodeName: string;
    parentId: string | null;
    supervisorOrg: string;
    totalPerson: number;
    name: string;
    id: string;
}

export interface GetAppAllocationPackageInfoReturn {
    packageType: number;
    packageInfos: {packageName: string; packageId: string; productIds: number[]}[];
}

export interface UserAuthPackageType {
    authTime: number;
    expireTime: number | null;
    functionId: string;
    heldId: number;
    orgId: string;
    orgName: string;
}

export interface ProjNameVo {
    //  代理工程id
    ppid: number;
    //  工程名称，用于校验工程
    projName: string;
    //  工程类型
    projType: number;
    //  工程模型（施工或预算）
    projModel: string;
}

export interface GetOrgListProjNodeByDeptIdReturn {
    value: string;
    key: string;
    id: string;
    type: number;
    parentId: string;
    pId?: string;
    name: string;
    title: string;
    sortOrder?: number;
    projects?: ProjNameVo[];
    children: [];

}

export interface GetDownloadUrlRes1 {
    [k: string]: unknown;
    fileMd5: string;
    fileUUID: string;
    fileSize: number;
    finished: boolean;
    uploadUrls: string[];
    moreParams: {key: string; value: string}[];
}

export interface FileType {
    fileName: string;
    fileSize: number;
    fileUuid: string;
    // 一个唯一性的标记,文件上传时会使用
    timeStamp?: number;
    createAt?: number;
    thumbUuid?: string;
    percent?: number;
    // 上传失败
    failed?: boolean;
}
export interface AttachmentPicType {
    fileName: string;
    fileSize: number;
    uuid: string;
}

export interface HouseDeptRes {
    org: {
        startDate?: number;
        endDate?: number;
    };
}

export interface GetDeptDetailResult {
    attachmentPics: AttachmentPicType[];
    attachments: AttachmentPicType[];
    bridgeTunnelRatio: number;
    buildOrg: string;
    chargePerson: string;
    contactAddress: string; // 联系地址
    contactPerson: string; // 项目联系人
    createTime: number; // 创建时间
    createUser: string; // 创建人
    deptId: string; // 项目部id
    deptType: number; // 项目类型(0 默认、1 高速公路、2 一级公路 3 二级公路 4 三级公路 5 四级公路 6 市政公路)
    designOrg: string; // 设计单位
    duration: number; // 工期，单位为月
    endCoordinate: string; // 终点坐标
    endDate: number; // 结束时间
    estimateInvestment: number; // 概算金额(万元)
    jiananInvertment: number; // 建安金额(万元)
    meterRuleId: string; // 计量规则id
    middleCoordinate: string; // 中点坐标
    mobile: string; // 电话号码
    modifyTime: number; // 最后修改时间
    modifyUser: string; // 最后修改人
    name: string; // 项目名称
    parentId: string; // 父节点id
    projectCode: string; // 项目编号
    projectMajor: number; // 项目专业:0-公路,1-市政,2-房建,3-园林,4-水利,5-安全,6-人防,7-电力,8-其他
    projectMilestone: string; // 项目里程
    remarks: string; // 项目概况
    shortName: string; // 项目简称
    startCoordinate: string; // 起点坐标
    startDate: number; // 起始时间
    status: number; // 状态(0拟建、1在建、2建成)
    supervisorOrg: string; // 质量监督单位
    totalInvertment: number; // 决算金额(万元)
    zipCode: string; //
}

export interface DownFileListType {
    dir: boolean; // 默认false  是文件
    fileList?: DownFileListType[]; // 文件/文件夹列表
    fileUuid?: string; // 文件uuid[文件时必传]
    name: string;
}
export interface PackageDownloadParams {
    fileList: DownFileListType[];
    packName: string;
}

export interface GetFileUrlRes {
    downloadUrls: string[];
    fileMD5: string;
    fileSize: number;
    fileUUID: string;
    fileName: string;
    fileType: number;
}

export interface DocType {
    docid: string;
    pathId: string;
    docName: string;
    suffix: string;
    docType: number;
    updater: string;
    updateTime: number;
    fileSize: number;
    fileMd5: string;
    tagInfoList: [
        {
            tagId: string;
            tagName: string;
        }
    ];
    nodeType: number;
    nodeId: string;
    relType: number;
    relTypeStr: string;
    fileUuid: string;
    thumbnailUuid: string;
    weaveTime: number;
    previewable: boolean;
    reference: number;
    authStatuses: [
        {
            authType: number;
        }
    ];
}

export interface GetDocTreeParams {
    nodeType: number;
    nodeId: string;
    needFileNum: boolean;
}

export interface PathType {
    pathId: string;
    pathName: string;
    fileNum: number;
    subList: PathType[];
    pathSource: number;
    lock: boolean;
    path: string;
    sort: number;
    authStatuses: [
        {
            authType: number;
        }
    ];
}

// 查询引用资料库列表参数
export interface GetDocListParams {
    docOrgRelationParam: {
        nodeType: number;
        nodeId: string;
        containChild: boolean;
    };
    pathId: string;
    docName?: string;
}

export interface DirType {
    pathId: string;
    pathName: string;
    pathSource: number;
    lock: boolean;
    path: string;
    sort: number;
    updateTime: string;
    authStatuses: [
        {
            authType: number;
        }
    ];
}

// 查询引用资料库列表返回结果
export interface GetDocListRes {
    docList: DocType[];
    dirList: DirType[];
    deptId: string;
    deptName: string;
}

// 查询引用资料库目录返回结果
export interface GetDocTreeRes {
    allPathAuth: boolean;
    pathSource: number;
    pathList: PathType[];
}

export interface TreeType {
    id: string;
    type: number;
    title: string;
    value: string;
    parentId: string;
    children: TreeType[];
    centerUserName: string | null;
}

export interface WBSType {
    wbsNodeId: string;
    wbsNodeName: string;
    level?: number;
    nodeId?: string;
    deptId?: string;
}

export interface GetProcessTemplatesParams {
    appModule: number;
    busiModule: "PLAN_APPROVAL" | "PLAN_REFORM";
    nodeId: string;
    page: number;
    size: number;
}

export interface ProcessTemplateItem {
    appModule: number;
    auth: number;
    busiModule: "PLAN_APPROVAL" | "PLAN_REFORM";
    id: string;
    nodeId: string;
    remark: string;
    status: number;
    switchStatus: number;
    typeName: string;
    updateTime: number;
    updateUser: string;
}

export interface GetProcessTemplatesRes {
    content: ProcessTemplateItem[];
}

/** 项目部下工程组织树 */
export interface OrgProjNodeVo {
    /** 节点id */
    id?: string;
    /** 节点类型: 0-项目部 1-标段 2-单项工程 3-单位工程 */
    type?: number;
    /** 父节点id，为null表示根节点 */
    parentId?: string;
    /** 节点名称 */
    name?: string;
    /** 排序字段 */
    sortOrder?: number;
    /** 工程列表 */
    projects?: ProjNameVo[];
}

export interface TreeNodeVO {
    /** 节点名称 */
    name: string;
    /** 关联构件树字段，楼层、专业、大类、小类、属性 */
    rel: string;
    /** 扩展属性 */
    ext?: string;
    /** 节点层级 */
    level: number;
    /** 子节点List */
    treeNodeList?: TreeNodeVO[];
    /** 是否为部位树 */
    isBws?: boolean;
    projType: number;
}

export interface ProjectTreeVO {
    /** 企业id */
    epid: number;
    /** 代理工程id */
    ppid: number;
    /** 部位树，后续会扩展构件树 */
    // type?: string;
    /** 树节点信息 */
    treeNodeList?: TreeNodeVO[];
    /** 是否抽取部位树（0-没有 1-抽取） */
    // isExtractLocationTree?: number;
    /** 工程类型 */
    // projType?: number;
}

export interface Motor3dStatusResult {
    /** 工程id */
    projid?: number;
    /** 代理工程id */
    ppid?: number;
    /** motor模型转换状态-1: 待处理， 0：处理失败， 1：处理成功  2：处理中 */
    status?: number;
    /** 转换结果 */
    handleInfo?: string;
    /** 转换类型 1:分级加载；2：静态模型 */
    type?: string;
    /** 转换操作 1：效果优先，3：性能优先 */
    option?: string;
    /** motor转换成功后此字段才有用 */
    motor3dRelationResult?: Motor3dRelationResult;
}

export interface Motor3dRelationResult {
    /** motor模型关联id */
    motor3dId?: string;
    /** motor模型版本号 */
    motor3d_version?: string;
    /** 模型文件大小 */
    motor3dFileSize?: number;
    /** motor转换工具版本号 */
    motorVersion?: string;
}

export interface ProjectDetailInfoVo {
    /** 代理工程id */
    ppid?: number;
    /** 项目部id */
    deptId?: string;
    /** 工程名称 */
    projName?: string;
    /** 工程类型 */
    projType?: string;
    /** 文件uuid */
    fileuuid?: string;
    /** 上传时间 */
    createDate?: string;
    /** 类型（取值：SLYS/SLSG） */
    projModel?: string;
    /** 工程类型 */
    projTypeInt?: number;
    /** 节点类型 0：项目部 1：标段 2：单项 3：单位 */
    nodeType?: number;
    /** 节点id */
    nodeId?: string;
    /** 是否抽取部位树（0：没有 1：抽取） */
    isExtractLocationTree?: number;
}
