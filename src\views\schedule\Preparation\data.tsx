import React from "react";
import {Col, Row} from "antd";
import {ComColumnsProps} from "../../../components/TableColumnsControl";
import {ApprovalStatus, PlanPreparationItemType} from "../../../api/Preparation/type";
import {ComFormItemProps} from "../../../components/FormItem";
import renderTableText from "../../../components/renderTableText";
import {momentText, getCycleMomentText} from "../../../assets/ts/utils";
import ComTag from "../../../components/ComTag";

export type ComColumnsType = ComColumnsProps<PlanPreparationItemType>;

export const tabList = [
    {
        label: "未发起",
        value: "1",
    },
    {
        label: "审批中",
        value: "2",
    },
    {
        label: "审批完成",
        value: "3",
    },
    {
        label: "无需审批",
        value: "0",
    },
    {
        label: "全部",
        value: "all",
    },
];

export const planTypeList = [
    {
        label: "总计划",
        value: "MASTER",
    }, {
        label: "年度计划",
        value: "YEAR"
    }, {
        label: "季度计划",
        value: "QUARTER"
    }, {
        label: "月度计划",
        value: "MONTH"
    }, {
        label: "周计划",
        value: "WEEK"
    }
];
export const cycleList = [
    {
        label: "全部",
        value: -1
    }, {
        label: "总",
        value: "MASTER"
    }, {
        label: "年",
        value: "YEAR"
    }, {
        label: "季",
        value: "QUARTER"
    }, {
        label: "月",
        value: "MONTH"
    }, {
        label: "周",
        value: "WEEK"
    }
];
export const QueryFormInit: ComFormItemProps[] = [
    {
        key: "type",
        type: "radio",
        typeConfig: {options: cycleList, optionType: "button", className: "planSearchRadioButton"},
        itemConfig: {name: "type", label: "", labelCol: {span: 0}, wrapperCol: {span: 24}},
        colConfig: {span: 6},
        nativeLabel: true,
    },
    {
        key: "nameKey",
        type: "search",
        typeConfig: {},
        itemConfig: {name: "nameKey"},
        colConfig: {span: 6, offset: 12}
    }
];
export const getPreparationColumns = (): ComColumnsType[] => [
    {
        title: "序号",
        align: "center",
        mustShow: true,
        show: true,
        fixed: "left",
        width: 80,
        render: (_text: unknown, _record: PlanPreparationItemType, index: number) => index + 1
    },
    {
        key: "name",
        title: "计划名称",
        dataIndex: "name",
        align: "left",
        fixed: "left",
        mustShow: false,
        show: true,
        width: 180,
        render: (name: string, record: PlanPreparationItemType) => (
            <Row align="middle" style={{flexWrap: "nowrap"}}>
                {record.approvalStatus === ApprovalStatus.RETRACT && <ComTag color="#717784">撤回</ComTag>}
                {record.approvalStatus === ApprovalStatus.RETURN && <ComTag color="#D40000">退回</ComTag>}
                {record.changeStatus === "Changed" && record.approvalStatus !== ApprovalStatus.APPROVAL_COMPLETED && <ComTag color="#FAAB0C">变更</ComTag>}
                <Col flex="1" style={{width: "0"}}>
                    {renderTableText(name)}
                </Col>
            </Row>
        )
    },
    {
        key: "nodeName",
        title: "所属组织",
        dataIndex: "nodeName",
        align: "left",
        mustShow: false,
        show: true,
        width: 180,
        render: renderTableText
    },
    {
        key: "type",
        title: "计划类型",
        dataIndex: "type",
        align: "left",
        mustShow: false,
        show: true,
        width: 160,
        render: (type: string) => planTypeList.find((item) => item.value === type)?.label ?? "--"
    },
    {
        key: "cycle",
        title: "计划周期",
        dataIndex: "cycle",
        align: "left",
        mustShow: false,
        show: true,
        width: 160,
        render: (text: number, record: PlanPreparationItemType) => renderTableText(getCycleMomentText(text, record.type))
    },
    {
        key: "startDate",
        title: "计划开始日期",
        dataIndex: "startDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 140,
        render: (text: number) => (text !== null ? renderTableText(momentText(text)) : "")
    },
    {
        key: "endDate",
        title: "计划完成日期",
        dataIndex: "endDate",
        align: "left",
        mustShow: false,
        show: true,
        width: 140,
        render: (text: number) => (text !== null ? renderTableText(momentText(text)) : "")
    },
    {
        key: "updateBy",
        title: "编制人",
        dataIndex: "updateBy",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: renderTableText
    },
    {
        key: "updateAt",
        title: "编制时间",
        dataIndex: "updateAt",
        align: "left",
        mustShow: false,
        show: true,
        width: 180,
        render: (text: number) => (text !== null ? renderTableText(momentText(text, "YYYY.MM.DD HH:mm:ss")) : "")
    },
];
export const ProjIconList = ["unmatch", "tujian", "anzhuang", "gangjin", "revit", "tekla", "jzx", "site", "c3d", "bentely", "rhino", "ifc", "catia", "pkpm"];
