import React, {ReactNode, memo, useCallback, CSSProperties, useMemo, useEffect} from "react";
import {createUseStyles} from "react-jss";
import {Typography} from "antd";
import {useLocation} from "react-router-dom";
import {useUnmount, useLocalStorageState} from "ahooks";
import DragResize from "../../../uikit/Components/DragResize";
import {useAppSelector, dispatch} from "../../store";
import Color from "../../assets/css/Color";
import FoldSvg from "../../assets/images/fold.svg";
import UnfoldSvg from "../../assets/images/unfold.svg";
import {SectionListType} from "../../store/common/actionTypes";
import {setCurSectionInfo} from "../../store/common/action";
import {changeLeftBoxVisible, setPageArr} from "../../store/no-persist/action";
import WbsEbsBox, {WbsEbsBoxProps} from "../WBS/WbsEbsBox";

const {Text} = Typography;

const useStyle = createUseStyles({
    box: {
        display: "flex",
        height: "100%",
    },
    leftBox: {
        borderRight: "1px solid #E1E2E5",
        height: "100%"
    },
    rightBox: {
        flexGrow: 1,
        position: "relative",
        overflowX: "scroll",
        background: "#fff",
    },
    title: {
        padding: 0
    },
    list: {
        color: Color["text-2"],
        height: 32,
        cursor: "pointer",
        lineHeight: "30px",
        width: "100%",
        paddingLeft: 10,
        "&:hover": {
            backgroundColor: Color["bg-4"],
            lineHeight: "30px",
            color: Color["text-2"],
            // fontWeight: 700,
            height: 32,
            cursor: "pointer",
        }
    },
    listSelected: {
        backgroundColor: Color["bg-4"],
        lineHeight: "32px",
        color: Color["text-1"],
        // fontWeight: 700,
        height: 32,
        cursor: "pointer",
        width: "100%",
        paddingLeft: 10,
    },
    unfoldIcon: {
        position: "absolute",
        top: "50%",
        left: 0,
        cursor: "pointer",
        zIndex: 4,
        transform: "translateY(-100px)",
    },
    foldIcon: {
        position: "absolute",
        top: "50%",
        cursor: "pointer",
        zIndex: 4,
        transform: "translateY(-100px)",
    }
});

export interface TableLayoutProps extends WbsEbsBoxProps {
    /** 是否展示标段 */
    showSection?: boolean;
    /** 是否隐藏全部节点 */
    hiddenAllNode?: boolean;
    sectionList?: SectionListType[];
    businessType?: string;
    processType?: string;
    children?: ReactNode;
    leftNode?: ReactNode;
}

/** 台账页面 */
const TableLayout = (props: TableLayoutProps) => {
    const {showSection = true, hiddenAllNode = false, children, businessType, sectionList: sectionListProps, leftNode, ...other} = props;
    const cls = useStyle();
    const {pathname} = useLocation();
    // const {orgInfo} = useAppSelector((state) => state.commonData);
    const {sectionList, curSectionInfo} = useAppSelector((state) => state.commonData);
    const {pageArr, leftBoxVisibleObj} = useAppSelector((state) => state.noRegister);
    const [leftBoxWidth, setLeftBoxWidth] = useLocalStorageState("tableLeftBoxWidth", {defaultValue: 400});
    const [sectionBoxHeight, setSectionBoxHeight] = useLocalStorageState("sectionBoxHeight", {defaultValue: 300});
    // const [showLeftBox, setShowLeftBox] = useState<boolean>(true);
    const showLeftBox = useMemo(() => leftBoxVisibleObj[pathname] ?? false, [leftBoxVisibleObj, pathname]);

    const handleSectionClick = useCallback(
        (el: SectionListType) => {
            dispatch(setCurSectionInfo(el));
        },
        []
    );

    // useEffect(() => {
    //     if (orgInfo.deptDataType === 1) {
    //         dispatch(changeLeftBoxVisible({[pathname]: orgInfo.deptDataType === 1}));
    //         // setShowLeftBox(false);
    //     }
    // }, [orgInfo.deptDataType, pathname]);

    const fillterSectionList = useMemo(() => {
        const tempSectionList = sectionListProps ?? sectionList;
        const list = tempSectionList.filter((section) => {
            // 全部节点
            if (hiddenAllNode && section.isAll !== undefined && section.isAll === true) {
                return false;
            }
            return true;
        });
        return list;
    }, [hiddenAllNode, sectionList, sectionListProps]);

    useEffect(() => {
        if (fillterSectionList.length === 0) {
            return;
        }
        if (hiddenAllNode && curSectionInfo?.isAll === true) {
            const defaultSection = fillterSectionList[0] ?? null;
            dispatch(setCurSectionInfo(defaultSection));
        }
    }, [hiddenAllNode, fillterSectionList, curSectionInfo]);

    useUnmount(() => {
        dispatch(setPageArr([]));
    });

    const handleLeftBoxStyleChange = (val: CSSProperties) => {
        setLeftBoxWidth(Number(val.width));
    };

    const handleSectionBoxStyleChange = (val: CSSProperties) => {
        setSectionBoxHeight(Number(val.height));
    };

    // const switchLeftBox = () => {
    //     // setShowLeftBox((s) => !s);
    // };

    const switchLeftBox = useCallback(
        () => {
            dispatch(changeLeftBoxVisible({[pathname]: !showLeftBox}));
        },
        [pathname, showLeftBox]
    );

    return (
        <div style={{position: "relative"}} className="box">
            {pageArr.length > 0 && (
                <div style={{position: "absolute", zIndex: 10, width: "100%", height: "100%", backgroundColor: "#fff"}}>
                    {pageArr[pageArr.length - 1]}
                </div>
            )}
            <div className={cls.box}>
                <div style={{height: "100%", width: showLeftBox && (showSection || businessType !== undefined) ? "auto" : 0, display: "inline-block"}}>
                    <DragResize
                        className={cls.leftBox}
                        style={{width: leftBoxWidth}}
                        directions={["right"]}
                        minWidth={256}
                        maxWidth={720}
                        onStyleChange={handleLeftBoxStyleChange}
                    >
                        <div style={{height: "100%", display: "flex", flexDirection: "column"}}>
                            <div className="title-14-bold" style={{padding: "20px 24px"}}>项目</div>
                            {businessType === undefined && (
                                <div style={{flexGrow: 1, height: 0, padding: "0 10px 0 14px"}}>
                                    {fillterSectionList
                                        .map((el) => (
                                            <Text
                                                ellipsis
                                                onClick={() => handleSectionClick(el)}
                                                key={`${el.nodeId}${el.nodeName}`}
                                                className={`${el.nodeId}${el.nodeName}` === `${curSectionInfo?.nodeId}${curSectionInfo?.nodeName}` ? cls.listSelected : cls.list}
                                            >
                                                {el.nodeName}
                                            </Text>
                                        ))}
                                </div>
                            )}
                            {businessType !== undefined && (
                                <>
                                    <DragResize
                                        style={{
                                            height: sectionBoxHeight,
                                            borderBottom: "1px solid rgba(225, 226, 229, 0.6)",
                                            padding: "0 10px 0 14px",
                                            marginBottom: 10
                                        }}
                                        directions={["bottom"]}
                                        minHeight={50}
                                        maxHeight={710}
                                        onStyleChange={handleSectionBoxStyleChange}
                                    >
                                        {fillterSectionList
                                            .map((el) => (
                                                <Text
                                                    ellipsis
                                                    onClick={() => handleSectionClick(el)}
                                                    key={`${el.nodeId}${el.nodeName}`}
                                                    className={`${el.nodeId}${el.nodeName}` === `${curSectionInfo?.nodeId}${curSectionInfo?.nodeName}` ? cls.listSelected : cls.list}
                                                >
                                                    {el.nodeName}
                                                </Text>
                                            ))}
                                    </DragResize>
                                    <div className="title-16-bold" style={{padding: "20px 24px", backgroundColor: "#fff"}}>WBS/EBS</div>
                                    <div style={{padding: "0 10px 0 14px", height: 0, flexGrow: 1, backgroundColor: "#fff", paddingBottom: 10}}>
                                        <WbsEbsBox
                                            businessType={businessType}
                                            {...other}
                                        />
                                    </div>
                                </>
                            )}
                        </div>
                    </DragResize>
                </div>
                {showLeftBox && <div>{leftNode}</div>}
                <div className={cls.rightBox}>
                    {
                        showLeftBox
                            ? <img className={cls.foldIcon} onClick={switchLeftBox} src={FoldSvg} />
                            : <img className={cls.unfoldIcon} onClick={switchLeftBox} src={UnfoldSvg} />
                    }
                    <div style={{width: "100%", height: "100%", overflow: "auto"}}>
                        <div style={{minWidth: 1400, overflow: "scroll", height: "100%"}}>
                            {children}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default memo(TableLayout);
