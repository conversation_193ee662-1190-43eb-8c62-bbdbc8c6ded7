import {Reducer} from "redux";
import {ConditionFlowChartData} from "../../../components/Rectification/models/flow-chart";
import {QsRecordDetailModel, Flow, RectificationActionType} from "../../../components/Rectification/models/rectification";
// eslint-disable-next-line import/no-cycle
import Actions from "./actions";

export interface RectificationDetailModel {
    loading: boolean;
    data: QsRecordDetailModel | null;
    detailVisible: boolean;
    flowChartData?: ConditionFlowChartData;
    customFieldValues: Map<string, string | string[]>;
    canDeclineToFlow: Flow[];
    rectificationDetailErrorVisible: boolean;
    // currentDetailHistory?: EditHistory[];
    printUrl: string;
    lawVisible: boolean;
    attachmentsUrl: Map<string, string>;
    visibleButtons: RectificationActionType[];
    /** 当前整改关联的检查信息 */
    relatedCheckInfo?: {
        id: string;
        /** 检查单位：0-施工自查 1-监理检查 3-业主检查 */
        buildType?: number;
        /** 检查类型: 0-日常检查 1-专项检查 2-节假日检查 3-不定期检查 4-综合性检查 5-其他 6-日检查 7-周检查 8-月检查 9-风险检查 10-安全大检查 */
        type: number;
        /** 检查分项 */
        checkSubOption?: string;
        /** 标段id */
        nodeId?: string;
        /** 节点类型 1分公司 2项目部 3标段 4单项 5单位 6工程 */
        nodeType?: number;
        /** 被检查标段id */
        beCheckNodeId?: string;
    };
}

export const initialState: RectificationDetailModel = {
    loading: true,
    data: null,
    detailVisible: false,
    customFieldValues: new Map(),
    canDeclineToFlow: [],
    rectificationDetailErrorVisible: false,
    printUrl: "",
    lawVisible: false,
    attachmentsUrl: new Map<string, string>(),
    visibleButtons: []
};

const rectificationDetailReducer: Reducer<RectificationDetailModel, Actions> = (state = initialState, action) => {
    switch (action.type) {
        case "SET_CURRENT_DETAIL_DATA":
            return {...state, data: action.payload};
        case "SET_VISIBLE_BUTTONS":
            return {...state, visibleButtons: action.payload};
        case "SET_CURRENT_CHECK_INFO":
            return {...state, relatedCheckInfo: action.payload};
        default:
            return {...state};
    }
};
export default rectificationDetailReducer;
