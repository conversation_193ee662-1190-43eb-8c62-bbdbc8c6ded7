import React, {useState, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {Space, Tree, TreeProps, Typography} from "antd";
import {useSelector} from "react-redux";
import clsx from "clsx";
import {ProjectTreeNodeData} from "./data";
import MyIcon from "../../../../../components/MyIcon";
import {RootState} from "../../../../../store/rootReducer";
import {dispatch} from "../../../../../store";
import {setCurSandTable} from "../../../../../store/status/action";
import {recordRecentOpenedSandTableProj} from "../../../../../api/recentProject/recentProjectApi";
import {projTypeIconMap, projTypeNameMap} from "../../../../../api/projListPageDataAndType";
import compStyles from "../../../../../assets/css/graghicStyles";

const {Text} = Typography;

const useStyle = createUseStyles({
    box: {},
});

interface RelatedProjectProps {
    deptTreeData: ProjectTreeNodeData[];
    /** 切换工程，是否刷新页面 */
    reloadWhenProjChange?: boolean;
}

const DeptTreeView = (props: RelatedProjectProps) => {
    const cls = useStyle();

    const {
        imgBox,
    } = compStyles();

    const {deptTreeData, reloadWhenProjChange} = props;
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const [treeData, setTreeData] = useState<ProjectTreeNodeData[]>([]);

    React.useEffect(() => {
        setTreeData(deptTreeData);
    }, [deptTreeData]);

    const renderText = (text: string) => (
        <Text
            ellipsis={{tooltip: text}}
            style={{width: "100%"}}
        >
            {text}
        </Text>
    );

    const getIconType = useCallback((deptDataType) => {
        switch (deptDataType) {
            case 0:
                return "icon-qiye";
            case 1:
                return "icon-fangjianxiangmu";
            case 2:
            default:
                return "icon-gaosuxiangmu";
        }
    }, []);

    const renderTreeNode = useCallback((node: ProjectTreeNodeData) => {
        const nodeData = node.orgInfo;
        if (nodeData.isBIM) {
            let projTypeIcon: string | undefined = projTypeIconMap.get(node.bimInfo !== null ? node.bimInfo.projType : 0);
            if (projTypeIcon === undefined) {
                projTypeIcon = "unmatch";
            }

            let projTypeName: string | undefined = projTypeNameMap.get(node.bimInfo !== null ? node.bimInfo.projType : 0);
            if (projTypeName === undefined) {
                projTypeName = "未知工程类型";
            }

            return (
                <Space>
                    <div className={clsx(imgBox)} title={projTypeName}>
                        <div className={["smallicon", projTypeIcon].join(" ")} />
                    </div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        if (nodeData.type === 0) {
            return (
                <Space>
                    <div><MyIcon type={getIconType(orgInfo.deptDataType)} fontSize={16} color="#000" style={{position: "relative", top: 2}} /></div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        if (nodeData.type === 1) {
            return (
                <Space>
                    <div style={{backgroundColor: "#2DA641", padding: "2px", color: "#fff", lineHeight: 1}}>标</div>
                    <div>{node.title}</div>
                </Space>
            );
        }
        return (
            <div>{renderText(node.title as string)}</div>
        );
    }, [getIconType, imgBox, orgInfo.deptDataType]);

    const handleDeptTreeSelect: TreeProps["onSelect"] = (_key, _node) => {
        const node = _node.node as unknown as ProjectTreeNodeData;
        if (_node.selected === true && node.orgInfo.isBIM && typeof node.bimInfo !== "undefined" && typeof node.bimInfo?.ppid !== "undefined") {
            recordRecentOpenedSandTableProj(orgInfo.orgId, node.bimInfo?.ppid).then((res) => {
                if (res.success && node.bimInfo !== null) {
                    dispatch(setCurSandTable(node.bimInfo));
                    if (reloadWhenProjChange === true) {
                        window.location.reload();
                    }
                }
            });
        }
    };

    return (
        <div className={cls.box} style={{display: "flex", flexDirection: "column", height: "100%"}}>
            <div style={{flexGrow: 1, height: 0}}>
                {
                    treeData.length > 0 && (
                        <Tree
                            defaultExpandAll
                            treeData={treeData}
                            titleRender={renderTreeNode}
                            onSelect={handleDeptTreeSelect}
                        />
                    )
                }
            </div>
        </div>
    );
};

export default DeptTreeView;
