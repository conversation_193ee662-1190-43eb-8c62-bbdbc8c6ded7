import React, {Key, useCallback, useState} from "react";
import {useSelector} from "react-redux";
import {Space, Button, Modal, message, Input, Form, Row, Col, Tree, Avatar} from "antd";
import {ExclamationCircleFilled} from "@ant-design/icons";
import {ReformDetailVo} from "../../../api/rectification/models/process";
import {ApprovalType, approvalTypeList, Flow, Person, RectificationActionType} from "../../../components/Rectification/models/rectification";
import {backReform, cancelReform, copyToReform, deleteReform, transferReform} from "../../../api/rectification/index";
import useStyle from "../style";
import FileBox from "../../../components/FileBox";
import {FileType} from "../../../api/common.type";
import {transerFileToAttachment} from "../../../components/Rectification/rectification-helpers";
import {RootState} from "../../../store/rootReducer";
import {ConditionFlowChartData} from "../../../components/Rectification/models/flow-chart";
import SelectPerson from "../../../components/SelectPerson";
import ComModal from "../../../components/ComModal";

const {TextArea} = Input;

interface RectifyButtonsProps {
    back: () => void;
    reformDetail?: ReformDetailVo;
    /* 提交的逻辑需要在父组件完成 */
    onSubmit?: () => void;
    visibleButtons: RectificationActionType[];
    flowChartData?: ConditionFlowChartData;
    // 处理成功了,不需要再留在这里的
    // onSuccessBack?: () => void;
    loading?: boolean;
    /* plan: 进度； 因为进度的撤回是删除，要做特殊处理，但是听说这个以后还是标准流程 */
    approvalType?: "plan";
}

const renderTitle = (type?: RectificationActionType) => {
    let title = "";
    switch (type) {
        case RectificationActionType.Revoke:
            title = "撤回";
            break;
        case RectificationActionType.Submit:
            title = "提交";
            break;
        case RectificationActionType.Decline:
            title = "退回";
            break;
        case RectificationActionType.CopyTo:
            title = "抄送";
            break;
        case RectificationActionType.HandOver:
            title = "转交";
            break;
        case RectificationActionType.Delete:
            title = "删除";
            break;
        default:
            title = "";
    }
    return title;
};

const hasPersonTypeList = [
    RectificationActionType.CopyTo,
    RectificationActionType.HandOver,
    RectificationActionType.Decline
];

// eslint-disable-next-line max-lines-per-function
const RectifyButtons = (props: RectifyButtonsProps) => {
    const cls = useStyle();
    const [declineForm] = Form.useForm();
    const {userInfo} = useSelector((state: RootState) => state.commonData);
    const {reformDetail, onSubmit, visibleButtons, flowChartData, loading = false, back, approvalType} = props;
    const [modalVisible, setModalVisible] = useState(false);
    const [comment, setComment] = useState<string>();
    const [curActionType, setCurActionType] = useState<RectificationActionType>();
    const [fileList, setFileList] = useState<FileType[]>([]);
    const [selectedPersons, setSelectedPersons] = useState<string[]>([]);
    const [declineFormVisible, setDeclineFormVisible] = useState(false);
    const [decFlowList, setDecFlowList] = useState<Flow[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<Key[]>([]);
    const [roles, setRoles] = useState<string[]>([]);
    const [persons, setPersons] = useState<Person[]>([]);
    const [positions, setPositions] = useState<string[]>([]);
    const [declineToType, setDeclineToType] = useState<string>("人员");
    const [declineFileList, setDeclineFileList] = useState<FileType[]>([]);

    const curTitle = renderTitle(curActionType);

    const handleModalClose = useCallback(
        () => {
            setModalVisible(false);
            setComment(undefined);
            setFileList([]);
            setSelectedPersons([]);
        },
        []
    );

    // 撤回
    const hanleRevoke = useCallback(
        () => {
            setCurActionType(RectificationActionType.Revoke);
            Modal.confirm({
                title: <div className={cls.tipTitle}>提示</div>,
                icon: <ExclamationCircleFilled className={cls.tipIcon} />,
                content: <p className={cls.tipContent}>是否确定撤回？</p>,
                onOk: async () => {
                    await cancelReform({
                        serialNum: reformDetail!.serialNum,
                        attachments: transerFileToAttachment(fileList),
                        message: comment
                    });
                    back();
                    // onSuccessBack();
                    message.success("撤回成功");
                }
            });
        },
        [cls, reformDetail, fileList, comment, back]
    );

    // 删除
    const handleDelete = useCallback(
        () => {
            setCurActionType(RectificationActionType.Delete);
            Modal.confirm({
                title: <div className={cls.tipTitle}>提示</div>,
                icon: <ExclamationCircleFilled className={cls.tipIcon} />,
                content: <p className={cls.tipContent}>是否确定删除？</p>,
                onOk: async () => {
                    await deleteReform(reformDetail!.serialNum ?? "");
                    back();
                    // onSuccessBack();
                    message.success("删除成功！");
                }
            });
        },
        [cls, reformDetail, back]
    );

    // 退回
    const handleDecline = useCallback(
        async () => {
            if (selectedKeys.length === 0) {
                message.warning("请选择退回的流程节点");
                return;
            }
            setCurActionType(RectificationActionType.Decline);
            await backReform({
                formTaskId: reformDetail!.formTaskId,
                flowNodeId: selectedKeys[0] as string,
                attachments: transerFileToAttachment(declineFileList),
                message: declineForm.getFieldValue("message")
            });
            back();
            // onSuccessBack();
            message.success("退回成功");
        },
        [declineFileList, declineForm, back, reformDetail, selectedKeys]
    );

    // 抄送
    const handleCopyTo = useCallback(
        async () => {
            setCurActionType(RectificationActionType.CopyTo);
            await copyToReform({
                copyToUsers: selectedPersons,
                formTaskId: reformDetail!.formTaskId,
                attachments: transerFileToAttachment(fileList),
                message: comment
            });
            handleModalClose();
            message.success("抄送成功");
        },
        [selectedPersons, reformDetail, fileList, comment, handleModalClose]
    );

    // 转交
    const handleHandOver = useCallback(
        async () => {
            if (selectedPersons.length === 0) {
                message.error("请选择转交人!");
                return;
            }
            if (selectedPersons.length > 1) {
                message.error("转交人只能有一个!");
                return;
            }
            setCurActionType(RectificationActionType.HandOver);
            await transferReform({
                formTaskId: reformDetail!.formTaskId,
                serialNum: reformDetail!.serialNum,
                fromUser: userInfo.username,
                toUser: selectedPersons[0],
                attachments: transerFileToAttachment(fileList),
                message: comment
            });
            handleModalClose();
            back();
            // onSuccessBack();
            message.success("操作成功");
        },
        [selectedPersons, reformDetail, userInfo.username, fileList, comment, handleModalClose, back]
    );
    // 进度审批的撤回是删除
    const handlePlanDelete = useCallback(() => {
        Modal.confirm({
            title: <div className={cls.tipTitle}>提示</div>,
            icon: <ExclamationCircleFilled className={cls.tipIcon} />,
            content: <p className={cls.tipContent}>是否确定撤回？</p>,
            onOk: async () => {
                await deleteReform(reformDetail!.serialNum ?? "");
                back();
                message.success("撤回成功！");
            }
        });
    }, [back, cls.tipContent, cls.tipIcon, cls.tipTitle, reformDetail]);

    const beforeAction = useCallback(
        (type: RectificationActionType) => {
            if (type === RectificationActionType.Revoke && approvalType === "plan") {
                // 进度审批的撤回是删除
                handlePlanDelete();
            } else {
                setModalVisible(true);
                setCurActionType(type);
            }
        },
        [approvalType, handlePlanDelete]
    );

    const beforeDecline = useCallback(
        () => {
            if (flowChartData === undefined) {
                return;
            }
            const fd = flowChartData;
            const canDeclineToFlow = (fd.historyFlowNodeIds ?? [])
                .map((id) => fd.flowNodeList.find((n) => n.flowNodeId === id))
                .map((n) => ({
                    id: n!.flowNodeId,
                    name: n!.flowNodeName,
                    defaultPersonIds: [],
                    persons: n?.approvalUsers?.map((u) => ({id: u.id, name: u.userName})) ?? [],
                    canSet: true,
                    roles: n?.approvalRoles?.map((r) => r.roleName) ?? [],
                    positions: n?.approvalPosts?.map((p) => p.postName) ?? [],
                    type: n!.type
                })) as Flow[];
            canDeclineToFlow.pop();
            setDecFlowList(canDeclineToFlow);
            setDeclineFormVisible(true);
        },
        [flowChartData]
    );

    const handleModelSure = useCallback(
        () => {
            if (curActionType === RectificationActionType.Revoke) {
                hanleRevoke();
            } else if (curActionType === RectificationActionType.CopyTo) {
                handleCopyTo();
            } else if (curActionType === RectificationActionType.HandOver) {
                handleHandOver();
            }
        },
        [curActionType, hanleRevoke, handleCopyTo, handleHandOver]
    );

    const handleSelect = useCallback(
        (keys: Key[]) => {
            if (keys.length <= 0) {
                return;
            }
            const key = keys[0];
            const flow = decFlowList.find((f) => f.id === key);
            if (flow === undefined) {
                return;
            }
            const getDeclineToType = (type: ApprovalType) => {
                if (type === ApprovalType.InitiatorSpecify) {
                    return "人员";
                }
                return approvalTypeList.find((v) => v.value === type)!.label.replace("指定", "");
            };
            setSelectedKeys(keys);
            setRoles(flow.roles);
            setPersons(flow.persons);
            setPositions(flow.positions);
            setDeclineToType(getDeclineToType(flow.type));
        },
        [decFlowList]
    );

    const handleDecCancel = useCallback(
        () => {
            setDeclineFormVisible(false);
            setSelectedKeys([]);
            setRoles([]);
            setPersons([]);
            setPositions([]);
            setDeclineToType("人员");
        },
        []
    );

    if (reformDetail === undefined) {
        return null;
    }
    return (
        <>
            <Space className={cls.footerWrapper}>
                {
                    visibleButtons.includes(RectificationActionType.Submit)
                        ? <Button onClick={onSubmit} type="primary" loading={loading}>提交</Button>
                        : null
                }
                {
                    visibleButtons.includes(RectificationActionType.Revoke)
                        ? <Button onClick={() => beforeAction(RectificationActionType.Revoke)}>撤回</Button>
                        : null
                }
                {
                    visibleButtons.includes(RectificationActionType.Decline)
                        ? <Button onClick={() => beforeDecline()}>退回</Button>
                        : null
                }
                {
                    visibleButtons.includes(RectificationActionType.CopyTo)
                        ? <Button onClick={() => beforeAction(RectificationActionType.CopyTo)}>抄送</Button>
                        : null
                }
                {
                    visibleButtons.includes(RectificationActionType.HandOver)
                        ? <Button onClick={() => beforeAction(RectificationActionType.HandOver)}>转交</Button>
                        : null
                }
                {
                    visibleButtons.includes(RectificationActionType.Delete)
                        ? <Button onClick={handleDelete}>删除</Button>
                        : null
                }
            </Space>
            <ComModal
                closable={false}
                title={curTitle}
                visible={modalVisible}
                maskClosable={false}
                width={720}
                footer={false}
                onCancel={handleModalClose}
                onOk={handleModelSure}
            >
                <div style={{padding: 16}}>
                    <Form layout="vertical">
                        <Form.Item label="意见：" rules={[{max: 1000}]}>
                            <TextArea
                                value={comment}
                                onChange={(e) => setComment(e.target.value)}
                                maxLength={1000}
                                showCount
                            />
                        </Form.Item>
                        <Form.Item label="附件">
                            <FileBox
                                value={fileList}
                                onChange={setFileList}
                            />
                        </Form.Item>
                        {curActionType !== undefined && hasPersonTypeList.includes(curActionType)
                            && (
                                <Form.Item label={`选择${curTitle}人`}>
                                    <SelectPerson
                                        value={selectedPersons.map((item) => ({userName: item}))}
                                        onChange={(value) => setSelectedPersons(value.map((item) => item.userName))}
                                        nodeId={reformDetail.nodeId ?? ""}
                                        nodeType={reformDetail.nodeType}
                                    />
                                </Form.Item>
                            )}
                    </Form>
                </div>
            </ComModal>
            <ComModal
                title="请选择要退回的节点"
                closable
                visible={declineFormVisible}
                maskClosable={false}
                width={720}
                footer={false}
                onCancel={handleDecCancel}
                onOk={handleDecline}
            >
                <div style={{padding: 16}}>
                    <Row className={cls.decLineBox}>
                        <Col span={12}>
                            <div style={{marginBottom: 10}}>退回节点：</div>
                            <Tree
                                selectedKeys={selectedKeys}
                                blockNode
                                onSelect={handleSelect}
                            >
                                {decFlowList.map((f) => <Tree.TreeNode key={f.id} title={<span>{f.name}</span>} />)}
                            </Tree>
                        </Col>
                        <Col span={12}>
                            <div style={{marginBottom: 10}}>{`相关${declineToType}`}</div>
                            <Tree>
                                {
                                    persons.map((v, i) => (
                                        <Tree.TreeNode
                                            key={i.toString()}
                                            title={(
                                                <Space>
                                                    <Avatar src={v.avatar}>{v.name}</Avatar>
                                                    {v.name}
                                                </Space>
                                            )}
                                        />
                                    ))
                                }
                                {roles.map((v, i) => <Tree.TreeNode key={i.toString()} title={v} />)}
                                {positions.map((v, i) => <Tree.TreeNode key={i.toString()} title={v} />)}
                            </Tree>
                        </Col>
                    </Row>
                    <div>
                        <Form
                            form={declineForm}
                            style={{width: "100%"}}
                        >
                            <Form.Item
                                label="意见"
                                name="message"
                            >
                                <TextArea maxLength={1000} showCount rows={3} />
                            </Form.Item>
                            <Form.Item label="附件">
                                <FileBox value={declineFileList} onChange={setDeclineFileList} />
                            </Form.Item>
                        </Form>
                    </div>
                </div>
            </ComModal>
        </>
    );
};

export default RectifyButtons;
