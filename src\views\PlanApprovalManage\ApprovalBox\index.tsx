import {<PERSON><PERSON>, <PERSON>, Pa<PERSON><PERSON>, Row} from "antd";
import React, {memo, useCallback, useEffect, useMemo, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {getPlanApprovalPage, planApprovalExport} from "../../../api/planApproval";
import {PlanApprovalPageList} from "../../../api/planApproval/type";
import useComState from "../../../assets/hooks/useComState";
import {BackType} from "../../../assets/ts/globalType";
import {isHasSectionInfo} from "../../../assets/ts/globalUtils";
import ComModalExport from "../../../components/ComModal/ComModalExport";
import ComTable from "../../../components/ComTable";
import QueryFormSingle from "../../../components/QueryFormSingle";
import TableColumnsControl, {ComColumnsProps} from "../../../components/TableColumnsControl";
import {addPage, popPage} from "../../../store/no-persist/action";
import {RootState} from "../../../store/rootReducer";
import {planTabList} from "../dataAndType";
import {columnsInit, QueryFormType, queryItemList} from "./dataAndType";
import OperationApproval from "./OperationApproval";

interface Props {
    tabsType: string; // 审批tabs当前菜单type
}
const ApprovalBox = (props: Props) => {
    const {tabsType} = props;
    const dispatch = useDispatch();
    const {curSectionInfo, orgInfo} = useSelector((state: RootState) => state).commonData;
    const [state, setState] = useComState({queryFormInit: queryItemList});
    const [queryFormData, setQueryFormData] = useState<QueryFormType | undefined>(); // 搜索条件
    const [columns, setColumns] = useState<ComColumnsProps<PlanApprovalPageList>[]>(columnsInit);
    const [tableData, setTableData] = useState<PlanApprovalPageList[]>([]);
    // const [curListItem, setCurListItem] = useState<PlanApprovalPageList | null>(null);
    const [tableLodding, setTableLodding] = useState(false);

    const getTableListData = useCallback((params = {}) => {
        if (curSectionInfo === undefined || curSectionInfo === null || curSectionInfo.id.length === 0) {
            return;
        }
        setTableLodding(true);
        const planTabListItem = planTabList.find((item) => item.key === tabsType);
        const query = {
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
            pageNum: state.curPage,
            pageSize: state.pageSize,
            // changeStatus: routerType,
            nameKey: queryFormData?.nameKey,
            type: queryFormData?.type === null ? undefined : queryFormData?.type,
            status: queryFormData?.status,
            processType: planTabListItem?.processType ?? 0,
            ...params,
        };
        getPlanApprovalPage(query)
            .then(
                (res) => {
                    if (res.success && res.data !== null && Array.isArray(res.data.items)) {
                        setTableData(res.data.items ?? []);
                        setState.setTotal(res.data.total);
                    }
                }
            )
            .finally(() => {
                setTableLodding(false);
            });
    }, [curSectionInfo, orgInfo.orgId, queryFormData, setState, state.curPage, state.pageSize, tabsType]);

    useEffect(() => {
        setState.setCurPage(1);
        getTableListData({pageNum: 1});
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orgInfo.orgId, curSectionInfo, tabsType]);

    useEffect(() => {
        state.queryForm.setFieldsValue({type: null});
    }, [state.queryForm]);

    useEffect(() => {
        // 切换项目部，回到列表页
        dispatch(popPage());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [orgInfo.orgId]);

    const handleBackPlan = useCallback((type: BackType = "cancel") => {
        dispatch(popPage());
        if (type === "ok") {
            // 确认 返回
            getTableListData();
        } else if (type === "cancel") {
            // 取消 返回
        }
    }, [dispatch, getTableListData]);

    const handleView = useCallback((item: PlanApprovalPageList) => {
        dispatch(addPage(
            <OperationApproval detailId={item?.id ?? ""} back={handleBackPlan} />
        ));
    }, [dispatch, handleBackPlan]);

    useEffect(() => {
        setColumns((s) => s.map((item) => {
            if (item.title === "操作") {
                return {
                    ...item,
                    render(_text: string, _record: PlanApprovalPageList, _index: number) {
                        return (
                            <Button type="link" onClick={() => handleView(_record)}>详情</Button>
                        );
                    }
                };
            }
            return item;
        }));
    }, [handleView]);

    const handleFormFinished = useCallback((values: QueryFormType) => {
        setState.setCurPage(1);
        setQueryFormData(values);
        getTableListData({pageNum: 1, ...values, type: values.type === null ? undefined : values.type});
    }, [getTableListData, setState]);

    const handleFormClear = useCallback(() => {
        state.queryForm.setFieldsValue({type: null});
        setState.setCurPage(1);
        setQueryFormData(undefined);
        getTableListData({pageNum: 1, nameKey: undefined, status: undefined, type: undefined});
    }, [getTableListData, setState, state.queryForm]);

    const handleFormValuesChange = useCallback((values) => {
        if (values.type !== undefined) {
            setState.setCurPage(1);
            setQueryFormData(values);
            getTableListData({pageNum: 1, ...values, type: values.type === null ? undefined : values.type});
        }
    }, [getTableListData, setState]);

    const rowSelection = useMemo(() => (
        {
            selectedRowKeys: state.selectIds,
            onChange: setState.setSelectIds,
            columnWidth: 60
        }
    ), [setState.setSelectIds, state.selectIds]);

    const renderFilter = useCallback(() => (
        <div>
            <QueryFormSingle<QueryFormType>
                form={state.queryForm}
                queryItemList={state.queryFormList}
                onFormFinish={handleFormFinished}
                onFormClear={handleFormClear}
                onFormValuesChange={handleFormValuesChange}
                formRow={{justify: "end"}}
            />
        </div>
    ), [handleFormClear, handleFormFinished, handleFormValuesChange, state.queryForm, state.queryFormList]);

    const paginationChange = useCallback((curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(Number(pageSizeVal));
        getTableListData({pageNum: curPageVal, pageSize: pageSizeVal});
    }, [getTableListData, setState]);

    const renderTableHeader = useCallback(() => (
        <Row justify="center">
            <Col>
                <TableColumnsControl
                    tableKey={tabsType}
                    setColumnsList={setColumns}
                    columnsList={columns}
                />
            </Col>
        </Row>
    ), [columns, tabsType]);

    const renderTable = useCallback(() => (
        <ComTable
            columns={columns.filter((el: ComColumnsProps<PlanApprovalPageList>) => el.show || el.mustShow)}
            dataSource={tableData}
            pagination={false}
            rowKey="id"
            rowSelection={rowSelection}
            loading={tableLodding}
        />
    ), [columns, rowSelection, tableData, tableLodding]);

    const renderPagination = useCallback(() => (
        <Row justify="space-between" style={{marginTop: 16}}>
            <Col>{`已选 ${state.selectIds.length} 项`}</Col>
            <Col>
                <Pagination
                    total={state.total}
                    showSizeChanger
                    showQuickJumper
                    current={state.curPage}
                    pageSize={state.pageSize}
                    onChange={paginationChange}
                    showTotal={(totalCount: number) => `共 ${totalCount} 条`}
                />
            </Col>
        </Row>
    ), [paginationChange, state.curPage, state.pageSize, state.selectIds.length, state.total]);

    const handleExport = useCallback(() => {
        if (!isHasSectionInfo()) {
            return;
        }
        if (curSectionInfo === null) {
            return;
        }
        const planTabListItem = planTabList.find((item) => item.key === tabsType);
        let params: {} = {
            deptId: orgInfo.orgId,
            processType: planTabListItem?.processType ?? 0,
            nodeId: curSectionInfo?.isAll !== true ? curSectionInfo?.id : undefined,
            nodeType: curSectionInfo?.isAll !== true ? curSectionInfo?.nodeType : undefined,
            // pageNum: state.curPage,
            // pageSize: state.pageSize,
        };
        if (state.selectIds.length > 0) {
            params = {
                ...params,
                ids: state.selectIds
            };
        } else {
            params = {
                ...params,
                ...queryFormData,
            };
        }
        planApprovalExport(params);
    }, [curSectionInfo, orgInfo.orgId, queryFormData, state.selectIds, tabsType]);

    const renderHeader = useCallback(() => (
        <Row align="middle" justify="space-between">
            <Col span={3}>
                <ComModalExport
                    totalNum={state.total}
                    selectedNum={state.selectIds.length}
                    onOk={handleExport}
                />
            </Col>
            <Col span={20}>
                {renderFilter()}
            </Col>
            <Col span={1}>
                {renderTableHeader()}
            </Col>
        </Row>
    ), [handleExport, renderFilter, renderTableHeader, state.selectIds.length, state.total]);
    return (
        <div>
            {renderHeader()}
            {renderTable()}
            {renderPagination()}
        </div>
    );
};

export default memo(ApprovalBox);
