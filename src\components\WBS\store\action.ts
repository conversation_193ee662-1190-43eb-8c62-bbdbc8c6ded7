import {createSFAPayloadAction} from "../../../store/utils";
import ActionTypes from "./actionTypes";
import {StateType} from "./reducer";

export const setCheckedKeys = createSFAPayloadAction(ActionTypes.CHECKED_KEYS, (payload: StateType["checkedKeys"]) => payload);
export const setSelectedKey = createSFAPayloadAction(ActionTypes.SELECTEDKEY, (payload: StateType["selectedKey"]) => payload);
export const setTreeData = createSFAPayloadAction(ActionTypes.TREEDATA, (payload: StateType["treeData"]) => payload);
export const setTreeDataObj = createSFAPayloadAction(ActionTypes.TREEDATAOBJ, (payload: StateType["treeDataObj"]) => payload);
export const setIsShow = createSFAPayloadAction(ActionTypes.ISSHOW, (payload: StateType["isShow"]) => payload);
export const setIsShowDrawer = createSFAPayloadAction(ActionTypes.ISSHOWDRAWER, (payload: StateType["isShowDrawer"]) => payload);
export const setDrawerCheckedNodes = createSFAPayloadAction(ActionTypes.DRAWERCHECKEDNODES, (payload: StateType["drawerCheckedNodes"]) => payload);
export const setOrginData = createSFAPayloadAction(ActionTypes.ORIGIN_DATA, (payload: StateType["originData"]) => payload);
export const setLeafIds = createSFAPayloadAction(ActionTypes.LEAF_IDS, (payload: StateType["leafIds"]) => payload);
export const setEbsTreeData = createSFAPayloadAction(ActionTypes.SET_EBS_TREE_DATA, (payload: StateType["ebsTreeData"]) => payload);
export const setQueryCheckedNode = createSFAPayloadAction(ActionTypes.SET_QUERY_CHECKED_NODE, (payload: StateType["queryCheckedNodes"]) => payload);
export const setWbsBindEbs = createSFAPayloadAction(ActionTypes.SET_WBS_BIND_EBS, (payload: StateType["wbsBindEbs"]) => payload);
export const setEbsBindWbs = createSFAPayloadAction(ActionTypes.SET_EBS_BIND_WBS, (payload: StateType["ebsBindWbs"]) => payload);
