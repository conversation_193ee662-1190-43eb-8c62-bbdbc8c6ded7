import React, {memo, useCallback, FC, ReactNode} from "react";
import {createUseStyles} from "react-jss";
import MyIcon from "../../../../components/MyIcon";

interface Props {
    title?: string;
    onBack?: () => void;
    titleContent?: ReactNode;
}
const useStyle = createUseStyles({
    header: {
        padding: "16px 24px 0"
    },
    title: {
        lineHeight: "28px",
        fontSize: 20,
        fontWeight: "bold",
        color: "#061127",
        marginTop: 6,
        "& .anticon": {
            marginRight: 10,
        },
    },

});
const PageTitle: FC<Props> = (props) => {
    const {title, onBack, titleContent} = props;
    const cls = useStyle();

    const handleBack = useCallback(
        () => {
            if (onBack !== undefined) {
                onBack();
            }
        },
        [onBack]
    );

    return (
        <div className={cls.header}>
            <div className={cls.title}>
                {onBack !== undefined ? <MyIcon type="icon-fanhui" onClick={handleBack} /> : null}
                {title !== undefined ? title : null}
                {titleContent !== undefined && titleContent}
            </div>
        </div>
    );
};

export default memo(PageTitle);
