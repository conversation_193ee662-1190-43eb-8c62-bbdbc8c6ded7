/* eslint-disable max-lines-per-function */
/* eslint-disable max-len */
import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import moment from "moment";
import {Collapse, Button, Row, Space, Form, Input, DatePicker, message, Col, Spin} from "antd";
import ComCollapsePanel from "../../../components/ComCollapsePanel";
import {RootState} from "../../../store/rootReducer";
import {createReform, getApprovalFormTemplateDetail, getProcessFlowChart, loadDefaultUserName, saveDefaultUserName, saveSignByNode} from "../../../api/rectification/index";
import {CustomFieldData, CustomFieldType} from "../../../components/Rectification/models/custom-field";
import {loadCurrentFlowAsync, transformFormDataToJsonValues, transformServerDataToCustomFieldData} from "../../../components/Rectification/rectification-helpers";
import CustomField from "../../../components/Rectification/CustomField";
import {ApprovalType, Flow} from "../../../components/Rectification/models/rectification";
import {FormTemplateVo} from "../../../api/rectification/models/process";
import TextEllipsis from "../../../components/TextEllipsis";
import {GetReportFormTemplateType, ReformInstCreateParam} from "../../../api/rectification/models/flowLine";
import {RectifyAddFormModel, LaunchReformParams, SignSuccessInfoType} from "../interface";
import {chunkArray} from "../../../assets/ts/utils";
import CombinCheckDetail from "../components/CombinCheckDetail";
import FlowChart from "../../../components/Rectification/FlowChart";
import {ConditionFlowChartData} from "../../../components/Rectification/models/flow-chart";
import BackIcon from "../../../components/MyIcon/BackIcon";
// import useIframeEmit from "../../../assets/hooks/useIframeEmit";
import {ColConfig, formColStyle} from "../../../assets/ts/form";
import {setIsShow} from "../../../components/WBS/store/action";
import RectificationSheetSign from "../components/RectificationSheetSign";
import useStyle from "../style";
// import ConnectedPersonSelector from "../../../components/PersonSelector";
import SelectPerson from "../../../components/SelectPerson";
import {ValueType} from "../../../components/SelectPerson/data";

const {TextArea} = Input;
const requiredFieldType = [
    CustomFieldType.MultiSelect,
    CustomFieldType.Select,
    CustomFieldType.NumberInput
];

interface RectifyAddProps {
    back: () => void;
    onSuccess?: () => void;
    moduleType: string;
}

// const renderPerSecId = (secId: string, orgId?: string) => {
//     if (secId === "") {
//         return undefined;
//     }
//     if (secId === orgId) {
//         return undefined;
//     }
//     return [secId];
// };

const RectifyAdd: React.FC<RectifyAddProps> = (props) => {
    const {back, onSuccess, moduleType} = props;
    const cls = useStyle();
    const dispatch = useDispatch();
    const {currentTempId} = useSelector((state: RootState) => state.rectificationTemp);

    const {orgInfo, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const {relatedCheckInfo} = useSelector((state: RootState) => state.rectificationDetail);
    const [formInstance] = Form.useForm<RectifyAddFormModel>();
    const [formPerson] = Form.useForm<{[key: string]: ValueType[]}>();
    const [customFieldValues, setCustomFieldValues] = useState<Map<string, string | string[]>>(new Map());
    const [flowList, setFlowList] = useState<Flow[]>([]);
    const templateInfo = useRef<FormTemplateVo>();
    // const [curFlowNodeId, setCurFlowNodeId] = useState<string>();
    // const [personDrawerVisible, setPersonDrawerVisible] = useState(false);
    // const [checkedPersonMap, setCheckedPersonMap] = useState<Map<string, TreeType[]>>(new Map());
    const [flowChartVisible, setFlowChartVisible] = useState(false);
    const [flowChartData, setFlowChartData] = useState<ConditionFlowChartData>();
    const customFieldList = useRef<CustomFieldData[]>([]);
    const [loading, setLoading] = useState(false);
    const [addLoading, setAddLoading] = useState(false);
    // const [personIndex, setPersonIndex] = useState(0);
    // const [flowAdditionalPerson, setFlowAdditionalPerson] = useState<Map<string, string[]>>(new Map());
    // useIframeEmit();

    const [noticeTemplateResult] = useState<GetReportFormTemplateType>();
    const [previewVisible, setPreviewVisible] = useState(false);
    const [launchReform] = useState<LaunchReformParams>();

    useEffect(() => () => {
        dispatch(setIsShow(false));
    }, [dispatch]);

    const updateFlowList = useCallback((map?: Map<string, string | string[]>) => {
        const innerList = customFieldList.current;
        const innerFields = map ?? new Map();
        const filedsContainsValue = Array.from(innerFields.entries()).filter((kv) => kv[1].length !== 0).map((kv) => kv[0]);
        const requiredFields = innerList.filter((c) => c.required && requiredFieldType.includes(c.type)).map((c) => c.id);
        const intersection = requiredFields.filter((n) => !filedsContainsValue.includes(n));
        if (intersection.length === 0) {
            const {formTmplId, procTmplId} = templateInfo.current!;
            loadCurrentFlowAsync(innerFields, innerList, formTmplId ?? "", procTmplId ?? "").then((res) => {
                loadDefaultUserName({deptId: orgInfo.orgId, nodeId: relatedCheckInfo?.nodeId ?? curSectionInfo?.nodeId ?? undefined}).then((userNameRes) => {
                    // TODO userNameRes.result.approvalUsers类型待修改
                    const list: ValueType[] = JSON.parse(userNameRes.result.approvalUsers) ?? [];
                    if (res.length > 1) {
                        // res[1] = {...res[1], defaultPersonIds: list.map((i) => i.centerUserName ?? "")};
                        // res[1] = {...res[1], defaultPersonIds: list};
                        // setCurFlowNodeId(res[1].id);
                        // setCheckedPersonMap(new Map(checkedPersonMap).set(res[1].id, list));
                        // setFlowAdditionalPerson((prev) => {
                        //     // const persons = list.map((v) => v.centerUserName ?? "");
                        //     return new Map(prev).set(res[1].id, list);
                        // });
                        // formPerson.setFieldsValue({[res[1].id]: list.map((v) => v.title).join(", ")});
                        formPerson.setFieldsValue({[res[1].id]: list});
                    }
                    setFlowList(res);
                });
            });
        } else {
            setFlowList([]);
        }
    }, [curSectionInfo, relatedCheckInfo, formPerson, orgInfo.orgId]);

    /**
     * 获取审批模板详情，获取审批模板流程图详情
     */
    const init = useCallback(
        async () => {
            setLoading(true);
            try {
                const detailRes = await getApprovalFormTemplateDetail(currentTempId);
                const chartRes = await getProcessFlowChart(currentTempId);
                const customFields = (detailRes.result.components ?? []).flat().map(transformServerDataToCustomFieldData);
                templateInfo.current = detailRes.result;
                customFieldList.current = customFields;
                updateFlowList();
                setFlowChartData(chartRes.result);
                setCustomFieldValues(new Map());
                /* const {result: noticeTemplateRes} = await getReportFormTemplate({
                    buildType: relatedCheckInfo?.buildType ?? 0,
                    type: "Rectification-Notice",
                    moduleType,
                    nodeId: orgInfo.orgId
                });
                setNoticeTemplateResult(noticeTemplateRes); */
            } finally {
                setLoading(false);
            }
        },
        [currentTempId, updateFlowList]
    );

    useEffect(() => {
        init();
    }, [init]);

    const handleCustomFieldChanged = useCallback((data: CustomFieldData, payload: string | string[]) => {
        const newCustomFieldValues = new Map(customFieldValues).set(data.id, payload);
        setCustomFieldValues(newCustomFieldValues);
        formInstance.setFields([{name: data.id, value: payload}]);
        if (requiredFieldType.includes(data.type)) {
            updateFlowList(newCustomFieldValues);
        }
    }, [customFieldValues, formInstance, updateFlowList]);

    const handleCustomFieldBlur = useCallback((data: CustomFieldData, payload?: string) => {
        if (payload === undefined) {
            return;
        }
        const newCustomFieldValues = new Map(customFieldValues).set(data.id, payload);
        setCustomFieldValues(newCustomFieldValues);
        formInstance.setFields([{name: data.id, value: payload}]);
        if (requiredFieldType.includes(data.type)) {
            updateFlowList(newCustomFieldValues);
        }
    }, [updateFlowList, customFieldValues, formInstance]);

    const handleSubmit = useCallback(
        async (fileInfo?: SignSuccessInfoType) => {
            const values = await formInstance.validateFields();
            const personObj = await formPerson.validateFields();

            setAddLoading(true);
            try {
                const jsonValues = transformFormDataToJsonValues(Object.fromEntries(customFieldValues.entries()), customFieldList.current);
                let nodeId = relatedCheckInfo?.nodeId;
                let nodeType = relatedCheckInfo?.nodeType;
                if (Boolean(nodeId) === false) {
                    nodeId = curSectionInfo?.nodeId === "" ? orgInfo.orgId : curSectionInfo?.nodeId;
                    nodeType = curSectionInfo?.nodeType;
                }
                const params: ReformInstCreateParam = {
                    comment: values.comment,
                    name: values.name,
                    jsonValues,
                    deadline: values.deadline.endOf("day").format("x"),
                    authEntityId: templateInfo.current!.authEntityId,
                    formTmplId: templateInfo.current!.formTmplId,
                    procTmplId: templateInfo.current!.procTmplId,
                    nodeId,
                    nodeType,
                    deptId: orgInfo.orgId,
                    buildType: relatedCheckInfo?.buildType,
                    checkFormId: relatedCheckInfo?.id ?? "",
                    checkType: relatedCheckInfo?.type ?? 0,
                    // 这个字段去掉了,后端会自己查寻的
                    // checkSubOption: relatedCheckInfo?.checkSubOption,
                    nodeUsers: flowList.filter((f) => f.type === ApprovalType.InitiatorSpecify).map((f) => ({
                        approvalNodeId: f.id,
                        approvalUsers: (personObj[f.id] ?? []).map((v) => v.userName)
                    })),
                    reformItem: 0
                };
                const {result} = await createReform(params, moduleType);

                // TODO userInfo类型待修改
                await saveDefaultUserName({
                    deptId: orgInfo.orgId,
                    nodeId: relatedCheckInfo?.nodeId ?? curSectionInfo?.nodeId ?? undefined,
                    userInfo: JSON.stringify(personObj[flowList[1].id] ?? [])
                });
                if (fileInfo !== undefined) {
                    await saveSignByNode({
                        noticefileId: fileInfo.fileId,
                        noticefileName: fileInfo.name,
                        flowNodeId: flowList[0].id,
                        serialNum: Number(result)
                    });
                }
                message.success("发起成功");
                localStorage.removeItem("signSuccessInfo");
                back();
                if (onSuccess !== undefined) {
                    onSuccess();
                }
                setAddLoading(false);
            } catch {
                setAddLoading(false);
            }
        },
        [formInstance, formPerson, customFieldValues, relatedCheckInfo, orgInfo.orgId, flowList, moduleType, curSectionInfo, back, onSuccess]
    );

    /* const handleToSign = useCallback(async () => {
        try {
            const values = await formInstance.validateFields();

            setLaunchReform({
                currentFlowNodeId: flowList[0].id,
                reformComment: values.comment,
                businessBindAdapterBeanName: moduleType === "SECURITY" ? "securityAndQualityV2BusinessBindAdapter" : "securityAndQualityV2BusinessBindAdapter",
                reformDeadline: Number(values.deadline.endOf("day").format("x")),
                checkId: relatedCheckInfo!.id,
                checkType: relatedCheckInfo!.type,
                processInstanceName: undefined,
                startTime: moment().valueOf(),
                startUser: userInfo.username
            });
            localStorage.removeItem("signSuccessInfo");
            setPreviewVisible(true);
        } catch (err) {
            console.log(err);
        }
    }, [relatedCheckInfo, flowList, formInstance, moduleType, userInfo.username]); */
    // const handlePersonSelectBoxChange = useCallback(
    //     (val: TreeType[]) => {
    //         if (curFlowNodeId !== undefined) {
    //             const newCheckedPersonMap = new Map(checkedPersonMap).set(curFlowNodeId, val);
    //             setCheckedPersonMap(newCheckedPersonMap);
    //             formPerson.setFieldsValue({[curFlowNodeId]: val.map((v) => v.title).join(", ")});
    //         }
    //     },
    //     [formPerson, curFlowNodeId, checkedPersonMap]
    // );

    // const handlePersonInputClick = useCallback(
    //     (id: string, idx: number) => {
    //         setCurFlowNodeId(id);
    //         setPersonDrawerVisible(true);
    //         setPersonIndex(idx);
    //     },
    //     []
    // );

    /* const handleFlowAddPerson = (id: string, checkedKeys: string[]) => {
        const newFlowAdditionalPerson = new Map<string, string[]>();
        flowAdditionalPerson.set(id, checkedKeys);
        for (const [k, v] of flowAdditionalPerson) {
            newFlowAdditionalPerson.set(k, v);
        }
        setFlowAdditionalPerson(newFlowAdditionalPerson);
    }; */

    const handleBack = useCallback(
        () => {
            back();
        },
        [back]
    );

    const custFieldDom = useMemo(() => chunkArray(customFieldList.current).map((cList, cvx) => (
        <Row key={cvx.toString()}>
            {
                cList.map((c) => (
                    <Col span={12} key={c.id}>
                        <Form.Item
                            label={c.name}
                            name={c.id}
                            rules={[{required: c.required, message: "必填项"}]}
                        >
                            <CustomField
                                maxLength={c.maxLength}
                                hint={c.hint}
                                style={{width: "100%"}}
                                {...(customFieldValues.has(c.id)
                                    ? {currentValue: customFieldValues.get(c.id)}
                                    : {})}
                                onChange={(d) => handleCustomFieldChanged(c, d)}
                                onBlur={(val) => handleCustomFieldBlur(c, val)}
                                data={c}
                                nodeId={relatedCheckInfo?.nodeId}
                                nodeType={relatedCheckInfo?.nodeType}
                            />
                        </Form.Item>
                    </Col>
                ))
            }
        </Row>
    )), [customFieldValues, relatedCheckInfo, handleCustomFieldChanged, handleCustomFieldBlur]);

    if (loading || relatedCheckInfo === undefined) {
        return (
            <div className={cls.loadingBox}>
                <Spin spinning={loading} delay={300} tip="加载中" />
            </div>
        );
    }

    return (
        <div className={cls.mainWrapper}>
            <Row justify="space-between" className="head" style={{padding: "0 24px"}}>
                <Space>
                    <BackIcon onClick={handleBack} />
                    <div className="title">添加整改审批</div>
                </Space>
                <Space>
                    <Button type="primary" onClick={() => setFlowChartVisible(true)}>查看流程图</Button>
                </Space>
            </Row>
            <div className={cls.contentWrapper}>
                <Collapse ghost expandIconPosition="right" defaultActiveKey={["2", "3"]}>
                    <ComCollapsePanel key="1" header="检查详情">
                        <CombinCheckDetail isInRectifyDetail />
                    </ComCollapsePanel>
                    <ComCollapsePanel key="2" header="整改详情">
                        <Form
                            labelCol={{span: 4}}
                            wrapperCol={{span: 18}}
                            labelAlign="right"
                            form={formInstance}
                        >
                            <Row>
                                <Col {...ColConfig}>
                                    <Form.Item {...formColStyle()} label="整改编号" name="name">
                                        <Input placeholder="请输入整改编号" />
                                    </Form.Item>
                                </Col>
                                <Col {...ColConfig}>
                                    <Form.Item {...formColStyle()} label="整改期限" name="deadline" rules={[{required: true, message: "请选择整改期限"}]}>
                                        <DatePicker
                                            style={{width: "100%"}}
                                            format="YYYY.MM.DD"
                                            disabledDate={(current) => {
                                                if (current !== null && current !== undefined) {
                                                    return current < moment().subtract(1, "days");
                                                }
                                                return false;
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span={24}>
                                    <Form.Item
                                        {...formColStyle()}
                                        label="整改要求"
                                        name="comment"
                                        rules={[{required: true, message: "请输入整改要求"}]}
                                    >
                                        <TextArea showCount maxLength={300} placeholder="请输入整改要求" />
                                    </Form.Item>
                                </Col>
                            </Row>
                            {custFieldDom}
                        </Form>
                    </ComCollapsePanel>
                    <ComCollapsePanel key="3" header="整改流程">
                        <Form
                            labelCol={{span: 4}}
                            labelAlign="right"
                            form={formPerson}
                        >
                            <Row>
                                {
                                    flowList.map((f) => (
                                        <Col span={12} key={f.id}>
                                            <Form.Item
                                                label={<TextEllipsis text={f.name} />}
                                                rules={[{required: f.canSet, message: "必填项"}]}
                                                name={f.id}
                                            >
                                                {/* <ConnectedPersonSelector
                                                    onPopup={() => null}
                                                    disabled={!f.canSet}
                                                    disableReason={f.disableReason}
                                                    mustCheckedPersonIds={f.defaultPersonIds}
                                                    selectedKeys={flowAdditionalPerson.get(f.id)}
                                                    onChange={(c) => handleFlowAddPerson(f.id, c)}
                                                /> */}
                                                <SelectPerson
                                                    inputDisabled={!f.canSet}
                                                    disableReason={f.disableReason}
                                                    nodeId={relatedCheckInfo.nodeId}
                                                    nodeType={relatedCheckInfo.nodeType}
                                                />
                                            </Form.Item>
                                        </Col>
                                    ))
                                }
                            </Row>
                        </Form>
                    </ComCollapsePanel>
                </Collapse>
            </div>
            <Space className={cls.footerWrapper}>
                <Button onClick={handleBack}>取消</Button>
                <Button
                    onClick={async () => handleSubmit()}
                    type="primary"
                    loading={addLoading}
                >
                    发起
                </Button>
            </Space>
            {/* <DrawerPersonSelectBox
                checkedPersonArr={checkedPersonMap.get(curFlowNodeId ?? "") ?? []}
                setCheckedPersonArr={handlePersonSelectBoxChange}
                key={curFlowNodeId}
                lubanFlag={1}
                onlyShowLubanFlag
                sectionIds={personIndex !== 1 ? undefined : renderPerSecId(relatedCheckInfo.beCheckNodeId ?? "", orgInfo.orgId)}
                buildTypeValue={personIndex !== 1 ? undefined : ["0", "1"]}
                visible={personDrawerVisible}
                onClose={() => setPersonDrawerVisible(false)}
            /> */}
            <FlowChart
                visible={flowChartVisible}
                onCancel={() => setFlowChartVisible(false)}
                data={flowChartData}
            />
            {(noticeTemplateResult?.sign === 1) && previewVisible && (
                <RectificationSheetSign
                    visible={previewVisible}
                    handleSubmit={handleSubmit}
                    noticeReportFormTemplate={noticeTemplateResult}
                    setVisible={setPreviewVisible}
                    launchReform={launchReform}
                />
            )}
        </div>
    );
};

export default RectifyAdd;
