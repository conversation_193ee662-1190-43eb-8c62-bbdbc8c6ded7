// 工程列表分页查询参数
export interface ProjListParam {
    filterNodeIds?: string[];
    extractStatus?: number; // 轻量化抽取状态【-2:未处理， -1: 待处理， 0：处理失败， 1：处理成功 2：处理中】，不传不过滤
    updateDateStart?: number; // 更新时间-起始，不传不过滤
    updateDateEnd?: number; // 更新时间-截止，不传不过滤
    pageNum: number; // 页码，默认1
    pageSize: number; // 页大小，默认15
    nodeId?: string; // 组织节点id，不传不过滤
    nodeType?: number; // 组织节点类型:分公司0、项目部 1，不传不过滤
    searchKey?: string; // 搜索关键字（工程名称/更新人），不传不过滤
}

export interface PageProjInfoResult {
    total: number; // 总数
    items: ProjInfo[]; // 条目列表
}

// 工程信息
export interface ProjInfo {
    fileuuid: string;
    serialNum: string;
    projId: number; // 工程id
    ppid: number; // 代理工程id
    projName: string; // 工程名称
    /*
     * 专业（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Remiz(班筑客户端上传)/8:班筑家装（PDS客户端）
     * 9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:ifc/14:CATIA/15:PKPM）
    */
    projType: number;
    updateUserName: string; // 更新人用户名
    updateRealName: string; // 更新人真实姓名
    updateDate: number; // 更新时间
    projSize: number; // 工程文件大小
    extractStatus: number; // 轻量化抽取状态【-2:未处理， -1: 待处理， 0：处理失败， 1：处理成功 2：处理中】
    priorityOption: number; // 转换操作 1：效果优先，3：性能优先
    motor3dType: string; // 转换类型 1:分级加载；2：静态模型
    motor3dId: string; // motor模型关联id
    projClassify: number; // 工程分类（2：pdf图纸工程；3：3D模型工程）
    projModel: string; // 工程属性（SLYS:算量预算/SLSG:算量施工/ZJMX:造价模型/-）
    nodeType: number; // 工程归属节点类型 0：项目部 1：标段 2：单项 3：单位
    nodeId: string; // 工程归属节点id
    nodeName: string; // 工程归属名称
    projPicUuid: string; // 工程缩略图uuid
    projPicDownloadUrl: string; // 工程缩略图下载地址
    projMemo: string; // 工程备注
}

export interface RecentProjInfo {
    ppid: number; // 代理工程id
    projName: string; // 工程名称
    projPicUuid: string; // 工程图片uuid
    /*
     * 专业（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Remiz(班筑客户端上传)/8:班筑家装（PDS客户端）
     * 9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:ifc/14:CATIA/15:PKPM）
    */
    projType: number;
    projSize: number; // 工程文件大小
    projClassify: number; // 工程分类（2:pdf图纸工程；3:3D模型工程）
    projModel: string; // 工程属性（SLYS:算量预算/SLSG:算量施工/ZJMX:造价模型/-）
    nodeType: number; // 工程归属节点类型 0：项目部 1：标段 2：单项 3：单位
    nodeId: string; // 工程归属节点id
    nodeName: string; // 工程归属名称
    extractStatus: number; // 轻量工程处理状态:(-2:未处理 -1: 待处理， 0：处理失败， 1：处理成功 2：处理中)
    motor3dId: string; // motor模型关联id
    hashUpdated: boolean; // 是否更新了
    hashAuth: boolean; // 是否有权限
    hashDeleted: boolean; // 是否删除了
    pdsStatus: number; // pds模型抽取状态：-1:失败  1:成功 2:处理中(C++抽取) 3:处理中(java导入和聚合) 0未处理/待处理/处理中(也是处理中) 【工程未删除，才有值】
    projPicDownloadUrl: string;
}

// 获取工程缩略图的查询参数
export interface ListProjFileInfoParam {
    ppids: number[]; // 代理工程id数组
    type: string; // 类型 hsfpic缩略图 hsfpic_big 大图
}

// 工程缩略图信息
export interface ProjFileInfo {
    ppid: number;
    md5: string;
    fileSize: number;
    fileUUID: string;
    fileName: string;
}

// 修改工程备注信息
export interface UpdateProjMemoParam {
    projId: number;
    projMemo: string;
}

// 触发Motor3d模型抽取请求参数
export interface ExtractMotorParam {
    projid: number; // 工程id
    type: string; // 转换类型 1:分级加载；2：静态模型
    option: string; // 转换操作 1：效果优先，3：性能优先
}

// 根据文件下载地址查询参数
export interface GetFileDownloadUrlParam {
    fileUUIDList: string[]; //
    fileType?: number; //
}

export interface GetFileDownloadUrlParam2 {
    downloadProjParams: DownloadProjParams[];
}
interface DownloadProjParams {
    fileUuid: string;
    fileName?: string;
}

export interface FileDownloadInfo {
    downloadUrls: string[];
    fileName: string;
    fileMD5: string;
    fileSize: number;
    fileUUID: string;
    fileType: number;
}

export interface GetOrgListProjNodeByDeptIdType {
    value: string;
    key: string;
    id: string;
    type: number;
    parentId: string;
    pId?: string;
    name: string;
    title: string;
    sortOrder: null;
    projects?: [];
    children: [];

}
