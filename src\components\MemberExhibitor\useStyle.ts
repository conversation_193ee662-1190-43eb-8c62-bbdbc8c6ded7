import {createUseStyles} from "react-jss";
// import Color from "../../assets/css/Color";
import slider from "../../assets/images/slider.png";


const useStyle = createUseStyles({
    memberExhibitorWrapper: {
        position: "absolute",
        bottom: -42,
        left: 0,
        right: 0,
        backgroundColor: "#fff",
        paddingTop: 6,
        borderLeft: "1px solid #E1E2E5",
        zIndex: 10,
        "& .ant-slider": {
            paddingBottom: 10,
            "& .ant-slider-handle": {
                width: 20,
                height: 18,
                background: `url(${slider}) no-repeat 50% 50% #fff`,
                transform: "translateX(-50%) translateY(-2px) !important",
                borderRadius: 4,
                border: "1px solid #E1E2E5",
                boxShadow: "0 1px 10px rgba(0, 0, 0, 5%), 0 4px 5px rgba(0, 0, 0, 8%), 0 2px 4px -1px rgba(0, 0, 0, 12%)",
            },
        },
        "& .ant-alert": {
            position: "absolute",
            top: "-64vh",
            left: "30%",
            width: 672,
            borderLeft: "3px solid #D40000",
            color: "#061127",
            "& .ant-alert-icon": {
                color: "#D40000",
                fontSize: 18,
                marginTop: 3
            },
            "& .ant-alert-message": {
                fontWeight: "bold",
                fontSize: 14,
            },
        },
        "& .ant-btn-icon-only": {
            width: "40px",
            height: "40px",
            padding: "4.9px 0",
            fontSize: "18px",
            borderRadius: "2px",
        },
        "& .ant-btn": {
            width: "40px",
            height: "40px",
            padding: "4.9px 0",
            fontSize: "18px",
            borderRadius: "2px",
        },
        "& .ant-picker": {
            padding: "6.5px 11px 6.5px",
        },
        "& .ant-picker-input > input": {
            fontSize: "16px",
        },
    },
    timeLineBox: {
        position: "absolute",
        zIndex: 10,
        top: -350,
        left: 20,
        width: 350,
        height: 350,
        overflowY: "hidden",
        "& .ant-timeline": {
            position: "absolute",
            zIndex: 10,
            width: 350,
            top: 350,
            // animation: "$timelineBoxRemove 3s linear",
            // animationFillMode: "forwards",
            "& .ant-timeline-item": {
                paddingBottom: 5,
                marginTop: 5,
                fontSize: 12,
                lineHeight: 1.5,
                // animationDuration: "8s",
                // animation: "$timelineInfo 19s linear 1s",
                animationFillMode: "forwards",
                "& .ant-timeline-item-tail": {
                    borderColor: "#1F54C5",
                    top: 14,
                    height: "calc(100% - 20px)"
                }
            }
        },
    },
    timeLineItem: {
        "& .ant-timeline-item-tail, & .ant-timeline-item-head, & .ant-timeline-item-content": {
            // opacity: 0.2,
            // animation: "$timelineOpacity 4s linear 0.1s infinite",
            // animationFillMode: "forwards",
        }
    },
    timeLineTitle: {
        paddingBottom: 4,
        fontSize: 14,
        lineHeight: "20px"
    },
    antMenu: {

    },
    iconButton: {
        zIndex: 11,
    },
    dateWrapper: {
        position: "absolute",
        top: 24,
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        paddingRight: 40,
        "& span": {
            color: "#717784",
            fontsize: 12
        }
    },

});
export default useStyle;
