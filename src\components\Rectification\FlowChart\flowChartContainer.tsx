import React, {useCallback, useEffect, useRef, useState} from "react";
import dagre, {graphlib} from "dagre";
import {Portal} from "react-portal";
import * as joint from "jointjs";
import {ConditionFlowChartData, FlowLine, LineInfo, ApprovalUserVO, ApprovalPostVO, ApprovalRoleVO} from "../models/flow-chart";
import useStyles from "./style";
import {ApprovalType, approvalTypeList, FlowState} from "../models/rectification";

export interface ConditionFlowChartProps {
    data: ConditionFlowChartData;
    flowState?: FlowState;
}

interface ConditionFlowChartStates {
    flowNodeName: string;
    approvalType: string;
    approvalDescription: string;
    approvalUsers: string;
    approvalMode: string;
    isStartNode: boolean;
    showNodeDetail: boolean;
    labelPopupLeft: number;
    labelPopupTop: number;
    tooltipVisible: boolean;
    isOnlyStartType: boolean;
}

const makeLink = (parentElementLabel: string, childElementLabel: string, isHighlight: boolean) => {
    const ret = new joint.shapes.standard.Link({
        source: {id: parentElementLabel},
        target: {id: childElementLabel},
        smooth: false,
        attrs: {
            line: {
                targetMarker: {
                    d: "M 4 -4 0 0 4 4"
                },
                stroke: isHighlight ? "red" : "rgb(81, 125, 206)"
            }
        },
        isHighlight
    });
    ret.router("manhattan", {
        excludeEnds: [],
        excludeTypes: [],
        startDirections: ["bottom"],
        endDirections: ["top"]
    });
    ret.connector("rounded", {
        radius: 10
    });
    return ret;
};

const makeElement = (label: string, isOverflow: boolean, name: string, canBackFlow = false) => {
    const letterSize = 12;
    const width = 123;
    const height = 35;
    return new joint.shapes.standard.Rectangle({
        id: label,
        size: {width, height},
        attrs: {
            label: {
                text: isOverflow ? `${name.slice(0, 5)}...` : name,
                fontSize: letterSize,
                fill: "black"
            },
            body: {
                fill: "white",
                width,
                height,
                rx: 0,
                ry: 0,
                stroke: canBackFlow ? "red" : "rgb(81, 125, 206)"
            }
        },
        isOverflow
    });
};

const getLineInfo = (lines: LineInfo[], id: string) => lines.find((value) => value.id === id);
const isWalkLine = (nodeList: string[] | undefined | null, sourceId: string, targetId: string) => {
    if (Array.isArray(nodeList)) {
        const hasSource = nodeList.includes(sourceId);
        const hasTarget = nodeList.includes(targetId);
        return hasSource && hasTarget;
    }
    return false;
};

const getChildIds = (lines: FlowLine[], nodeLines: string[], curLineId: string) => {
    const childids: string[] = [];
    lines.forEach((value) => {
        if (value.sourceTenantTaskId === curLineId) {
            if (!nodeLines.includes(value.targetTenantTaskId)) {
                childids.push(value.targetTenantTaskId);
            } else {
                childids.push(...getChildIds(lines, nodeLines, value.targetTenantTaskId));
            }
        }
    });
    return childids;
};

const getparentIds = (lines: FlowLine[], nodeLines: string[], curLineId: string) => {
    const parentids: string[] = [];
    lines.forEach((value) => {
        if (value.targetTenantTaskId === curLineId) {
            if (!nodeLines.includes(value.sourceTenantTaskId)) {
                parentids.push(value.sourceTenantTaskId);
            } else {
                parentids.push(...getparentIds(lines, nodeLines, value.sourceTenantTaskId));
            }
        }
    });
    return parentids;
};

const makeLineInfo = (lines: FlowLine[], nodeLines: string[], curLineId: string, lineInfo: LineInfo) => {
    lines.forEach((value) => {
        if (value.sourceTenantTaskId === curLineId) {
            if (!nodeLines.includes(value.targetTenantTaskId)) {
                lineInfo.childIds.push(value.targetTenantTaskId);
            } else {
                const l = getChildIds(lines, nodeLines, value.targetTenantTaskId);
                lineInfo.childIds.push(...l);
            }
        }
        if (value.targetTenantTaskId === curLineId) {
            if (!nodeLines.includes(value.sourceTenantTaskId)) {
                lineInfo.parentIds.push(value.sourceTenantTaskId);
            } else {
                lineInfo.parentIds.push(...getparentIds(lines, nodeLines, value.sourceTenantTaskId));
            }
        }
    });
};

const getSourceList = (sourceId: string, nodeList: FlowLine[]) => {
    const temp: FlowLine[] = [];
    nodeList.forEach((item) => {
        if (sourceId === item.sourceTenantTaskId) {
            temp.push({
                sourceTenantTaskId: item.sourceTenantTaskId,
                targetTenantTaskId: item.targetTenantTaskId,
                priority: item.priority,
            });
        }
    });
    if (temp.length !== 0) {
        temp.sort((a, b) => a.priority - b.priority);
    }
    return temp;
};

const adjacencyListToCells = (serverData: ConditionFlowChartData, context: CanvasRenderingContext2D) => {
    const elements: joint.dia.Cell[] = [];
    let links: joint.dia.Link[] = [];
    const joinNodes: string[] = [];
    const nodeLines: LineInfo[] = [];
    serverData.flowNodeList.forEach((value) => {
        const fName = value.flowNodeName;
        const metrics = context.measureText(fName);
        const isOverflow = metrics.width >= 100;
        if (value.flowNodeType === "JOIN_NODE") {
            joinNodes.push(value.flowNodeId);
        } else if (serverData.historyFlowNodeIds === undefined || serverData.historyFlowNodeIds === null) {
            elements.push(makeElement(value.flowNodeId, isOverflow, fName, false));
        } else {
            elements.push(
                makeElement(
                    value.flowNodeId,
                    isOverflow,
                    fName,
                    serverData.historyFlowNodeIds.includes(value.flowNodeId)
                )
            );
        }
    });

    joinNodes.forEach((value) => {
        const ret: LineInfo = {id: "", parentIds: [], childIds: []};
        ret.id = value;
        makeLineInfo(serverData.flowLineList, joinNodes, value, ret);
        if (!nodeLines.includes(ret)) {
            nodeLines.push(ret);
        }
    });
    serverData.flowLineList.forEach((value) => {
        const lineSource = getLineInfo(nodeLines, value.sourceTenantTaskId);
        const lineTarget = getLineInfo(nodeLines, value.targetTenantTaskId);
        if (lineSource !== undefined && lineTarget === undefined) {
            lineSource.parentIds.forEach((id) => {
                links.push(makeLink(
                    id,
                    value.targetTenantTaskId,
                    isWalkLine(serverData.historyFlowNodeIds, id, value.targetTenantTaskId)
                ));
            });
        }
        if (lineTarget !== undefined && lineSource === undefined) {
            lineTarget.childIds.forEach((id) => {
                links.push(makeLink(
                    value.sourceTenantTaskId,
                    id,
                    isWalkLine(serverData.historyFlowNodeIds, value.sourceTenantTaskId, id)
                ));
            });
        }
        if (lineSource === undefined && lineTarget === undefined) {
            links.push(makeLink(
                value.sourceTenantTaskId,
                value.targetTenantTaskId,
                isWalkLine(serverData.historyFlowNodeIds, value.sourceTenantTaskId, value.targetTenantTaskId)
            ));
        }
    });
    links = links.sort((a, b) => a.prop("isHighlight") - b.prop("isHighlight"));
    return elements.concat(links);
};

const ConditionFlowChart = (props: ConditionFlowChartProps) => {
    const cls = useStyles();
    const {data, flowState} = props;
    const [allState, setAllState] = useState<ConditionFlowChartStates>({
        flowNodeName: "",
        approvalType: "",
        approvalDescription: "",
        approvalUsers: "",
        approvalMode: "",
        isStartNode: false,
        showNodeDetail: false,
        labelPopupLeft: 0,
        labelPopupTop: 0,
        tooltipVisible: false,
        isOnlyStartType: false
    });
    const sortNodeList = useRef<Set<string>>(new Set());
    const el = useRef<HTMLDivElement>(null);
    const graph = useRef<joint.dia.Graph>(new joint.dia.Graph());
    const paper = useRef<joint.dia.Paper | null>(null);
    const canvas = useRef<HTMLCanvasElement>();
    const canvasContext = useRef<CanvasRenderingContext2D>();
    const endNodeId = useRef<string>("");
    const flowLineList = useRef<FlowLine[]>([]);

    const recursiveParse = useCallback((nodeList: FlowLine[] | null) => {
        if (nodeList === null || nodeList.length === 0) {
            return;
        }
        if (nodeList.length === 1) {
            sortNodeList.current.add(nodeList[0].targetTenantTaskId);

            if (nodeList[0].targetTenantTaskId === endNodeId.current) {
                sortNodeList.current.add(nodeList[0].sourceTenantTaskId);
            } else {
                recursiveParse(getSourceList(nodeList[0].targetTenantTaskId, flowLineList.current));
            }
        } else {
            nodeList.forEach((item) => {
                if (item.targetTenantTaskId === endNodeId.current) {
                    sortNodeList.current.add(item.sourceTenantTaskId);
                } else {
                    recursiveParse(getSourceList(item.targetTenantTaskId, flowLineList.current));
                }
            });
        }
    }, []);

    const setData = useCallback((chartInfo: ConditionFlowChartData) => {
        if (paper.current === null) {
            return;
        }
        const endNode = chartInfo.flowNodeList.find((item) => item.flowNodeType === "END_NODE");
        endNodeId.current = endNode !== undefined ? endNode.flowNodeId : "";
        if (flowState === FlowState.Done && Array.isArray(chartInfo.historyFlowNodeIds)) {
            chartInfo.historyFlowNodeIds.push(endNodeId.current);
        }
        recursiveParse(getSourceList(chartInfo.rootNodeId, chartInfo.flowLineList));
        paper.current.off("element:mouseenter");
        paper.current.on("element:mouseenter", (view) => {
            const id = view.model.attributes.id as string;
            const node = chartInfo.flowNodeList.find((f) => f.flowNodeId === id);
            if (node === undefined) {
                return;
            }
            const {flowNodeId, flowNodeName, isOrApproval, type, approvalPosts, approvalUsers, approvalRoles} = node;
            const endNodeInner = flowNodeId === endNodeId.current;
            if (endNodeInner) {
                return;
            }

            const startNode = flowNodeId === chartInfo.rootNodeId;
            if (startNode && flowState === undefined) {
                return;
            }
            if (!(type in ApprovalType)) {
                return;
            }
            const approvalType = approvalTypeList.find((v) => v.value === type)?.label;
            let names: (Partial<ApprovalRoleVO> & Partial<ApprovalUserVO> & Partial<ApprovalPostVO>)[] = [];
            if (Array.isArray(approvalPosts) && approvalPosts.length > 0) {
                names = approvalPosts;
            } else if (Array.isArray(approvalUsers) && approvalUsers.length > 0) {
                names = approvalUsers;
            } else if (Array.isArray(approvalRoles) && approvalRoles.length > 0) {
                names = approvalRoles;
            }
            const approvalNameRow = `${approvalType}：`;
            /* eslint-disable-next-line */
            const approvalName = names ? names.map((n) => (n.roleName || n.postName || ((n.realName || n.userName) + (n.isLeave ? "（已离职）" : ""))) as string).join("，") : "";
            const approvalMode = isOrApproval ? "或签" : "会签";

            const rect = view.$el[0].getBoundingClientRect();
            setAllState({
                tooltipVisible: true,
                labelPopupLeft: rect.right,
                /* eslint-disable-next-line */
                labelPopupTop: rect.top + rect.height + 5,
                flowNodeName,
                approvalType: approvalType ?? "",
                approvalUsers: approvalName,
                approvalMode,
                approvalDescription: approvalNameRow,
                isStartNode: startNode,
                showNodeDetail: flowState !== undefined,
                isOnlyStartType: type === ApprovalType.OnlyStart
            });
        });
        paper.current.on("element:mouseleave", () => {
            setAllState((prev) => ({...prev, tooltipVisible: false}));
        });
        if (canvasContext.current === undefined) {
            throw new TypeError("component not mount");
        }
        const cells = adjacencyListToCells(chartInfo, canvasContext.current);
        graph.current.resetCells(cells);
        joint.layout.DirectedGraph.layout(graph.current, {
            dagre,
            graphlib,
            setLinkVertices: false,
            marginX: 5,
            marginY: 5
        });
        const bbox = graph.current.getBBox();
        if (bbox === null) {
            return;
        }
        // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
        paper.current.setDimensions(bbox.width + (2 * bbox.x), bbox.height + (2 * bbox.y));
        setTimeout(() => {
            cells.forEach((cell) => {
                if (!(cell instanceof joint.shapes.standard.Rectangle)) {
                    return;
                }
                const pos = cell.position();
                pos.x += 1;
                cell.position(pos.x, pos.y);
            });
            el.current!.scrollIntoView({block: "center", inline: "center"});
        }, 10);
    }, [flowState, recursiveParse]);

    useEffect(() => {
        paper.current = new joint.dia.Paper({
            el: el.current!,
            width: 1000,
            height: 700,
            model: graph.current,
            interactive: false
        });
        canvas.current = document.createElement("canvas");
        const context = canvas.current.getContext("2d");
        if (context === null) {
            throw new TypeError("cannot get context 2d from canvas");
        }
        canvasContext.current = context;
    }, []);

    useEffect(() => {
        setData(data);
        return () => {
            if (paper.current !== null) {
                paper.current.undelegateEvents();
                paper.current.remove();
                paper.current.unbind();
            }
            // eslint-disable-next-line react-hooks/exhaustive-deps
            graph.current.destroy();
        };
    }, [setData, data]);

    return (
        <div className={cls.wrapper}>
            <Portal>
                <div
                    className={`ant-tooltip  ant-tooltip-placement-bottom ${!allState.tooltipVisible ? "ant-tooltip-hidden" : ""}`}
                    style={{left: allState.labelPopupLeft, top: allState.labelPopupTop, transformOrigin: "50% 44px"}}
                >
                    <div className="ant-tooltip-content">
                        <div className="ant-tooltip-inner" role="tooltip">
                            {
                                !allState.isStartNode ? <div className="title">{allState.flowNodeName}</div> : null
                            }
                            {
                                allState.showNodeDetail
                                    ? (
                                        <div className={cls.detail}>
                                            <table>
                                                <tbody>
                                                    {
                                                        !allState.isStartNode
                                                            ? (
                                                                <tr>
                                                                    <th>审批类型:</th>
                                                                    <td>{allState.approvalType}</td>
                                                                </tr>
                                                            )
                                                            : null
                                                    }
                                                    <tr>
                                                        <th className={cls.titleThBox}>{allState.approvalDescription}</th>
                                                        <td id="elApprovalName">{allState.approvalUsers}</td>
                                                    </tr>
                                                    {
                                                        !allState.isStartNode && !allState.isOnlyStartType
                                                            ? (
                                                                <tr id="approvalModelRow">
                                                                    <th>审批方式:</th>
                                                                    <td id="elApprovalMode">{allState.approvalMode}</td>
                                                                </tr>
                                                            )
                                                            : null
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    )
                                    : null
                            }
                        </div>
                    </div>
                </div>
            </Portal>
            {
                flowState !== undefined
                    ? (
                        <div className={cls.conditionFlowChartLegend}>
                            <ul>
                                <li>
                                    <i className={cls.legendIconPastNode} />
                                已审批节点
                                </li>
                                <li>
                                    <i className={cls.legendIconNextNode} />
                                待审批节点
                                </li>
                            </ul>
                        </div>
                    )
                    : null
            }
            <div ref={el} style={{overflow: "hidden", margin: "0 auto"}} />
        </div>
    );
};

export default ConditionFlowChart;
