export interface ProjNameVo {
    /** 代理工程Id */
    ppid?: number;
    /** 工程名称 */
    projName?: string;
    /** 工程类型 */
    projType?: number;
}

/** 项目部下工程组织树 */
export interface OrgProjNodeVo {
    /** 节点id */
    id?: string;
    /** 节点类型: 0-项目部 1-标段 2-单项工程 3-单位工程 */
    type?: number;
    /** 父节点id，为null表示根节点 */
    parentId?: string;
    /** 节点名称 */
    name?: string;
    /** 排序字段 */
    sortOrder?: number;
    /** 工程列表 */
    projects?: ProjNameVo[];
}

/** 项目部组织树 */
export interface OrgNodeInfo {
    /** 节点id */
    id: string;
    /** 节点类型: 0-项目部 1-标段 2-单项工程 3-单位工程 */
    type: number;
    /** 节点名称 */
    name: string;
    /** 节点名称 */
    isBIM: boolean;
}
