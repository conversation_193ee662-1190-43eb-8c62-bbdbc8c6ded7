import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {ApprovalBackOperationMessageParam, ApprovalCancelOperationMessageParam, ApprovalCopyToOperationMessageParam, ApprovalFlowVo, ApprovalNodeVo, ApprovalOperationMessageParam, ApprovalTransferParam, CfcaSignParams, CfcaSignReturn, GetReportFormTemplateType, ListApprovalLineNodeParam, ListReformQueryParam, NewPageRes, ProcessNodePageParams, ProcessNodePageReturn, QuerySignByNodeParams, QuerySignByNodeReturn, ReformFormItemVo, ReformInstCreateParam, SignPageUrlParams, TemporarySaveFormParams} from "./models/flowLine";
import {ApprovalTypeVo, FormTemplateVo, ListReformTypeQueryParam, ProRes, ReformDetailVo, ReformInstSubmitParam} from "./models/process";


/** 发布流程类型模板和表单模板详情接口 */
export const getApprovalFormTemplateDetail = async (approvalTypeId: string): Promise<ProRes<FormTemplateVo>> => Fetch({
    url: `/process/approval/common/new/approvalFormTemplateDetail/${approvalTypeId}`,
    methods: "get"
});

/** 获取审批类型的流程图 PC-BV （co3.1.0&iworksApp1.1.0新增） */
export const getProcessFlowChart = async (approvalTypeId: string): Promise<ProRes<ApprovalFlowVo>> => Fetch({
    url: `/process/approval/flow/flowChart/approvalType/${approvalTypeId}`,
    methods: "get"
});

/** 分页获取整改审批表单列表 */
export const getReformList = async (param: ListReformQueryParam, type = "PLAN_REFORM"): Promise<ProRes<NewPageRes<ReformFormItemVo>>> => Fetch({
    url: `/process/reform/instance/${type}/pageForm`,
    methods: "post",
    data: param
});

/** 获取整改表单实例详情 */
export const getReformDetail = async (serialNum: string, type = "PLAN_REFORM"): Promise<ProRes<ReformDetailVo>> => Fetch({
    url: `/process/reform/instance/${type}/formDetail/${serialNum}`,
    methods: "get"
});

/** 流程打印模板跳转url获取 */
export const getReformViewUrl = async (processInstanceId: string): Promise<ProRes<string>> => Fetch({
    url: `/process/approval/common/new/v2/formViewUrl/${processInstanceId}`,
    methods: "get"
});

/** 获取审批实例的流程图 */
export const getReformFlowChart = async (serialNum: string, approvalTypeId: string): Promise<ProRes<ApprovalFlowVo>> => Fetch({
    url: `/process/approval/flow/flowChart/${serialNum}/${approvalTypeId}`,
    methods: "get"
});

/** 获取可用整改类型列表 */
export const getReformType = async (params: ListReformTypeQueryParam, type = "PLAN_REFORM"): Promise<ProRes<ApprovalTypeVo[] | null>> => Fetch({
    url: `/process/reform/template/${type}/listReformType`,
    methods: "post",
    data: params
});

/** 通过填写表单获取审批类型对应的节点列表 */
export const getListApprovalLineNode = async (params: ListApprovalLineNodeParam): Promise<ProRes<ApprovalNodeVo[]>> => Fetch({
    url: "/process/approval/flow/listApprovalLineNode",
    methods: "post",
    data: params
});

/** 发起人创建整改审批表单 */
export const createReform = async (param: ReformInstCreateParam, type = "PLAN_REFORM"): Promise<ProRes<string>> => Fetch({
    url: `/process/reform/instance/${type}/create`,
    methods: "post",
    data: param
});

/** 发起人-审批人提交整改审批表单 */
export const submitReform = async (params: ReformInstSubmitParam, type = "PLAN_REFORM"): Promise<WebRes<string>> => Fetch({
    url: `/process/reform/instance/${type}/submit`,
    methods: "post",
    data: params
});

/** 审批人转交 */
export const transferReform = async (params: ApprovalTransferParam): Promise<ProRes<string>> => Fetch({
    url: "/process/approval/operation/transfer",
    methods: "post",
    data: params
});

/** 审批人抄送（返回抄送失败人员列表） */
export const copyToReform = async (params: ApprovalCopyToOperationMessageParam): Promise<ProRes<string>> => Fetch({
    url: "/process/approval/operation/copyTo",
    methods: "post",
    data: params
});

/** 发起人删除审批表单 */
export const deleteReform = async (serialNum: string): Promise<ProRes<string>> => Fetch({
    url: `/process/approval/operation/ApprovalForm/${serialNum}`,
    methods: "delete"
});

/** 发起人撤销审批表单 */
export const cancelReform = async (param: ApprovalCancelOperationMessageParam): Promise<ProRes<string>> => Fetch({
    url: "/process/approval/operation/cancel",
    methods: "post",
    data: param
});

/** 审批人驳回审批 */
export const backReform = async (param: ApprovalBackOperationMessageParam): Promise<ProRes<string>> => Fetch({
    url: "/process/approval/operation/back",
    methods: "post",
    data: param
});

/** 抄送人评论审批 */
export const commentReform = async (param: ApprovalOperationMessageParam): Promise<ProRes<string>> => Fetch({
    url: "/process/approval/operation/comment",
    methods: "post",
    data: param
});

/** 获取通知单和整改单地址 */
export const getReportFormFillUrls = async (
    bid: string,
    deptId: string,
    type: string,
    moduleType: string,
    buildType: number,
    falseFlag = 0
): Promise<ProRes<string[]>> => Fetch({
    url: "/builder/appconfig/general/report-form/preview-urls",
    methods: "get",
    data: {bid, deptId, type, moduleType, buildType, falseFlag}
});

/** 存储整改人 */
export const saveDefaultUserName = async (param: {deptId: string; nodeId?: string; userInfo: string}): Promise<ProRes<string>> => Fetch({
    url: "/process/reform/instance/saveUserName",
    methods: "post",
    data: param
});

/** 获取上次整改人 */
export const loadDefaultUserName = async (param: {deptId: string; nodeId?: string}): Promise<ProRes<{approvalUsers: string}>> => Fetch({
    url: "/process/reform/instance/defaultUserName",
    methods: "post",
    data: param
});

/** 获取单个高级表单模块信息 */
export const getReportFormTemplate = async (
    params: {buildType: number; nodeId: string; moduleType: string; type: string}
): Promise<ProRes<GetReportFormTemplateType>> => Fetch({
    url: "/builder/appconfig/general/report-form/template",
    methods: "get",
    data: params
});

/** 资料模版条目-流程节点设置列表接口 */
export const getProcessNodePage = async (params: ProcessNodePageParams): Promise<WebRes<ProcessNodePageReturn>> => Fetch({
    url: "/luban-misc/data/template/item/process/node/page",
    methods: "get",
    data: params
});

/** 表单-编辑地址接口（v1） */
export const getSignPageUrl = async (params: SignPageUrlParams): Promise<WebRes<string>> => Fetch({
    url: "/sphere/sign/page-url",
    methods: "get",
    data: params
});

/** 获取当前审批节点是否有pdf */
export const querySignByNode = async (params: QuerySignByNodeParams): Promise<WebRes<QuerySignByNodeReturn>> => Fetch({
    url: "/sphere/sign/query-by-node",
    methods: "post",
    data: params
});

export const saveSignByNode = async (params: Partial<QuerySignByNodeReturn>): Promise<WebRes<string>> => Fetch({
    url: "/sphere/sign/save-pdf",
    methods: "post",
    data: params
});

/** 提交签名签章 */
export const cfcaSign = async (params: CfcaSignParams): Promise<WebRes<CfcaSignReturn>> => Fetch({
    url: "/luban-misc/sign/cfca/sign",
    methods: "post",
    data: params
});

/** 保存临时预览的高级表单内容 */
export const temporarySaveForm = async (params: TemporarySaveFormParams): Promise<WebRes<string>> => Fetch({
    url: "/process/rs/common/process/reportInstance/temp-form-save",
    methods: "post",
    data: params
});
