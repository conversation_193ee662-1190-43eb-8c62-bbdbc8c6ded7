/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/camelcase */
// 调用的center的接口

import Fetch from "../../service/Fetch";
// import {getState} from "../store";
import {DepartmentTreeType, DepartmentType, GetSectionListRes, JobOrPostType, DepartmentAndJobListType, InfraSectionListParams, InfraSectionListReturn, PersonTreeType, GetDangerSourceListRes, GetRiskEvaluationRes, WBSNodeType, GetCheckListProjectCategoryType, GetCheckSecurityListChooseType, GetCheckQualityListChooseType, ParamsIdsType, CheckListSatuteGistType, DangerOptionType, EnterpriseRes, GetOrgNodeDetailReturn, FindUsersParams, FindUsersReturn, GetUserSignImageReturn, FindUserType, WBSBusinessType} from "./type";
import {WebRes, WebResResult} from "../common.type";

// const centerBaseUrl = () => getState().commonData.url.baseUrl;

// 通过项目获取标段信息
export const getSectionList = async (ids: string[]) => Fetch<GetSectionListRes>({
    // baseUrl: centerBaseUrl(),
    url: "/builder/orgnode/list",
    methods: "post",
    data: {
        deptIds: ids,
        page: 1,
        size: 500
    }
});

// 获取工作区域
// export const getWorkAreaList = async (_id: string) => {
//     const {commonData, threeAndSection} = getState();
//     const nodeId = threeAndSection.curSectionInfo?.nodeId;
//     return Fetch<WebRes<WorkAreaType[]>>({
//         url: "/sphere/personmgr/center/queryAreaList",
//         methods: "get",
//         data: {
//             partId: nodeId === "" ? commonData.orgInfo.orgId : nodeId,
//             partType: nodeId === "" ? 1 : 2
//         }
//     });
// };

// 获取工种list
export const getJobList = async (craftType: number | string, projectId: string) => Fetch<WebRes<JobOrPostType[]>>({
    url: "/sphere/personmgr/center/queryCraftList",
    data: {
        craftType,
        projectId
    }
});

// 获取岗位信息
export const getPostList = async (orgId: string) => Fetch<WebRes<JobOrPostType[]>>({
    url: "/sphere/personmgr/center/queryPostList",
    data: {
        orgId
    }
});

// 获取项目或标段下部门信息
export const getDepartmentList = async (buildType: string, parentId: string) => Fetch<WebRes<DepartmentTreeType[]>>({
    url: "/sphere/personmgr/center/querySectionList",
    data: {
        buildType,
        parentId
    }
});

// 获取项目或标段下部门和岗位信息
export const getDepartmentAndPostList = async (buildType: number, parentId: string) => Fetch<WebRes<DepartmentAndJobListType>>({
    url: "/sphere/personmgr/center/querySectionAndPostList",
    data: {
        buildType,
        parentId
    }
});

// 获取班组信息
export const getTeamList = async (id: string, partType?: number) => Fetch<WebRes<DepartmentType[]>>({
    url: "/sphere/personmgr/center/queryTeamList",
    data: {
        partId: id,
        partType: partType ?? 2,
    }
});
// 查询标段列表
export const infraSectionList = async (params: InfraSectionListParams): Promise<WebRes<InfraSectionListReturn[]>> => Fetch({
    url: "/sphere/infra/section/list",
    methods: "post",
    data: params
});
export const userLogin = async (params: {loginType: string; password: string; username: string}): Promise<WebRes<string>> => Fetch({
    // baseUrl: centerBaseUrl(),
    methods: "post",
    url: "/auth-server/auth/token",
    data: params,
    needToken: false
});

export const getEnterprises = async (params: {}) => Fetch<WebRes<string>>({
    // baseUrl: centerBaseUrl(),
    methods: "get",
    url: "/auth-server/auth/enterprises",
    data: params,
});

export const getEnterprise = async (epid: number) => Fetch<WebRes<EnterpriseRes>>({
    // baseUrl: centerBaseUrl(),
    methods: "put",
    url: "/auth-server/auth/enterprise",
    data: {
        epid
    },
});

export const getAuthCode = async () => Fetch({
    // baseUrl: centerBaseUrl(),
    url: "/auth-server/auth/authgroup/192"
});

export const getLoadPersonTree = async (jobType: number, projectId: string, lubanFlag: 0 | 1 = 0) => Fetch<WebRes<PersonTreeType>>({
    methods: "get",
    url: "/sphere/personmgr/person/loadPersonTree",
    data: {
        jobType,
        projectId,
        lubanFlag
    },
});
// 获取风险的过程区域
export const getOptionList = async () => Fetch<WebResResult<DangerOptionType[]>>({
    url: "/builder/appconfig/general/dangersource/option/source/CUSTOM"
});
// 获取风险的工序/行为/设备
export const getItemOptionList = async (val: string[]) => Fetch<WebResResult<DangerOptionType[]>>({
    url: "/builder/appconfig/general/dangersource/itemoption/source/CUSTOM",
    methods: "post",
    data: val
});

// 根据关键字获取风险信息
export const getDangerSourceList = async (val: string, params?: {}) => Fetch<GetDangerSourceListRes>({
    methods: "post",
    // baseUrl: centerBaseUrl(),
    url: "/builder/appconfig/general/dangersource/source/CUSTOM",
    data: {
        keyword: val,
        page: 1,
        size: 10,
        ...params,
    }
});

// 获取风险信息
export const getDangerSourceData = async (params: {}) => Fetch<GetDangerSourceListRes>({
    methods: "post",
    // baseUrl: centerBaseUrl(),
    url: "/builder/appconfig/general/dangersource/source/CUSTOM",
    data: params
});

// 获取风险评价
export const getRiskEvaluation = async () => Fetch<GetRiskEvaluationRes>({
    // baseUrl: centerBaseUrl(),
    url: "/builder/appconfig/general/dangersource/riskevaluation",
});
// 获取整棵wbs树
export const getWBSAllTree = async (deptId: string, sectionId?: string) => Fetch<WebRes<WBSNodeType[]>>({
    // baseUrl: centerBaseUrl(),
    url: "/luban-infrastructure-center/wbs/all-tree",
    data: {
        dept_id: deptId,
        section_id: sectionId ?? ""
    }
});

// 获取所有WBS节点类型接口
export const getAllWBSTypeList = async () => Fetch<WebRes<WBSBusinessType[]>>({
    // baseUrl: centerBaseUrl(),
    url: "/luban-infrastructure-center/wbs-template/node/type",
});

export const getCheckTypeList = async (keyword: string) => Fetch<any>({
    methods: "post",
    // baseUrl: centerBaseUrl(),
    url: "/builder/appconfig/general/security/check/checkType/cf60c42cbb8a688dc22ee124327a7834/source/CUSTOM",
    data: {
        // keyword,
        // page: 1,
        // size: 10
        optionId: "", itemOptionId: "", keyword, orders: [{direction: 1, property: "updateTime"}], page: 1, size: 10
    }
});

// 工程类别列表  检查模块类型 security(安全) quality（质量）
export const getCheckListProjectCategory = async (type: string) => Fetch<WebResResult<GetCheckListProjectCategoryType[]>>({
    methods: "get",
    url: `/businessdata/rs/check/${type}/listProjectCategory`,
});

// 选择检查项列表(质量)
export const getCheckQualityListChoose = async (projectCategoryId: string) => Fetch<WebResResult<GetCheckQualityListChooseType[]>>({
    methods: "get",
    url: `/businessdata/rs/check/quality/listChooseCheckItem/${projectCategoryId}`,
});

// 选择检查项列表(安全)
export const getCheckSecurityListChoose = async (projectCategoryId: string) => Fetch<WebResResult<GetCheckSecurityListChooseType[]>>({
    methods: "get",
    url: `/businessdata/rs/check/security/listChooseCheckItem/${projectCategoryId}`,
});
// 法规依据列表
export const postCheckListSatuteGist = async (params: ParamsIdsType) => Fetch<WebResResult<CheckListSatuteGistType[]>>({
    methods: "post",
    url: "/businessdata/rs/security/check/listSatuteGist",
    data: params
});
// 获取标段详情
export const getOrgNodeDetail = async (nodeId: string) => Fetch<WebResResult<GetOrgNodeDetailReturn>>({
    methods: "get",
    url: `/builder/orgnode/nodeId/${nodeId}`,
});
/** 获取人员授权权限 */
export const findUsers = async (params: FindUsersParams): Promise<WebResResult<FindUsersReturn>> => Fetch({
    url: "/builder/userRest/findUsers",
    methods: "post",
    data: params
});
// 获取用户签名照片
export const getUserSignImage = async (id: string) => Fetch<WebResResult<GetUserSignImageReturn>>({
    methods: "get",
    url: `/builder/userRest/findUserSign/${id}`,
});
/** 获取用户身份证、签名、签章信息 */
export const findUserIdcardSign = async (userName: string): Promise<WebResResult<FindUserType>> => Fetch({
    url: `/builder/userRest/finduser-idcard-sign/${userName}`,
    methods: "get",
});
