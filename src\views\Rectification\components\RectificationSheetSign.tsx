/* eslint-disable max-nested-callbacks */
/* eslint-disable max-len */
import React, {useCallback, useEffect, useState} from "react";
import {Button, Divider, message, Modal, Space, Spin, Tabs} from "antd";
import {useSelector} from "react-redux";
import moment from "moment";
import {isEqual, cloneDeep} from "lodash-es";
import jsPDF from "jspdf";
import {getReportFormFillUrls, saveSignByNode, temporarySaveForm} from "../../../api/rectification/index";
import {RootState} from "../../../store/rootReducer";
import {QuerySignByNodeReturn} from "../../../api/rectification/models/flowLine";
import {getPreviewUrl} from "../../../api/common.api";
import uploadFileOss from "../../../components/UploadFileCom/uploadFile";
import {FormInfoType, PreviewInfoType, RectificationSheetSignProps, SignSuccessInfoType} from "../interface";

const currentReformId = "asd";

type RectificationType = "Rectification-Notice" | "Rectification-Reply";
// eslint-disable-next-line max-lines-per-function
const RectificationSheetSign: React.FC<RectificationSheetSignProps> = (props) => {
    const {handleSubmit, reformDetail, replyReportFormTemplate, visible, noticeReportFormTemplate, setVisible, fileList, defaultAnnotation, formPdfInfo, operateMsg, launchReform} = props;
    const {orgInfo, userInfo, token} = useSelector((state: RootState) => state.commonData);
    // const {currentReformId} = useSelector((state: RootState) => state.rectificationTemp);
    const [signatureUrl, setSignatureUrl] = useState("");
    const [currentForm, setCurrentForm] = useState<RectificationType>("Rectification-Notice");
    const [formInfoList, setFormInfoList] = useState<FormInfoType[]>([]);
    const [modalType, setModalType] = useState<"sign" | "preview">("preview");
    const [signPdfInfo, setSignPdfInfo] = useState<SignSuccessInfoType | null>(null);
    const [loadCount, setLoadCount] = useState(0);
    const [previewInfoList, setPreviewInfoList] = useState<PreviewInfoType[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const updatePreviewInfo = async () => {
            const list = [];
            if ((formPdfInfo?.noticefileId ?? "").length > 0) {
                list.push({fileName: formPdfInfo?.noticefileName ?? "", uuid: formPdfInfo?.noticefileId ?? "", formType: "Rectification-Notice"});
            }
            if ((formPdfInfo?.replyFileId ?? "").length > 0) {
                list.push({fileName: formPdfInfo?.replyFileName ?? "", uuid: formPdfInfo?.replyFileId ?? "", formType: "Rectification-Reply"});
            }
            try {
                setLoading(true);
                const previewList = await Promise.all(list.map(async (item) => {
                    const {formType, fileName, uuid} = item;
                    const previewUrl = await getPreviewUrl({fileName, uuid});
                    return {
                        fileId: uuid,
                        fileName,
                        previewUrl: `${previewUrl}?rowpdf=1`,
                        formType,
                        formName: formType === "Rectification-Notice" ? "整改通知单" : "整改回复单"
                    };
                }));
                setPreviewInfoList(previewList);
            } catch (err) {
                console.log(err);
            } finally {
                if ((formPdfInfo?.noticefileId ?? "").length > 0 && (formPdfInfo?.replyFileId ?? "").length > 0) {
                    setLoading(false);
                }
            }
        };
        updatePreviewInfo();
    }, [formPdfInfo]);
    // 获取有数据的表单地址
    const getUrlList = useCallback(async (listParams: {templateType?: string; serialNum?: string}) => {
        try {
            let params = {};
            if (operateMsg !== undefined) {
                params = {
                    ...params,
                    operateMsg,
                    operator: userInfo.username
                };
            }
            const reportFormTemplate = listParams.templateType === "Rectification-Reply" ? replyReportFormTemplate : noticeReportFormTemplate;
            if (reportFormTemplate !== undefined && reportFormTemplate.sign === 1) {
                // 获取临时预览的表单id
                const {data} = await temporarySaveForm({
                    approvalOperationAttachmentVos: fileList ?? [],
                    epid: Number(userInfo?.epid) ?? 0,
                    userName: userInfo.username,
                    ...params,
                    ...listParams
                });
                const {result} = await getReportFormFillUrls(data, orgInfo.orgId, reportFormTemplate?.type, reportFormTemplate?.moduleType, 0, 1);
                setFormInfoList((oldList) => [
                    ...oldList,
                    {...reportFormTemplate, urls: result ?? []}
                ]);
            }
        } catch (err) {
            console.log(err);
        }
    }, [fileList, noticeReportFormTemplate, operateMsg, orgInfo.orgId, replyReportFormTemplate, userInfo]);
    useEffect(() => {
        if (visible) {
            if (launchReform !== undefined) {
                const {currentFlowNodeId, ...other} = launchReform;
                getUrlList({...other, templateType: "Rectification-Notice"});
            } else if (reformDetail !== undefined) {
                const {serialNum} = reformDetail;
                if (formPdfInfo?.noticefileId === null && formPdfInfo.replyFileId === null) {
                    getUrlList({serialNum, templateType: "Rectification-Notice"});
                    getUrlList({serialNum, templateType: "Rectification-Reply"});
                } else if (formPdfInfo?.noticefileId === null || formPdfInfo?.replyFileId === null) {
                    const needQueryTem = formPdfInfo?.replyFileId === null ? "Rectification-Reply" : "Rectification-Notice";
                    getUrlList({serialNum, templateType: needQueryTem});
                }
            }
        }
    }, [formPdfInfo, getUrlList, launchReform, reformDetail, visible]);
    const savePdfFile = useCallback((fileId: string, fileName: string, type: RectificationType) => {
        let params: Partial<QuerySignByNodeReturn> = {flowNodeId: reformDetail?.currentFlowNodeId, serialNum: Number(reformDetail?.serialNum ?? "0")};
        if (type === "Rectification-Notice") {
            params = {...params, noticefileId: fileId, noticefileName: fileName};
        } else if (type === "Rectification-Reply") {
            params = {...params, replyFileId: fileId, replyFileName: fileName};
        }
        // 保存pdf和节点的关联关系
        saveSignByNode(params)
            .then((_res) => {
                getPreviewUrl({fileName, uuid: fileId}).then((previewUrl) => {
                    setPreviewInfoList((oldData) => {
                        const index = oldData.findIndex((i) => i.formType === type);
                        const newData = cloneDeep(oldData);
                        newData[index] = {...newData[index], previewUrl: `${previewUrl}?rowpdf=1`, fileName, fileId};
                        return newData;
                    });
                });
            })
            .catch((err) => {
                console.log(err);
            });
    }, [reformDetail]);
    const mergeCanvas = useCallback(async (list: string[], defaultWidth = 592.28, defaultHeight = 841.89): Promise<string> => new Promise((resolve, reject) => {
        const baseList: string[] = [];
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.width = 794;
        canvas.height = 1123 * list.length;
        list.forEach((url, index) => {
            const img = new Image();
            img.src = url;
            img.crossOrigin = "Anonymous";
            const isLoaded = img.complete && img.naturalHeight !== 0;
            if (!isLoaded) {
                img.onload = () => {
                    // eslint-disable-next-line chai-friendly/no-unused-expressions
                    context?.drawImage(img, 0, 1123 * index, 794, 1123);
                    const base64 = canvas.toDataURL("image/png", 2.0);
                    baseList.push(base64);
                    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
                    if (baseList[list.length - 1]) {
                        const i = list.length > 0 ? list.length : 1;
                        // eslint-disable-next-line new-cap
                        const pdf = new jsPDF("p", "pt", [Math.round(defaultWidth / i), defaultHeight]);
                        pdf.addImage(baseList[list.length - 1], "png", 0, 0, Math.round(defaultWidth / i), defaultHeight);
                        // const dataUrl = pdf.output("blob");
                        const dataUrl = pdf.output("dataurlstring");
                        resolve(dataUrl);
                    }
                };
                img.onerror = () => {
                    message.warning("图片加载失败");
                    reject(canvas.toDataURL("image/png", {width: 592.28, height: 841.89, cols: 1, encoderOptions: 2}));
                };
            }
        });
    }), []);
    // base64转换成文件
    const dataUrlToFile = useCallback((dataurl, filename) => {
        const arr = dataurl.split(",");
        const type = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let len: number = bstr.length;
        const u8arr = new Uint8Array(len);
        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
        while (len--) {
            u8arr[len] = bstr.charCodeAt(len);
        }
        return new File([u8arr], filename, {type});
    }, []);
    const uploadFile = useCallback(async (url, formType: RectificationType) => {
        try {
            const formName = formType === "Rectification-Notice" ? "整改通知单" : "整改回复单";
            const fileName = `${userInfo.username}_${formName}_${moment(new Date()).format("YYYYMMDDHHmmss")}.pdf`;
            const file = dataUrlToFile(url, fileName);
            await uploadFileOss({
                file,
                onSuccess: async (fileInfo) => {
                    const previewUrl = await getPreviewUrl({fileName, uuid: fileInfo.fileUuid});
                    setPreviewInfoList((old) => [...old, {fileId: fileInfo.fileUuid, fileName, previewUrl, formType, formName}]);
                    if (reformDetail !== undefined && launchReform === undefined) {
                        savePdfFile(fileInfo.fileUuid, fileName, formType);
                    }
                }
            });
        } catch (err) {
            console.log(err);
            message.warning("表单加载失败了～");
            setVisible(false);
        } finally {
            setLoading(false);
        }
    }, [dataUrlToFile, launchReform, reformDetail, savePdfFile, setVisible, userInfo.username]);

    const canvasToMerge = useCallback((canvas, type) => {
        const list = canvas.map((item: any) => item.toDataURL("image/png", 2.0));
        mergeCanvas(list).then((res) => {
            uploadFile(res, type);
        }).catch((err) => {
            console.log(err);
        });
    }, [mergeCanvas, uploadFile]);
    const noticePulling = useCallback(() => {
        const timer = setTimeout(() => {
            /* eslint-disable-next-line  @typescript-eslint/ban-ts-ignore */
            // @ts-ignore
            const noticeCanvas0 = Notice0?.window.document.querySelector("canvas");
            /* eslint-disable-next-line  @typescript-eslint/ban-ts-ignore */
            // @ts-ignore
            const noticeCanvas1 = Notice1?.window.document.querySelectorAll("canvas");
            if (noticeCanvas0 !== null && noticeCanvas0 !== undefined && noticeCanvas1.length > 0) {
                clearTimeout(timer);
                setTimeout(() => canvasToMerge([noticeCanvas0, ...noticeCanvas1], "Rectification-Notice"), 1000);
            } else {
                clearTimeout(timer);
                noticePulling();
            }
        }, 500);
    }, [canvasToMerge]);
    const replyPulling = useCallback(() => {
        const timer = setTimeout(() => {
            /* eslint-disable-next-line  @typescript-eslint/ban-ts-ignore */
            // @ts-ignore
            const replyCanvas0 = Reply0?.window.document.querySelector("canvas");
            /* eslint-disable-next-line  @typescript-eslint/ban-ts-ignore */
            // @ts-ignore
            const replyCanvas1 = Reply1?.window.document.querySelectorAll("canvas");
            if (replyCanvas0 !== null && replyCanvas0 !== undefined && replyCanvas1.length > 0) {
                clearTimeout(timer);
                setTimeout(() => canvasToMerge([replyCanvas0, ...replyCanvas1], "Rectification-Reply"), 1000);
            } else {
                clearTimeout(timer);
                replyPulling();
            }
        }, 500);
    }, [canvasToMerge]);
    useEffect(() => {
        if (loadCount > 0 && loadCount < 5 && loadCount % (formInfoList.length + 1) === 0) {
            if ((formPdfInfo?.noticefileId ?? "").length === 0 && (formPdfInfo?.replyFileId ?? "").length === 0) {
                setLoading(true);
            }
            if (formInfoList.find((i) => i.type === "Rectification-Notice") !== undefined && (formPdfInfo?.noticefileId ?? "").length === 0) {
                noticePulling();
            }
            if (formInfoList.find((i) => i.type === "Rectification-Reply") !== undefined && (formPdfInfo?.replyFileId ?? "").length === 0) {
                replyPulling();
            }
        }
    }, [formInfoList, formPdfInfo, loadCount, noticePulling, replyPulling]);

    const handleSign = useCallback(() => {
        setModalType("sign");
        const currentFormTemplate = formInfoList.find((item) => item.type === currentForm);
        const currentPreviewInfo = previewInfoList.find((item) => item.formType === currentForm);
        setSignatureUrl(`./signature/?pdf=${currentPreviewInfo?.fileId}&fileName=${encodeURIComponent(currentPreviewInfo?.fileName ?? "")}&processId=${reformDetail?.currentFlowNodeId ?? launchReform?.currentFlowNodeId}&templateItemId=${currentFormTemplate?.id ?? ""}&annotation=${encodeURIComponent(defaultAnnotation ?? "")}&processInstanceId=${currentReformId}&token=${token}`);
    }, [formInfoList, previewInfoList, reformDetail, launchReform, defaultAnnotation, token, currentForm]);
    useEffect(() => {
        const listenSignStatus = () => {
            const signSuccessInfo: SignSuccessInfoType | null = JSON.parse(localStorage.getItem("signSuccessInfo") ?? "null");
            if (signSuccessInfo !== null) {
                setSignPdfInfo((olddata) => {
                    if (isEqual(olddata, signSuccessInfo)) {
                        return olddata;
                    }
                    return signSuccessInfo;
                });
            }
        };
        window.addEventListener("storage", listenSignStatus);
        return () => {
            window.removeEventListener("storage", listenSignStatus);
        };
    }, []);
    useEffect(() => {
        if (signPdfInfo !== null) {
            setModalType("preview");
            if (reformDetail !== undefined) {
                const type = signPdfInfo.name.includes("通知单") ? "Rectification-Notice" : "Rectification-Reply";
                savePdfFile(signPdfInfo.fileId, signPdfInfo.name, type);
            } else {
                getPreviewUrl({fileName: signPdfInfo.name, uuid: signPdfInfo.fileId}).then((previewUrl) => {
                    setPreviewInfoList([
                        {
                            previewUrl,
                            fileName: signPdfInfo.name,
                            fileId: signPdfInfo.fileId,
                            formName: "整改通知单",
                            formType: "Rectification-Notice"
                        }
                    ]);
                });
            }
        }
    }, [reformDetail, savePdfFile, signPdfInfo]);
    const changeTab = useCallback((key: string) => {
        setCurrentForm(key as RectificationType);
    }, []);
    const handleClose = useCallback(() => {
        if (modalType === "preview") {
            Modal.confirm({
                content: "请确认所有表单已经签名签章完成！",
                onOk: () => {
                    if (handleSubmit !== undefined) {
                        if (signPdfInfo !== null && launchReform !== undefined) {
                            handleSubmit(signPdfInfo);
                        } else if (launchReform !== undefined && (previewInfoList.length > 0 && previewInfoList[0].fileId !== undefined)) {
                            handleSubmit({
                                fileId: previewInfoList[0].fileId,
                                name: previewInfoList[0].fileName,
                                md5: "",
                                size: 0
                            });
                        } else {
                            handleSubmit();
                        }
                    }
                    setVisible(false);
                },
                centered: true
            });
        } else {
            setModalType("preview");
        }
    }, [handleSubmit, launchReform, modalType, previewInfoList, setVisible, signPdfInfo]);
    return (
        <>
            <Modal
                width="80vw"
                title={modalType === "preview" ? "表单预览" : "签名签章"}
                onCancel={handleClose}
                footer={null}
                visible={visible}
                destroyOnClose
            >
                {modalType === "preview" && (
                    <>
                        <div style={{display: "flex", justifyContent: "end"}}>
                            <Space>
                                <Button onClick={() => handleSign()} disabled={loading}>
                                    签名签章
                                </Button>
                            </Space>
                        </div>
                        <Divider style={{margin: "20px 0"}} />
                        {(previewInfoList.length === 0 || loading) && (
                            <div style={{width: "100%", height: "70vh"}}>
                                <Spin spinning={loading} />
                            </div>
                        )}
                        {!loading && (
                            <Tabs tabPosition="left" accessKey={currentForm} onTabClick={changeTab}>
                                {previewInfoList.map((item) => (
                                    <Tabs.TabPane tab={item.formName} key={item.formType}>
                                        <div style={{height: "70vh", width: "100%", overflowY: "scroll"}}>
                                            <iframe
                                                src={item.previewUrl}
                                                frameBorder="0"
                                                height="100%"
                                                width="100%"
                                            />
                                        </div>
                                    </Tabs.TabPane>
                                ))}
                            </Tabs>
                        )}
                    </>

                )}
                {modalType === "sign" && (
                    <div style={{height: "70vh", width: "100%"}}>
                        <iframe
                            id="sign-pdf"
                            name="sign-pdf"
                            src={signatureUrl}
                            width="100%"
                            height="100%"
                            onLoad={() => {
                                // console.log("onload");
                            }}
                        />
                    </div>
                )}
                <div style={{display: "none"}}>
                    {formInfoList.map((info) => {
                        if ((info.urls ?? []).length > 0) {
                            return info.urls.map((item, index) => (
                                <iframe
                                    id={`${info.type.split("-")[1]}${index}`}
                                    name={`${info.type.split("-")[1]}${index}`}
                                    src={item}
                                    width="100px"
                                    height="100px"
                                    onLoad={() => {
                                        setLoadCount((old) => old + 1);
                                    }}
                                />
                            ));
                        }
                        return null;
                    })}
                </div>
            </Modal>

        </>
    );
};

export default RectificationSheetSign;
