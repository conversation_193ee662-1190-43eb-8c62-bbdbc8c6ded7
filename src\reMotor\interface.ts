import Motor from "@motor/core";

export interface ProjectAction {
    type: "main" | "sub";
    behavior: "open" | "close";
    status: "begin" | "end";
    id: string;
}

export interface ComponentInfo {
    guid: string;
    name: string;
    projectGuid: string;
    bindIOTItemId?: string;
}

export interface PickInfo {
    pickObj?: Motor.PickObject;
    windowPosition: Motor.Vector2;
    position?: Motor.Vector3;
}

export interface ChoosedItemInfo {
    pickObj?: Motor.PickObject;
    chooseModel: boolean;
}

export interface ProjectListener {
    notifyProjectChanged(action: ProjectAction): boolean;
}

export interface ComponentListener {
    notifySelComponentChanged(component: ChoosedItemInfo): boolean;
}

export interface MouseEventListener {
    inputType: Motor.InputType;
    notifyMouseEvent(action: PickInfo): void;
}

export interface SimpleMotorElement {
    bimId?: string;
}

export interface BIMCompInfo {
    guid: string; // 唯一码
    name: string; // 名称
    path: string; // 路径
    pathWithoutMajor: string; // 不带专业的路径
    bimGuid: string; // 对应BIM的构件guid
    floor: string; // 楼层
    major: string; // 专业
    mainType: string; // 大类
    subType: string; // 小类
}

export interface DirTreeWithPath extends Motor.MotorCore.DirTree {
    path: string;
    children?: DirTreeWithPath[];
}

/** 相机的特定角度 */
// eslint-disable-next-line import/prefer-default-export
export enum CameraSpecAngle {
    Up,
    Down,
    Left,
    Right,
    Front,
    Behind,
    LeftUp,
    LeftDown,
    RgihtUp,
    RightDown
}
