// 246
const BASEURL = "http://**************:8182/gateway"; // 基地址
const DEVBASEURL = BASEURL;
const PRODUCTID = "192";
const WORKBENCHURL = "http://**************:8182/front/misc-web/#/"; // 工作台地址
// const MOTORFRAMEURL = "http://localhost:8290/"; // motor-frame地址
const MOTORFRAMEURL = "http://**************:8182/front/motor-frame/"; // motor-frame地址
const BASEPDS = "http://**************:8182/pds";
const SHELLDOWNURL = "http://down.lubansoft.com/extract_1.1.0_20210806.exe"; // 固定成正式服地址
const MOTORVIEWURL = "http://**************:8182/front/luban-bim-web/motorview"
window.embedPlatform = true; // 是否部署平台，false为独立部署
window._PLATFORM_ = "http://**************:8182/front/iworks-web-new"; // 数字平台访问地址， 当embedPlatform为false则不读取此数据
window.motorFrameUrl = MOTORFRAMEURL;
window.currentDomain = ""; // 暂时先不用，当前部署的域名地址，因为有时候部署在路径下，就会导致找不到文件资源

window.PlatformHeaderConfig = {
    baseUrl: BASEURL, // 接口请求地址
    token: localStorage.getItem("token"),
    productId: "192", // 当前产品id,如数字平台是192，用于判断跳转其它应用时是否需要换token(产品id为192-id类型的，设置成192)
    workbenchUrl: WORKBENCHURL, // 工作台地址
};

window.__ModelUrlConfig__ = {
    BaseUrl: BASEURL,
    // ${window.location.host}${window.location.pathname}
    MOTOR_BASE_URL: `${window.location.href.split("#")[0]}motorview/motor`,
}

window.__IWorksConfig__ = {
    baseUrl: `${BASEURL}`,
    devBaseUrl: `${DEVBASEURL}`,
    basePds: "",
    productId: PRODUCTID,
    basePds: `${BASEPDS}`,
    shellDownUrl: `${SHELLDOWNURL}`,
    motorViewUrl: `${MOTORVIEWURL}`,
    motorEditor: "",
    shellDownUrl: "",
    login: {
        backgroundLayout: "left",
        mode: "luban",
        layout: "center",
        accountRegular: /^[0-9a-zA-Z\u4e00-\u9fa5]+$/,
        accountErrorMsg: "用户名包含了空格， 请检查后重新输入",
        tabTitle: window.titleConfig?.planWeb,
        title: window.titleConfig?.planWeb,
        background: `no-repeat center/cover url(login/loginBg.png)`,
        titleLogo: "login/loginTitle.png",
        bgLogo: "login/bgLogo.png",
        logoMsg: "欢迎回来!",
        disableQuickLogin: true,
        disableSignUp: true,
        normalPlaceholder: ["请输入用户名", "手机号"],
        disableForgotPassword: true,
        disableInputIcon: true,
        // extraDownload: {
        //     name: "APP下载",
        //     items: [{
        //         image: "/assets/default_applogo1.png",
        //         title: "iworks",
        //         qrCode: {
        //             ios: "www.baidu.ios.com",
        //             android: "www.baidu.android.com"
        //         }
        //     }],
        // },
        extraStyle: {
            loginBox: {
                opacity: 1,
                borderRadius: 8
            },
            logoStyle: {
                margin: "40px 0",
                height: "30px"
            },
            logoMsgStyle: {
                color: "#000",
                fontWeight: "bold",
                fontSize: 18
            },
            backgroundLogoStyle: {
                width: 140
            }
        },
        setCompany: true,
        mainUrl: "/main",
        request: {
            signUp: "http://passport.luban.com/luban-pass/html/regist.html",
            forgotPassword: "http://passport.luban.com/luban-pass/html/username-find-pass.html",
            // forgotPassword: "http://localhost:8080/forgotPassword",
            login: {
                method: "POST",
                // url: "http://pdscenter.lubansoft.net/rs/tickets/tgt",
                url: `${BASEURL}/auth-server/auth/token`,
                extraData: {
                    loginType: "192", // 有无iworks-web 192的权限都能登录 并且跳转到多系统选择页面
                    md5: "96e79218965eb72c92a549dd5a330112",
                    // service: "http://center.lubansoft.net"
                },
                body: {
                    username: "username",
                    password: "password",
                    // md5: "password",
                    loginType: "loginType",
                },
                query: {
                    service: "service",
                },
                credentials: true,
            },
            getCompany: {
                method: "GET",
                url: `${BASEURL}/auth-server/auth/enterprises`,
                extraData: {
                    // md5: "96e79218965eb72c92a549dd5a330112",
                },
                header: {
                    loginResponse: "access-token"
                },
                // retJson: {
                //     "code": "code",
                //     "msg": "msg",
                //     "data.epid": "enterpriseId"
                // },
                credentials: true,
            },
            setCompany: {
                method: "PUT",
                url: `${BASEURL}/auth-server/auth/enterprise`,
                extraData: {
                    // md5: "96e79218965eb72c92a549dd5a330112",
                },
                header: {
                    loginResponse: "access-token"
                },
                body: {
                    "epid": "epid"
                },
                credentials: true
            }
        }
    }
}
