{
  "compilerOptions": {
    "target": "es2015",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "jsx": "react",
    "moduleResolution": "node",
    "module": "commonjs",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": "./",
    "composite": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noEmitOnError": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowUmdGlobalAccess": true,
    "incremental": true,
    "removeComments": true,
    "newLine": "lf",
    "typeRoots": [
      "./node_modules/@types",
      "./typings"
    ]
  },
  "baseUrl": ".",
  "paths": {
    "~/*": ["src/*"],
    "@/*": ["src/*"]
  },
  "include": [
    "./src/**/*.tsx",
    "./src/**/*.ts",
    "./uikit/**/*.tsx",
    "./uikit/**/*.ts"
  ],
  "exclude": [
    "dist",
    "node_modules",
    "**/*.spec.ts",
    "__tests__"
  ]
}
