import React, {use<PERSON><PERSON>back, useEffect, useMemo, useState} from "react";
import {useSelector} from "react-redux";
import moment, {isMoment, Moment} from "moment";
import {Button, Row, Space, Checkbox, List, notification, Popover, DatePicker, Col, message, Typography, Spin} from "antd";
import {CheckboxChangeEvent} from "antd/lib/checkbox";
import ProcessForm from "./ProcessForm";
import {CompStateParam, CompWithoutProcess, ProcessStatus} from "../../../../../api/processManager/type";
import {saveCompProcessList} from "../../../../../api/processManager";
import {RootState} from "../../../../../store/rootReducer";
import {isDefined} from "../../../../../assets/ts/utils";
import {getBIMCompInfo} from "../../../../../reMotor/utils";
import {ComStateWithProgress, getAllProcessList, getLatestFinishedProcess, getMaxAndMinDate, getProcessItemStatus, getProcessProgressInfo, isProcessFinished, isValidDate, validateCompProcess} from "../../helps";
import ProcessListItem from "./ProcessListItem";
import useStyle from "./style";
import {dispatch} from "../../../../../store";
import {setAnalyzedData} from "../../../../../store/sandAnalyzedData/action";
import {CehckeoutIcon} from "../../../../../assets/icons";

const {Text} = Typography;

// interface ProcessManageProps {
//     visible?: boolean;
//     onClose?: () => void;
// }

const getProcessDataByChecked = (data: CompStateParam[], checked: boolean) => {
    if (!checked) {
        return data;
    }
    return data.filter((v) => getProcessItemStatus(v) === ProcessStatus.Doing);
};
const setIsFinishedFn = (list: CompStateParam[]) => {
    if (list.length === 0) {
        return false;
    }
    return list.every((v) => isValidDate(v.lifeCycles.endDate) && isValidDate(v.lifeCycles.startDate));
};

const setCompFinishDateFn = (list: CompStateParam[], finishDate?: number) => {
    if (isValidDate(finishDate)) {
        return moment(finishDate);
    }
    if (list.length === 0) {
        return null;
    }
    if (list.every((v) => isProcessFinished(v))) {
        const allEndDate = list.map((v) => moment(v.lifeCycles.endDate));
        return moment.max(...allEndDate);
    }
    return null;
};

/**
 * 获取构件完工时间的逻辑
 * 若在工程设置-构件完工设置选择了【自动设置】，则自动读取最后完工工序的实际结束时间
 * 否则读取用户手动设置的构件完工时间
 * @param finishDate 用户手动设置的构件完工日期
 * @param autoFinish 构件是否自动完工：1：自动设置；0：手动设置
 * @param latestFinishedProcess 最后完工的工序，为空则表示存在未完工的工序
 */
const getCompFinishedDate = (
    finishDate: Moment | null,
    autoFinish?: 0 | 1,
    latestFinishedProcess?: CompStateParam
) => {
    if (!isDefined(latestFinishedProcess)) {
        return undefined;
    }
    if (autoFinish === 1) {
        return latestFinishedProcess.lifeCycles.endDate;
    }
    if (isMoment(finishDate)) {
        return finishDate.endOf("day").valueOf();
    }
    return undefined;
};

const addKey = "add";
const emptyProcessData: CompStateParam = {
    earlyWarn: true,
    lifeCycles: {
        planStartDate: 0,
        planEndDate: 0
    },
    stateColor: "",
    stateKey: addKey,
    stateName: "",
    statePath: ""
};
type ProcessActionType = "edit" | "add" | "delete";

// eslint-disable-next-line max-lines-per-function
const ProcessManage = () => {
    const cls = useStyle();
    const {curSandTable, curProjSettingInfo} = useSelector((state: RootState) => state.statusData);
    const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const {selectedComps} = useSelector((state: RootState) => state.processData);
    const [loading, setLoading] = useState(false);
    const [processData, setProcessData] = useState<ComStateWithProgress[]>([]);
    const [editProcess, setEditProcess] = useState<CompStateParam>();
    const [showDoingProcess, setShowDoingProcess] = useState<boolean>(false);
    const [isFinished, setIsFinished] = useState<boolean>(false); // 当前选中构件的工序是否已全部完工
    const [actionType, setActionType] = useState<ProcessActionType>();

    // 不能定义两个相同的工序，已经定义过的工序节点禁用
    const [tmplTreeDisabledKeys, setTmplTreeDisabledKeys] = useState<Set<string>>(new Set());
    const [compFinishDate, setCompFinishDate] = useState<Moment | null>(null); // 构件的完工时间
    const [compWithoutProcessInfo, setCompWithoutProcessInfo] = useState<CompWithoutProcess>();
    const [popoverVisible, setPopoverVisible] = useState<boolean>(false);

    const getProcessList = useCallback(
        async () => {
            setLoading(true);
            try {
                if (!isDefined(curSandTable)) {
                    return;
                }
                const easyCompList = selectedComps.map(getBIMCompInfo);
                const basePageParams = {
                    size: 5000,
                    ppid: curSandTable.ppid,
                    projectInfos: easyCompList.map((v) => ({handle: v.bimGuid, path: v.path, floor: v.floor}))
                };
                const allProcessList = await getAllProcessList(basePageParams);
                const {passed, processList, compInfo} = validateCompProcess(easyCompList.length, allProcessList);
                if (!passed) {
                    notification.error({
                        message: "操作失败！",
                        description: "请选择工序信息完全一致的部位进行批量定义工序操作"
                    });
                    return;
                }
                const tempSet: Set<string> = new Set();
                const tempProcessList: ComStateWithProgress[] = [];
                const minAndMaxDate = getMaxAndMinDate(processList);
                processList.forEach((v) => {
                    tempSet.add(v.stateKey);
                    const curProgressInfo = getProcessProgressInfo(v, minAndMaxDate);
                    tempProcessList.push({...v, progressInfo: curProgressInfo});
                });
                setProcessData(tempProcessList);
                setCompWithoutProcessInfo(compInfo);
                // 当且仅当所有构件的工序都完工时，才可设置构件的完工时间
                setIsFinished(setIsFinishedFn(processList));
                setEditProcess(undefined);
                setTmplTreeDisabledKeys(tempSet);
                setCompFinishDate(setCompFinishDateFn(processList, compInfo?.finishEndDate));
            } finally {
                setLoading(false);
            }
        },
        [curSandTable, selectedComps]
    );

    useEffect(
        () => {
            getProcessList();
        },
        [getProcessList]
    );

    const handleUpdateProcess = useCallback(
        (list: CompStateParam[], finishDate?: Moment | null) => {
            if (!isDefined(curSandTable)) {
                return;
            }
            const easyCompList = selectedComps.map(getBIMCompInfo);
            const latestFinishedProcess = getLatestFinishedProcess(list);
            saveCompProcessList({
                finishEndDate: getCompFinishedDate(finishDate ?? null, curProjSettingInfo?.autoFinish, latestFinishedProcess),
                finishStateKey: latestFinishedProcess?.stateKey,
                ppid: curSandTable.ppid,
                infos: list.length === 0 ? undefined : list,
                projectInfos: easyCompList.map((v) => ({handle: v.bimGuid, path: v.path, floor: v.floor}))
            }).then(() => {
                getProcessList().then(() => {
                    if (analyzedData.handler !== undefined && analyzedData.isReady === true) {
                        dispatch(setAnalyzedData({
                            isReady: false,
                            handler: analyzedData.handler
                        }));
                        // eslint-disable-next-line max-nested-callbacks
                        analyzedData.handler.refresh().then(() => {
                            dispatch(setAnalyzedData({
                                isReady: true,
                                handler: analyzedData.handler
                            }));
                        });
                    }
                });
            });
        },
        [analyzedData.handler, analyzedData.isReady, curProjSettingInfo, curSandTable, getProcessList, selectedComps]
    );

    /** 工序的新增、更新、删除统一处理 */
    const beforeUpdateProcess = useCallback(
        (item: CompStateParam, type?: ProcessActionType) => {
            const realAction = type ?? actionType;
            let tempProcessData: CompStateParam[] = [];
            if (realAction === "delete") {
                tempProcessData = processData.filter((v) => v.stateKey !== item.stateKey);
            }
            if (realAction === "add") {
                const filteredData = processData.filter((v) => v.stateKey !== addKey);
                tempProcessData = [item, ...filteredData];
            }
            if (realAction === "edit") {
                tempProcessData = [...processData];
                const itemIndex = tempProcessData.findIndex((v) => v.stateKey === item.stateKey);
                tempProcessData.splice(itemIndex, 1, item);
            }
            handleUpdateProcess(tempProcessData);
        },
        [handleUpdateProcess, processData, actionType]
    );

    const beforeAddProcess = useCallback(
        () => {
            setActionType("add");
            setEditProcess(emptyProcessData);
            setProcessData((prev) => [emptyProcessData, ...prev]);
        },
        []
    );

    const beforeEditProcess = useCallback(
        (item) => {
            setActionType("edit");
            setEditProcess(item);
        },
        []
    );

    const beforeDeleteProcess = useCallback(
        (item: CompStateParam) => {
            beforeUpdateProcess(item, "delete");
        },
        [beforeUpdateProcess]
    );

    const handleCancelProcessForm = useCallback(
        () => {
            if (actionType === "add") {
                setProcessData((prev) => prev.filter((v) => v.stateKey !== addKey));
            }
            setActionType(undefined);
            setEditProcess(undefined);
        },
        [actionType]
    );

    /** 构件完工 */
    const handleCompFinished = useCallback(
        () => {
            handleUpdateProcess(processData, compFinishDate);
            setPopoverVisible(false);
        },
        [compFinishDate, handleUpdateProcess, processData]
    );

    const handleCheckBoxChange = useCallback(
        (e: CheckboxChangeEvent) => {
            setShowDoingProcess(e.target.checked);
        },
        []
    );

    const handlePopverVisibleChange = useCallback(
        (newVisible: boolean) => {
            setPopoverVisible(newVisible);
        },
        []
    );

    /** 构件完工日期不得早于最后一道工序完成日期 */
    const disadledCompFinishDate = useCallback(
        (date: Moment) => {
            if (processData.length === 0) {
                return false;
            }
            const latestProcessEndDate = getLatestFinishedProcess(processData.filter((v) => v.stateKey !== addKey))?.lifeCycles?.endDate;
            if (isValidDate(latestProcessEndDate)) {
                return date.endOf("day") < moment(latestProcessEndDate);
            }
            // message.error("存在未完成的工序，无法定义构件完工日期！");
            return true;
        },
        [processData]
    );

    const handleUnfinishBtnClick = useCallback(
        () => {
            message.error("存在未完成工序，无法定义构件完工状态");
        },
        []
    );

    const renderListItem = useCallback(
        (item: CompStateParam) => {
            if (item.stateKey === editProcess?.stateKey) {
                return (
                    <ProcessForm
                        data={item.stateKey === addKey ? undefined : item}
                        onSubmit={beforeUpdateProcess}
                        onCalcel={handleCancelProcessForm}
                        tmplTreeDisabledKeys={tmplTreeDisabledKeys}
                    />
                );
            }
            return (
                <ProcessListItem
                    data={item}
                    onEdit={beforeEditProcess}
                    onDelete={beforeDeleteProcess}
                    isCompFinished={isValidDate(compWithoutProcessInfo?.finishEndDate)}
                />
            );
        },
        [
            editProcess,
            beforeEditProcess,
            beforeDeleteProcess,
            compWithoutProcessInfo,
            beforeUpdateProcess,
            handleCancelProcessForm,
            tmplTreeDisabledKeys
        ]
    );

    const finishSettingView = useMemo(
        () => {
            if (!isFinished || curProjSettingInfo?.autoFinish === 1) {
                return (
                    <Button
                        className={cls.disabledBtn}
                        style={{width: 112}}
                        onClick={handleUnfinishBtnClick}
                        disabled={curProjSettingInfo?.autoFinish === 1 || processData.length === 0}
                    >
                        完工
                    </Button>
                );
            }
            const content = (
                <div style={{width: 300}}>
                    <DatePicker
                        style={{width: "100%"}}
                        disabledDate={disadledCompFinishDate}
                        onChange={setCompFinishDate}
                        value={compFinishDate}
                        allowClear={false}
                    />
                    <Row style={{margin: "24px -16px -6px -16px"}}>
                        <Col span={12}>
                            <Button type="link" style={{width: "100%", fontSize: 14}} onClick={() => setPopoverVisible(false)}>
                                取消
                            </Button>
                        </Col>
                        <Col span={12}>
                            <Button type="link" style={{width: "100%", fontSize: 14}} onClick={handleCompFinished}>
                                确定
                            </Button>
                        </Col>
                    </Row>
                </div>
            );
            return (
                <Popover
                    content={content}
                    title={<Text strong>完工时间</Text>}
                    trigger="click"
                    visible={popoverVisible}
                    onVisibleChange={handlePopverVisibleChange}
                    overlayClassName={cls.popoverBox}
                >
                    <Button style={{width: 112, fontSize: 14}}>完工</Button>
                </Popover>
            );
        },
        [
            popoverVisible,
            cls.popoverBox,
            processData,
            cls.disabledBtn,
            isFinished,
            curProjSettingInfo,
            disadledCompFinishDate,
            handleCompFinished,
            handlePopverVisibleChange,
            compFinishDate,
            handleUnfinishBtnClick
        ]
    );

    const compFinishedView = useMemo(
        () => {
            if (isValidDate(compWithoutProcessInfo?.finishEndDate)) {
                return (
                    <Row align="middle" className={cls.comFinishBox}>
                        <div className={cls.checkoutIconBox}>
                            <CehckeoutIcon />
                        </div>
                        <Text strong style={{fontSize: 16, margin: "0 24px 0 12px"}}>
                            构件已完工
                        </Text>
                        <Text>
                            完工日期：
                            {moment(compWithoutProcessInfo?.finishEndDate).format("YYYY.MM.DD")}
                        </Text>
                    </Row>
                );
            }
            return null;
        },
        [cls, compWithoutProcessInfo]
    );

    return (
        <div className={cls.box}>
            <Spin spinning={loading}>
                <Row align="middle" justify="space-between" style={{marginBottom: 24}}>
                    <Space size={8}>
                        <Button
                            type="primary"
                            disabled={editProcess !== undefined}
                            onClick={beforeAddProcess}
                            style={{width: 112, fontSize: 14}}
                        >
                            添加工序
                        </Button>
                        {finishSettingView}
                    </Space>
                    <Checkbox
                        checked={showDoingProcess}
                        onChange={handleCheckBoxChange}
                    >
                        仅显示进行中的工序
                    </Checkbox>
                </Row>
                {compFinishedView}
                <List
                    itemLayout="vertical"
                    dataSource={getProcessDataByChecked(processData, showDoingProcess)}
                    split={false}
                    renderItem={(item) => (
                        <List.Item key={item.stateKey} className={cls.listItemBox}>
                            {renderListItem(item)}
                        </List.Item>
                    )}
                    className={cls.prosessListBox}
                />
            </Spin>
        </div>
    );
};

export default ProcessManage;
