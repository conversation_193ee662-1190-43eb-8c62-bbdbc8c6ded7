import React from "react";

export const ProcessManagementIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M12 27H8C4.7 27 2 24.3 2 21C2 17.7 4.7 15 8 15H13V17H8C5.8 17 4 18.8 4 21C4 23.2 5.8 25 8 25H12V27Z"
            fill="currentColor"
        />
        <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M26.6386 14H29.1908C29.705 13.1161 30 12.0908 30 11C30 7.7 27.3 5 24 5H4V7H24C26.2 7 28 8.8 28 11C28 12.1914 27.4721 13.2656 26.6386 14Z"
            fill="currentColor"
        />
        <path
            d="M30 23V21H27.9C27.7 20.1 27.4 19.3 26.9 18.5L28.4 17L27 15.6L25.5 17.1C24.8 16.6 23.9 16.2 23 16.1V14H21V16.1C20.1 16.3 19.3 16.6 18.5 17.1L17 15.6L15.6 17L17.1 18.5C16.6 19.2 16.2 20.1 16.1 21H14V23H16.1C16.3 23.9 16.6 24.7 17.1 25.5L15.6 27L17 28.4L18.5 26.9C19.2 27.4 20.1 27.8 21 27.9V30H23V27.9C23.9 27.7 24.7 27.4 25.5 26.9L27 28.4L28.4 27L26.9 25.5C27.4 24.8 27.8 23.9 27.9 23H30ZM22 26C19.8 26 18 24.2 18 22C18 19.8 19.8 18 22 18C24.2 18 26 19.8 26 22C26 24.2 24.2 26 22 26Z"
            fill="currentColor"
        />
    </svg>
);
export const ExitFullscreen = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22 4H20V12H28V10H22V4Z" fill="currentColor" />
        <path d="M10 4H12V12H4V10H10V4Z" fill="currentColor" />
        <path d="M12 20H4V22H10V28H12V20Z" fill="currentColor" />
        <path d="M20 20H28V22H22V28H20V20Z" fill="currentColor" />
    </svg>
);
export const ReplaceProjectIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M26 26H5.8L9.4 29.6L8 31L2 25L8 19L9.4 20.4L5.8 24H26V17H28V24C28 25.1 27.1 26 26 26Z" fill="black" />
        <path d="M22.6 11.6L26.2 8H6V15H4V8C4 6.9 4.9 6 6 6H26.2L22.6 2.4L24 1L30 7L24 13L22.6 11.6Z" fill="black" />
    </svg>
);
export const SceneEngineeringIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16 3.3L26.8 9.6V22.3L16 28.7L5.2 22.4V9.6L16 3.3ZM16 1C15.8 1 15.6 1 15.5 1.1L3.5 8.1C3.2 8.3 3 8.6 3 9V23C3 23.4 3.2 23.7 3.5 23.9L15.5 30.9C15.6 31 15.8 31 16 31C16.2 31 16.4 31 16.5 30.9L28.5 23.9C28.8 23.7 29 23.4 29 23V9C29 8.6 28.8 8.3 28.5 8.1L16.5 1.1C16.4 1 16.2 1 16 1Z" fill="#1F54C5" />
        <path d="M16 12L19.3 13.9V18L16 20L12.8 18V14L16 12ZM16 9.60001C15.9 9.60001 15.9 9.60001 15.8 9.70001L10.8 12.7C10.7 12.8 10.6 12.9 10.6 13.1V19.1C10.6 19.3 10.7 19.4 10.8 19.5L15.8 22.5C15.9 22.5 15.9 22.6 16 22.6C16.1 22.6 16.1 22.6 16.2 22.5L21.2 19.5C21.3 19.4 21.4 19.3 21.4 19.1V13.1C21.4 12.9 21.3 12.8 21.2 12.7L16.2 9.70001C16.1 9.60001 16.1 9.60001 16 9.60001Z" fill="#1F54C5" />
        <path d="M17.1 21.4H14.9V28.9H17.1V21.4Z" fill="#1F54C5" />
        <path d="M19.8 14.8L18.7 12.9L26.3 8.60001L27.4 10.5L19.8 14.8Z" fill="#1F54C5" />
        <path d="M12.2 14.8L4.59998 10.5L5.69998 8.60001L13.3 12.9L12.2 14.8Z" fill="#1F54C5" />
    </svg>
);
export const AlarmBubbleIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.87588 3.43467C7.74738 0.563163 12.403 0.563163 15.2745 3.43467C18.146 6.30617 18.146 10.9618 15.2745 13.8333L11.4894 17.6184C10.7084 18.3994 9.44203 18.3994 8.66098 17.6184L4.87588 13.8333C2.00438 10.9618 2.00438 6.30617 4.87588 3.43467Z" fill="currentColor" />
    </svg>
);
export const DefaultStatusIcon = () => (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="0.5" y="0.5" width="15" height="15" rx="1.5" fill="currentColor" stroke="#061127" />
    </svg>
);
export const ProjSettingsIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M27.5 8.15L15.5 1.15C15.2 0.95 14.8 0.95 14.5 1.15L2.5 8.15C2.2 8.35 2 8.65 2 9.05V23.05C2 23.45 2.2 23.75 2.5 23.95L14.5 30.95C14.8 31.15 15.2 31.15 15.5 30.95L16 30.6583V28.35V17V16.65L26 10.85V14H28V9.05C28 8.65 27.8 8.35 27.5 8.15ZM15 3.25L25 9.05L15 14.85L5 9.05L15 3.25ZM4 10.75L14 16.55V28.25L4 22.45V10.75Z" fill="currentColor" />
        <path d="M24 24.3C24.6 24.7 25.3 24.9 26 24.9C28.2 24.9 30 23.1 30 20.9C30 20.5 29.9 20.2 29.9 19.9L27.5 22.3C27.1 22.7 26.6 22.9 26 22.9C24.9 22.9 24 22 24 20.9C24 20.3 24.2 19.8 24.6 19.5L27 17.1C26.7 17 26.3 17 26 17C23.8 17 22 18.8 22 21C22 21.7 22.2 22.4 22.6 23L17 28.5L18.4 29.9L24 24.3Z" fill="currentColor" />
    </svg>
);

export const CehckeoutIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16 2C8.3 2 2 8.3 2 16C2 23.7 8.3 30 16 30C23.7 30 30 23.7 30 16C30 8.3 23.7 2 16 2ZM14 21.6L9 16.6L10.6 15L14 18.4L21.4 11L23 12.6L14 21.6Z" fill="currentColor" />
    </svg>
);

export const ResetCameraIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.0018 18.802C12.5651 18.802 11.2071 18.2849 10.1562 17.3416L9.07468 18.4231L8.74219 14.7674L12.3988 15.099L11.3313 16.1656C12.0803 16.8166 13.0209 17.1728 14.0009 17.1728C16.2278 17.1728 18.1091 15.2915 18.1091 13.0638C18.1091 12.6149 18.4748 12.2491 18.9237 12.2491C19.3699 12.2491 19.7366 12.6123 19.7409 13.0594C19.7409 14.616 19.1433 16.065 18.0592 17.1421C16.9812 18.2123 15.5401 18.802 14.0018 18.802Z" fill="currentColor" />
        <path d="M9.07549 13.8801C8.62661 13.8801 8.26086 13.5144 8.26086 13.0655C8.26086 11.5124 8.85849 10.0634 9.94261 8.98627C11.0206 7.91615 12.4617 7.32727 14 7.32727C15.4367 7.32727 16.7947 7.8444 17.8447 8.78765L18.9254 7.70702L19.2579 11.3628L15.603 11.0311L16.6687 9.96365C15.9197 9.31352 14.9791 8.95827 13.9991 8.95827C11.7714 8.95827 9.89011 10.8395 9.89011 13.0664C9.89011 13.5144 9.52436 13.8801 9.07549 13.8801Z" fill="currentColor" />
        <path d="M2.80088 23.8332C1.2565 23.8332 0 22.5767 0 21.0324V5.99987C0 4.4555 1.2565 3.199 2.80088 3.199H8.04388C8.19963 2.98112 8.34838 2.74925 8.49451 2.5235C9.22163 1.393 10.1168 0.002625 12.0094 0H15.9863C17.8833 0 18.7801 1.39388 19.5003 2.51388C19.6481 2.744 19.7995 2.9785 19.9561 3.199H25.1991C26.7426 3.199 28 4.4555 28 5.99987V21.0324C28 22.5767 26.7435 23.8332 25.1991 23.8332H2.80088ZM2.80088 5.36637C2.45175 5.36637 2.16738 5.65075 2.16738 5.99987V21.0341C2.16738 21.3832 2.45175 21.6667 2.80088 21.6667H25.1991C25.5483 21.6667 25.8326 21.3824 25.8326 21.0332V6.00075C25.8326 5.65163 25.5483 5.36725 25.1991 5.36725H19.621C19.1573 5.36725 18.7276 5.15812 18.4415 4.79325C18.1571 4.42925 17.9139 4.05387 17.6803 3.689C16.8954 2.471 16.6154 2.1665 15.9863 2.1665H12.0094C11.3873 2.1665 11.0906 2.48937 10.3224 3.68375C10.0695 4.07662 9.83413 4.4415 9.55588 4.79325C9.27063 5.15637 8.84189 5.3655 8.37814 5.3655H2.80088V5.36637Z" fill="currentColor" />
    </svg>
);

export const SingleChoiceIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.0876 7.25737H0.763941V5.43798H12.0782L12.0876 7.25737ZM23.3922 16.7522L0.763941 16.6879V14.8666H23.3922V16.7522ZM23.4016 26.1143H0.763941V24.2952H23.3922L23.4016 26.1143ZM16.5162 4.77133L19.6208 7.87601L26.4968 1L27.8801 2L19.1068 10.5L15.1829 6.1049L16.5162 4.77133Z" fill="currentColor" />
        <path d="M12.0876 7.25737H0V4.59811H12.0876V7.25737ZM0 16.6857V14.0267H23.4016L23.3922 16.3008V16.7522V14.8666H0.763941V16.6879L23.3922 16.7522L0 16.6857ZM23.4016 26.1143H0V23.4553H23.4016V26.1143ZM16.1389 3.77127L19.2436 6.87598L26.1195 0L28 1.88008L19.2433 10.6365L14.2588 5.65171L16.1389 3.77127ZM26.4968 1L27.8801 2L19.1068 10.5L15.1829 6.1049L16.5162 4.77133L19.6208 7.87601L26.4968 1ZM12.0876 7.25737L12.0782 5.43798H0.763941V7.25737H12.0876ZM23.4016 26.1143L23.3922 24.2952H0.763941V26.1143H23.4016Z" fill="currentColor" />
    </svg>
);

export const MultipleChoiceIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path opacity="0.7" d="M24.7779 6.02211H22.2593V3.22211C22.2593 1.54774 21.2744 0 19.4593 0H3.22211C1.53367 0 0 1.40703 0 3.22211V18.8965C0 20.5709 1.40704 22.1186 3.22211 22.1186H5.7407V24.7779C5.7407 26.4523 7.14774 28 8.96281 28H24.7779C26.4523 28 28 26.593 28 24.7779V9.10352C28 7.27437 26.607 6.02211 24.7779 6.02211ZM5.88141 9.10352L5.7407 20.7256H3.08141C2.09648 20.7256 1.81508 19.6 1.81508 18.6291V4.33367C1.81508 3.34874 2.23719 1.81508 3.22211 1.81508H19.0372C20.0221 1.81508 20.5709 2.37789 20.5709 3.48945V5.86734H9.24422C7.27437 6.16281 5.88141 7.27437 5.88141 9.10352ZM24.0744 25.9035H9.66633C9.18758 25.8951 8.73079 25.7011 8.39221 25.3626C8.05363 25.024 7.85969 24.5672 7.85126 24.0884V9.66633C7.85126 8.68141 8.69548 7.85126 9.66633 7.85126H24.0884C25.0734 7.85126 25.9035 8.69548 25.9035 9.66633V23.9477C25.9035 25.2 25.0593 25.9035 24.0744 25.9035ZM22.5407 12.7337C22.3857 12.5854 22.1895 12.4874 21.9779 12.4523C21.6965 12.4523 21.5558 12.593 21.4151 12.7337L15.2663 18.8965L12.4663 16.0965C12.3113 15.9482 12.1151 15.8502 11.9035 15.8151C11.6221 15.8151 11.4814 15.9558 11.3407 16.0965L10.9186 16.5186C10.7779 16.6593 10.6372 16.9407 10.6372 17.0814C10.6372 17.3628 10.7779 17.5035 10.9186 17.6442L14.5628 21.2884C14.8442 21.5698 15.2663 21.5698 15.5477 21.4291C15.6884 21.4291 15.9698 21.2884 16.1106 21.1477L23.1176 14.2814C23.2583 14.1407 23.399 13.8593 23.399 13.7186C23.399 13.4372 23.2583 13.2965 23.1176 13.1558L22.5407 12.7337Z" fill="currentColor" />
    </svg>
);

export const AllChoiceIcon = () => (
    <svg width="1em" height="1em" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.2766 19.6596H2.38297C1.06901 19.6596 0 18.5909 0 17.2766V2.38297C0 1.06871 1.06901 0 2.38297 0H17.2766C18.5906 0 19.6596 1.06874 19.6596 2.38297V5.3617C19.6596 6.01969 19.1261 6.55318 18.4681 6.55318C17.8101 6.55318 17.2766 6.01969 17.2766 5.3617V2.38297H2.38297V17.2766H17.2766V13.7022C17.2766 13.0442 17.8101 12.5107 18.4681 12.5107C19.1261 12.5107 19.6596 13.0442 19.6596 13.7022V17.2766C19.6596 18.5909 18.5906 19.6596 17.2766 19.6596Z" fill="currentColor" />
        <path d="M25.6171 28H10.7234C9.40947 28 8.34045 26.9313 8.34045 25.6171V22.0426C8.34045 21.3846 8.87395 20.8511 9.53194 20.8511C10.1899 20.8511 10.7234 21.3846 10.7234 22.0426V25.6171H25.6171V10.7234H10.7234V13.7022C10.7234 14.3601 10.1899 14.8936 9.53194 14.8936C8.87395 14.8936 8.34045 14.3601 8.34045 13.7022V10.7234C8.34045 9.40916 9.40947 8.34045 10.7234 8.34045H25.6171C26.931 8.34045 28 9.4092 28 10.7234V25.6171C28 26.9313 26.931 28 25.6171 28Z" fill="currentColor" />
    </svg>
);
