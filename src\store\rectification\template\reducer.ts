import {Reducer} from "redux";
import {RectifyQueryFormModel} from "../../../views/Rectification/interface";
import Actions from "./actions";

export interface RectificationTempModel {
    /** 当前审批模板id */
    currentTempId: string;
    rectifyStatus: "list" | "add" | "edit";
    /** 当前整改实例id */
    currentReformId: string;
    rectifyQueryOptions?: RectifyQueryFormModel & {processType?: number};
}

export const initialState: RectificationTempModel = {
    currentTempId: "",
    rectifyStatus: "list",
    currentReformId: ""
};

const rectificationTempReducer: Reducer<RectificationTempModel, Actions> = (state = initialState, action) => {
    switch (action.type) {
        case "SET_CURRENT_TEMP_ID":
            return {...state, currentTempId: action.payload};
        case "SET_RECTIFY_STATUS":
            return {...state, rectifyStatus: action.payload};
        case "SET_CURRENT_REFORM_ID":
            return {...state, currentReformId: action.payload};
        case "SET_RECTIFY_QUERY_OPTIONS":
            return {...state, rectifyQueryOptions: action.payload};
        default:
            return {...state};
    }
};
export default rectificationTempReducer;
