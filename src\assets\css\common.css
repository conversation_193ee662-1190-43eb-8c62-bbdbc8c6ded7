@import url("./color.css");
@import url("../../../uikit/assest/css/standard.css");
@import url("../../../uikit/assest/css/common.css");
@font-face {
    font-family: "DINAlternateBold";
    src: url("../../../public/DIN\ Alternate\ Bold.ttf");
}
@font-face {
    font-family: "DINRegular";
    src: url("../../../public/DIN-Regular.otf");
}
* {
    font-family: Microsoft YaHei, Source Han Sans CN;
}
ul,
li {
    list-style: none;
    margin: 0;
    padding: 0;
}
.box {
    width: 100%;
    height: 100%;
}
.title {
    font-weight: 700;
    font-size: 18px;
    color: var(--text-1);
}
/* 标题 */
.title12 {
    font-weight: 700;
    font-size: 12px;
    color: var(--text-1);
}
.title14 {
    font-weight: 700;
    font-size: 14px;
    color: var(--text-1);
}
.title16 {
    font-weight: 700;
    font-size: 16px;
    color: var(--text-1);
}
.title18 {
    font-weight: 700;
    font-size: 18px;
    color: var(--text-1);
}
/* 文本 */
.text12 {
    font-weight: 400;
    font-size: 12px;
    color: var(--text-2);
}
.text14 {
    font-weight: 400;
    font-size: 14px;
    color: var(--text-2);
}
.text16 {
    font-weight: 400;
    font-size: 16px;
    color: var(--text-2);
}
/* 注释 */
.note12 {
    font-weight: 400;
    font-size: 12px;
    color: var(--text-3);
}
.note14 {
    font-weight: 400;
    font-size: 14px;
    color: var(--text-3);
}
.note14 {
    font-weight: 400;
    font-size: 14px;
}
/* 提示 */
.prompt12 {
    font-weight: 400;
    font-size: 12px;
    color: var(--text-4);
}
.prompt14 {
    font-weight: 400;
    font-size: 14px;
    color: var(--text-4);
}
.contentBox {
    padding: 24px 24px;
}
.center {
    display: flex;
    align-items: center;
    justify-content: center;
}
.centerY {
    display: flex;
    align-items: center;
}
.centerX {
    display: flex;
    justify-content: center;
}
@media print {
    .noprint {
        visibility: hidden;
    }
}
.noScrollBar::-webkit-scrollbar {
    display: none;
}
*::-webkit-scrollbar-track {
    background-color: #fff;
}
*::-webkit-scrollbar {
    width: 8px;
    height: 6px;
}
*::-webkit-scrollbar-thumb {
    background-color: #f2f3f5;
    border-radius: 8px;
}
.shadow0 {
    border: 1px solid #dde2ee;
    box-sizing: border-box;
    border-radius: 4px;
}
.shadow1 {
    border: 1px solid #dde2ee;
    box-sizing: border-box;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}
.shadow2 {
    border: 1px solid #dde2ee;
    box-sizing: border-box;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}
.shadow3 {
    border: 1px solid #dde2ee;
    box-sizing: border-box;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

.motor-frame {
    width: 100%;
    height: 100%;
    border: none
}

.totalBox {
    position: relative;
}
.totalBox::after {
    position: absolute;
    display: inline-block;
    width: 100%;
    height: 40px;
    top: 40px;
    left: 0;
    border: 1px solid red;
    content: attr(total-num);
}
div[class=gantt_grid_data]:last-child div[data-column-name=start_date] {
    overflow: inherit;
}
.lb-iworks-frame-wrapper{
    min-width:1226px !important;
}
