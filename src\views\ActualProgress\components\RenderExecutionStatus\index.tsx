import React from "react";
import MyIcon from "../../../../components/MyIcon";

interface Props {
    text: string;
    value: number;
}

const RenderExecutionStatus = (props: Props) => {
    const {text, value} = props;
    if (value === 2) {
        // 已完成
        return (
            <div style={{padding: "3px 4px", backgroundColor: "#EBFAED", color: "#0F3916", width: "fit-content"}}>
                <MyIcon type="icon-hegemianxing" style={{color: "#2DA641", marginRight: 4}} />
                {text}
            </div>
        );
    }
    if (value === 1) {
        // 进行中
        return (
            <div style={{padding: "3px 4px", backgroundColor: "#E9EFFC", color: "#0A1C42", width: "fit-content"}}>
                <MyIcon type="icon-jinhangzhong" style={{color: "#1F54C5", marginRight: 4}} />
                {text}
            </div>
        );
    }
    // 未开始
    return (
        <div style={{padding: "3px 4px", backgroundColor: "#F5F5F6", color: "#717784", width: "fit-content"}}>
            <MyIcon type="icon-jinhangzhong" style={{color: "#717784", marginRight: 4}} />
            {text}
        </div>
    );
};

export default RenderExecutionStatus;
