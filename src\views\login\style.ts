import {createUseStyles} from "react-jss";

const modelStyle = {
    tab: {
        "& .ant-tabs-nav-list": {
            display: "flex",
            width: "100%",
            padding: "0 1px 0 0",
        },
        "& .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab": {
            fontSize: "16px"
        },
        "& .ant-btn-text": {
            background: "#3b6acc !important",
        },
        "& .ant-btn-primary": {
            background: "#1f54c5 !important",
            borderColor: "#1f54c5 !important",
        },
        "& .ant-btn-primary:hover, & .ant-btn-primary:focus": {
            background: "#7c82b8",
            borderColor: "#7c82b8"
        },
        "& .ant-btn-primary:active": {
            background: "#494f85",
            borderColor: "#494f85"
        },
        "& #normalLogin a": {
            color: "#1f54c5",
        },
        "& .ant-input,& .ant-input-password, & .ant-form-item-has-error .ant-input:not(.ant-form-item-has-error .ant-input-disabled)": {
            background: "#f7f8fa"
        },
    },
    loading: {
        position: "absolute",
        top: "0",
        left: "0",
        width: "100%",
        height: "100%",
        zIndex: "11"
    }
};

const useLoginStyle = createUseStyles(modelStyle);

export default useLoginStyle;
