import {DownOutlined, UpOutlined} from "@ant-design/icons";
import {Button, Col, Form, FormProps, Row, Space} from "antd";
import {FormInstance, useForm} from "antd/lib/form/Form";
import React, {CSSProperties, useState} from "react";
import {createUseStyles} from "react-jss";
import ComFormItem, {ComFormItemProps} from "../FormItem";

interface QueryFormProps<T> {
    formConfig?: FormProps<T>;
    onFormFinish?: (values: T) => void;
    onFormValuesChange?: FormProps["onValuesChange"];
    onFormClear?: () => void;
    queryItemList: ComFormItemProps[];
    form?: FormInstance;
    labelMaxWidth?: number;
    style?: CSSProperties;
}

const useStyles = createUseStyles({
    queryItem: {
        marginBottom: 16,
    }
});

const QueryForm = <T extends unknown>(props: QueryFormProps<T>) => {
    const cls = useStyles();
    const [defaultForm] = useForm();
    const [isExpand, setIsExpand] = useState<boolean>(false);
    const {
        formConfig = {},
        onFormFinish,
        onFormValuesChange,
        queryItemList,
        onFormClear,
        form = defaultForm,
        labelMaxWidth = 100,
        style
    } = props;

    const queryItemListData = queryItemList.filter((item) => item.isDisplay !== false);

    const clearForm = () => {
        form.resetFields();
        if (onFormClear !== undefined) {
            onFormClear();
        }
    };

    const handleIsExand = () => {
        setIsExpand(!isExpand);
    };

    const dealQueryItemList = () => {
        if (!isExpand) {
            return queryItemListData.slice(0, 3);
        }
        return queryItemListData;
    };

    const colPush = () => {
        if (queryItemListData.length > 3) {
            return isExpand ? (4 - (queryItemListData.length % 4) - 1) * 6 : 0;
        }
        return (4 - (queryItemListData.length % 4) - 1) * 6;
    };

    return (
        <Form
            style={{paddingTop: 24, paddingBottom: 16, ...style}}
            onFinish={onFormFinish}
            onValuesChange={onFormValuesChange}
            form={form}
            {...formConfig}
        >
            <Row>
                {dealQueryItemList().map((item: ComFormItemProps) => {
                    if (item.type === "select") {
                        return (
                            <ComFormItem
                                className={cls.queryItem}
                                labelMaxWidth={labelMaxWidth}
                                {...item}
                                typeConfig={{placeholder: "全部", ...item.typeConfig}}
                            />
                        );
                    }
                    return (
                        <ComFormItem
                            className={cls.queryItem}
                            labelMaxWidth={labelMaxWidth}
                            {...item}
                        />
                    );
                })}
                <Col span={6} push={colPush()} style={{textAlign: "right"}}>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            搜索
                        </Button>
                        <Button onClick={clearForm}>重置</Button>
                        {queryItemListData.length > 3 && (
                            <Button
                                onClick={handleIsExand}
                                type="link"
                            >
                                {isExpand === true ? "收起" : "展开"}
                                {isExpand === true ? <UpOutlined /> : <DownOutlined />}
                            </Button>
                        )}
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};

export default QueryForm;
