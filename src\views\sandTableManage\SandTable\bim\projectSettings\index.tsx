import {Checkbox, DatePicker, Form, Row, Space, Spin, Switch, Typography, message} from "antd";
import React, {memo, useCallback, useEffect, useState} from "react";
import moment, {Moment} from "moment";
import {useSelector, useDispatch} from "react-redux";
import ComModal from "../../../../../components/ComModal";
import useStyle from "./style";
import {RootState} from "../../../../../store/rootReducer";
import {getProjectDuration, saveProjectDuration} from "../../../../../api/sandManager";
import {getDeptDetail, getDeptHouseDetail} from "../../../../../api/common.api";
import {setCurProjSettingInfo} from "../../../../../store/status/action";
import {isDefined} from "../../../../../assets/ts/utils";

const {Text} = Typography;

interface ProjectSettingsProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    onOk: () => void;
}
interface ProjSettingFormType {
    planStartDate: Moment;
    planEndDate: Moment;
    warningNotice: boolean;
    completeSettings: "auto" | "manual";
}
export interface PostProjectSettingsParams {
    planStartDate: number;
    planCompleteDate: number;
    completeSettings: string;
    warningNotice: string;
}

const ProjectSettings = (props: ProjectSettingsProps) => {
    const {visible, setVisible, onOk} = props;
    const cls = useStyle();
    const dispatch = useDispatch();
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {orgInfo} = useSelector((state: RootState) => state.commonData);

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [isAutoAcquisition, setIsAutoAcquisition] = useState(false);
    useEffect(() => {
        if (!visible) {
            form.resetFields();
        }
    }, [form, visible]);

    const getProjDuration = useCallback(() => {
        getProjectDuration(curSandTable?.ppid ?? 0).then((res) => {
            const {data} = res;
            if (data === null || data === undefined) {
                return;
            }
            form.setFieldsValue({
                planStartDate: moment(data.planStartDate),
                planEndDate: moment(data.planEndDate),
                warningNotice: data.warnStatus === 1,
                completeSettings: "auto"
            });
        });
    }, [curSandTable, form]);
    useEffect(() => {
        if (visible) {
            getProjDuration();
        }
    }, [getProjDuration, visible]);
    const onAutoAcquisitionChange = useCallback((e) => {
        if (e.target.checked as boolean) {
            if (orgInfo.deptDataType === 2) {
                getDeptDetail(orgInfo.orgId).then((res) => {
                    const {result} = res;
                    if (result.startDate === null || result.endDate === null) {
                        message.warning("未读取到数据，请手动填写开工日期、竣工日期！");
                        return;
                    }
                    setIsAutoAcquisition(e.target.checked);
                    form.setFieldsValue({
                        planStartDate: moment(result.startDate),
                        planEndDate: moment(result.endDate)
                    });
                }).catch((err) => {
                    console.log(err);
                });
            }
            if (orgInfo.deptDataType === 1) {
                getDeptHouseDetail(orgInfo.orgId).then((res) => {
                    const {result} = res;
                    if (!isDefined(result.org)) {
                        message.warning("未读取到数据，请手动填写开工日期、竣工日期！");
                        return;
                    }
                    const {startDate, endDate} = result.org;
                    if (startDate === null || endDate === null) {
                        message.warning("未读取到数据，请手动填写开工日期、竣工日期！");
                        return;
                    }
                    setIsAutoAcquisition(e.target.checked);
                    form.setFieldsValue({
                        planStartDate: moment(startDate),
                        planEndDate: moment(endDate)
                    });
                }).catch((err) => {
                    console.log(err);
                });
            }
        } else {
            setIsAutoAcquisition(e.target.checked);
        }
    }, [form, orgInfo.deptDataType, orgInfo.orgId]);
    const handleOk = useCallback(() => {
        setLoading(true);
        form.validateFields()
            .then((values: ProjSettingFormType) => {
                saveProjectDuration({
                    planStartDate: values.planStartDate.startOf("day").valueOf(),
                    planEndDate: values.planEndDate.endOf("day").valueOf(),
                    ppid: curSandTable?.ppid ?? 0,
                    warnStatus: values.warningNotice ? 1 : 0,
                    autoFinish: 1
                }).then(() => {
                    // eslint-disable-next-line max-nested-callbacks
                    getProjectDuration(curSandTable?.ppid ?? 0).then((res) => {
                        dispatch(setCurProjSettingInfo(res.data));
                    });
                    if (onOk instanceof Function) {
                        onOk();
                    }
                });
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [curSandTable, dispatch, form, onOk]);

    return (
        <ComModal
            title="工程设置"
            visible={visible}
            onCancel={() => setVisible(false)}
            onOk={handleOk}
            destroyOnClose
        >
            <Spin spinning={loading}>
                <div style={{padding: "0px 16px"}}>
                    <Row className={cls.bg} style={{height: 56, padding: "0px 16px"}} justify="space-between" align="middle">
                        <Space>
                            <div className={cls.autoFetch} />
                            <Text style={{
                                fontWeight: 700,
                                fontSize: "14px",
                                lineHeight: "20px",
                                color: "#33394D",
                            }}
                            >
                                {"自动获取计划开工日期、竣工日期"}
                            </Text>
                        </Space>
                        <Checkbox onChange={onAutoAcquisitionChange} checked={isAutoAcquisition} />
                    </Row>
                </div>
                <Form
                    form={form}
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                    style={{padding: "24px 54px"}}
                >
                    <Form.Item
                        label="计划开工日期"
                        labelAlign="left"
                        colon={false}
                        name="planStartDate"
                        rules={[
                            {
                                required: true,
                                message: "请输入开工日期"
                            }
                        ]}
                    >
                        <DatePicker allowClear style={{width: "100%"}} format="YYYY.MM.DD" disabled={isAutoAcquisition} />
                    </Form.Item>
                    <Form.Item
                        label="计划竣工日期"
                        labelAlign="left"
                        colon={false}
                        name="planEndDate"
                        rules={[
                            {
                                required: true,
                                message: "请输入竣工日期"
                            }
                        ]}
                    >
                        <DatePicker
                            allowClear
                            style={{width: "100%"}}
                            format="YYYY.MM.DD"
                            disabled={isAutoAcquisition}
                            disabledDate={(cur: Moment) => {
                                const planStartDate = form.getFieldValue(["planStartDate"]);
                                if (Boolean(planStartDate) === true) {
                                    return cur < planStartDate;
                                }
                                return false;
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="报警通知"
                        labelAlign="left"
                        colon={false}
                        name="warningNotice"
                        valuePropName="checked"
                    >
                        <Switch />
                    </Form.Item>
                </Form>
            </Spin>
        </ComModal>
    );
};

export default memo(ProjectSettings);
