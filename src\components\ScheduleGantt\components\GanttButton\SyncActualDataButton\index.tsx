import {<PERSON><PERSON>, Popconfirm} from "antd";
import React, {useCallback, useContext, useState} from "react";
import MyIconFont from "../../../../MyIconFont";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";

const SyncActualDataButton = () => {
    const cls = useStyles();
    const {checkoutStatus, updatePlanTaskMethodRef, doSyncProcess} = useContext(EditorContext);
    const [syncLoading, setSyncLoading] = useState(false);

    // syncType: 1 | 2 同步类型：1-全覆盖同步，2-仅同步未修改过的
    const handleSyncProcess = useCallback(async (syncType: 1 | 2) => {
        console.log("同步质量验评 syncType", syncType);
        setSyncLoading(true);
        try {
            if (syncType === 2 && checkoutStatus === true) {
                // 如果仅同步未修改过的，需要进行一次保存操作
                if (updatePlanTaskMethodRef.current !== undefined) {
                    await updatePlanTaskMethodRef.current();
                }
            }
            await doSyncProcess(syncType);
        } catch (error) {
            console.log("error", error);
        } finally {
            setSyncLoading(false);
        }
    }, [checkoutStatus, doSyncProcess, updatePlanTaskMethodRef]);

    return (
        <Popconfirm
            title="是否覆盖手动修改的数据？"
            okText="是"
            cancelText="否"
            onConfirm={async () => handleSyncProcess(1)}
            onCancel={async () => handleSyncProcess(2)}
        >
            <Button
                className={cls.textButton}
                type="text"
                icon={<MyIconFont type="icon-shuaxin" fontSize={18} />}
                loading={syncLoading}
            >
                同步质量验评
            </Button>
        </Popconfirm>
    );
};

export default SyncActualDataButton;
