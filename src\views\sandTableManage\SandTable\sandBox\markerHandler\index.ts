import Motor from "@motor/core";
import {isEmpty, isNotNullOrUndefined} from "../../../../../assets/ts/utils";

import MotorMarkerHandler, {MotorCartographic} from "../../../../../assets/ts/graphUtils/MotorMarkerHandler";
import MotorUtils from "../../../../../assets/ts/graphUtils/MotorUtils";
import {MotorContext} from "../../../../../reMotor";
import HtmlMarkerInMotorViewerMgr, {MarkerTipWithIdRender} from "../../modelView/HtmlMarker/HtmlMarkerInMotorViewerMgr";

import MarkerTipView from "../MarkerTip";
import MarkerTipMgr from "../MarkerTip/MarkerTipMgr";
import {BindBimInfoWithDetail} from "./dataOrInterface";
import {SandDataInitializeBase} from "../handler/dataOrInterface";

export interface WarningMarkerItem extends MotorCartographic {
    detail: BindBimInfoWithDetail;
    tip: string;
}

const WarningMarkerTypeStr = "WarningMarker";

class WarningMarkerHandler {
    private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    private markerHandler: MotorMarkerHandler = new MotorMarkerHandler(WarningMarkerTypeStr);

    private itemList: WarningMarkerItem[] = [];

    private mapBoxCenter: Map<string, Motor.Vector3> = new Map();

    private isDestroyed = false;

    private isFilterActual = false;

    constructor(isFilterActual: boolean) {
        this.bimProject = MotorContext.getCurBIMProject();
        this.isFilterActual = isFilterActual;
    }

    public showWarningMarkerByDate(curTime: Date): void {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const warningFilter = dataContainer.getFilterInfo("warn");
            const ebsFilter = dataContainer.getFilterInfo("ebs");
            if ((!warningFilter.isCheckAll && warningFilter.setCheckedKeys.size === 0)
             || (!ebsFilter.isCheckAll && ebsFilter.setCheckedKeys.size === 0)) {
                return;
            }

            const queryItem = dataContainer.queryWarningListByTime(curTime);
            let addDataItems: BindBimInfoWithDetail[] = [];
            let removeDataItems: BindBimInfoWithDetail[] = [];
            if (typeof queryItem !== "undefined") {
                removeDataItems = queryItem.disappearedCompList.map((el) => ({
                    compKey: el,
                    warnId: "",
                }));

                if (this.isFilterActual) {
                    const findRes = dataContainer.queryAccumulatedCompListByActualTime(curTime);
                    if (typeof findRes !== "undefined") {
                        let compKeySet = new Set<string>();
                        if (!ebsFilter.isCheckAll) {
                            compKeySet = new Set<string>(findRes.filter((el) => ebsFilter.setCheckedKeys.has(dataContainer.getPathFromCompKey(el) ?? "")).map((el) => el));
                        } else {
                            compKeySet = new Set<string>(findRes);
                        }

                        queryItem.warningType1CompList = queryItem.warningType1CompList.filter((x) => compKeySet.has(x));
                        queryItem.warningType2CompList = queryItem.warningType2CompList.filter((x) => compKeySet.has(x));
                        queryItem.warningType3CompList = queryItem.warningType3CompList.filter((x) => compKeySet.has(x));
                        queryItem.warningType4CompList = queryItem.warningType4CompList.filter((x) => compKeySet.has(x));
                    }
                } else if (!ebsFilter.isCheckAll) {
                    const keyList = queryItem.warningType1CompList.concat(queryItem.warningType2CompList
                        .concat(queryItem.warningType3CompList).concat(queryItem.warningType4CompList));
                    const compKeySet = new Set<string>(keyList.filter((el) => ebsFilter.setCheckedKeys.has(dataContainer.getPathFromCompKey(el) ?? "")));
                    queryItem.warningType1CompList = queryItem.warningType1CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType2CompList = queryItem.warningType2CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType3CompList = queryItem.warningType3CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType4CompList = queryItem.warningType4CompList.filter((x) => compKeySet.has(x));
                }

                const warnId1 = dataContainer.getWarnIdByType(1) ?? "";
                const items1 = queryItem.warningType1CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId1,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId1)) {
                    addDataItems = addDataItems.concat(items1);
                } else {
                    removeDataItems = removeDataItems.concat(items1);
                }

                const warnId2 = dataContainer.getWarnIdByType(2) ?? "";
                const items2 = queryItem.warningType2CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId2,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId2)) {
                    addDataItems = addDataItems.concat(items2);
                } else {
                    removeDataItems = removeDataItems.concat(items2);
                }

                const warnId3 = dataContainer.getWarnIdByType(3) ?? "";
                const items3 = queryItem.warningType3CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId3,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId3)) {
                    addDataItems = addDataItems.concat(items3);
                } else {
                    removeDataItems = removeDataItems.concat(items3);
                }

                const warnId4 = dataContainer.getWarnIdByType(4) ?? "";
                const items4 = queryItem.warningType4CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId4,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId4)) {
                    addDataItems = addDataItems.concat(items4);
                } else {
                    removeDataItems = removeDataItems.concat(items4);
                }

                this.removeMarkersForCompList(removeDataItems);
                this.addMarkersForCompList(addDataItems);
            }
        }
    }

    public jumpToDate(curTime: Date): void {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const warningFilter = dataContainer.getFilterInfo("warn");
            const ebsFilter = dataContainer.getFilterInfo("ebs");
            if ((!warningFilter.isCheckAll && warningFilter.setCheckedKeys.size === 0)
             || (!ebsFilter.isCheckAll && ebsFilter.setCheckedKeys.size === 0)) {
                return;
            }

            const queryItem = dataContainer.queryAccumulatedWarningListByTime(curTime);

            let dataItems: BindBimInfoWithDetail[] = [];
            if (typeof queryItem !== "undefined") {
                if (!ebsFilter.isCheckAll) {
                    const keyList = queryItem.warningType1CompList.concat(queryItem.warningType2CompList
                        .concat(queryItem.warningType3CompList).concat(queryItem.warningType4CompList));
                    const compKeySet = new Set<string>(keyList.filter((el) => ebsFilter.setCheckedKeys.has(dataContainer.getPathFromCompKey(el) ?? "")));
                    queryItem.warningType1CompList = queryItem.warningType1CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType2CompList = queryItem.warningType2CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType3CompList = queryItem.warningType3CompList.filter((x) => compKeySet.has(x));
                    queryItem.warningType4CompList = queryItem.warningType4CompList.filter((x) => compKeySet.has(x));
                }

                const warnId1 = dataContainer.getWarnIdByType(1) ?? "";
                const items1 = queryItem.warningType1CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId1,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId1)) {
                    dataItems = dataItems.concat(items1);
                }

                const warnId2 = dataContainer.getWarnIdByType(2) ?? "";
                const items2 = queryItem.warningType2CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId2,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId2)) {
                    dataItems = dataItems.concat(items2);
                }

                const warnId3 = dataContainer.getWarnIdByType(3) ?? "";
                const items3 = queryItem.warningType3CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId3,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId3)) {
                    dataItems = dataItems.concat(items3);
                }

                const warnId4 = dataContainer.getWarnIdByType(4) ?? "";
                const items4 = queryItem.warningType4CompList.map((el) => ({
                    compKey: el,
                    warnId: warnId4,
                }));
                if (warningFilter.isCheckAll || warningFilter.setCheckedKeys.has(warnId4)) {
                    dataItems = dataItems.concat(items4);
                }
            }
            this.initHandlers(dataItems);
        }
    }

    public init(dataContainer: SandDataInitializeBase): void {
        this.dataContainer = dataContainer;
        this.reset();
    }

    public reset(): void {
        this.bimProject = MotorContext.getCurBIMProject();
        this.isDestroyed = false;
        this.markerHandler.init();
        this.itemList = [];
        MarkerTipMgr.clear();
        HtmlMarkerInMotorViewerMgr.removeMarkerTipRender(WarningMarkerTypeStr);
    }

    public unInit(): void {
        this.markerHandler.unInit();
        this.isDestroyed = true;
        MarkerTipMgr.clear();
        HtmlMarkerInMotorViewerMgr.removeMarkerTipRender(WarningMarkerTypeStr);
    }

    private addMarkerList(itemList: WarningMarkerItem[]) {
        const curItemList = this.itemList.concat(itemList);
        this.resetMarkerList(curItemList);
    }

    private removeMarkersForCompList(dataList: BindBimInfoWithDetail[]) {
        if (dataList.length > 0) {
            const {itemList} = this;
            const filterItems = itemList.filter((x) => !dataList.some((item) => x.detail.compKey === item.compKey));
            this.resetMarkerList(filterItems);
        }
    }

    private addMarkersForCompList(dataList: BindBimInfoWithDetail[]) {
        if (dataList.length > 0) {
            this.addCommonDataListForComp(dataList);
        }
    }

    private initHandlers(dataList: BindBimInfoWithDetail[]) {
        this.markerHandler.reset();
        if (dataList.length > 0) {
            this.setCommonDataListForComp(dataList);
        }
    }

    private resetMarkerList(itemList: WarningMarkerItem[]) {
        this.itemList = itemList;
        this.markerHandler.reset();
        if (!this.isDestroyed && Boolean(itemList.length)) {
            this.markerHandler.batchAddMarkerCartographic(itemList);
        }

        itemList.map((el) => MarkerTipMgr.addTipInfo(el.id, el.tip));

        const renderHandler: MarkerTipWithIdRender = {
            renderType: WarningMarkerTypeStr,
            idList: itemList.map((el) => el.id),
            markerHandler: this.markerHandler,
            renderInstance: MarkerTipView
        };
        HtmlMarkerInMotorViewerMgr.resetMarkerTipRender(renderHandler);
    }

    private async setCommonDataListForComp(dataList: BindBimInfoWithDetail[]) {
        const project = MotorContext.getProject();
        const {bimProject, dataContainer, mapBoxCenter} = this;
        if (isNotNullOrUndefined(bimProject) && isNotNullOrUndefined(project) && isNotNullOrUndefined(dataContainer)) {
            const compKeyList = dataList.map((el) => el.compKey);
            const mapInfo: Map<string, BindBimInfoWithDetail> = new Map(dataList.map((el) => [dataContainer.getHandleFromCompKey(el.compKey) ?? "", el]));
            const comps = dataContainer.queryComponentsByCompKeyFromCache(compKeyList);
            let markerResult: WarningMarkerItem[] = [];
            for (let i = 0; i < comps.length; ++i) {
                const compItem = comps[i];
                const compInfo = MotorUtils.getBIMCompInfo(compItem);
                const findItem = mapInfo.get(compInfo.bimGuid);

                if (typeof findItem !== "undefined") {
                    const {id} = compItem;

                    if (!isEmpty(id)) {
                        const idParam = id ?? "";
                        let boxCenter: Motor.MotorCore.Vector3 = new Motor.MotorCore.Vector3();
                        const find = mapBoxCenter.get(idParam);
                        if (find !== undefined) {
                            boxCenter = find;
                        } else {
                            const box = await project.getElmentBoundingBox(idParam);
                            // eslint-disable-next-line max-depth
                            if (box !== undefined) {
                                boxCenter = box.center;
                                mapBoxCenter.set(idParam, boxCenter);
                            }
                        }

                        const resultItemList = this.addBIMMarkerItem(boxCenter, compInfo.bimGuid, findItem);
                        markerResult = markerResult.concat(resultItemList);
                    }
                }
            }
            this.resetMarkerList(markerResult);
        }
    }

    private async addCommonDataListForComp(dataList: BindBimInfoWithDetail[]) {
        const project = MotorContext.getProject();
        const {bimProject, dataContainer, mapBoxCenter} = this;
        if (isNotNullOrUndefined(bimProject) && isNotNullOrUndefined(project) && isNotNullOrUndefined(dataContainer)) {
            const compKeyList = dataList.map((el) => el.compKey);
            const mapInfo: Map<string, BindBimInfoWithDetail> = new Map(dataList.map((el) => [dataContainer.getHandleFromCompKey(el.compKey) ?? "", el]));
            const comps = dataContainer.queryComponentsByCompKeyFromCache(compKeyList);
            let markerResult: WarningMarkerItem[] = [];
            for (let i = 0; i < comps.length; ++i) {
                const compItem = comps[i];
                const compInfo = MotorUtils.getBIMCompInfo(compItem);
                const findItem = mapInfo.get(compInfo.bimGuid);

                if (typeof findItem !== "undefined") {
                    const {id} = compItem;

                    if (!isEmpty(id)) {
                        const idParam = id ?? "";
                        let boxCenter: Motor.MotorCore.Vector3 = new Motor.MotorCore.Vector3();
                        const find = mapBoxCenter.get(idParam);
                        if (find !== undefined) {
                            boxCenter = find;
                        } else {
                            const box = await project.getElmentBoundingBox(idParam);
                            // eslint-disable-next-line max-depth
                            if (box !== undefined) {
                                boxCenter = box.center;
                                mapBoxCenter.set(idParam, boxCenter);
                            }
                        }

                        const resultItemList = this.addBIMMarkerItem(boxCenter, compInfo.bimGuid, findItem);
                        markerResult = markerResult.concat(resultItemList);
                    }
                }
            }
            this.addMarkerList(markerResult);
        }
    }

    private getItemTip(item: BindBimInfoWithDetail) {
        const {warnId, compKey} = item;
        const {dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer)) {
            return `构件名称：${dataContainer.queryComponentNameFromCache(compKey)}/r/n报警级别：${dataContainer.getWarnNameById(warnId)}`;
        }

        return "";
    }

    private addBIMMarkerItem(position: Motor.Vector3, id: string, findItem: BindBimInfoWithDetail): WarningMarkerItem[] {
        const markerResult: WarningMarkerItem[] = [];
        const pos = MotorMarkerHandler.fromCartesian(position, id);
        if (pos.x !== 0 && pos.y !== 0) {
            const newItem = MotorMarkerHandler.createMotorCartographic(pos.id, WarningMarkerTypeStr, pos.x, pos.y, pos.z);
            const newMarkerItem: WarningMarkerItem = {
                ...newItem,
                detail: findItem,
                tip: this.getItemTip(findItem),
            };

            newMarkerItem.billBoardInfo.scale = 1;
            newMarkerItem.billBoardInfo.image = this.getItemImg(findItem);
            markerResult.push(newMarkerItem);
        }

        return markerResult;
    }

    private getItemImg(item: BindBimInfoWithDetail) {
        let resultImg = "./model/images/level4_warning.png";
        const {warnId} = item;
        const {dataContainer} = this;
        let warnLevel = 0;
        if (isNotNullOrUndefined(dataContainer)) {
            warnLevel = dataContainer.getWarnTypeById(warnId);
        }

        switch (warnLevel) {
            case 4:
                resultImg = "./model/images/level1_warning.png";
                break;
            case 3:
                resultImg = "./model/images/level2_warning.png";
                break;
            case 2:
                resultImg = "./model/images/level3_warning.png";
                break;
            case 1:
            default:
                resultImg = "./model/images/level4_warning.png";
        }

        return resultImg;
    }
}

export default WarningMarkerHandler;
