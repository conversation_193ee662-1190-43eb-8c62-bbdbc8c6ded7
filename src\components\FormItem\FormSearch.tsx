import React, {Key, useState, useEffect, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {SearchOutlined} from "@ant-design/icons";
import {Input, InputProps} from "antd";

const useStyle = createUseStyles({
    box: {
        display: "flex",
        justifyContent: "flex-end"
    },
});

export interface FormSearchProps extends Omit<InputProps, "value" | "onChange" | "border" | "onPressEnter"> {
    value?: string;
    onChange?: (val?: Key) => void;
}

const FormSearch = (props: FormSearchProps) => {
    const {value, onChange, style, ...other} = props;
    const [state, setState] = useState<Key | undefined>();
    const cls = useStyle();

    useEffect(() => {
        setState(value);
    }, [value]);

    const handleEnter = useCallback(
        () => {
            if (onChange !== undefined) {
                onChange(state);
            }
        },
        [onChange, state]
    );

    const handleChange: InputProps["onChange"] = useCallback(
        (e) => {
            setState(e.target.value);
        },
        []
    );

    return (
        <div className={cls.box}>
            <div style={{width: 238, height: 38, marginLeft: 4, border: "1px solid #E1E2E5"}}>
                <SearchOutlined style={{fontSize: 14, margin: 13, cursor: "pointer"}} onClick={handleEnter} />
                <Input
                    bordered={false}
                    value={state}
                    onChange={handleChange}
                    onPressEnter={handleEnter}
                    placeholder="输入计划名称"
                    style={{width: 187, height: 20, outline: "none", border: "none", marginTop: 10, ...style}}
                    {...other}
                />
            </div>
        </div>
    );
};

export default FormSearch;
