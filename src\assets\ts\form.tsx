/* eslint-disable @typescript-eslint/no-explicit-any */
import {FormInstance, Modal} from "antd";
import _ from "lodash-es";
import React from "react";
import {createUseStyles} from "react-jss";
import Color from "../css/Color";
import {getState} from "../../store";
import {SectionListType} from "../../store/common/actionTypes";

const {confirm} = Modal;
export const ColConfig = {
    span: 8,
    xxl: {span: 6}
};

export const formLayout = {
    labelCol: {span: 8},
    wrapperCol: {span: 16}
};

export const formColStyle = (labelMaxWidth = 140) => (
    {
        labelCol: {flex: `0 0 ${labelMaxWidth}px`},
        wrapperCol: {flex: "1 1"}
    }
);

export const formColCss = createUseStyles({
    item: {
        "& .ant-form-item-label": {
            overflow: "visible",
            paddingLeft: 12,
            "& label": {
                alignItems: "center",
            }
        },
        "& .ant-form-item-control": {
            paddingRight: 12,
            width: "1px",
        }
    }
});

export const useViewFormStyle = createUseStyles({
    item: {
        "& .ant-form-item-label": {
            overflow: "visible",
            paddingLeft: 12,
            "& label": {
                color: Color["text-2"],
                alignItems: "baseline",
            }
        },
        "& .ant-form-item-control": {
            paddingRight: 12,
            width: "1px",
            "& .ant-form-item-control-input": {
                alignItems: "baseline"
            },
            "& .ant-form-item-control-input-content": {
                color: Color["text-1"]
            }
        }
    }
});

interface FormRulesProps {
    type?: "input" | "textArea";
    title?: string;
    required?: boolean;
}
export const formRules = (props: FormRulesProps = {}) => {
    const {
        type = "input",
        title = "该项",
        required = true,
    } = props;

    if (type === "textArea") {
        return [
            {required, message: title === "该项" ? "该项为必填项!" : `请输入${title}!`},
            {max: 200, message: "超出最大长度!"}
        ];
    }
    return [
        {required, message: title === "该项" ? "该项为必填项!" : `请输入${title}!`},
        {max: 100, message: "超出最大长度!"}
    ];
};

export const isLeave = (onOk: () => void) => {
    confirm({
        title: <div className="title" style={{fontSize: 14}}>提示</div>,
        content: <p style={{color: Color["text-2"]}}>当前表单尚未保存,确定离开吗?</p>,
        onOk
    });
};

export const positiveIntegerFormatter = (value: string | number | undefined) => {
    if (typeof value === "string") {
        return !_.isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, "0") : "0";
    }
    if (typeof value === "number") {
        return !_.isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, "0") : "0";
    }
    return "";
};

export const percentageFormatter = (value: string | number | undefined) => {
    if (typeof value === "string") {
        return !_.isNaN(Number(value)) ? `${value.replace(/^(0+)|[^\d]/g, "")}%` : "0%";
    }
    if (typeof value === "number") {
        return !_.isNaN(value) ? `${String(value).replace(/^(0+)|[^\d]/g, "")}%` : "0%";
    }
    return "0%";
};

export const setFormNodeInfoByNodeId = (nodeId: string, form: FormInstance) => {
    const {sectionList} = getState().commonData;
    const findSection = sectionList.find((el: any) => el.nodeId === nodeId);
    const tempForm = form as unknown as FormInstance<{nodeInfo?: SectionListType}>;
    tempForm.setFieldsValue({nodeInfo: findSection});
};

export const getNodeNameById = (val?: string) => {
    if (val === undefined) {
        return "";
    }
    const {sectionList} = getState().commonData;
    const findData = sectionList.find((el: any) => el.nodeId === val);
    if (findData === undefined) {
        return "";
    }
    return findData.name;
};
