import React, {useCallback, useContext, useEffect, useState} from "react";
import {Button} from "antd";
import {getPlanInfoDetail} from "../../../../../api/Preparation";
import {ApprovalStatus} from "../../../../../api/Preparation/type";
import {getReformDetail} from "../../../../../api/rectification";
import {ApprovalCommentVo} from "../../../../../api/rectification/models/process";
import ComDrawer from "../../../../ComDrawer";
import CommentComp from "../../../../Rectification/comment";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";

const ApprovalInfoButton = () => {
    const cls = useStyles();
    const {planInfo} = useContext(EditorContext);
    const [commentList, setCommentList] = useState<ApprovalCommentVo[]>([]);
    const [approvalInfoVisible, setApprovalInfoVisible] = useState(false);

    const init = useCallback(
        async () => {
            if (planInfo.id !== undefined) {
                const {data: planDetailData} = await getPlanInfoDetail(planInfo.id);
                if (planDetailData.approvalId.length > 0) {
                    const {result} = await getReformDetail(planDetailData.approvalId, "PLAN_APPROVAL");
                    setCommentList(result.comments ?? []);
                }
            }
        },
        [planInfo.id]
    );

    useEffect(() => {
        init();
    }, [init]);

    if (planInfo.approvalStatus === ApprovalStatus.RETURN) {
        return (
            <>
                <Button
                    className={cls.button}
                    size="large"
                    onClick={() => setApprovalInfoVisible(true)}
                >
                    审批信息
                </Button>
                <ComDrawer
                    title="审批信息"
                    width={600}
                    visible={approvalInfoVisible}
                    destroyOnClose
                    maskClosable
                    footer={null}
                    onCancel={() => setApprovalInfoVisible(false)}
                >
                    <div style={{padding: "15px"}}>
                        <CommentComp commentList={commentList} />
                    </div>
                </ComDrawer>
            </>
        );
    }

    return null;
};

export default ApprovalInfoButton;
