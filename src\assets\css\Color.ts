const Color = {
    // 主题颜色
    primary: "#1f54c5",
    "primary-1": "#1F54C5",
    "primary-2": "#225DDD",
    "primary-3": "#1C4BB0",
    // fontColor
    "text-1": "#061127",
    "text-2": "#33394D",
    "text-3": "#717784",
    "text-4": "#C0C4CC",

    // backgroundColor
    "bg-1": "#FFFFFF",
    "bg-2": "#F7F8FA",
    "bg-3": "#F2F3F5",
    "bg-4": "#EAEFFB",
    // borderColor
    "light-line-1": "#EBEEF3",
    "dark-line-2": "#DDE2EE",

    // statusColor
    "green-1": "#168C41",
    "green-2": "#E8F4EC",
    "green-3": "#44BA62",
    "green-4": "#117841",

    "blue-1": "#007AC1",
    "blue-2": "#E6F2F9",
    "blue-3": "#35A7D9",
    "blue-4": "#005EA4",

    "yellow-1": "#FFBB00",
    "yellow-2": "#FFF8E6",
    "yellow-3": "#FFD13E",
    "yellow-4": "#DB9A00",

    "red-1": "#D32D28",
    "red-2": "#FBEAEA",
    "red-3": "#E46959",
    "red-4": "#B41D24",
    // shadow 阴影
    // 无效果
    noEffect: {
        border: "1px solid #DDE2EE",
        boxSizing: "border-box",
        borderRadius: "4px"
    },
    smallEffect: {
        border: "1px solid #DDE2EE",
        boxSizing: "border-box",
        boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.1)",
        borderRadius: "4px",
    },
    mediumEffect: {
        border: "1px solid #DDE2EE",
        boxSizing: "border-box",
        boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
        borderRadius: "4px",
    },
    largeEffect: {
        border: "1px solid #DDE2EE",
        boxSizing: "border-box",
        boxShadow: "0px 8px 16px rgba(0, 0, 0, 0.1)",
        borderRadius: "4px",
    }

};
export default Color;
