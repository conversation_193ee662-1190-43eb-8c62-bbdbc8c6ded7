import React, {useEffect, useState} from "react";
import {useControllableValue} from "ahooks";
import * as PdsApi from "../../api/pds";
import {toArr} from "../../assets/ts/utils";
import ComTree, {ComTreeProps} from "../../../uikit/Components/ComTree";
import {TreeNodeType} from "../../../uikit/ts/type";
import {ValueType} from "./data";

interface TreeUserInfoOrgType extends PdsApi.UserInfoTypeByOrg {
    children?: PdsApi.UserInfoTypeByOrg[];
}

interface OriginType extends TreeUserInfoOrgType, PdsApi.UserInfoType {
}


const toTree = (list: TreeUserInfoOrgType[]): TreeUserInfoOrgType[] => {
    const {
        keyField = "id",
        childField = "children",
        parentField = "parentId"
    } = {};

    const tree = [];
    const record: {[key: string]: TreeUserInfoOrgType[]} = {};

    for (let i = 0, len = list.length; i < len; i++) {
        const item = list[i];
        const id = item[keyField] as string;

        if (record[id] !== undefined) {
            item[childField] = record[id];
        } else {
            record[id] = [];
            item[childField] = record[id];
        }

        if (item[parentField] === item[keyField]) {
            tree.push(item);
        } else {
            const parentId = item[parentField] as string;
            if (record[parentId] === undefined) {
                record[parentId] = [];
            }
            record[parentId].push(item);
        }
    }

    return tree;
};

const dealOrgTreeToTree = (val: TreeUserInfoOrgType[], disabledKeys: string[]): TreeNodeType<OriginType>[] => {
    const dealTree = (node: TreeUserInfoOrgType): TreeNodeType<OriginType> => {
        if (Array.isArray(node.users)) {
            node.children = toArr(node.children ?? []).concat(node.users as unknown as TreeUserInfoOrgType);
        }
        const tempNode = node as unknown as OriginType;
        const {children, ...other} = node;
        const key = tempNode.id ?? tempNode.userName ?? "";
        const title = tempNode.name ?? tempNode.userName;
        return {
            title,
            key,
            originData: other,
            disabled: disabledKeys.includes(key),
            children: toArr(node.children ?? []).map((el) => dealTree(el)) as unknown as TreeNodeType<OriginType>[],
        };
    };
    return toArr(val).map((el) => dealTree(el));
};

interface PersonTreeOrgProps extends ComTreeProps {
    originPersonData?: PdsApi.UserInfoTypeByOrg[];
    value?: ValueType[];
    onChange?: (val: ValueType[]) => void;
    disabledKeys?: string[];
}

const PersonTreeOrg = (props: PersonTreeOrgProps) => {
    const {disabledKeys, searchKey, originPersonData = []} = props;
    // const {orgInfo, orgList} = useSelector((state: RootState) => state.commonData);
    const [state, setState] = useControllableValue<ValueType[]>(props);
    const [treeData, setTreeData] = useState<TreeNodeType<OriginType>[]>([]);
    // const [originPersonData, setOriginPersonData] = useState<PdsApi.UserInfoTypeByOrg[]>([]);

    // const handleGetPerson = useCallback(
    //     () => {
    //         const orgDetail = orgList.find((v) => v.id === orgInfo.orgId);
    //         PdsApi.getUserInfoByFilterOrg({deptIds: [orgInfo.orgId], orgIds: [orgDetail?.parentId]})
    //             .then((res) => {
    //                 setOriginPersonData(res);
    //                 const allUsers: EasyPerson[] = (res ?? [])
    //                     .map((v) => v.users ?? [])
    //                     .flat()
    //                     .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
    //                 onSetPersons(allUsers);
    //             });
    //     },
    //     [onSetPersons, orgInfo.orgId, orgList]
    // );

    // useEffect(() => {
    //     handleGetPerson();
    // }, [handleGetPerson]);

    useEffect(() => {
        const orgUserTree = toTree(originPersonData);
        setTreeData(dealOrgTreeToTree(orgUserTree, disabledKeys ?? []));
    }, [disabledKeys, originPersonData]);

    const handleAllCheckedLeafNode: ComTreeProps["onAllCheckedLeafNode"] = (checkedLeafNodes) => {
        const checkedNodes = checkedLeafNodes as unknown as TreeNodeType<OriginType>[];
        const userNames = checkedNodes
            .filter((el) => el.originData.userName !== undefined)
            .map((el) => ({userName: el.originData.userName ?? "", deptId: el.originData.deptIds}));
        setState(userNames);
    };

    return (
        <ComTree
            checkable
            checkedKeys={toArr(state).map((el) => el.userName)}
            onAllCheckedLeafNode={handleAllCheckedLeafNode}
            treeData={treeData}
            searchKey={searchKey}
        />
    );
};

export default PersonTreeOrg;
