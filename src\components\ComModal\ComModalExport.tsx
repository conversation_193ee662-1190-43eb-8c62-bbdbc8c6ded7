import {Button} from "antd";
import React, {ReactNode, useCallback, useState} from "react";
import {isNil} from "lodash-es";
import ComModal from ".";
import BtnIcon from "../MyIcon/BtnIcon";
import PermissionCode from "../PermissionCode";

interface ComModalExportProps {
    totalNum: number;
    selectedNum: number;
    btnText?: string;
    content?: ReactNode;
    onOk: () => void;
    triggerNode?: ReactNode;
    onClick?: () => void;
}
const ComModalExport = (props: ComModalExportProps) => {
    const {totalNum, selectedNum, onOk, content, btnText = "导出"} = props;
    const [visible, setVisible] = useState(false);

    // const buttonClick = () => {
    //     setVisible(true);
    //     if (onClick !== undefined) {
    //         onClick();
    //     }
    // };

    const handleOk = useCallback(
        () => {
            onOk();
            setVisible(false);
        },
        [onOk]
    );

    return (
        <>
            <div onClick={() => setVisible(true)} style={{display: "inline-block"}}>
                <PermissionCode
                    authcode="ProjectPlatform-Plan-Progress-Establishment:export"
                >
                    <Button icon={<BtnIcon type="icon-daochu" />}>{btnText}</Button>
                </PermissionCode>
            </div>
            <ComModal
                title="批量导出"
                // triggerNode={triggerNode ?? <Button icon={<BtnIcon type="icon-daochu" />}>导出</Button>}
                onOk={handleOk}
                visible={visible}
                onCancel={() => setVisible(false)}
            >
                {!isNil(content)
                    ? content
                    : <p style={{padding: 24}}>{`已选择${selectedNum !== 0 ? selectedNum : totalNum}条数据进行批量导出操作!`}</p>}

            </ComModal>
        </>
    );
};

export default ComModalExport;
