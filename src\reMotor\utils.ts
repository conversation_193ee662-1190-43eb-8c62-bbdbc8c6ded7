import {MotorCore, PickObject, ViewPosition} from "@motor/core";
import {isNonEmptyArray} from "../assets/ts/utils";
import {BIMCompInfo, CameraSpecAngle, DirTreeWithPath} from "./interface";

export const pathSeparator = "☎";

export const getBIMCompInfo = (pickInfo?: PickObject): BIMCompInfo => {
    const result: BIMCompInfo = {
        guid: "",
        name: "",
        path: "",
        pathWithoutMajor: "",
        bimGuid: "",
        floor: "",
        major: "",
        mainType: "",
        subType: ""
    };

    if (pickInfo !== undefined) {
        result.guid = pickInfo.id ?? "";
        result.bimGuid = pickInfo.bimId ?? "";
        const dirs = pickInfo.dir;
        if (Array.isArray(dirs) && dirs.length > 0) {
            // eslint-disable-next-line prefer-destructuring
            result.floor = dirs[0];
            result.major = dirs.length > 1 ? dirs[1] : "";
            result.mainType = dirs.length > 2 ? dirs[2] : "";
            result.subType = dirs.length > 3 ? dirs[3] : "";
            result.name = dirs.length > 4 ? dirs[4] : "";
            result.path = dirs.join("☎");

            const dirsClone = dirs.slice();
            dirsClone.splice(1, 1);
            result.pathWithoutMajor = dirsClone.join("☎");
        }
    }

    return result;
};

/**
 * 递归遍历motor提供的原生部位树，每个节点加上path字段
 * @param tree 不包含根节点的部位树
*/
export const addDirTreePath = (tree: MotorCore.DirTree[]) => {
    const actuator = (data: MotorCore.DirTree[], pathPrefix = ""): DirTreeWithPath[] => data.map((v) => {
        const path = `${pathPrefix}${v.name}`;
        return {
            path,
            name: v.name,
            children: isNonEmptyArray(v.children) ? actuator(v.children, `${path}☎`) : undefined
        };
    });
    return actuator(tree);
};

export const getViewPositionBySpecAngle = (angle: CameraSpecAngle): Partial<ViewPosition> => {
    let res: Partial<ViewPosition> = {};
    switch (angle) {
        case CameraSpecAngle.Up:
            res = {phi: 0, theta: -90};
            break;
        case CameraSpecAngle.Down:
            res = {phi: 180, theta: 90};
            break;
        case CameraSpecAngle.Left:
            res = {phi: 90, theta: 0};
            break;
        case CameraSpecAngle.Right:
            res = {phi: 270, theta: 0};
            break;
        case CameraSpecAngle.Front:
            res = {phi: 0, theta: 0};
            break;
        case CameraSpecAngle.Behind:
            res = {phi: 180, theta: 0};
            break;
        case CameraSpecAngle.LeftUp:
            res = {phi: 45, theta: -45};
            break;
        case CameraSpecAngle.LeftDown:
            res = {phi: 45, theta: 45};
            break;
        case CameraSpecAngle.RgihtUp:
            res = {phi: 315, theta: -45};
            break;
        case CameraSpecAngle.RightDown:
            res = {phi: 315, theta: 45};
            break;
        default:
            throw new Error("unexpected angle");
    }
    return res;
};
