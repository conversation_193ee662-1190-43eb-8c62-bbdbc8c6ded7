import React, {useState, useEffect, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {isEqual} from "lodash-es";
import {Input, Select, Space, SelectProps} from "antd";
import {toArr} from "../../assets/ts/utils";
import ComDrawer from "../ComDrawer";
import ComSearch from "../../../uikit/Components/ComSearch";
import PersonTreeOrg from "./PersonTreeOrg";
import {EasyPerson, ValueType} from "./data";
import PersonTreeRole from "./PersonTreeRole";
import {RootState} from "../../store/rootReducer";
import {getSearchUsersOfRole, getUserInfoByFilterOrg, UserInfoTypeByOrg} from "../../api/pds";

const useStyle = createUseStyles({
    box: {},
});

const FilterTypeOptions = [
    {label: "按组织架构", value: 1},
    {label: "按角色", value: 2}
];

export interface SelectPersonProps {
    value?: ValueType[];
    onChange?: (val: ValueType[]) => void;
    disabledKeys?: string[];
    inputDisabled?: boolean;
    disableReason?: string;
    nodeId?: string;
    nodeType?: number;
}

const renderInputValue = (disabled: boolean, reason: string, allPerson: EasyPerson[], value?: ValueType[]) => {
    if (disabled) {
        return reason;
    }
    const personMap = new Map<string, string>(allPerson.map((v) => [v.userName, v.realName]));
    return toArr(value ?? []).map((el) => personMap.get(el.userName) ?? "").join(",");
};

const SelectPerson = (props: SelectPersonProps) => {
    const {orgInfo, orgList} = useSelector((state: RootState) => state.commonData);
    const {value, onChange, disabledKeys, inputDisabled = false, disableReason = "", nodeId, nodeType} = props;
    const cls = useStyle();
    const [state, setState] = useState<ValueType[]>([]);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [personType, setPersonType] = useState(2); // 目前只留了角色，组织机构去掉了
    const [key, setKey] = useState<string>();
    const [allPerson, setAllPerson] = useState<EasyPerson[]>([]);
    const [originPersonData, setOriginPersonData] = useState<UserInfoTypeByOrg[]>([]);

    useEffect(() => {
        if (!isEqual(value, state)) {
            setState(value ?? []);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const getPersonData = useCallback(
        () => {
            const orgDetail = orgList.find((v) => v.id === orgInfo.orgId);
            if (personType === 1) {
                getUserInfoByFilterOrg({deptIds: [orgInfo.orgId], orgIds: [orgDetail?.parentId]})
                    .then((res) => {
                        setOriginPersonData(res);
                        const allUsers: EasyPerson[] = (res ?? [])
                            .map((v) => v.users ?? [])
                            .flat()
                            .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
                        setAllPerson(allUsers);
                    });
            } else {
                // getUserInfoOfRoleByFilterOrg({deptIds: [orgInfo.orgId], orgIds: [orgDetail?.parentId]})
                //     .then((res) => {
                //         setOriginPersonData(res);
                //         const allUsers: EasyPerson[] = (res ?? [])
                //             .map((v) => v.users ?? [])
                //             .flat()
                //             .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
                //         setAllPerson(allUsers);
                //     });
                getSearchUsersOfRole({
                    deptId: orgInfo.orgId,
                    nodeId,
                    nodeType,
                })
                    .then((res) => {
                        setOriginPersonData(res);
                        const allUsers: EasyPerson[] = (res ?? [])
                            .map((v) => v.users ?? [])
                            .flat()
                            .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
                        setAllPerson(allUsers);
                    });
            }
        },
        [nodeId, nodeType, orgInfo.orgId, orgList, personType]
    );

    useEffect(
        () => {
            getPersonData();
        },
        [getPersonData]
    );

    const openDrawer = () => setDrawerVisible(true);
    const closeDrawer = () => setDrawerVisible(false);

    const handleOk = () => {
        closeDrawer();
        if (onChange !== undefined) {
            onChange(state);
        }
    };

    const handleCancel = () => {
        setState(value ?? []);
        closeDrawer();
    };

    const handleInputClick = () => {
        openDrawer();
    };

    const handleSelect: SelectProps<number>["onSelect"] = (val: number) => {
        setPersonType(val);
    };

    return (
        <div className={cls.box}>
            <Input
                onClick={handleInputClick}
                value={renderInputValue(inputDisabled, disableReason, allPerson, value)}
                disabled={inputDisabled}
            />
            <ComDrawer
                title="选择人员"
                visible={drawerVisible}
                width={520}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <div style={{padding: 24, height: "100%", display: "flex", flexDirection: "column"}}>
                    <div style={{marginBottom: 12}}>
                        <Space style={{display: "none"}}>
                            <Select<number> value={personType} onSelect={handleSelect} options={FilterTypeOptions} style={{width: 120}} />
                            <ComSearch value={key} onChange={setKey} style={{width: "100%"}} />
                        </Space>
                        <ComSearch value={key} onChange={setKey} style={{width: "100%"}} />
                    </div>
                    {
                        personType === 1 && (
                            <PersonTreeOrg
                                searchKey={key}
                                value={state}
                                onChange={setState}
                                disabledKeys={disabledKeys}
                                originPersonData={originPersonData}
                            />
                        )
                    }
                    {
                        personType === 2 && (
                            <PersonTreeRole
                                value={state}
                                onChange={setState}
                                disabledKeys={disabledKeys}
                                searchKey={key}
                                originPersonData={originPersonData}
                            />
                        )
                    }
                </div>
            </ComDrawer>
        </div>
    );
};

export default SelectPerson;
