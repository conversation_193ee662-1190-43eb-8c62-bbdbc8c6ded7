import {EyeOutlined} from "@ant-design/icons";
import {Col, Row, Space, Tooltip, TreeProps} from "antd";
import React, {Key} from "react";
import {useDispatch, useSelector} from "react-redux";
import Color from "../../assets/css/Color";
import {toArr} from "../../assets/ts/utils";
import {RootState} from "../../store/rootReducer";
import MyIcon from "../MyIcon";
import {WbsEbsTreeDataNode} from "./data";
import {setCheckedKeys, setIsShow, setQueryCheckedNode} from "./store/action";
import WBSTree from "./WBSTree";

// 废弃了
/**
 * @deprecated
 */
const WBS = () => {
    const dispatch = useDispatch();
    const {checkedKeys, leafIds, wbsBindEbs, ebsBindWbs} = useSelector((state: RootState) => state.wbsTree);

    const handleCheck: TreeProps["onCheck"] = (_checkedVals, info) => {
        let nodes: WbsEbsTreeDataNode[] = [];
        nodes = info.checkedNodes.filter((item: WbsEbsTreeDataNode) => {
            if (item.type === "ebs") {
                return true;
            }
            return (item.children ?? []).length === 0 || item.key === "all" || item.key === "noRelevance";
        });
        dispatch(setQueryCheckedNode(nodes));
        dispatch(setCheckedKeys(_checkedVals as unknown as Key[]));
    };

    const handleClose = () => {
        dispatch(setIsShow(false));
        dispatch(setCheckedKeys(["all"]));
        dispatch(setQueryCheckedNode([{key: "all", title: "全部"}]));
    };

    const renderTreeNode: TreeProps["titleRender"] = (nodeData) => {
        if (wbsBindEbs[nodeData.key] !== undefined) {
            const bindData = wbsBindEbs[nodeData.key];
            const renderTitle = toArr(bindData.ebsList).map((el) => <div>{el.pathName}</div>);
            return (
                <Space>
                    {nodeData.title}
                    <Tooltip overlayStyle={{width: 400}} trigger={["hover"]} placement="right" title={renderTitle}>
                        <EyeOutlined />
                    </Tooltip>
                </Space>
            );
        }
        if (ebsBindWbs[nodeData.key] !== undefined) {
            const bindData = ebsBindWbs[nodeData.key];
            const renderTitle = toArr(bindData.wbsList).map((el) => <div>{el.pathName}</div>);
            return (
                <Space>
                    {nodeData.title}
                    <Tooltip overlayStyle={{width: 400}} trigger={["hover"]} placement="right" title={renderTitle}>
                        <EyeOutlined />
                    </Tooltip>
                </Space>
            );
        }
        return (
            <div>{nodeData.title}</div>
        );
    };

    return (
        <div style={{padding: 24, borderRight: `1px solid ${Color["light-line-1"]}`, height: "100%", display: "flex", flexDirection: "column"}}>
            <Row justify="space-between" style={{marginBottom: 10}}>
                <Col>
                    <Space style={{height: "100%"}}>
                        <MyIcon fontSize={20} type="icon-fenxiang1" />
                        <div style={{fontSize: 14}} className="title">关联WBS/EBS</div>
                    </Space>
                </Col>
                <Col>
                    <MyIcon type="icon-shanchu-1" onClick={handleClose} />
                </Col>
            </Row>
            <div style={{flexGrow: 1}}>
                <WBSTree
                    isShowSearch={false}
                    query
                    leafIds={leafIds}
                    checkable
                    checkedKeys={checkedKeys}
                    onCheck={handleCheck}
                    titleRender={renderTreeNode}
                />
            </div>
        </div>
    );
};

export default WBS;
