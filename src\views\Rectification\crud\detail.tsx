/* eslint-disable max-len */
import React, {useCallback, useEffect, useRef, useState} from "react";
import {useSelector} from "react-redux";
import {cloneDeep} from "lodash-es";
import {Collapse, Row, Space, Input, message, Form, Spin, Descriptions, Button} from "antd";
import {commentReform, getReformDetail, getReformFlowChart, saveSignByNode, submitReform} from "../../../api/rectification/index";
import {ApprovalCommentVo, ApprovalNodeUserParam, ReformDetailVo, ReformInstSubmitParam} from "../../../api/rectification/models/process";
import {ApprovalRole, ApprovalType, Flow, FlowState, RectificationActionType} from "../../../components/Rectification/models/rectification";
import {getButtonVisiblePolicy, loadCurrentFlowAsync, rules, setIsReturnBackFn, transerFileToAttachment, transformCustomFieldMap, transformJsonValues, transferAttachmentVoToFileType} from "../../../components/Rectification/rectification-helpers";
import ComCollapsePanel from "../../../components/ComCollapsePanel";
import {FileType} from "../../../api/common.type";
import FileBox from "../../../components/FileBox";
import FlowChart from "../../../components/Rectification/FlowChart";
import {ConditionFlowChartData} from "../../../components/Rectification/models/flow-chart";
import CommentComp from "../../../components/Rectification/comment";
import {ApprovalFlowNodeVo, ApprovalNodeVo} from "../../../api/rectification/models/flowLine";
import {RectifySubmitFormModel, renderSubmitFormValues, SignSuccessInfoType} from "../interface";
import SubmitForm, {SubmitFormRef} from "../components/SubmitForm";
import PersonForm, {PersonFormRef} from "../components/PersonForm";
import RectifyButtons from "../components/RectifyButtons";
import CombinCheckDetail from "../components/CombinCheckDetail";
// import RectificationSheetSign from "../components/RectificationSheetSign";
import BackIcon from "../../../components/MyIcon/BackIcon";
// import useIframeEmit from "../../../../assets/hooks/useIframeEmit";
import {CustomFieldData, CustomFieldType} from "../../../components/Rectification/models/custom-field";
// import {getPreviewUrl} from "../../../api/common.api";
// import {setPreviewUrl} from "../../../store/common/action";
import useStyles from "../style";
import {RootState} from "../../../store/rootReducer";

const {TextArea} = Input;
const requiredFieldType = [
    CustomFieldType.MultiSelect,
    CustomFieldType.Select,
    CustomFieldType.NumberInput
];
export const renderNodeUsers = (list?: ApprovalNodeVo[]): ApprovalNodeUserParam[] => {
    if (list === undefined) {
        return [];
    }
    const res: ApprovalNodeUserParam[] = [];
    list.filter((v) => v.approverType === 3).forEach((info) => {
        const {approvalPostVos, approvalUserVos, approvalRoleVos, approvalNodeId} = info;
        const approvalUsers: string[] = [];
        if (Array.isArray(approvalUserVos)) {
            approvalUserVos.forEach((v) => approvalUsers.push(v.userName));
        } else if (Array.isArray(approvalPostVos)) {
            approvalPostVos.forEach((v) => approvalUsers.push(v.postName));
        } else if (Array.isArray(approvalRoleVos)) {
            approvalRoleVos.forEach((v) => approvalUsers.push(v.roleName));
        }
        res.push({approvalNodeId, approvalUsers});
    });
    return res;
};
// interface AdvancedFormType {
//     name: string;
//     value: string;
// }

interface RectifyDetailProps {
    back: () => void;
    moduleType: string;
    // 处理成功了,不需要再留在这里的
    // onSuccessBack?: () => void;
}

// eslint-disable-next-line max-lines-per-function
const RectifyDetail: React.FC<RectifyDetailProps> = (props) => {
    const {back, moduleType} = props;
    const cls = useStyles();
    // const dispatch = useDispatch();
    // const {orgInfo, userInfo} = useSelector((state: RootState) => state.commonData);
    const {currentReformId} = useSelector((state: RootState) => state.rectificationTemp);
    const [reformDetail, setReormDetail] = useState<ReformDetailVo>();
    const [commentForm] = Form.useForm<RectifySubmitFormModel>();
    const [visibleButtons, setVisibleButtons] = useState<RectificationActionType[]>([]);
    /* 审批记录列表 */
    const [commentList, setCommentList] = useState<ApprovalCommentVo[]>([]);
    /* 把整改实例详情审批意见列表的第二个(如果有)单独拿出来作为整改记录详情展示 */
    const [rectifyRecord, setRectifyRecord] = useState<ApprovalCommentVo>();
    const [fileList, setFileList] = useState<FileType[]>([]);
    const [flowChartVisible, setFlowChartVisible] = useState(false);
    const [flowChartData, setFlowChartData] = useState<ConditionFlowChartData>();
    const [loading, setLoading] = useState(false);
    /* 撤回和退回的需要特殊处理 */
    const [isReturnBack, setIsReturnBack] = useState(false);
    const [nodeUsers, setNodeUsers] = useState<Flow[]>();
    const submitRef = useRef<SubmitFormRef>(null);
    const personRef = useRef<PersonFormRef>(null);
    // const [noticeMenuList, setNoticeMenuList] = useState<AdvancedFormType[]>([]); // 通知单 列表
    // const [correctionSheetList, setCorrectionSheetList] = useState<AdvancedFormType[]>([]); // 整改单 列表
    // 当前节点是否是整改人节点
    const [isRectifyPerson, setIsRectifyPerson] = useState(false);
    const fieldListRef = useRef<CustomFieldData[]>([]);
    const fieldValuesRef = useRef<Map<string, string | string[]>>(new Map());
    const [personFormKey, setPersonFormKey] = useState<number>(new Date().getTime());
    // useIframeEmit();
    // 签章相关
    // const RectifySignRef = useRef<RectifySignRefs | null>(null);
    const rectifyNodeRef = useRef<ApprovalFlowNodeVo>(); // 整改人节点
    // const [launchReform, setLaunchReform] = useState<LaunchReformParams>();
    // const [previewVisible, setPreviewVisible] = useState(false);
    // const [messageInfo, setMessageInfo] = useState<{
    //     defaultAnnotation: string | undefined;
    //     operateMsg: string | undefined;
    //     fileList: FileType[] | undefined;
    // }>();
    // const [noticeTemplateResult, setNoticeTemplateResult] = useState<GetReportFormTemplateType>();
    // const [replyTemplateResult, setReplyTemplateResult] = useState<GetReportFormTemplateType>();
    // const [signPdfInfo, setSignPdfInfo] = useState<QuerySignByNodeReturn>();
    const [submitLoading, setSubmitLoading] = useState(false);
    const init = useCallback(
        async () => {
            setLoading(true);
            try {
                const {result} = await getReformDetail(currentReformId, moduleType);
                if (result !== undefined && setIsReturnBackFn(result.processStatus)) {
                    const tempData = transformJsonValues(result.componentJsons);
                    const tempFieldData = transformCustomFieldMap(result.componentJsons);
                    const flowRes = await loadCurrentFlowAsync(
                        tempFieldData,
                        tempData,
                        result.formTemplId!,
                        result.approvalTypeId,
                        result.serialNum,
                    );
                    setNodeUsers(flowRes);
                }
                /* const {result: noticeTemplateRes} = await getReportFormTemplate({
                    buildType: 0,
                    type: "Rectification-Notice",
                    moduleType: `${moduleType}_V2`,
                    nodeId: orgInfo.orgId
                });
                setNoticeTemplateResult(noticeTemplateRes);
                if (!result.startFlowNode) {
                    const {result: replyTemplateRes} = await getReportFormTemplate({
                        buildType: 0,
                        type: "Rectification-Reply",
                        moduleType: `${moduleType}_V2`,
                        nodeId: orgInfo.orgId
                    });
                    setReplyTemplateResult(replyTemplateRes);
                } */

                fieldListRef.current = transformJsonValues(result.componentJsons);
                fieldValuesRef.current = transformCustomFieldMap(result.componentJsons);
                setReormDetail(result);
                const chartRes = await getReformFlowChart(result.serialNum, result.approvalTypeId);
                const flowNodeList = chartRes.result.flowNodeList ?? [];
                // 第二个节点为整改人节点
                const rectifyNode = flowNodeList[1];
                if (rectifyNode === undefined || rectifyNode === null) {
                    message.error("流程节点错误!");
                }
                rectifyNodeRef.current = rectifyNode;
                const copyComments = cloneDeep(result.comments ?? []);
                const policy = getButtonVisiblePolicy(result.processStatus as FlowState, result.approvalRoles, result.startFlowNode);
                const showButtons = rules.get(policy) ?? [];
                const rectifyNodeRecord = copyComments.filter((v) => v.flowNodeId === rectifyNode.flowNodeId);
                setIsRectifyPerson(result.currentFlowNodeId === rectifyNode.flowNodeId);
                setRectifyRecord(rectifyNodeRecord.length > 0 ? rectifyNodeRecord[rectifyNodeRecord.length - 1] : undefined);
                setCommentList(copyComments);
                setVisibleButtons(showButtons);
                setFlowChartData(chartRes.result);
                setIsReturnBack(setIsReturnBackFn(result.processStatus));
            } finally {
                setLoading(false);
            }
        },
        [currentReformId, moduleType]
    );

    useEffect(() => {
        init();
    }, [init]);
    /* useEffect(() => {
        if (reformDetail !== undefined && !previewVisible) {
            // 获取当前节点是否已有表单pdf
            querySignByNode({
                serialNum: reformDetail.serialNum,
                flowNodeId: reformDetail?.currentFlowNodeId ?? "",
                flowNodeIdList: (reformDetail?.comments ?? []).map((i) => ({flowNodeId: i.flowNodeId ?? "", operationType: i.operationType ?? 1}))
            })
                .then((res) => {
                    setSignPdfInfo(res.data);
                })
                .catch((err) => {
                    console.log(err);
                });
        }
    }, [previewVisible, reformDetail]); */
    // 回复单
    /* const handleViewReplyFormUrl = useCallback(() => {
        if (reformDetail !== undefined) {
            getReportFormFillUrls(reformDetail.serialNum, orgInfo.orgId, "Rectification-Reply", `${moduleType}_V2`, 0).then((res) => {
                if (res.code === 200 && Array.isArray(res.result)) {
                    const newResult: AdvancedFormType[] = [];
                    res.result.forEach((item, index) => {
                        newResult.push({
                            name: `第${index + 1}页`,
                            value: item,
                        });
                    });
                    setCorrectionSheetList(newResult ?? []);
                }
            });
        }
    }, [reformDetail, orgInfo.orgId, moduleType]);
    const handleViewNoticePdf = useCallback(() => {
        getPreviewUrl({fileName: signPdfInfo?.noticefileName, uuid: signPdfInfo?.noticefileId}).then((res) => {
            dispatch(
                setPreviewUrl({
                    url: `${res}?rowpdf=1`,
                    name: signPdfInfo?.noticefileName ?? ""
                })
            );
        });
    }, [dispatch, signPdfInfo]);
    // 通知单
    const handleViewNoticeFormUrl = useCallback(() => {
        if (reformDetail !== undefined) {
            getReportFormFillUrls(reformDetail.serialNum, orgInfo.orgId, "Rectification-Notice", `${moduleType}_V2`, 0).then((res) => {
                if (res.code === 200 && Array.isArray(res.result)) {
                    const newResult: AdvancedFormType[] = [];
                    res.result.forEach((item, index) => {
                        newResult.push({
                            name: `第${index + 1}页`,
                            value: item,
                        });
                    });
                    setNoticeMenuList(newResult ?? []);
                }
            });
        }
    }, [reformDetail, orgInfo.orgId, moduleType]);
    const handleViewReplyPdf = useCallback(() => {
        getPreviewUrl({fileName: signPdfInfo?.replyFileName, uuid: signPdfInfo?.replyFileId}).then((res) => {
            dispatch(
                setPreviewUrl({
                    url: `${res}?rowpdf=1`,
                    name: signPdfInfo?.replyFileName ?? ""
                })
            );
        });
    }, [dispatch, signPdfInfo]); */
    const updateFlowList = useCallback((map?: Map<string, string | string[]>) => {
        const innerList = fieldListRef.current;
        const innerFields = map ?? new Map();
        const filedsContainsValue = Array.from(innerFields.entries()).filter((kv) => kv[1].length !== 0).map((kv) => kv[0]);
        const requiredFields = innerList.filter((c) => c.required && requiredFieldType.includes(c.type)).map((c) => c.id);
        const intersection = requiredFields.filter((n) => !filedsContainsValue.includes(n));
        if (intersection.length === 0) {
            const {approvalTypeId, formTemplId} = reformDetail!;
            loadCurrentFlowAsync(innerFields, innerList, formTemplId ?? "", approvalTypeId ?? "").then((res) => setNodeUsers(res));
        } else {
            setNodeUsers([]);
        }
        setPersonFormKey(new Date().getTime());
    }, [reformDetail]);

    const handleFieldsChange = useCallback(
        (data: CustomFieldData, payload?: string | string[]) => {
            if (payload === undefined) {
                return;
            }
            const newCustomFieldValues = new Map(fieldValuesRef.current).set(data.id, payload);
            if (requiredFieldType.includes(data.type)) {
                updateFlowList(newCustomFieldValues);
            }
        },
        [updateFlowList]
    );

    const handleSubmit = useCallback(
        async (fileInfo?: SignSuccessInfoType) => {
            setSubmitLoading(true);
            try {
                const values = await commentForm.validateFields();
                if (submitRef.current !== null) {
                    await submitRef.current.handleSubmit();
                }
                const roles = reformDetail?.approvalRoles ?? [];
                /** 判断是否是抄送行为 */
                const isCopyTo = roles.includes(ApprovalRole.CopyPerson) && !roles.includes(ApprovalRole.ApprovalPerson);
                /** 附件 */
                const attachments = transerFileToAttachment(fileList);
                if (isCopyTo) {
                    await commentReform({
                        formTaskId: reformDetail!.formTaskId,
                        message: values.message,
                        attachments
                    });
                } else {
                    let cancelExtra: Partial<ReformInstSubmitParam> = {};
                    if (reformDetail?.startFlowNode === true && isReturnBack && submitRef.current !== null && personRef.current !== null) {
                        const submitFormValues = await submitRef.current.handleSubmit();
                        const {jsonValues} = submitRef.current;
                        cancelExtra = {
                            nodeUsers: await personRef.current?.getNodeUsers(),
                            comment: submitFormValues.comment,
                            deadline: submitFormValues.deadline?.endOf("day").format("x"),
                            jsonValues
                        };
                    }
                    let backExtra: Partial<ReformInstSubmitParam> = {};
                    if (reformDetail?.startFlowNode === false && isReturnBack) {
                        backExtra = {
                            nodeUsers: nodeUsers?.filter((f) => f.type === ApprovalType.InitiatorSpecify).map((f) => ({
                                approvalNodeId: f.id,
                                approvalUsers: f.persons.map((v) => v.name)
                            }))
                        };
                    }
                    await submitReform({
                        serialNum: reformDetail!.serialNum,
                        formTaskId: reformDetail!.formTaskId,
                        checkFormId: "qwdqwd",
                        message: values.message,
                        deptId: reformDetail?.deptId,
                        nodeId: reformDetail?.nodeId,
                        nodeType: reformDetail?.nodeType,
                        attachments,
                        // nodeUsers: renderNodeUsers(nodeUsers),
                        ...cancelExtra,
                        ...backExtra
                    }, moduleType);
                }
                if (fileInfo !== undefined) {
                    await saveSignByNode({
                        noticefileId: fileInfo.fileId,
                        noticefileName: fileInfo.name,
                        flowNodeId: reformDetail!.currentFlowNodeId,
                        serialNum: Number(reformDetail!.serialNum)
                    });
                }
                message.success("操作成功");
                setSubmitLoading(false);
                back();
                // onSuccessBack();
            } catch (err) {
                console.log(err);
                setSubmitLoading(false);
            }
        },
        [commentForm, reformDetail, fileList, back, isReturnBack, moduleType, nodeUsers]
    );
    /* const handleToSign = useCallback(async () => {
        try {
            const values = await commentForm.validateFields();
            if (rectifyNodeRef.current?.flowNodeId === reformDetail?.currentFlowNodeId) { // 整改人节点
                setMessageInfo({
                    defaultAnnotation: undefined,
                    operateMsg: values.message ?? "",
                    fileList
                });
            } else if (reformDetail?.startFlowNode ?? false) { // 发起人节点
                if (submitRef.current !== null) {
                    const submitFormValues = await submitRef.current.handleSubmit();
                    setMessageInfo({
                        defaultAnnotation: undefined,
                        operateMsg: submitFormValues.comment ?? "",
                        fileList: undefined
                    });
                    setLaunchReform({
                        currentFlowNodeId: reformDetail?.currentFlowNodeId ?? "",
                        reformComment: submitFormValues.comment,
                        businessBindAdapterBeanName: moduleType === "SECURITY" ? "securityAndQualityV2BusinessBindAdapter" : "securityAndQualityV2BusinessBindAdapter",
                        reformDeadline: Number(submitFormValues.deadline.endOf("day").format("x")),
                        checkId: "wefwf",
                        checkType: 0,
                        processInstanceName: undefined,
                        startTime: moment().valueOf(),
                        startUser: userInfo.username
                    });
                }
            } else { // 整改人之后的节点
                const needReplyPdf = replyTemplateResult?.sign === 1 && (signPdfInfo?.replyFileId ?? "").length === 0;
                setMessageInfo({
                    defaultAnnotation: values.message ?? "",
                    operateMsg: needReplyPdf ? rectifyRecord?.commentMsg : undefined,
                    fileList: needReplyPdf ? (rectifyRecord?.attachments ?? []).map((item) => ({fileName: item.name ?? "", fileSize: item.size ?? 0, fileUuid: item.uuid ?? ""})) : []
                });
            }
            localStorage.removeItem("signSuccessInfo");
            setPreviewVisible(true);
        } catch (err) {
            console.log(err);
        }
    }, [commentForm, fileList, moduleType, rectifyRecord, reformDetail, replyTemplateResult, signPdfInfo, userInfo.username]); */
    if (loading || reformDetail === undefined) {
        return (
            <div className={cls.mainWrapper}>
                <Row justify="space-between" className="head" style={{padding: "0 24px"}}>
                    <Space>
                        <BackIcon onClick={back} />
                        <div className="title">整改详情</div>
                    </Space>
                </Row>
                <div style={{flexGrow: 1}} className="center">
                    <Spin spinning={loading} delay={300} tip="加载中" />
                </div>
            </div>
        );
    }

    /* const goToLink = (url: string) => {
        if (url !== "") {
            window.open(url);
        }
    }; */
    // 通知单
    /* const renderNoticeMenu = () => (
        <Menu>
            {noticeMenuList.map((item) => (
                <Menu.Item key={item.value} onClick={() => goToLink(item.value ?? "")}>
                    {item.name}
                </Menu.Item>
            ))}
        </Menu>
    );

    // 回复单
    const renderReplyMenu = () => (
        <Menu>
            {correctionSheetList.map((item) => (
                <Menu.Item key={item.value} onClick={() => goToLink(item.value ?? "")}>
                    {item.name}
                </Menu.Item>
            ))}
        </Menu>
    ); */

    return (
        <div className={cls.mainWrapper}>
            <Row justify="space-between" className="head" style={{padding: "0 24px"}}>
                <Space>
                    <BackIcon onClick={back} />
                    <div className="title">整改详情</div>
                </Space>
                <Button type="primary" onClick={() => setFlowChartVisible(true)}>查看流程图</Button>
                {/* <Space>
                    {(noticeTemplateResult?.sign === 0 || (noticeTemplateResult?.sign === 1 && (signPdfInfo?.noticefileId ?? "").length === 0)) && (
                        <Dropdown overlay={renderNoticeMenu} placement="bottomCenter" trigger={["click"]}>
                            <Button type="primary" onClick={() => handleViewNoticeFormUrl()}>
                                整改通知单
                                <DownOutlined />
                            </Button>
                        </Dropdown>
                    )}
                    {noticeTemplateResult?.sign === 1 && (signPdfInfo?.noticefileId ?? "").length > 0 && (
                        <Button type="primary" onClick={() => handleViewNoticePdf()}>
                            整改通知单
                        </Button>
                    )}
                    {replyTemplateResult?.sign === 1 && (signPdfInfo?.replyFileId ?? "").length > 0 && (
                        <Button type="primary" onClick={() => handleViewReplyPdf()}>
                            整改回复单
                        </Button>
                    )}
                    {(replyTemplateResult?.sign === 0 || (replyTemplateResult?.sign === 1 && (signPdfInfo?.replyFileId ?? "").length === 0)) && (
                        <Dropdown overlay={renderReplyMenu} placement="bottomCenter" trigger={["click"]}>
                            <Button type="primary" onClick={() => handleViewReplyFormUrl()}>
                                整改回复单
                                <DownOutlined />
                            </Button>
                        </Dropdown>
                    )}

                    <Button type="primary" onClick={() => setFlowChartVisible(true)}>查看流程图</Button>
                </Space> */}
            </Row>
            <div className={cls.contentWrapper}>
                <Collapse ghost defaultActiveKey={["2", "3", "4", "5"]} expandIconPosition="right">
                    <ComCollapsePanel key="1" header="检查详情">
                        <CombinCheckDetail isInRectifyDetail />
                    </ComCollapsePanel>
                    <ComCollapsePanel key="2" header="整改详情">
                        <div style={{padding: "0 20px"}}>
                            {
                                reformDetail !== undefined
                                    ? (
                                        <SubmitForm
                                            ref={submitRef}
                                            nodeId={reformDetail.nodeId}
                                            nodeType={reformDetail.nodeType}
                                            onFieldsChange={handleFieldsChange}
                                            formData={renderSubmitFormValues(reformDetail)}
                                            isStartFlowNode={reformDetail.startFlowNode}
                                            defaultFieldList={transformJsonValues(reformDetail.componentJsons)}
                                            defaultCustomFieldValues={transformCustomFieldMap(reformDetail.componentJsons)}
                                        />
                                    )
                                    : null
                            }
                        </div>
                    </ComCollapsePanel>
                    {
                        reformDetail.startFlowNode && isReturnBack && nodeUsers !== undefined
                            ? (
                                <ComCollapsePanel key="3" header="整改流程">
                                    <PersonForm
                                        key={personFormKey}
                                        ref={personRef}
                                        flowList={nodeUsers}
                                        nodeId={reformDetail.nodeId}
                                        nodeType={reformDetail.nodeType}
                                    />
                                </ComCollapsePanel>
                            )
                            : null
                    }
                    <ComCollapsePanel key="4" header="整改记录">
                        <div style={{padding: "0 20px"}}>
                            {
                                visibleButtons.includes(RectificationActionType.Submit) && reformDetail.comments?.length === 1
                                    ? (
                                        <Form
                                            style={{marginTop: 20}}
                                            form={commentForm}
                                            className={cls.specFormBox}
                                        >
                                            <Form.Item
                                                label={reformDetail.comments?.length === 1 ? "整改措施：" : "审批意见："}
                                                rules={[{required: reformDetail.comments?.length === 1, message: "请输入整改措施"}]}
                                                name="message"
                                            >
                                                <TextArea maxLength={300} showCount />
                                            </Form.Item>
                                            <Form.Item label="附件">
                                                <FileBox style={{width: 564}} value={fileList} onChange={setFileList} />
                                            </Form.Item>
                                        </Form>
                                    )
                                    : null
                            }
                            {/* {rectifyRecord !== undefined && <CommentComp commentList={[rectifyRecord]} />} */}
                            {
                                rectifyRecord !== undefined
                                    ? (
                                        <Descriptions className={cls.desBox}>
                                            <Descriptions.Item label="整改措施" span={3}>{rectifyRecord.commentMsg}</Descriptions.Item>
                                            <Descriptions.Item label="附件" span={3}>
                                                {
                                                    (rectifyRecord.attachments ?? []).length > 0
                                                        ? (
                                                            <FileBox
                                                                isDownload
                                                                value={transferAttachmentVoToFileType(rectifyRecord.attachments)}
                                                                isEditName={false}
                                                                isUpload={false}
                                                                isDelete={false}
                                                                style={{width: "100%"}}
                                                            />
                                                        )
                                                        : null
                                                }
                                            </Descriptions.Item>
                                        </Descriptions>
                                    )
                                    : null
                            }
                        </div>
                    </ComCollapsePanel>
                    <ComCollapsePanel key="5" header="审批流程">
                        <div style={{padding: "0 20px"}}>
                            {
                                visibleButtons.includes(RectificationActionType.Submit) && reformDetail.comments?.length !== 1
                                    ? (
                                        <Form
                                            style={{marginTop: 20}}
                                            form={commentForm}
                                            className={cls.specFormBox}
                                        >
                                            <Form.Item
                                                label={isRectifyPerson ? "整改措施：" : "审批意见："}
                                                rules={[{required: isRectifyPerson, message: "请输入整改措施"}]}
                                                name="message"
                                            >
                                                <TextArea maxLength={300} showCount />
                                            </Form.Item>
                                            <Form.Item label="附件">
                                                <FileBox style={{width: 564}} value={fileList} onChange={setFileList} />
                                            </Form.Item>
                                        </Form>
                                    )
                                    : null
                            }
                            <div className="ant-form ant-form-horizontal">
                                <div className="ant-row ant-form-item">
                                    <div className="ant-col ant-form-item-label" style={{width: 100, textAlign: "right"}}>
                                        <label title="审批日志">审批日志</label>
                                    </div>
                                    <CommentComp className="ant-col ant-form-item-control" commentList={commentList} />
                                </div>
                            </div>
                        </div>
                    </ComCollapsePanel>
                </Collapse>
            </div>
            <RectifyButtons
                reformDetail={reformDetail}
                visibleButtons={visibleButtons}
                onSubmit={handleSubmit}
                loading={submitLoading}
                flowChartData={flowChartData}
                back={back}
            />
            <FlowChart
                visible={flowChartVisible}
                onCancel={() => setFlowChartVisible(false)}
                data={flowChartData}
                flowState={reformDetail.processStatus as FlowState}
            />
            {/* {(replyTemplateResult?.sign === 1 || noticeTemplateResult?.sign === 1) && previewVisible && (
                <RectificationSheetSign
                    visible={previewVisible}
                    handleSubmit={handleSubmit}
                    reformDetail={reformDetail}
                    replyReportFormTemplate={replyTemplateResult}
                    noticeReportFormTemplate={noticeTemplateResult}
                    setVisible={setPreviewVisible}
                    formPdfInfo={signPdfInfo}
                    launchReform={launchReform}
                    {...messageInfo}
                />
            )} */}
        </div>
    );
};

export default RectifyDetail;
