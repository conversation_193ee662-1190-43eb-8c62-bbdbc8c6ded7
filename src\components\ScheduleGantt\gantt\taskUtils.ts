/* eslint-disable no-bitwise */
/* eslint-disable max-len */
/* eslint-disable import/no-cycle */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import {v4 as uuidv4} from "uuid";
import {message, Modal} from "antd";
import moment from "moment";
import {GanttTask, GanttLink} from "./interface";
import {notNullOrUndefined} from "../common/function";  // eslint-disable-line
import {isStandardCalendar} from "../common/calendar";
import {TaskStatus} from "../common/constant";
import ganttManager from "./ganttManager";
import {getPlanStartDate} from "./calendarUtils";
import {DutyPerson} from "../components/SelectPersonDrawer/data";
import {updatePlanInfo} from "../../../api/Preparation";

export const moveUpTask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const index = gantt.getTaskIndex(selectedId);
    const pId = gantt.getParent(selectedId);
    if (index !== 0) {
        gantt.moveTask(selectedId, index - 1, pId);
    }
};

export const moveDownTask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const pId = gantt.getParent(selectedId);
    const nextId = gantt.getNextSibling(selectedId);
    if (nextId !== null) {
        const nextIndex = gantt.getTaskIndex(nextId);
        gantt.moveTask(selectedId, nextIndex, pId);
    }
};

export const upgradeTask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const pId = gantt.getParent(selectedId);
    if (pId !== "0") {
        const index = gantt.getTaskIndex(pId);
        const grandparentId = gantt.getParent(pId);
        gantt.moveTask(selectedId, index + 1, grandparentId);
    }
};

export const degradeTask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const index = gantt.getTaskIndex(selectedId);
    if (index !== 0) {
        const preId = gantt.getPrevSibling(selectedId);
        const children = gantt.getChildren(preId);
        gantt.moveTask(selectedId, children.length, preId);
    }
};

export const createTask = () => {
    const selectedId = gantt.getSelectedId();
    // const parentTask = gantt.getTask(gantt.getParent(selectedId));
    // const parentStartDate = parentTask?.start_date;
    // console.log("ganttManager.planStartDate", ganttManager.planStartDate);
    const startDate = ganttManager.planStartDate !== undefined
        ? getPlanStartDate(ganttManager.planStartDate)
        : undefined;
    // console.log("startDate", startDate);
    const newTask = {
        type: gantt.config.types.task,
        text: "新任务",
        duration: ganttManager.durationFormatter.parse("1"),
        start_date: startDate
    };
    if (selectedId === null) {
        gantt.createTask(newTask, "0");
    } else {
        gantt.createTask(newTask, gantt.getParent(selectedId) as string);
    }
};

export const createSubtask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const parentTask: GanttTask = gantt.getTask(selectedId);
    const newTask = {
        type: gantt.config.types.task,
        text: "新任务",
        duration: parentTask.duration,
        start_date: parentTask.start_date,
        end_date: parentTask.end_date,
        request_duration: parentTask.request_duration,
        request_start_date: parentTask.request_start_date,
        request_end_date: parentTask.request_end_date,
    };
    gantt.createTask(newTask, selectedId);
};

export const createMilepost = () => {
    const selectedId = gantt.getSelectedId();
    const startDate = ganttManager.planStartDate !== undefined
        ? getPlanStartDate(ganttManager.planStartDate)
        : undefined;
    const newMilestone = {
        type: gantt.config.types.milestone,
        text: "新里程碑",
        start_date: startDate
    };
    if (selectedId === null) {
        gantt.createTask(newMilestone, "0");
    } else {
        gantt.createTask(newMilestone, gantt.getParent(selectedId) as string);
    }
};

export const deleteTask = () => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        message.error("请先选中一条任务");
        return;
    }
    const task = gantt.getTask(selectedId);
    Modal.confirm({
        title: `是否删除该${task?.type === gantt.config.types.milestone ? "里程碑" : "任务"}`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
            if (!gantt.isTaskExists(selectedId)) {
                return;
            }
            gantt.deleteTask(selectedId);
            if (gantt.getTaskCount() === 0) {
                ganttManager.isWbs = false;
                ganttManager.editorContext.setIsWbs(false);
                // 解除上级计划关联
                const planId = ganttManager.editorContext.planInfo.id;
                if (planId !== undefined) {
                    try {
                        const res = await updatePlanInfo({id: planId, parentId: undefined, parentPlanTaskIds: []});
                        if (res.data !== undefined) {
                            ganttManager.editorContext.setPlanInfo({
                                ...ganttManager.editorContext.planInfo,
                                parentId: undefined,
                            });
                        }
                    } catch (error) {
                        console.log("error", error);
                    }
                }
            }
        }
    });
};

export const getTaskStatus = (actualStartDate: Date | undefined, actualEndDate: Date | undefined) => {
    let taskStatus = TaskStatus.NOT_START;
    if (actualStartDate !== undefined) {
        if (actualEndDate !== undefined) {
            taskStatus = TaskStatus.COMPLETED;
        } else {
            taskStatus = TaskStatus.IN_PROGRESS;
        }
    } else {
        taskStatus = TaskStatus.NOT_START;
    }
    return taskStatus;
};

export const updateTaskHasPhoto = (hasPhoto: boolean) => {
    const selectedId = gantt.getSelectedId();
    if (selectedId === null) {
        return;
    }
    const task = gantt.getTask(selectedId);
    gantt.updateTask(selectedId, {...task, hasPhoto});
};

// 任务的实际时间是否是从工序报验同步
export const checkIsSyncTask = (task: GanttTask) => (task.actual_syn & 0b100) !== 0b100;

// 实际开始时间是否为同步
export const checkIsSyncStart = (task: GanttTask) => (task.actual_syn | 0b110) !== 0b111;

// 修改实际开始时间，并标记实际时间为手动修改
export const updateModifyStartSync = (task: GanttTask) => task.actual_syn | 0b101;

// 实际完成时间是否为同步
export const checkIsSyncEnd = (task: GanttTask) => (task.actual_syn | 0b101) !== 0b111;

// 修改实际完成时间，并标记实际时间为手动修改
export const updateModifyEndSync = (task: GanttTask) => task.actual_syn | 0b110;

/**
 * 自动继承汇总工序报验实际时间
 */
const updateProcessInspectionTasks = (rootTask: GanttTask): GanttTask[] => {
    if (gantt.isTaskExists(rootTask.id) === false) {
        return [];
    }
    // 保存发生变化的任务
    const taskMap = new Map<string | number, GanttTask>();

    // 先执行向下继承操作
    const updateChildTask = (task: GanttTask, parentTask: GanttTask | null, needUpdate: boolean) => {
        // console.log("updateChildTask parentTask", parentTask);
        // console.log("updateChildTask task", task);
        const isSyncStart = checkIsSyncStart(task);
        const isSyncEnd = checkIsSyncEnd(task);
        // console.log("updateChildTask isSyncStart", isSyncStart);
        // console.log("updateChildTask isSyncEnd", isSyncEnd);
        // 先执行向下继承操作，里程碑不会向下继承
        if (parentTask !== null && task.type !== gantt.config.types.milestone) {
            const isSyncParentTask = checkIsSyncTask(parentTask);
            const needUpdateChild = needUpdate || isSyncParentTask;
            // console.log("isSyncParentTask", isSyncParentTask);
            // console.log("isSyncStart", isSyncStart);
            // console.log("isSyncEnd", isSyncEnd);
            if (needUpdateChild
                && (isSyncStart || isSyncEnd)
                && (notNullOrUndefined(parentTask.actual_start) || notNullOrUndefined(parentTask.actual_end))) {
                // 根据父节点时间自动继承子节点时间
                if (isSyncStart) {
                    // 当前节点实际开始时间为同步，则继承父节点时间
                    task.actual_start = parentTask.actual_start;
                }
                if (isSyncEnd) {
                    // 当前节点实际完成时间为同步，则继承父节点时间
                    task.actual_end = parentTask.actual_end;
                }
                if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                    if (task.actual_end! < task.actual_start!) {
                        task.actual_end = task.actual_start;
                        task.actual_syn = updateModifyEndSync(task);
                    }
                    task.actual_duration = gantt.getCalendar("custom").calculateDuration({
                        start_date: task.actual_start,
                        end_date: task.actual_end,
                    });
                } else {
                    task.actual_duration = -1;
                }
                // 将同步状态设置和父节点一样
                task.actual_syn = parentTask.actual_syn;
                task.taskStatus = getTaskStatus(task.actual_start, task.actual_end);
                // console.log("向下继承父节点实际时间", task);
                taskMap.set(task.id, task);
            }
        }
        const childrenTaskId = gantt.getChildren(task.id);
        // 获取所有子任务状态
        childrenTaskId.map((taskChildId: string | number) => {
            let childTask: GanttTask | undefined = taskMap.get(taskChildId);
            if (childTask === undefined) {
                childTask = updateChildTask(gantt.getTask(taskChildId), task, needUpdate);
            }
            return childTask;
        });
        return task;
    };

    updateChildTask(rootTask, null, false);

    // 执行向上汇总操作
    const updateParentTask = (task: GanttTask) => {
        // console.log("updateParentTask", task);
        const isSyncStart = checkIsSyncStart(task);
        const isSyncEnd = checkIsSyncEnd(task);
        const isSyncTask = checkIsSyncTask(task);
        // console.log("updateParentTask isSyncStart", isSyncStart);
        // console.log("updateParentTask isSyncEnd", isSyncEnd);
        // console.log("updateParentTask isSyncTask", isSyncTask);
        // 从同步工序报验的任务节点开始向上汇总
        if (isSyncTask && (notNullOrUndefined(task.actual_start) || notNullOrUndefined(task.actual_end))) {
            if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                task.actual_duration = gantt.getCalendar("custom").calculateDuration({
                    start_date: task.actual_start,
                    end_date: task.actual_end,
                });
            } else {
                task.actual_duration = -1;
            }
            taskMap.set(task.id, task);
            // console.log("child task", task);
            return task;
        }
        const childrenTaskId = gantt.getChildren(task.id);
        // 获取所有子任务
        const childrenList: (GanttTask | undefined)[] = childrenTaskId.map((taskChildId: string | number) => {
            let childTask: GanttTask | undefined = taskMap.get(taskChildId);
            if (childTask === undefined) {
                childTask = updateParentTask(gantt.getTask(taskChildId));
            }
            return childTask;
        });
        // console.log("向上汇总子节点实际时间 childrenList", childrenList);
        // let statusRes: number | undefined;
        let projectActualStart: Date | undefined;
        let projectActualEnd: Date | undefined;
        let hasEnd = true;
        for (let i = 0, len = childrenList.length; i < len; i++) {
            const childTask: GanttTask = childrenList[i] as GanttTask;
            // 如果是里程碑，则不参与汇总
            if (childTask.type !== gantt.config.types.milestone) {
                // 计算父任务实际开始时间，根据最早的实际开始时间
                if (childTask.actual_start !== undefined) {
                    if (projectActualStart === undefined || projectActualStart > childTask.actual_start) {
                        projectActualStart = childTask.actual_start;
                    }
                }
                // 计算父任务实际完成时间，子任务全部结束的情况下，根据最晚的实际完成时间
                if (hasEnd && childTask.actual_end !== undefined) {
                    if (projectActualEnd === undefined || projectActualEnd < childTask.actual_end) {
                        projectActualEnd = childTask.actual_end;
                    }
                } else {
                    projectActualEnd = undefined;
                    hasEnd = false;
                }
            }
        }
        // console.log("向上汇总子节点实际时间 task", task);
        // console.log("向上汇总子节点实际时间 projectActualStart", projectActualStart);
        // console.log("向上汇总子节点实际时间 projectActualEnd", projectActualEnd);
        if (task.actual_start !== projectActualStart
            || task.actual_end !== projectActualEnd) {
            // 数据发生变化则更新
            if (isSyncStart) {
                // 当前节点实际开始时间为同步，则更新修改
                task.actual_start = projectActualStart;
            }
            if (isSyncEnd) {
                // 当前节点实际完成时间为同步，则更新修改
                task.actual_end = projectActualEnd;
            }
            // task.actual_start = projectActualStart;
            // task.actual_end = projectActualEnd;
            task.taskStatus = getTaskStatus(task.actual_start, task.actual_end);
            if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                if (task.actual_end! < task.actual_start!) {
                    task.actual_end = task.actual_start;
                    task.actual_syn = updateModifyEndSync(task);
                }
                task.progress = 1;
                task.actual_duration = gantt.getCalendar("custom").calculateDuration({
                    start_date: task.actual_start,
                    end_date: task.actual_end,
                });
            } else {
                task.actual_duration = -1;
            }
            // console.log("向上汇总子节点实际时间", task);
            taskMap.set(task.id, task);
        }
        return task;
    };

    updateParentTask(rootTask);

    // console.log("taskMap", taskMap);
    const res: GanttTask[] = [];
    taskMap.forEach((task: GanttTask) => res.push(task));
    // console.log("res", res);
    return res;
};

const updateProcessInspectionActualTimeWithTaskList = (tasks: GanttTask[]) => {
    let updateTasks: GanttTask[] = [];
    // 自动继承汇总工序报验实际时间
    tasks.forEach((task: GanttTask) => {
        // console.log("updateProcessInspectionChildTasks", task);
        updateTasks = [...updateTasks, ...updateProcessInspectionTasks(task)];
    });
    // 批量更新
    gantt.batchUpdate(() => {
        updateTasks.forEach((t: GanttTask) => {
            gantt.updateTask(t.id, t);
        });
    });
};

/**
 * 根据工序报验更新实际时间
 */
export const updateAllProcessInspectionActualTime = () => {
    setTimeout(() => {
        const rootTasks = gantt.getTaskBy((task: GanttTask) => task.parent === "0", null);
        // console.log("rootTasks", rootTasks);
        updateProcessInspectionActualTimeWithTaskList(rootTasks);
    });
};

/**
 * 根据根节点项目刷新任务
 */
export const updateTasksWithRootProject = (rootProjectId: string | number): GanttTask[] => {
    if (gantt.isTaskExists(rootProjectId) === false) {
        return [];
    }
    // 保存发生变化的父任务
    const taskMap = new Map<string | number, GanttTask>();

    // 递归计算父任务执行状态
    const checkTask = (taskId: string | number) => {
        const task = gantt.getTask(taskId);
        // console.log("checkTask", task);
        if (task.type === gantt.config.types.task || task.type === gantt.config.types.milestone) {
            // 叶子结点的任务直接返回
            if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                task.actual_duration = gantt.calculateDuration({
                    start_date: task.actual_start,
                    end_date: task.actual_end,
                    task
                });
            } else {
                task.actual_duration = -1;
            }
            // console.log("child task", task);
            return task;
        }
        const taskIdChildren = gantt.getChildren(taskId);
        // 获取所有子任务状态
        const childrenList: (GanttTask | undefined)[] = taskIdChildren.map((taskChildId: string | number) => {
            let childTask: GanttTask | undefined = taskMap.get(taskChildId);
            if (childTask === undefined) {
                childTask = checkTask(taskChildId);
            }
            return childTask;
        });
        let statusRes: number | undefined;
        let projectActualStart: Date | undefined;
        let projectActualEnd: Date | undefined;
        // 假设project默认存在完成时间
        let hasEnd = true;
        // 根据一级子任务状态得出当前任务状态
        for (let i = 0, len = childrenList.length; i < len; i++) {
            const childTask: GanttTask = childrenList[i] as GanttTask;
            if (childTask.type !== gantt.config.types.milestone) {
                const status = childTask.taskStatus;
                if (i === 0) {
                    // 默认状态为第一个子任务的状态
                    statusRes = status;
                } else if (status !== statusRes) {
                    // 如果存在子任务状态不同，则直接设状态为进行中
                    statusRes = TaskStatus.IN_PROGRESS;
                }
                // 计算父任务实际开始时间，根据最早的实际开始时间
                if (childTask.actual_start !== undefined) {
                    if (projectActualStart === undefined || projectActualStart > childTask.actual_start) {
                        projectActualStart = childTask.actual_start;
                    }
                }
                // 计算父任务实际完成时间，子任务全部结束的情况下，根据最晚的实际完成时间
                if (hasEnd && childTask.actual_end !== undefined) {
                    if (projectActualEnd === undefined || projectActualEnd < childTask.actual_end) {
                        projectActualEnd = childTask.actual_end;
                    }
                } else {
                    projectActualEnd = undefined;
                    hasEnd = false;
                }
            }
        }
        if (task.taskStatus !== statusRes
            || task.actual_start !== projectActualStart
            || task.actual_end !== projectActualEnd) {
            if (task.actual_start !== projectActualStart) {
                task.actual_syn = updateModifyStartSync(task);
            }
            if (task.actual_end !== projectActualEnd) {
                task.actual_syn = updateModifyEndSync(task);
            }
            // 数据发生变化则更新
            task.taskStatus = statusRes;
            task.actual_start = projectActualStart;
            task.actual_end = projectActualEnd;
            if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                task.progress = 1;
                task.actual_duration = gantt.calculateDuration({
                    start_date: task.actual_start,
                    end_date: task.actual_end,
                    task
                });
            } else {
                task.actual_duration = -1;
            }

            taskMap.set(task.id, task);
        }
        return task;
    };

    checkTask(rootProjectId);

    // console.log("taskMap", taskMap);
    const res: GanttTask[] = [];
    taskMap.forEach((task: GanttTask) => res.push(task));
    // console.log("res", res);
    return res;
};

export const getRootIdWithTask = (task: GanttTask | string | number): string | number | undefined => {
    let ganttTask: GanttTask | undefined;
    if (typeof task === "string" || typeof task === "number") {
        if (gantt.isTaskExists(task)) {
            ganttTask = gantt.getTask(task);
        }
    } else {
        ganttTask = task;
    }
    if (ganttTask === undefined) {
        return;
    }
    // task.parent
    let rootProjectId: string | number = ganttTask.id;
    // 查找最上级任务
    while (rootProjectId !== undefined) {
        const parentId = gantt.getParent(rootProjectId);
        if (gantt.isTaskExists(parentId) === true) {
            rootProjectId = parentId;
        } else {
            break;
        }
    }
    return rootProjectId;
};

// 根据子任务刷新父任务
export const updateProjectActualTimeWithTask = (task: GanttTask | string | number) => {
    const rootProjectId = getRootIdWithTask(task);
    if (rootProjectId !== undefined) {
        const updateTasks: GanttTask[] = updateTasksWithRootProject(rootProjectId);
        // 批量更新
        gantt.batchUpdate(() => {
            updateTasks.forEach((t: GanttTask) => {
                gantt.updateTask(t.id, t);
            });
        });
    }
};

// 根据多个子任务刷新父任务
export const updateProjectActualTimeWithTaskList = (tasks: (GanttTask | string | number)[]) => {
    const rootIds: (string | number)[] = [];
    tasks.forEach((task) => {
        const rootProjectId = getRootIdWithTask(task);
        if (rootProjectId !== undefined && rootIds.includes(rootProjectId) === false) {
            rootIds.push(rootProjectId);
        }
    });
    let updateTasks: GanttTask[] = [];
    rootIds.forEach((rootId: string | number) => {
        // console.log("updateTasksWithRootProject", rootId);
        updateTasks = [...updateTasks, ...updateTasksWithRootProject(rootId)];
    });
    // 批量更新
    gantt.batchUpdate(() => {
        updateTasks.forEach((t: GanttTask) => {
            gantt.updateTask(t.id, t);
        });
    });
};

// 计算更新全部 预计/实际 结束时间
export const updateAllProjectActualTime = () => {
    // console.log("updateAllProjectActualTime");
    setTimeout(() => {
        const updateTasks: GanttTask[] = [];
        gantt.eachTask((task: GanttTask) => {
            if (notNullOrUndefined(task.actual_start) && notNullOrUndefined(task.actual_end)) {
                task.actual_duration = gantt.calculateDuration({
                    start_date: task.actual_start,
                    end_date: task.actual_end,
                    task
                });
                updateTasks.push(task);
            }
        });
        // 批量更新
        gantt.batchUpdate(() => {
            updateTasks.forEach((t: GanttTask) => {
                gantt.updateTask(t.id, t);
            });
        });
    });
    // setTimeout(() => {
    //     const rootTasks = gantt.getTaskBy((task: GanttTask) => task.parent === "0", null);
    //     console.log("rootTasks", rootTasks);
    //     updateProjectActualTimeWithTaskList(rootTasks);
    // });
};

/**
 * 格式化工期
 */
export const calculateDuration = (duration: number | undefined): string | null => {
    if (duration === undefined || duration < 0) {
        return null;
    }
    return ganttManager.durationFormatter.format(duration);
};

/**
 * 计算实际滞后偏差，(实际完成时间 或 当前时间) - 计划完成时间
 */
export const calculateOffset = (task: GanttTask): number | null => {
    let planEnd: Date | undefined;
    let endDate: Date | undefined;
    if (task.taskStatus === TaskStatus.IN_PROGRESS) {
        planEnd = task.end_date;
        // 进行中，取当前时间作为结束时间
        endDate = gantt.getCalendar("custom").getClosestWorkTime({date: new Date(), dir: "past"});
    } else if (task.taskStatus === TaskStatus.COMPLETED) {
        planEnd = task.end_date;
        endDate = task.actual_end;
    }
    if (planEnd === undefined || endDate === undefined) {
        return null;
    }
    let symbol = 1;
    let startDate = planEnd;
    if (planEnd > endDate) {
        symbol = -1;
        startDate = endDate;
        endDate = planEnd;
    }
    // console.log("startDate", startDate);
    // console.log("endDate", endDate);
    // const offset = gantt.calculateDuration({
    //     start_date: startDate,
    //     end_date: actualEnd,
    //     task
    // });
    const momentEnd = moment(endDate).startOf("day");
    const momentStart = moment(startDate).startOf("day");
    const offset = momentEnd.diff(momentStart, "days");
    return offset * symbol;
};

// /**
//  * 计算预计完成时间
//  */
// export const calculateEstimatedEnd = (task: GanttTask): Date | undefined => {
//     // console.log("calculateEstimatedEnd task", task);
//     if (task !== undefined
//         && task.taskStatus === TaskStatus.IN_PROGRESS
//         && typeof task.duration === "number"
//         && task.duration > 0
//         && task.actual_start !== undefined
//         && typeof task.progress === "number"
//         && task.progress > 0
//         && task.progress_record_date !== undefined
//         && task.progress_record_date > task.actual_start) {
//         // 累计完成修改时间
//         let modifyDate = task.progress_record_date;
//         modifyDate = gantt.date.add(gantt.date.day_start(modifyDate), isStandardCalendar(ganttManager.currentCalendarInfo) ? 17 : 0, "hour");
//         // modifyDate = gantt.getClosestWorkTime({
//         //     dir: "future",
//         //     date: modifyDate,
//         //     unit: gantt.config.duration_unit,
//         //     task
//         // });
//         // 已经消耗的工期
//         const progressDuration = gantt.calculateDuration({
//             start_date: task.actual_start,
//             end_date: modifyDate,
//             task
//         });
//         // 根据消耗的工期和累计完成计算预计完成时间
//         let forecastDuration;
//         if (task.is_typical_offset === true) {
//             // （典型偏差）
//             forecastDuration = Math.ceil(progressDuration / task.progress);
//         } else {
//             // （非典型偏差）
//             const planDuration = Math.ceil(task.duration * (1 - task.progress));
//             forecastDuration = progressDuration + planDuration;
//         }
//         const estimatedEnd = gantt.calculateEndDate({
//             start_date: task.actual_start,
//             duration: forecastDuration,
//             unit: gantt.config.duration_unit,
//             task
//         });
//         return estimatedEnd;
//     }
//     return undefined;
// };

// /**
//  * 计算预测滞后偏差，预计/实际完成时间和计划完成时间的差值
//  */
// export const calculateForecastOffset = (task: GanttTask): number | undefined => {
//     let planEnd: Date | undefined;
//     let actualEnd: Date | undefined;
//     if (task.taskStatus === TaskStatus.IN_PROGRESS) {
//         planEnd = task.end_date;
//         actualEnd = task.estimated_completion_date;
//     } else if (task.taskStatus === TaskStatus.COMPLETED) {
//         planEnd = task.end_date;
//         actualEnd = task.actual_end;
//     }
//     if (planEnd === undefined || actualEnd === undefined) {
//         return undefined;
//     }
//     let symbol = 1;
//     let startDate = planEnd;
//     let endDate = actualEnd;
//     if (planEnd > actualEnd) {
//         symbol = -1;
//         startDate = actualEnd;
//         endDate = planEnd;
//     }
//     // console.log("startDate", startDate);
//     // console.log("endDate", endDate);
//     const offset = gantt.calculateDuration({
//         start_date: startDate,
//         end_date: endDate,
//         task
//     });
//     return offset * symbol;
// };

/**
 * 根据进度更新任务
 */
export const updateTaskWithProgess = (task: GanttTask) => {
    // console.log("updateTaskWithProgess", task);
    switch (task.progress) {
        case 0: {
            task.taskStatus = TaskStatus.NOT_START;
            task.actual_duration = -1;
            task.actual_start = undefined;
            task.actual_end = undefined;
            break;
        }
        case 1: {
            task.taskStatus = TaskStatus.COMPLETED;
            if (notNullOrUndefined(task.actual_start)) {
                let endDate = new Date();
                task.actual_end = endDate;
                endDate = gantt.date.add(gantt.date.day_start(endDate), isStandardCalendar(ganttManager.currentCalendarInfo) ? 17 : 0, "hour");
                task.actual_duration = gantt.calculateDuration({
                    start_date: task.actual_start,
                    end_date: endDate,
                    task
                });
            }
            break;
        }
        default: {
            task.taskStatus = TaskStatus.IN_PROGRESS;
            task.actual_duration = -1;
            task.actual_end = undefined;
            break;
        }
    }
    gantt.updateTask(task.id, task);
    // updateProjectActualTimeWithTask(task);
};

export const linksWithPredecessors = (predecessors: string, task: {id: string; text: string}): GanttLink[] => {
    const links: GanttLink[] = [];
    const preTasks: string[] = predecessors.split(",").map((str: string) => str.trim());
    preTasks.forEach((preItem) => {
        if (preItem.length > 0) {
            if (ganttManager.linksFormatter.canParse(preItem)) {
                const t: GanttLink = ganttManager.linksFormatter.parse(preItem);
                if (t.source !== null) {
                    const link: GanttLink = {
                        id: uuidv4().replace(/-/g, ""),
                        source: t.source,
                        target: task.id,
                        type: t.type,
                        lag: t.lag
                    };
                    links.push(link);
                } else {
                    message.info({content: `${task.text}的前置任务"${preItem}"寻找不到`});
                }
            } else {
                message.info({content: `${task.text}的前置任务"${preItem}"格式不对`});
            }
        }
    });
    return links;
};

export const linksWithTargetId = (targetId: string | number) => gantt.getLinks().filter((item: GanttLink) => item.target === targetId);

export const taskPredecessors = (task: GanttTask) => {
    const labels: string[] = [];
    gantt.getLinks().forEach((link: GanttLink) => {
        if (link.target === task.id) {
            labels.push(String(ganttManager.linksFormatter.format(link)));
        }
    });
    // const links = task.$target;
    // if (links === null || links === undefined) {
    //     return undefined;
    // }
    // const labels = [];
    // for (let i = 0; i < links.length; i++) {
    //     const link = gantt.getLink(links[i]);
    //     labels.push(String(ganttLinksFormatter().format(link)));
    // }
    return labels.join(", ");
};

/**
 * 获取总工期
 */
export const getTotalDuration = () => {
    let start_date: Date | null = null;
    let end_date: Date | null = null;
    gantt.eachTask((task: GanttTask) => {
        // console.log("eachTask task", task);
        if (start_date === null || start_date.getTime() > task.start_date.getTime()) {
            start_date = task.start_date;
        }
        if (end_date === null || end_date.getTime() < task.end_date.getTime()) {
            end_date = task.end_date;
        }
    });
    // console.log("start_date", start_date);
    // console.log("end_date", end_date);
    if (start_date !== null && end_date !== null) {
        const duration = gantt.getCalendar("custom").calculateDuration({start_date, end_date});
        const span = Number.parseFloat(ganttManager.durationFormatter.format(duration));
        return span;
    }
    return 0;
};

/**
 * 获取责任人显示文本，使用中文逗号"，"分隔
 */
export const getDutyPersonLabel = (dutyPerson?: DutyPerson) => {
    // console.log("dutyPerson", dutyPerson);
    const input = dutyPerson?.input ?? "";
    const select = dutyPerson?.select.map((item) => ganttManager.allPersonUserRealNameMap.get(item) ?? item).join("，") ?? "";
    // console.log("input", input);
    // console.log("select", select);
    return [input, select].filter((item) => item.length > 0).join("，");
};

export const getParentPlanTasks = () => {
    const {parentId} = ganttManager.editorContext.planInfo;
    if (parentId === undefined || parentId.length === 0) {
        return {parentPlanTasks: [], leafTasks: []};
    }
    const parentPlanTasks: GanttTask[] = [];
    gantt.eachTask((task: GanttTask) => {
        if (task.planId === parentId) {
            parentPlanTasks.push(task);
        }
    });

    const leafTasks = parentPlanTasks.filter((task: GanttTask) => {
        const children = gantt.getChildren(task.id);
        const parentPlanChildren = children.filter((childId) => {
            const child = gantt.getTask(childId);
            return child.planId === parentId;
        });
        return parentPlanChildren.length === 0;
    });
    // console.log("parentPlanTasks", parentPlanTasks);
    // console.log("leafTasks", leafTasks);
    return {parentPlanTasks, leafTasks};
};
