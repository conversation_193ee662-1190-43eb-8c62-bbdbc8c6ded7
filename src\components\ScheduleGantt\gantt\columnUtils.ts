/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import moment from "moment";
// import {cloneDeep} from "lodash-es";
import {ColumnItem} from "../api/plan/type";
import {CustomValueType, TaskStatus, taskStatusOptions} from "../common/constant";
import {updateGanttConfig, setShowGanttTimeLine} from "./ganttConfig";
import ganttManager from "./ganttManager";
import {GanttTask, GanttColumn, GanttLink} from "./interface";
import {calculateDuration, calculateOffset, checkIsSyncEnd, checkIsSyncStart, getDutyPersonLabel} from "./taskUtils";
import mileStonePNG from "../../../../static/image/gantt/milestone.png";

export type GanttColumnType = "wbs" // 序号
    | "text" // 任务名称
    | "duration" // 计划工期
    | "start_date" // 计划开始
    | "end_date" // 计划完成
    | "request_duration" // 要求工期
    | "request_start_date" // 要求开始
    | "request_end_date" // 要求完成
    | "predecessors" // 前置任务
    | "dutyUnit" // 责任单位
    | "dutyPerson" // 责任人
    | "taskStatus" // 执行状态
    | "actual_duration" // 实际工期
    | "actual_start" // 实际开始
    | "actual_end" // 实际完成
    | "actualPlan" // 实际进度
    | "lagDeviation" // 滞后偏差
    | "hasRelateModel" // 关联模型
    | "hasRelatePhoto"; // 关联照片

export type ServerGanttColumnType = "wbs" // 序号
    | "taskName" // 任务名称
    | "planDuration" // 计划工期
    | "planStartDate" // 计划开始
    | "planEndDate" // 计划完成
    | "requestDuration" // 要求工期
    | "requestStartDate" // 要求开始
    | "requestEndDate" // 要求完成
    | "predecessors" // 前置任务
    | "dutyUnit" // 责任单位
    | "dutyPerson" // 责任人
    | "taskStatus" // 执行状态
    | "actualDuration" // 实际工期
    | "actualStartDate" // 实际开始
    | "actualEndDate" // 实际完成
    | "actualPlan" // 实际进度
    | "lagDeviation" // 滞后偏差
    | "hasRelateModel" // 关联模型
    | "hasRelatePhoto"; // 关联照片

/**
* 本地内置列，后端和前端的column code映射, key-后端Code value-前端Code
*/
export const ganttColumnNameMap = new Map<ServerGanttColumnType, GanttColumnType>([
    ["wbs", "wbs"],
    ["taskName", "text"],
    ["planDuration", "duration"],
    ["planStartDate", "start_date"],
    ["planEndDate", "end_date"],
    ["requestDuration", "request_duration"],
    ["requestStartDate", "request_start_date"],
    ["requestEndDate", "request_end_date"],
    ["predecessors", "predecessors"],
    ["actualDuration", "actual_duration"],
    ["actualStartDate", "actual_start"],
    ["actualEndDate", "actual_end"],
    ["dutyUnit", "dutyUnit"],
    ["dutyPerson", "dutyPerson"],
    ["taskStatus", "taskStatus"],
    ["lagDeviation", "lagDeviation"],
    ["hasRelateModel", "hasRelateModel"],
    ["hasRelatePhoto", "hasRelatePhoto"],
    ["actualPlan", "actualPlan"],
]);

export interface GanttColumnItem {
    columnId: GanttColumnType;
    editable?: boolean;
}

export const getGanttEditorMap = () => {
    const {durationFormatter} = ganttManager;
    const minDate = new Date(2000, 0, 1);
    const maxDate = new Date(2100, 11, 31);
    const minActualDate = new Date(2000, 0, 1);
    const maxActualDate = moment().endOf("day").toDate();

    // const staffList = gantt.serverList(staffsKey) as TaskDutyPerson[];
    const editorMap = new Map<GanttColumnType, any>();
    editorMap.set("text", {type: "text", map_to: "text"});
    editorMap.set("duration", {type: "duration", map_to: "duration", min: 0, max: 9999, formatter: durationFormatter});
    editorMap.set("start_date", {type: "date", map_to: "start_date", min: minDate, max: maxDate});
    editorMap.set("end_date", {type: "date", map_to: "end_date", min: minDate, max: maxDate});
    editorMap.set("request_duration", {type: "duration", map_to: "request_duration", min: 0, max: 9999, formatter: durationFormatter});
    editorMap.set("request_start_date", {type: "date", map_to: "request_start_date", min: minDate, max: maxDate});
    editorMap.set("request_end_date", {type: "date", map_to: "request_end_date", min: minDate, max: maxDate});
    editorMap.set("actual_duration", {type: "duration", map_to: "actual_duration", min: 0, max: 9999, formatter: durationFormatter});
    editorMap.set("actual_start", {type: "date", map_to: "actual_start", min: minActualDate, max: maxActualDate});
    editorMap.set("actual_end", {type: "date", map_to: "actual_end", min: minActualDate, max: maxActualDate});
    editorMap.set("actualPlan", {type: "actualPlan", map_to: "actualPlan"});
    // editorMap.set("predecessors", {type: "predecessor", map_to: "auto", formatter: linksFormatter});
    editorMap.set("dutyUnit", {type: "text", map_to: "dutyUnit"});
    // editorMap.set("dutyPerson", {type: "text", map_to: "dutyPerson"});
    return editorMap;
};

const formatDateText = (date: Date | string) => moment(date).format("YYYY.MM.DD");

export const getGanttColumnMap = (): Map<GanttColumnType, GanttColumn> => {
    const {durationFormatter} = ganttManager;
    const editorMap = getGanttEditorMap();

    const columnMap = new Map<GanttColumnType, GanttColumn>();
    columnMap.set("wbs", {name: "wbs", label: "序号", width: 60, template: gantt.getWBSCode.bind(gantt), resize: true});
    columnMap.set("text", {
        name: "text",
        label: "任务名称",
        width: 160,
        align: "left",
        tree: true,
        editor: editorMap.get("text"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return `
                    <div style="display: flex; align-items: center; flex-wrap: nowrap">
                        <img src=${mileStonePNG} width="20px" height="20px" style="margin-right: 8px" />
                        <span class="gantt_grid_ellipsis_text" style="flex: 1">${task.text}</span>
                    </div>
                `;
            }
            return `<div class="gantt_grid_ellipsis_text GanttTask" >${task.text}</div>`;
        }
    });
    columnMap.set("duration", {
        name: "duration",
        label: "计划工期",
        width: 80,
        align: "left",
        editor: editorMap.get("duration"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return null;
            }
            const duration = durationFormatter.format(task.duration);
            const requestTask = ganttManager.getRequestTaskWithTask(task);
            if (requestTask !== undefined) {
                const {request_duration} = requestTask;
                if (request_duration !== undefined && task.duration > request_duration) {
                    return `<div style="color: red">${duration}</div>`;
                }
            }
            return duration;
        }
    });
    columnMap.set("start_date", {
        name: "start_date",
        label: "计划开始",
        width: 140,
        align: "left",
        editor: editorMap.get("start_date"),
        resize: true,
        template: (task: GanttTask) => {
            const dateText = formatDateText(task.start_date);
            return `${dateText}`;
        }
    });
    columnMap.set("end_date", {
        name: "end_date",
        label: "计划完成",
        width: 140,
        align: "left",
        editor: editorMap.get("end_date"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return null;
            }
            const dateText = formatDateText(task.end_date);
            const requestTask = ganttManager.getRequestTaskWithTask(task);
            if (requestTask !== undefined) {
                const {request_end_date} = requestTask;
                if (request_end_date !== undefined && task.end_date > request_end_date) {
                    return `<div style="color: red">${dateText}</div>`;
                }
            }
            return `${dateText}`;
        }
    });
    columnMap.set("request_duration", {
        name: "request_duration",
        label: "要求工期",
        width: 80,
        align: "left",
        editor: editorMap.get("request_duration"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return null;
            }
            const requestDuration = calculateDuration(task.request_duration);
            return requestDuration;
        }
    });
    columnMap.set("request_start_date", {
        name: "request_start_date",
        label: "要求开始",
        width: 140,
        align: "left",
        editor: editorMap.get("request_start_date"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone || task.request_start_date === undefined) {
                return null;
            }
            const dateText = formatDateText(task.request_start_date);
            return `${dateText}`;
        }
    });
    columnMap.set("request_end_date", {
        name: "request_end_date",
        label: "要求完成",
        width: 140,
        align: "left",
        editor: editorMap.get("request_end_date"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone || task.request_end_date === undefined) {
                return null;
            }
            const dateText = formatDateText(task.request_end_date);
            return `${dateText}`;
        }
    });
    columnMap.set("predecessors", {
        name: "predecessors",
        label: "前置任务",
        width: 80,
        align: "left",
        // editor: editorMap.get("predecessors"),
        resize: true,
        template: (task: GanttTask) => {
            const labels: string[] = [];
            gantt.getLinks().forEach((link: GanttLink) => {
                if (link.target === task.id) {
                    labels.push(String(ganttManager.linksFormatter.format(link)));
                }
            });
            const str = labels.join(", ");
            return ganttManager.fromType === "plan"
                ? `<div class="gantt_button_grid" data-action="editPredecessor" style="width: 100%; height: 100%">${str}</div>`
                : str;
        }
    });
    columnMap.set("taskStatus", {
        name: "taskStatus",
        label: "执行状态",
        width: 80,
        align: "left",
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return null;
            }
            const status = taskStatusOptions.find((option) => option.key === (task.taskStatus ?? TaskStatus.NOT_START))?.label;
            return status ?? "";
        },
    });
    columnMap.set("actual_duration", {
        name: "actual_duration",
        label: "实际工期",
        width: 80,
        align: "left",
        editor: editorMap.get("actual_duration"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone) {
                return null;
            }
            const actualDuration = calculateDuration(task.actual_duration);
            return actualDuration;
        },
    });
    columnMap.set("actual_start", {
        name: "actual_start",
        label: "实际开始",
        width: 140,
        align: "left",
        editor: editorMap.get("actual_start"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone || task.actual_start === undefined) {
                return "";
            }
            const dateText = formatDateText(task.actual_start);
            const offset = calculateOffset(task) ?? 0;
            // eslint-disable-next-line no-nested-ternary
            const color = offset > 0 ? "red" : offset < 0 ? "green" : "black";
            return `<div style="color: ${color}" class=${checkIsSyncStart(task) === false && !gantt.config.readonly ? "gantt_column_gai" : ""}>${dateText}</div>`;
        }
    });
    columnMap.set("actual_end", {
        name: "actual_end",
        label: "实际完成",
        width: 140,
        align: "left",
        editor: editorMap.get("actual_end"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.type === gantt.config.types.milestone || task.actual_end === undefined) {
                return "";
            }
            const dateText = formatDateText(task.actual_end);
            const offset = calculateOffset(task) ?? 0;
            // eslint-disable-next-line no-nested-ternary
            const color = offset > 0 ? "red" : offset < 0 ? "green" : "black";
            return `<div style="color: ${color}" class=${checkIsSyncEnd(task) === false && !gantt.config.readonly ? "gantt_column_gai" : ""}>${dateText}</div>`;
        }
    });
    columnMap.set("actualPlan", {
        name: "actualPlan",
        label: "实际进度",
        width: 140,
        align: "left",
        editor: editorMap.get("actualPlan"),
        resize: true,
        template: (task: GanttTask) => {
            if (task.actualPlan === undefined) {
                return "";
            }
            return `<div >${task.actualPlan ?? 0}%</div>`;
        }
    });
    columnMap.set("lagDeviation", {
        name: "lagDeviation",
        label: "滞后偏差",
        width: 80,
        align: "right",
        resize: true,
        template: (task: GanttTask) => {
            const offset = calculateOffset(task);
            if (offset !== null) {
                // eslint-disable-next-line no-nested-ternary
                const color = offset > 0 ? "red" : offset < 0 ? "green" : "black";
                return `<div style="color: ${color}">${offset}</div>`;
            }
            return null;
        }
    });
    columnMap.set("dutyUnit", {
        name: "dutyUnit",
        label: "责任单位",
        width: 140,
        align: "left",
        editor: editorMap.get("dutyUnit"),
        resize: true,
    });
    columnMap.set("dutyPerson", {
        name: "dutyPerson",
        label: "责任人",
        width: 140,
        align: "left",
        // editor: editorMap.get("dutyPerson"),
        resize: true,
        template(task: GanttTask) {
            const dutyPersonLabel = getDutyPersonLabel(task.dutyPerson);
            return `<div 
                        title="${dutyPersonLabel}" 
                        class="gantt_button_grid gantt_grid_ellipsis_text duty_person" 
                        style="width: 100%; height: 100%" 
                        ${ganttManager.fromType === "plan" ? "data-action=\"selectDutyPerson\"" : ""}
                    >
                        ${dutyPersonLabel}
                    </div>`;
        }
    });
    columnMap.set("hasRelateModel", {
        name: "hasRelateModel",
        label: "关联模型",
        width: 100,
        align: "left",
        resize: true,
        template: (task: GanttTask) => {
            const hasEbs = task.bindType === -1 && task.hasEbs === true;
            const hasBindComp = task.bindType === 2;
            if (hasEbs || hasBindComp) {
                return "<i id=\"relateBIM\" class=\"fa gantt_button_grid gantt_grid_edit fa-link\" data-action=\"relateBIM\"></i>";
            }
            return null;
        }
    });
    columnMap.set("hasRelatePhoto", {
        name: "hasRelatePhoto",
        label: "关联照片",
        width: 100,
        align: "left",
        resize: true,
        template: (task: GanttTask) => {
            if (task.hasPhoto === true) {
                return "<span class=\"fa gantt_button_grid gantt_grid_edit fa-image\" style=\"color: #517DCE; opacity: 1\" data-action=\"relatePhoto\"></span>";
            }
            return null;
        }
    });
    return columnMap;
};


// 筛选显示列
export const updateColumnsConfig = () => {
    const ganttColumnMap = getGanttColumnMap();
    // 自定义列
    const getCustomEditor = (column: ColumnItem) => {
        switch (column.valueType) {
            case CustomValueType.number:
                return {type: "custom_number", map_to: column.columnId};
            case CustomValueType.date:
                return {type: "date", map_to: column.columnId};
            default:
                return {type: "text", map_to: column.columnId};
        }
    };

    const editorMap = getGanttEditorMap();
    const newColumns: any[] = [ganttColumnMap.get("wbs")];
    // console.log("ganttManager.ganttAllColumnList", ganttManager.ganttAllColumnList);
    // console.log("ganttManager.ganttLocalColumnList", ganttManager.ganttLocalColumnList);
    // 实际进度有自定义列配置
    ganttManager.ganttAllColumnList.forEach((column) => {
        const localColumnId = ganttColumnNameMap.get(column.columnId as ServerGanttColumnType) ?? column.columnId;
        let isLocalColumn = true; // 是否是本地内置列
        let columnEditable = true;
        if (ganttManager.ganttLocalColumnList.length > 0) {
            const localColumnConfig = ganttManager.ganttLocalColumnList.find((item) => item.columnId === localColumnId);
            if (localColumnConfig === undefined) {
                isLocalColumn = false;
            } else {
                columnEditable = localColumnConfig.editable === true;
            }
        }
        if (column.hidden !== true) {
            // 添加自定义列
            if (column.valueType !== null && column.valueType !== undefined) {
                if (ganttManager.fromType === "actual") {
                    newColumns.push({
                        name: column.columnId,
                        label: column.name,
                        width: 80,
                        align: "left",
                        editor: getCustomEditor(column),
                        resize: true,
                        custom: true,
                    });
                }
            } else if (isLocalColumn) {
                // 添加内置列
                newColumns.push({
                    ...ganttColumnMap.get(localColumnId as GanttColumnType),
                    editor: columnEditable ? editorMap.get(localColumnId as GanttColumnType) : null,
                    resize: true,
                });
            }
        }
    });
    gantt.config.columns = newColumns;
    gantt.render();
    setShowGanttTimeLine(false, true);
};

/**
 * 设置列显示控制
 * @param columnControlList
 */
export const setGanttAllColumns = (allColumn: ColumnItem[]) => {
    // 保存列显示控制信息
    ganttManager.ganttAllColumnList = allColumn;
    updateGanttConfig();
};

// // 添加合计行
// export const addTotalRow = () => {
//     const rowNodes = Array
//         .from(document.querySelectorAll(".gantt_row,[data-task-id]"))
//         .filter((node) => node.classList.contains("gantt_row"));
//     console.log("rowNodes", rowNodes);
//     if (rowNodes.length === 0) {
//         return;
//     }
//     const lastTaskRowNode = rowNodes[rowNodes.length - 1];
//     (lastTaskRowNode as any).style.marginBottom = "40px";
//     const columnNameEl = lastTaskRowNode.querySelectorAll("[data-column-name]");
//     console.log("taskRowNodes", columnNameEl);
//     columnNameEl.forEach((el: any) => {
//         const key = el.getAttribute("data-column-name");
//         if (key === "wbs") {
//             el.style.overflow = "none";
//             const totalDom = document.createElement("div");
//             totalDom.innerHTML = "合计";
//             console.log("appendChild", el, totalDom);
//             el.appendChild(totalDom);
//         }
//         if (key === "duration") {
//             el.style.overflow = "none";
//             const totalDurationDom = document.createElement("div");
//             totalDurationDom.innerHTML = "111";
//             console.log("appendChild", el, totalDurationDom);
//             el.appendChild(totalDurationDom);
//         }
//         // if (key === "duration") {
//         //     nameEl.appendChild("");
//         // }
//     });
// };
