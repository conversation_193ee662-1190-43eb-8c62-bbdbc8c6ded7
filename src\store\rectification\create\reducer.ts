import {Reducer} from "redux";
import {ConditionFlowChartData, FlowType} from "../../../components/Rectification/models/flow-chart";
import Actions from "./actions";

export interface CreateRectificationModel {
    currentFlowType?: FlowType;
    flowChartData?: ConditionFlowChartData;
    changingFlowType: boolean;
    filteredFlowTypes: FlowType[];
    rectVisible: boolean;
    flowTemplateVisible: boolean;
}
export const initialState: CreateRectificationModel = {
    filteredFlowTypes: [],
    changingFlowType: false,
    rectVisible: false,
    flowTemplateVisible: false
};

const CreateRectificationReducer: Reducer<CreateRectificationModel, Actions> = (state = initialState, action) => {
    switch (action.type) {
        case "SET_FLOW_CHART_DATA":
            return {...state, flowChartData: action.payload};
        case "SET_CHANGING_FLOW_TYPE":
            return {...state, changingFlowType: action.payload};
        case "SET_FILTERED_FLOW_TYPE":
            return {...state, filteredFlowTypes: action.payload};
        case "SET_REACT_VISIBLE":
            return {...state, rectVisible: action.payload};
        case "SET_FLOW_TEMPLATE_VISIBLE":
            return {...state, flowTemplateVisible: action.payload};
        default:
            return {...state};
    }
};
export default CreateRectificationReducer;
