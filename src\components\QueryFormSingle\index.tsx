// import {Form, FormProps, Row, RowProps} from "antd";
// import {SearchOutlined} from "@ant-design/icons";
import {Button, Col, Form, FormProps, Row, RowProps, Space} from "antd";
import {FormInstance, useForm} from "antd/lib/form/Form";
import React, {CSSProperties} from "react";
import {createUseStyles} from "react-jss";
import ComFormItem, {ComFormItemProps} from "../FormItem";

interface QueryFormProps<T> {
    formConfig?: FormProps<T>;
    onFormFinish?: (values: T) => void;
    onFormValuesChange?: FormProps["onValuesChange"];
    onFormClear?: () => void;
    queryItemList: ComFormItemProps[];
    form?: FormInstance;
    labelMaxWidth?: number;
    style?: CSSProperties;
    /* 是否显示操作按钮, 暂时不建议去掉涉及到实时更新的问题 */
    isOperationBtn?: boolean;
    /* form row的布局 */
    formRow?: RowProps;
}

const useStyles = createUseStyles({
    queryItem: {
        marginBottom: 16,
    }
});
/*
* copy QueryForm; 只有一行情况下使用
*/
const QueryFormSingle = <T extends unknown>(props: QueryFormProps<T>) => {
    const cls = useStyles();
    const [defaultForm] = useForm();
    const {
        formConfig = {},
        onFormFinish,
        onFormValuesChange,
        queryItemList,
        onFormClear,
        form = defaultForm,
        labelMaxWidth = 100,
        style,
        isOperationBtn = true,
        formRow
    } = props;

    const queryItemListData = queryItemList.filter((item) => item.isDisplay !== false);

    const clearForm = () => {
        form.resetFields();
        if (onFormClear !== undefined) {
            onFormClear();
        }
    };

    const dealQueryItemList = () => queryItemListData;

    return (
        <Form
            style={{marginTop: 16, ...style}}
            onFinish={onFormFinish}
            onValuesChange={onFormValuesChange}
            form={form}
            {...formConfig}
        >
            <Row {...formRow}>
                {dealQueryItemList().map((item: ComFormItemProps) => {
                    if (item.type === "select") {
                        return (
                            <ComFormItem
                                className={cls.queryItem}
                                labelMaxWidth={labelMaxWidth}
                                {...item}
                                typeConfig={{placeholder: "全部", ...item.typeConfig}}
                            />
                        );
                    }
                    return (
                        <ComFormItem
                            className={cls.queryItem}
                            labelMaxWidth={labelMaxWidth}
                            {...item}
                        />
                    );
                })}
                {
                    isOperationBtn && (
                        <Col span={3} style={{textAlign: "right"}}>
                            <Space>
                                <Button type="primary" htmlType="submit">
                                    搜索
                                </Button>
                                <Button onClick={clearForm}>重置</Button>
                            </Space>
                        </Col>
                    )
                }
                {/* <Col span={4} push={14}>
                    <div style={{width: 238, height: 38, marginLeft: 4, border: "1px solid #E1E2E5"}}>
                        <SearchOutlined style={{fontSize: 14, margin: 13}} />
                        <input placeholder="输入计划名称" style={{width: 187, height: 20, outline: "none", border: "none", marginTop: 10}} />
                    </div>
                </Col> */}
            </Row>
        </Form>
    );
};

export default QueryFormSingle;
