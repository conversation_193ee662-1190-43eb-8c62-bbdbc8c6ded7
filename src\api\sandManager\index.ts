/* eslint-disable no-underscore-dangle */
import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {ProjectDurationResult, WarnTypeListResult} from "./type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;

/** 获取指定工程的设置信息 */
export const getProjectDuration = async (ppid: number): Promise<WebRes<ProjectDurationResult>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/sand-table/get-proj-duration/${ppid}`
});

// 新增-更新工程的设置信息
export const saveProjectDuration = async (params: Omit<ProjectDurationResult, "startDate" | "endDate">): Promise<WebRes<string>> => Fetch({
    methods: "post",
    url: `${baseUrl}/iworks/sand-table/proj-duration/save-or-update`,
    data: params
});

// 查询状态预警列表
export const getWarnTypeList = async (): Promise<WebRes<WarnTypeListResult[]>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/state/warnType/find`
});
