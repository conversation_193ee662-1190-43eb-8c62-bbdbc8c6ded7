import React, {useCallback, useEffect, useState, useMemo} from "react";
import {useDispatch, useSelector} from "react-redux";
import {Col, Row, Tabs} from "antd";
import useStyle from "./style";
import BimLeft from "./bim/Left";
import SandBoxLeft from "./sandBox/Left";
import ModelView from "./modelView";
import {MotorContext, ProjectAction, useProjListener} from "../../../reMotor";
import {isDefined} from "../../../assets/ts/utils";
import {RootState} from "../../../store/rootReducer";
import {getProjProcessTmplByPpid} from "../../../api/processManager";
import {sandTableFullScreenDomId, transformIworksTmplTreeToCenter} from "./helps";
import {setCurProcessTmplId, setProcessTmplTree} from "../../../store/process/action";
import {getProjectDuration} from "../../../api/sandManager";
import {setCurProjSettingInfo, setCurSandTable} from "../../../store/status/action";
import {CheckedInfoType} from "./sandBox/index.d";
import {setPlayDimension, setPlayPattern, setShowTodayStatus} from "../../../store/sandManage/action";
import PageTitle from "./pageTitle";

const {TabPane} = Tabs;
const rightViewStyle = {height: "100%", overflow: "hidden"};

const SandTable = () => {
    const cls = useStyle();
    const dispatch = useDispatch();
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {authorityTabs} = useSelector((state: RootState) => state.sandManageData);

    const [curTab, setCurTab] = useState<string>(authorityTabs.length > 0 ? authorityTabs[0].key : "sandBox");
    const [bimReady, setBimReady] = useState<boolean>(false);
    const [sandBoxLeft, setSandBoxLeft] = useState<{activeTab: string; checkedInfo: CheckedInfoType}>();
    const init = useCallback(
        async () => {
            if (!isDefined(curSandTable)) {
                return;
            }
            const {data: projSettingInfo} = await getProjectDuration(curSandTable.ppid);
            const {data: processTmplData} = await getProjProcessTmplByPpid(curSandTable.ppid);
            const originTmplTree = processTmplData?.projStateNode;
            const tempTmplTree = isDefined(originTmplTree) ? transformIworksTmplTreeToCenter([originTmplTree]) : [];
            dispatch(setCurProjSettingInfo(projSettingInfo));
            dispatch(setCurProcessTmplId(processTmplData?.templateId ?? `${curSandTable.projName}工序模板`));
            dispatch(setProcessTmplTree(tempTmplTree ?? []));
        },
        [curSandTable, dispatch]
    );

    useEffect(
        () => {
            init();
        },
        [init]
    );

    const handleTabChange = useCallback(
        (key: string) => {
            setCurTab(key);
            if (key === "sandBox") {
                dispatch(setShowTodayStatus(true));
            } else {
                const curProj = MotorContext.getProject();
                if (curProj !== null) {
                    curProj.setVisibility(true);
                    curProj.resetColor();
                }
            }
        },
        [dispatch]
    );

    const handleBimProjectChange = useCallback(
        (info: ProjectAction) => {
            if (info.type === "main" && info.status === "end") {
                setBimReady(true);
            }
            return true;
        },
        []
    );
    const handleCheckedInfo = useCallback((activeTab, checkedInfo) => {
        setSandBoxLeft({activeTab, checkedInfo});
    }, []);

    useProjListener(handleBimProjectChange);

    const leftView = useMemo(() => {
        if (bimReady) {
            return curTab === "sandBox" ? <SandBoxLeft handleCheckedInfo={handleCheckedInfo} /> : <BimLeft />;
        }
        return <Col flex="480px" />;
    }, [bimReady, curTab, handleCheckedInfo]);

    // eslint-disable-next-line arrow-body-style
    const rightView = useMemo(() => {
        return (
            <Col flex="auto" style={rightViewStyle}>
                <ModelView
                    curTab={curTab}
                    checkedInfo={sandBoxLeft?.checkedInfo}
                    changeTab={sandBoxLeft?.activeTab}
                />
            </Col>
        );
    }, [curTab, sandBoxLeft]);

    const handleBack = useCallback(
        () => {
            dispatch(setPlayPattern(["actual"]));
            dispatch(setPlayDimension("procedure"));
            dispatch(setShowTodayStatus(true));
            dispatch(setCurSandTable(null));
        },
        [dispatch]
    );

    return (
        <>
            <PageTitle
                onBack={handleBack}
                title={curSandTable?.projName ?? ""}
            />
            {authorityTabs.length > 0 && (
                <div className={cls.root}>
                    <div className={cls.topTab}>
                        <Tabs activeKey={curTab} onChange={handleTabChange} className={cls.tabBox}>
                            {authorityTabs.map((i) => <TabPane tab={i.label} key={i.key} />)}
                        </Tabs>
                    </div>
                    <Row
                        wrap={false}
                        className={cls.contentBox}
                        id={sandTableFullScreenDomId}
                    >
                        {leftView}
                        {rightView}
                    </Row>
                </div>
            )}
        </>
    );
};

export default SandTable;
