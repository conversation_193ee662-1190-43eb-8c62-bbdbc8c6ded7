import React, {FC} from "react";
import {CustomFieldProps, FileInfo} from "../../models/custom-field";
import FileBox from "../../../FileBox";
import {FileType} from "../../../../api/common.type";
import {getSuffix} from "../../../../assets/ts/utils";

const transerFileToFileInfo = (list: FileType[]): FileInfo[] => list.map((v) => ({
    fileSysUuid: v.fileUuid,
    fileSize: v.fileSize,
    fileName: v.fileName,
    fileExtension: getSuffix(v.fileName),
    fileSysMd5: v.fileUuid
}));

export const transferFileInfoToFileType = (list: FileInfo[]): FileType[] => list.map((v) => ({
    fileUuid: v.fileSysUuid,
    fileSize: v.fileSize,
    fileName: v.fileName
}));

const getAttachment = (curValue?: string) => {
    if (curValue === undefined) {
        return [];
    }
    if (curValue === "") {
        return [];
    }
    const temp: FileInfo[] = JSON.parse(curValue);
    if (Array.isArray(temp) && temp.every((v) => Boolean(v.fileSysUuid) && Boolean(v.fileName))) {
        return transferFileInfoToFileType(temp);
    }
    return [];
};

const RenderAttachment: FC<CustomFieldProps> = (props) => {
    const {onChange, currentValue, data} = props;

    const isAttachData = typeof currentValue === "string" || typeof currentValue === "undefined";
    if (!isAttachData) {
        return null;
    }

    return (
        <FileBox
            uploadText="上传附件"
            value={getAttachment(currentValue as string | undefined)}
            onChange={(fList) => onChange(JSON.stringify(transerFileToFileInfo(fList)))}
            key={data.id}
        />
    );
};
export default RenderAttachment;
