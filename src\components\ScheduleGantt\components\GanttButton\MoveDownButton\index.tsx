import React, {useContext} from "react";
import {But<PERSON>} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import {moveDownTask} from "../../../gantt/taskUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const MoveDownButton = () => {
    const cls = useStyles();
    const {isWbs, checkoutStatus} = useContext(EditorContext);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-xiayi" fontSize={18} />}
            onClick={moveDownTask}
            disabled={isWbs}
        >
            下移
        </Button>
    );
};

export default MoveDownButton;
