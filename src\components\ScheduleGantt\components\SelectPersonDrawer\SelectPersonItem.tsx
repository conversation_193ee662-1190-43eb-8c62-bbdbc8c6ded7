import React, {useState, useEffect, useCallback, useMemo, useContext} from "react";
import {useSelector} from "react-redux";
import {Input} from "antd";
import {DutyPerson} from "./data";
import {RootState} from "../../../../store/rootReducer";
import {getSearchUsersOfRole, UserInfoTypeByOrg} from "../../../../api/pds";
import SelectPersonDrawer from "./index";
import EditorContext from "../../views/GanttEditor/context";
import {getDutyPersonLabel} from "../../gantt/taskUtils";

export interface SelectPersonItemProps {
    value?: DutyPerson;
    onChange?: (val: DutyPerson) => void;
    placeholder?: string;
    inputDisabled?: boolean;
    disableReason?: string;
    nodeId?: string;
    nodeType?: number;
}

const SelectPersonItem = (props: SelectPersonItemProps) => {
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {value, onChange, placeholder, inputDisabled, nodeId, nodeType} = props;
    const {planInfo} = useContext(EditorContext);
    const [dutyPerson, setDutyPerson] = useState<DutyPerson>();
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [originPersonData, setOriginPersonData] = useState<UserInfoTypeByOrg[]>([]);

    useEffect(() => {
        if (value !== undefined) {
            setDutyPerson(value);
        }
    }, [value]);

    const getPersonData = useCallback(
        () => {
            getSearchUsersOfRole({deptId: orgInfo.orgId, nodeId, nodeType})
                .then((res) => {
                    setOriginPersonData(res);
                });
        },
        [nodeId, nodeType, orgInfo.orgId]
    );

    useEffect(
        () => {
            getPersonData();
        },
        [getPersonData]
    );

    const handleChangePerson = (_dutyPerson: DutyPerson) => {
        setDrawerVisible(false);
        setDutyPerson(_dutyPerson);
        if (onChange !== undefined) {
            onChange(_dutyPerson);
        }
    };

    const handleInputClick = () => {
        setDrawerVisible(true);
    };

    const realName = useMemo(() => getDutyPersonLabel(value), [value]);

    return (
        <>
            <Input
                onClick={handleInputClick}
                value={realName}
                placeholder={placeholder}
                disabled={inputDisabled}
            />
            <SelectPersonDrawer
                visible={drawerVisible}
                setVisible={setDrawerVisible}
                value={dutyPerson}
                onChange={handleChangePerson}
                nodeId={planInfo.nodeId}
                nodeType={planInfo.nodeType}
                originUserData={originPersonData}
            />
        </>
    );
};

export default SelectPersonItem;
