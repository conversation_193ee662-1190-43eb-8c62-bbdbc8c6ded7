import {ColumnsType} from "antd/lib/table";
import {DocType} from "../../api/common.type";
import {momentText} from "../../assets/ts/utils";
import renderTableText from "../renderTableText";

// eslint-disable-next-line import/prefer-default-export
export const columns: ColumnsType<DocType> = [
    {
        title: "序号",
        align: "center",
        width: 80,
        fixed: "left",
        render: (_text: unknown, _records: DocType, index: number) => index + 1
    },
    {
        key: "number",
        title: "资料名称",
        dataIndex: "docName",
        align: "left",
        fixed: "left",
        render: renderTableText,
    },
    {
        key: "checkTime",
        title: "编制日期",
        dataIndex: "updateTime",
        align: "center",
        width: 120,
        render: (_text: string | number) => momentText(_text)
    },
];
