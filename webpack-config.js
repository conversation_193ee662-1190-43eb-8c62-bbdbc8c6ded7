const path = require("path");
const fs = require('fs');
const argv = require('yargs').argv;
const {env} = argv;
let envObj = {};//环境对象关键字存储
const fileList = fs.readdirSync(`${__dirname}/script/config`)?.map((item)=>envObj[item?.split(".")[0]] = item);
const envConfig = envObj[env];

module.exports = {
    // 具体可以看README的 自定义功能

    // 自定义 入口文件
    customEntry: "",
    // 拷贝文件，已经有static和 public了
    customCopyPlugin: [
       {
            from: envConfig !== undefined
            ? path.resolve(__dirname, `script/config`,`${envConfig}`)
            : path.resolve(__dirname, "public","config.js"),//config本地环境切换
            to: path.resolve(__dirname, "dist", "config.js"),
        },
        {
            from: path.resolve(__dirname, "./public"),
            to: path.resolve(__dirname, "./dist"),
        },
        {
            from: path.resolve(__dirname, "./node_modules/@motor/core"),
            to: path.resolve(__dirname, "./dist"),
        }
        
    ],
    htmlChunk: {
        $all: {
            title: "进度管理系统",
            headChunk: [
                `<meta charset="utf-8">`,
                `<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>`,
                `<meta name="format-detection" content="email=no">`,
                `<meta name="format-detection" content="telephone=no">`,
                `<link type="text/css" rel="stylesheet" href="./css/noSelect.css" disabled=true>`,
                `<script src="/general-config/global-config.js"></script>`,
                `<script src="/general-config/title/config.js"></script>`, 
                `<link rel="icon" href="/general-config/title/favicon.ico" />`,
                `<script src="config.js"></script>`,
                `<script src="./static/iconfont.js"></script>`
            ],
            scriptChunk: [
                `<script> document.title = window.titleConfig?.planWeb </script>`,
            ],
        },
    },
    server: {
        // 只打包编译的目录
        workPage: [],
        alias: {
            "@": path.resolve(__dirname, "src"),
            "~": path.resolve(__dirname, "src"),
            env: path.resolve(__dirname, "env/beta.ts"),
            "react": path.resolve(__dirname, "./node_modules/react"),
            "react-dom": path.resolve(__dirname, "./node_modules/react-dom"),
            "antd": path.resolve(__dirname, "./node_modules/antd"),
            "redux-dynamic-modules": path.resolve(__dirname, "./node_modules/redux-dynamic-modules"),
            "redux-dynamic-modules-thunk": path.resolve(__dirname, "./node_modules/redux-dynamic-modules-thunk"),
            "redux": path.resolve(__dirname, "./node_modules/redux"),
            "react-redux": path.resolve(__dirname, "./node_modules/react-redux"),
        },
    },
    build: {
        beta: {
            alias: {
                "@": path.resolve(__dirname, "src"),
                "~": path.resolve(__dirname, "src"),
                env: path.resolve(__dirname, "env/beta.ts"),
                "react": path.resolve(__dirname, "./node_modules/react"),
                "react-dom": path.resolve(__dirname, "./node_modules/react-dom"),
                "antd": path.resolve(__dirname, "./node_modules/antd"),
                "redux-dynamic-modules": path.resolve(__dirname, "./node_modules/redux-dynamic-modules"),
                "redux-dynamic-modules-thunk": path.resolve(__dirname, "./node_modules/redux-dynamic-modules-thunk"),
                "redux": path.resolve(__dirname, "./node_modules/redux"),
                "react-redux": path.resolve(__dirname, "./node_modules/react-redux"),
            },
        },
        image: {
            alias: {
                "@": path.resolve(__dirname, "src"),
                env: path.resolve(__dirname, "env/image.ts"),
            },
        },
        prod: {
            alias: {
                "@": path.resolve(__dirname, "src"),
                env: path.resolve(__dirname, "env/prod.ts"),
            },
        },
    },
    //是否启动图片压缩
    isImageCompression:false,
    // 是否启动 start 性能调试
    isSpeedMeasurePlugin:false,
    // 是否启动 build 打包分析调试
    isBundleAnalyzerPlugin:false,
    // 是否线上服务的log的开关
    isDropConsole: false,
};
