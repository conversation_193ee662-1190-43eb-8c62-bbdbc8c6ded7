import React, {useCallback, useEffect, useRef, useState} from "react";
import {debounce} from "lodash-es";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {Col, DrawerProps, Input, Pagination, Row} from "antd";
import {DataNode} from "antd/lib/tree";
import useComState from "../../assets/hooks/useComState";
import {RootState} from "../../store/rootReducer";
import ComDrawer from "../ComDrawer";
import ComTable from "../ComTable";
import MyIcon from "../MyIcon";
import {getDangerControlList} from "../../api/danger";
import {DangerListType} from "../../api/danger/type";
import {columns} from "./data";

const useStyles = createUseStyles({
    root: {
        display: "flex",
        flexDirection: "column",
        flex: "1 1 0",
        height: "100%",
        padding: 24,
    },
});

interface DrawerDangerSelectProps {
    drawerConfig?: DrawerProps;
    visible: boolean;
    onClose: () => void;
    title?: string;
    width?: number;
    selectDanger?: DangerListType;
    onSelectDanger: (danger?: DangerListType) => void;
}

const treeIconStyle = {
    marginTop: 2,
    fontSize: 20,
};

const DrawerDangerSelect = (props: DrawerDangerSelectProps) => {
    const {
        visible,
        onClose,
        title = "选择风险",
        width = 750,
        selectDanger,
        onSelectDanger,
    } = props;
    const cls = useStyles();
    const tableSearchInputRef = useRef<Input>(null);
    const expandedKeysRef = useRef<React.Key[]>([]);
    const {orgInfo, curSectionInfo, sectionList} = useSelector((rootState: RootState) => rootState.commonData);
    // const {sectionList = [], curSectionInfo, curBuildInfo} = useSelector((state: RootState) => state.threeAndSection); // 所以的标段
    const [state, setState] = useComState({});
    const [dataSource, setDataSource] = useState<DangerListType[]>();
    const [tempSelectedDanger, setTempSelectedDanger] = useState<DangerListType | undefined>(selectDanger);

    const getDangerListCallback = useCallback(async (queryParams) => {
        if (orgInfo?.orgId !== undefined) {
            let nodeIdList: string[] = [];
            if (curSectionInfo?.classification === 1) {
                // 监理单位
                nodeIdList = sectionList
                    .filter((el) => el.classification === 1 || el.parentId === curSectionInfo?.id)
                    .map((el) => el.id);
            }
            let nodeId: string | undefined;
            if (curSectionInfo?.classification === 0) {
                // 施工单位
                nodeId = curSectionInfo?.id;
            }
            const params = {
                deptId: orgInfo.orgId,
                page: state.curPage,
                size: state.pageSize,
                nodeId,
                nodeIdList,
                ...queryParams
            };
            const {code, data} = await getDangerControlList(params);
            if (code === 200) {
                setDataSource(data.items);
                setState.setTotal(data.totalCount);
            }
        }
    }, [orgInfo, state.curPage, state.pageSize, sectionList, curSectionInfo, setState]);

    useEffect(() => {
        if (visible) {
            setDataSource([]);
            setTempSelectedDanger(selectDanger);
            getDangerListCallback({});
        }
    }, [getDangerListCallback, selectDanger, visible]);

    // 表格搜索
    const handleSearchTable = useCallback(debounce(() => {
        const name = tableSearchInputRef.current?.state.value ?? "";
        setState.setCurPage(1);
        const params = {
            name,
        };
        setState.setQueryFormData(params);
        getDangerListCallback(params);
    }, 500), [setState, state.queryFormData, getDangerListCallback]);

    const handlePaginationChange = useCallback((curPageVal: number, pageSizeVal?: number) => {
        setState.setCurPage(curPageVal);
        setState.setPageSize(pageSizeVal ?? 10);
    }, [setState]);

    const updatePathTreeIcon = useCallback((node: DataNode): DataNode => {
        const iconType = expandedKeysRef.current.includes(node.key) ? "icon-openfile" : "icon-unfile";
        return {
            ...node,
            children: node.children?.map((child) => updatePathTreeIcon(child)) ?? [],
            icon: <MyIcon type={iconType} style={treeIconStyle} />
        };
    }, []);

    // 选择文件
    const handleSelectDanger = useCallback((record: DangerListType, selected: boolean) => {
        if (selected === true) {
            setTempSelectedDanger(record);
        } else {
            setTempSelectedDanger(undefined);
        }
    }, []);

    const handleOk = () => {
        onSelectDanger(tempSelectedDanger);
        onClose();
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <ComDrawer
            onCancel={handleCancel}
            onOk={handleOk}
            visible={visible}
            title={title}
            width={width}
            bodyStyle={{padding: 0}}
        >
            <div className={cls.root}>
                <Input.Search
                    style={{width: 300, flexShrink: 0}}
                    placeholder="输入风险名称"
                    ref={tableSearchInputRef}
                    onChange={handleSearchTable}
                    onSearch={handleSearchTable}
                />
                <ComTable<DangerListType>
                    style={{marginTop: 16}}
                    rowKey="id"
                    columns={columns}
                    dataSource={dataSource}
                    rowSelection={{
                        type: "radio",
                        selectedRowKeys: tempSelectedDanger !== undefined ? [tempSelectedDanger.id] : [],
                        onSelect: handleSelectDanger,
                        columnWidth: 60
                    }}
                />
                <Row justify="end" style={{marginTop: 20, flexShrink: 0}}>
                    <Col>
                        <Pagination
                            total={state.total}
                            showSizeChanger
                            current={state.curPage}
                            pageSize={state.pageSize}
                            onChange={handlePaginationChange}
                        />
                    </Col>
                </Row>
            </div>
        </ComDrawer>
    );
};

export default DrawerDangerSelect;
