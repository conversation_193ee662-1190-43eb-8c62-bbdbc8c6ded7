import React from "react";
import {useSelector} from "react-redux";

import {RootState} from "../../store/rootReducer";
import RecentProject from "./RecentProject";
import SandTable from "./SandTable";

const SandTableManage = () => {
    const {curSandTable} = useSelector((state: RootState) => state.statusData);

    return curSandTable === null
        ? <RecentProject />
        : <SandTable />;
};

export default React.memo(SandTableManage);
