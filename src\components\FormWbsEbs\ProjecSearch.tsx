import React, {useState, useEffect, useCallback} from "react";
import {createUseStyles} from "react-jss";
import {Space, Input, InputProps} from "antd";
import {cloneDeep} from "lodash-es";
import {ProjectTreeNodeData, relateOptions, majorOptions, EBSNodeType} from "./data";
import ComSelect, {ComSelectProps} from "../ComSelect";
import {OrgProjNodeVo, ProjNameVo} from "../../api/common.type";
import {toArr} from "../../assets/ts/utils";

const useStyle = createUseStyles({
    box: {},
});

export interface ProjectSearchProps {
    projectTreeValue?: EBSNodeType[];
    projectTree: ProjectTreeNodeData[];
    onProjectTreeChange: (val: ProjectTreeNodeData[]) => void;
}


const ProjectSearch = (props: ProjectSearchProps) => {
    const {projectTree, onProjectTreeChange, projectTreeValue} = props;
    const cls = useStyle();
    // 专业
    const [major, setMajor] = useState<number>(0);
    // 关联状态
    const [related, setRelated] = useState<number>(-1);
    // 搜索关键字
    const [search, setSearch] = useState<string>();
    const [selectedPpid, setSelectedPpid] = useState<number[]>([]);

    useEffect(() => {
        setSelectedPpid(toArr(projectTreeValue ?? []).map((el) => el.ppid));
    }, [projectTreeValue]);

    const filterTree = useCallback(
        (val: ProjectTreeNodeData) => {
            const tempVal = val as unknown as {originData: OrgProjNodeVo};
            if (toArr(tempVal.originData.projects ?? []).length === 0) {
                val.children = toArr(val.children ?? []).map((el) => filterTree(el));
            } else {
                val.children = toArr(val.children ?? []).filter((el) => {
                    let isShow = true;
                    const tempOriginData = el.originData as unknown as ProjNameVo;
                    if (major !== 0 && tempOriginData.projType !== major) {
                        isShow = false;
                    }
                    // 当前工程是不是已经关联过了
                    const curPpidRelated = selectedPpid.includes(tempOriginData.ppid ?? 0);
                    // 未关联
                    if (isShow && related === 0 && curPpidRelated) {
                        isShow = false;
                    }
                    // 关联
                    if (isShow && related === 1 && curPpidRelated === false) {
                        isShow = false;
                    }
                    // 根据名称搜索
                    if (isShow && Boolean(search) === true) {
                        isShow = tempOriginData.projName?.includes(search ?? "") ?? false;
                    }
                    return isShow;
                });
            }
            return val;
        },
        [major, related, search, selectedPpid]
    );

    useEffect(() => {
        const tempProjectTree = cloneDeep(projectTree);
        onProjectTreeChange(tempProjectTree.map((el) => filterTree(el)));
    }, [filterTree, onProjectTreeChange, projectTree]);

    const handleSelectMajor: ComSelectProps["onSelect"] = (val: unknown) => {
        setMajor(Number(val));
    };

    const handlSelectRelate: ComSelectProps["onSelect"] = (val: unknown) => {
        setRelated(Number(val));
    };

    const handleKeySearch: InputProps["onChange"] = (val) => {
        setSearch(val.target.value);
    };

    return (
        <div className={cls.box}>
            <Space>
                <ComSelect
                    style={{width: 120}}
                    value={major}
                    onSelect={handleSelectMajor}
                    label="专业类型"
                    options={majorOptions}
                />
                <ComSelect
                    style={{width: 120}}
                    value={related}
                    label="关联状态"
                    options={relateOptions}
                    onSelect={handlSelectRelate}
                />
                <Input
                    value={search}
                    placeholder="输入名称"
                    onChange={handleKeySearch}
                />
            </Space>
        </div>
    );
};

export default ProjectSearch;
