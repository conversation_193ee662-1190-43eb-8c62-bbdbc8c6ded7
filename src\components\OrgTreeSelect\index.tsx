/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {forwardRef, Ref, useEffect, useState} from "react";
import {Row, Select, SelectProps} from "antd";
import {OrgNode} from "../../api/common.type";


export interface OrgTreeSelectProps extends SelectProps {
    value?: OrgNode;
    onChange?: (value: OrgNode | null) => void;
}

const OrgTreeSelect = (props: OrgTreeSelectProps, ref: Ref<any>) => {
    const {value: _value, onChange, ...otherProps} = props;
    const [value, setValue] = useState<OrgNode>();
    const [open, setOpen] = useState(false);

    useEffect(() => {
        setValue(_value);
    }, [_value]);

    return (
        <Select
            ref={ref}
            value={value?.name}
            open={open}
            onDropdownVisibleChange={setOpen}
            dropdownStyle={{padding: 0}}
            dropdownRender={() => (
                <div style={{color: "#061127"}}>
                    <Row justify="space-between" align="middle" style={{height: "48px"}}>
                        <div style={{marginLeft: 16, fontWeight: "bold"}}>组织架构</div>
                    </Row>
                </div>
            )}
            {...otherProps}
        />
    );
};

export default forwardRef(OrgTreeSelect);
