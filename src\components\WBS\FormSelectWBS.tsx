import {Input, TreeProps} from "antd";
import {DataNode} from "antd/lib/tree";
import {isEqual, uniqBy} from "lodash-es";
import React, {useEffect, useState} from "react";
import {createUseStyles} from "react-jss";
import {WBSType} from "../../api/common.type";
import {emptyFun} from "../../assets/ts/utils";
import ComDrawer, {ComDrawerProps} from "../ComDrawer";
import WBSTree, {WBSTreeProps} from "./WBSTree";

const useStyle = createUseStyles({
    box: {},
});

export const getLeafNode = (nodeData: DataNode) => {
    const leafNodes: DataNode[] = [];
    const traverTree = (val: DataNode) => {
        if (Array.isArray(val.children) && val.children.length > 0) {
            val.children.forEach((item) => traverTree(item));
        } else {
            leafNodes.push(val);
        }
    };
    traverTree(nodeData);
    return leafNodes;
};

export interface FormSelectWBSProps extends WBSTreeProps {
    value?: WBSType[] | null;
    onChange?: (value: WBSType[]) => void;
    drawerConfig?: ComDrawerProps;
}

const FormSelectWBS = (props: FormSelectWBSProps) => {
    const {value, onChange = emptyFun, drawerConfig, ...other} = props;
    const cls = useStyle();
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [saveValue, setSaveValue] = useState<WBSType[]>([]);

    useEffect(() => {
        if (!isEqual(value, saveValue)) {
            setSaveValue(value ?? []);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const openDrawer = () => setDrawerVisible(true);
    const closeDrawer = () => setDrawerVisible(false);

    const handleOk = () => {
        onChange(saveValue);
        closeDrawer();
    };

    const handleCancel = () => {
        setSaveValue(value ?? []);
        closeDrawer();
    };

    const handleWBSChange: TreeProps["onCheck"] = (_val, info) => {
        const leafNodes: WBSType[] = getLeafNode(info.node).map((el) => ({wbsNodeId: `${el.key}`, wbsNodeName: `${el.title}`}));
        const leafNodeIds = leafNodes.map((el) => el.wbsNodeId);
        if (info.checked) {
            setSaveValue(uniqBy(leafNodes.concat(saveValue), "wbsNodeId"));
        } else {
            setSaveValue(saveValue.filter((el) => !leafNodeIds.includes(el.wbsNodeId)));
        }
    };

    return (
        <div className={cls.box}>
            <Input onClick={openDrawer} value={value === undefined ? "" : saveValue.map((el) => el.wbsNodeName).join(",")} />
            <ComDrawer
                title="WBS分部分项"
                visible={drawerVisible}
                width={520}
                {...drawerConfig}
                onOk={handleOk}
                onCancel={handleCancel}
            >
                <div style={{padding: 24, height: "100%"}}>
                    <WBSTree
                        {...other}
                        checkable
                        checkedKeys={saveValue.map((el) => el.wbsNodeId)}
                        onCheck={handleWBSChange}
                    />
                </div>
            </ComDrawer>
        </div>
    );
};

export default FormSelectWBS;
