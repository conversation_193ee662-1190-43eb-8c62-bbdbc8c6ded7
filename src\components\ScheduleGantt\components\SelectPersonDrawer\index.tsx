import React, {useState, useEffect, use<PERSON><PERSON>back, Dispatch, SetStateAction} from "react";
import {useSelector} from "react-redux";
import {Col, Input, Row, Tag} from "antd";
import ComDrawer from "../../../ComDrawer";
import ComSearch from "../../../../../uikit/Components/ComSearch";
import {DutyPerson, EasyPerson, ValueType} from "./data";
import PersonTreeRole from "./PersonTreeRole";
import {RootState} from "../../../../store/rootReducer";
import {getSearchUsersOfRole, UserInfoTypeByOrg} from "../../../../api/pds";
import ganttManager from "../../gantt/ganttManager";
import useStyles from "./style";

export interface SelectPersonDrawerProps {
    visible: boolean;
    setVisible: Dispatch<SetStateAction<boolean>>;
    value?: DutyPerson;
    onChange?: (val: DutyPerson) => void;
    disabledKeys?: string[];
    inputDisabled?: boolean;
    disableReason?: string;
    nodeId?: string;
    nodeType?: number;
    originUserData?: UserInfoTypeByOrg[];
}

const SelectPersonDrawer = (props: SelectPersonDrawerProps) => {
    const cls = useStyles();
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {visible, setVisible, value, onChange, disabledKeys, nodeId, nodeType, originUserData} = props;
    const [inputPerson, setInputPerson] = useState<string>("");
    const [checkedPerson, setCheckedPerson] = useState<ValueType[]>([]);
    const [key, setKey] = useState<string>();
    const [originPersonData, setOriginPersonData] = useState<UserInfoTypeByOrg[]>([]);
    const [allPerson, setAllPerson] = useState<EasyPerson[]>([]);

    useEffect(() => {
        if (visible) {
            setInputPerson("");
            setCheckedPerson([]);
            if (value !== undefined) {
                setInputPerson(value?.input ?? "");
                setCheckedPerson(value?.select.map((item) => ({
                    userName: item,
                    realName: ganttManager.allPersonUserRealNameMap.get(item) ?? ""
                })) ?? []);
            }
        } else {
            setKey("");
        }
    }, [visible, value]);

    const getPersonData = useCallback(
        () => {
            getSearchUsersOfRole({deptId: orgInfo.orgId, nodeId, nodeType})
                .then((res) => {
                    setOriginPersonData(res);
                    const allUsers: EasyPerson[] = (res ?? [])
                        .map((v) => v.users ?? [])
                        .flat()
                        .map((v) => ({userName: v.userName ?? "", realName: v.realName ?? ""}));
                    setAllPerson(allUsers);
                });
        },
        [nodeId, nodeType, orgInfo.orgId]
    );

    useEffect(
        () => {
            if (originUserData !== undefined && originUserData?.length > 0) {
                setOriginPersonData(originUserData);
            } else {
                getPersonData();
            }
        },
        [getPersonData, originUserData]
    );

    const handleOk = () => {
        setVisible(false);
        if (onChange !== undefined) {
            const dutyPerson: DutyPerson = {
                input: inputPerson,
                select: checkedPerson.map((item) => item.userName),
            };
            onChange(dutyPerson);
        }
    };

    const handlePersonRemove = (val: ValueType) => {
        const filterData = checkedPerson.filter((e) => e.userName !== val.userName);
        setCheckedPerson(filterData);
    };

    return (
        <ComDrawer
            title="选择人员"
            visible={visible}
            width={720}
            onOk={handleOk}
            onCancel={() => setVisible(false)}
        >
            <Row style={{height: "100%", flexDirection: "column"}}>
                <Col style={{width: "100%", padding: 24, flexShrink: 0}}>
                    <div className={cls.title} style={{marginBottom: 16}}>
                        <div className={cls.titleIcon} />
                        添加外部人员
                    </div>
                    <Input
                        style={{marginBottom: 4}}
                        value={inputPerson}
                        onChange={(e) => setInputPerson(e.target.value)}
                        placeholder="输入人员名称，并用逗号隔开"
                    />
                    <div className={cls.subTitle}>人员名称用逗号隔开</div>
                </Col>
                <Col style={{padding: "0 24px"}}>
                    <div className={cls.title} style={{marginBottom: 16}}>
                        <div className={cls.titleIcon} />
                        选择内部人员
                    </div>
                </Col>
                <Row style={{flex: "1 0 0", padding: "0 24px 24px", height: 0}}>
                    <Row style={{width: "100%", border: "1px solid #E1E2E5"}}>
                        <Col span={12} style={{height: "100%", borderRight: "1px solid #E1E2E5"}}>
                            <div style={{height: "100%", display: "flex", flexDirection: "column", padding: ""}}>
                                <div className={cls.selectBoxHeader}>
                                    {`选项合集（${checkedPerson.length}/${allPerson.length}项）`}
                                </div>
                                <div style={{padding: "16px 24px"}}>
                                    <ComSearch value={key} onChange={setKey} style={{width: "100%"}} />
                                </div>
                                <PersonTreeRole
                                    boxStyle={{padding: "0 24px"}}
                                    value={checkedPerson}
                                    onChange={setCheckedPerson}
                                    disabledKeys={disabledKeys}
                                    searchKey={key}
                                    originPersonData={originPersonData}
                                />
                            </div>
                        </Col>
                        <Col span={12} style={{height: "100%"}}>
                            <div className={cls.selectBoxHeader}>
                                {`已选合集（${checkedPerson.length}项）`}
                            </div>
                            <div style={{padding: "16px 24px"}}>
                                {checkedPerson.map((el) => (
                                    <Tag key={el.userName} style={{backgroundColor: "#E1E2E5", marginBottom: 8}} closable onClose={() => handlePersonRemove(el)}>
                                        {el.realName}
                                    </Tag>
                                ))}
                            </div>
                        </Col>
                    </Row>
                </Row>
            </Row>
        </ComDrawer>
    );
};

export default SelectPersonDrawer;
