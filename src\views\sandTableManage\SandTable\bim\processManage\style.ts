import {createUseStyles} from "react-jss";
import copmFinishBg from "../../../../../assets/images/copmFinish_bg.png";

const useStyle = createUseStyles({
    box: {
        display: "flex",
        flexDirection: "column",
        height: "100%"
    },
    prosessListBox: {
        flexGrow: 1,
        overflow: "auto"
    },
    checkoutIconBox: {
        color: "#2DA641",
        fontSize: 18,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    listItemBox: {
        background: "#F5F5F6",
        padding: "16px !important",
        marginBottom: 16,
        "& .actionBox": {
            display: "none"
        },
        "& .statusNameBox": {
            display: "flex",
            textAlign: "right",
            justifyContent: "end"
        },
        "&:hover": {
            "& .actionBox": {
                display: "inline-flex"
            },
            "& .statusNameBox": {
                display: "none"
            }
        }
    },
    iconRect: {
        width: 12,
        height: 12,
        display: "inline-block",
        marginRight: 8
    },
    comFinishBox: {
        height: 48,
        background: `url(${copmFinishBg}) no-repeat 0 0`,
        padding: "0 16px",
        marginBottom: 24
    },
    formBox: {
        "& .ant-form-item-label": {
            width: 112
        },
        "& .ant-form-item-control": {
            width: "calc(100% - 112px)"
        },
        "& .ant-form-item-required::before": {
            content: "'' !important",
            display: "none !important"
        },
        "& .ant-form-item-required::after": {
            display: "inline-block",
            marginLeft: 4,
            color: "#ff4d4f",
            fontSize: 14,
            fontFamily: "SimSun, sans-serif",
            lineHeight: 1,
            content: "'*' !important"
        },
        "& .ant-switch-checked": {
            backgroundColor: "#2DA641"
        }
    },
    processDateBox: {
        marginTop: 12,
        marginBottom: 20
    },
    progressBox: {
        position: "relative",
        height: 24
    },
    timeProgressBox: {
        position: "absolute",
        height: 10
    },
    planTimeProgressBox: {
        position: "absolute",
        height: 24
    },
    boldText: {
        fontWeight: 700,
        color: "#061127"
    },
    statusText: {
        color: "#717784"
    },
    disabledBtn: {
        borderColor: "#E1E2E5",
        color: "#C8CBCF !important"
    },
    popoverBox: {
        "& .ant-popover-title": {
            borderBottom: "none",
            padding: "16px 16px 0 16px"
        }
    }
});

export default useStyle;
