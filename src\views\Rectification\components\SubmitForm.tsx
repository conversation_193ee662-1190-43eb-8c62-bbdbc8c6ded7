import React, {forwardRef, useCallback, useImperativeHandle, useState} from "react";
import {Col, DatePicker, Descriptions, Form, Input, Row} from "antd";
import {RectifyAddFormModel} from "../interface";
import CustomField from "../../../components/Rectification/CustomField";
import {chunkArray, timeFormat} from "../../../assets/ts/utils";
import {CustomFieldData, CustomFieldType} from "../../../components/Rectification/models/custom-field";
import {ComponentJsonValueSaveParam} from "../../../api/rectification/models/process";
import {transformDataToJsonValues} from "../../../components/Rectification/rectification-helpers";
import FileBox from "../../../components/FileBox";
import {FileInfo} from "../../../components/Rectification/models/attachment";
import {transferFileInfoToFileType} from "../../../components/Rectification/CustomField/comp/renderAttachment";
import useStyle from "../style";
import {ColConfig, formColStyle} from "../../../assets/ts/form";

const {TextArea} = Input;

export interface SubmitFormRef {
    handleSubmit: () => Promise<RectifyAddFormModel>;
    jsonValues: ComponentJsonValueSaveParam[];
}

interface SubmitFormProps {
    nodeId?: string;
    nodeType?: number;
    formData?: RectifyAddFormModel;
    defaultFieldList?: CustomFieldData[];
    isStartFlowNode?: boolean;
    defaultCustomFieldValues?: Map<string, string | string[]>;
    onFieldsChange?: (data: CustomFieldData, payload?: string | string[]) => void;
}

const renderDesc = (list: CustomFieldData[], fieldValues: Map<string, string[] | string>) => list.map((v) => {
    const value = fieldValues.get(v.id);
    if ([CustomFieldType.Input, CustomFieldType.TextArea].includes(v.type)) {
        return (
            <Descriptions.Item key={v.id} label={v.name}>
                {value}
            </Descriptions.Item>
        );
    }
    if ([CustomFieldType.Select].includes(v.type) && Array.isArray(v.data)) {
        const optValue = v.data.find((opt) => opt.id === value)?.value ?? "";
        return (
            <Descriptions.Item key={v.id} label={v.name}>
                {optValue}
            </Descriptions.Item>
        );
    }
    if ([CustomFieldType.MultiSelect].includes(v.type) && Array.isArray(v.data) && Array.isArray(value)) {
        const optValue = v.data.filter((opt) => value.includes(opt.id)).map((opt) => opt.value)?.join(", ") ?? "";
        return (
            <Descriptions.Item key={v.id} label={v.name}>
                {optValue}
            </Descriptions.Item>
        );
    }
    if (v.type === CustomFieldType.Person && Array.isArray(value) && value.every((d) => typeof d === "string")) {
        return (
            <Descriptions.Item key={v.id} label={v.name}>
                {value.join(", ")}
            </Descriptions.Item>
        );
    }
    if (v.type === CustomFieldType.Attachment && typeof value === "string" && value.length > 0) {
        const attachmentList: FileInfo[] = JSON.parse(value);
        if (Array.isArray(attachmentList) && attachmentList.every((d) => Boolean(d.fileSysUuid) && Boolean(d.fileName))) {
            return (
                <Descriptions.Item key={v.id} label={v.name}>
                    <FileBox
                        isDownload
                        value={transferFileInfoToFileType(attachmentList)}
                        isEditName={false}
                        isUpload={false}
                        isDelete={false}
                        style={{width: "100%"}}
                    />
                </Descriptions.Item>
            );
        }
    }
    if (v.type === CustomFieldType.DateInput) {
        return (
            <Descriptions.Item key={v.id} label={v.name}>
                {timeFormat(value)}
            </Descriptions.Item>
        );
    }
    return (
        <Descriptions.Item key={v.id} label={v.name}>
            {value}
        </Descriptions.Item>
    );
});

const SubmitForm = forwardRef<SubmitFormRef, SubmitFormProps>((props, ref) => {
    const cls = useStyle();
    const {
        nodeId,
        nodeType,
        formData,
        defaultFieldList = [],
        isStartFlowNode = false,
        defaultCustomFieldValues,
        onFieldsChange
    } = props;
    const [submitForm] = Form.useForm<RectifyAddFormModel>();
    const [customFieldValues, setCustomFieldValues] = useState<Map<string, string | string[]>>(defaultCustomFieldValues ?? new Map());
    useImperativeHandle(ref, () => ({
        handleSubmit: async () => submitForm.validateFields(),
        jsonValues: transformDataToJsonValues(customFieldValues, defaultFieldList)
    }), [submitForm, defaultFieldList, customFieldValues]);

    const handleFieldsChange = useCallback(
        (info: CustomFieldData, val?: string | string[]) => {
            if (val === undefined) {
                return;
            }
            setCustomFieldValues(new Map(customFieldValues).set(info.id, val));
            if (onFieldsChange !== undefined) {
                onFieldsChange(info, val);
            }
        },
        [customFieldValues, onFieldsChange]
    );

    if (!isStartFlowNode) {
        return (
            <Descriptions className={cls.desBox}>
                <Descriptions.Item label="整改编号">{formData?.name}</Descriptions.Item>
                <Descriptions.Item label="整改期限">
                    {formData?.deadline !== undefined ? formData.deadline.format("YYYY.MM.DD") : null}
                </Descriptions.Item>
                <Descriptions.Item label="整改要求">{formData?.comment}</Descriptions.Item>
                {chunkArray(defaultFieldList, 3).map((dv) => renderDesc(dv, customFieldValues))}
            </Descriptions>
        );
    }

    return (
        <Form
            className={cls.specFormBox}
            form={submitForm}
            initialValues={{...formData}}
            labelCol={{span: 4}}
            wrapperCol={{span: 18}}
        >
            <Row wrap>
                <Col {...ColConfig}>
                    <Form.Item {...formColStyle()} label="整改编号" name="name">
                        <Input disabled placeholder="请输入整改编号" />
                    </Form.Item>
                </Col>
                <Col {...ColConfig}>
                    <Form.Item {...formColStyle()} label="整改期限" name="deadline" rules={[{required: true, message: "请选择整改期限"}]}>
                        <DatePicker style={{width: "100%"}} format="YYYY.MM.DD" />
                    </Form.Item>
                </Col>
                <Col span={24}>
                    <Form.Item {...formColStyle()} label="整改要求" name="comment" rules={[{required: true, message: "请输入整改要求"}]}>
                        <TextArea showCount maxLength={300} placeholder="请输入整改要求" />
                    </Form.Item>
                </Col>
                {
                    defaultFieldList.map((c) => (
                        <Col span={12} key={c.id}>
                            <Form.Item
                                label={c.name}
                                name={c.id}
                                rules={[{required: c.required, message: "必填项"}]}
                                initialValue={customFieldValues.get(c.id)}
                            >
                                <CustomField
                                    maxLength={c.maxLength}
                                    hint={c.hint}
                                    style={{width: "100%"}}
                                    {...(customFieldValues.has(c.id)
                                        ? {currentValue: customFieldValues.get(c.id)}
                                        : {})}
                                    onChange={(val) => handleFieldsChange(c, val)}
                                    onBlur={(val) => handleFieldsChange(c, val)}
                                    data={c}
                                    nodeId={nodeId}
                                    nodeType={nodeType}
                                />
                            </Form.Item>
                        </Col>
                    ))
                }
            </Row>
        </Form>
    );
});

export default SubmitForm;
