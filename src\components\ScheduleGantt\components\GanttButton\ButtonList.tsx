import {isEqual} from "lodash-es";
import React, {ReactElement, useMemo} from "react";
import GanttButton, {GanttButtonType} from "./index";
import useStyles from "./styles";

interface ButtonListProps {
    buttons?: (ReactElement | GanttButtonType)[];
}

const ButtonList: React.FC<ButtonListProps> = (props: ButtonListProps) => {
    const cls = useStyles();
    const {buttons = []} = props;

    const toolButtonsWithKey = useMemo(() => buttons.map((button, index) => ({
        key: `${button}${index}`,
        button,
    })), [buttons]);

    return (
        <div className={cls.buttonList}>
            {toolButtonsWithKey.map(({key, button}) => <GanttButton key={key} button={button} />)}
        </div>
    );
};

export default React.memo(ButtonList, (prev, next) => isEqual(prev, next));
