import React, {useContext} from "react";
import {But<PERSON>} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import {moveUpTask} from "../../../gantt/taskUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const MoveUpButton = () => {
    const cls = useStyles();
    const {isWbs, checkoutStatus} = useContext(EditorContext);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-shangyi" fontSize={18} />}
            onClick={moveUpTask}
            disabled={isWbs}
        >
            上移
        </Button>
    );
};

export default MoveUpButton;
