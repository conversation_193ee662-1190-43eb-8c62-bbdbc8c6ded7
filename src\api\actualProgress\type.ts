/* 计划类型 */
export const actualTypeList = [
    {label: "全部", value: ""},
    {label: "总", value: "MASTER"},
    {label: "年", value: "YEAR"},
    {label: "季", value: "QUARTER"},
    {label: "月", value: "MONTH"},
    {label: "周", value: "WEEK"},
];

/* 计划状态 */
export const actualProgressStatusList = [
    {label: "全部状态", value: ""},
    {label: "未开始", value: 0},
    {label: "进行中", value: 1},
    {label: "已完成", value: 2},
];

export interface ActualProgressPageParam {
    /* 计划名称模糊搜索:空，查询所有 */
    nameKey?: string;
    /* 组织节点ID */
    nodeId?: string;
    /* 组织节点类型 */
    nodeType?: number;
    /* 当前页，从1开始 */
    pageNum: number;
    /* 页大小 */
    pageSize: number;
    /* 计划状态 */
    status?: number;
    /* 计划类型 */
    type?: string;
}

export interface ActualProgressItem {
    /* 计划ID */
    id: string;
    /* 计划周期 */
    cycle: string;
    /* 计划名称 */
    name: string;
    /* 计划状态：执行状态，0-未开始 1-进行中 2-完成 */
    actualStatus: number;
    /* 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type: string;
    /* 实际开始日期，可为空 */
    actualStartDate?: number;
    /* 实际完成日期，可为空 */
    actualEndDate?: number;
    /* 计划开始日期，可为空 */
    startDate: number;
    /* 计划完成日期，可为空 */
    endDate: number;
}

export interface GetActualProgressPageReturn {
    items: ActualProgressItem[];
    total: number;
}

export interface ActualWarnListParam {
    deptId: string;
    /* 计划名称模糊搜索:空，查询所有 */
    nameKey?: string;
    /* 组织节点ID */
    nodeId?: string;
    /* 组织节点类型 */
    nodeType?: number;
    /* 进度计划ID */
    planId?: string;
    /* 计划类型 */
    type?: string;
    /* 报警级别，一、二、三、四，值越小，延期越严重，空查询所有 */
    warnLevel?: number;
}

export interface ActualWarnItem {
    actualDuration: number;//  实际工期，单位天integer(int32)
    actualEndDate: number;// 实际完成日期，可为空string(date-time)
    actualSatus: number;// 执行状态，0-未开始 1-进行中 2-完成integer(int32)
    actualStartDate: number;// 实际开始日期，可为空string(date-time)
    deviationDays: number;// 偏差值，单位天integer(int32)
    doDays: number;// 已进行工期，单位天integer(int32)
    id: string;// 任务idstring
    name: string;// 任务名称string
    parentTaskId: string; // 父任务ID
    planDuration: number;// 计划工期，单位天integer(int32)
    planEndDate: number;// 计划日期string(date-time)
    planId: string;// 所属计划idstring
    planName: string;// 所属计划名称string
    planStartDate: number;// 计划开始日期string(date-time)
    warnLevel: number;// 报警级别，一、二、三、四，值越小，延期越严重integer(int32)
    wbsNo: string;// wbs序号
    /** 后端无这个字段，前端自己加上的 */
    children?: ActualWarnItem[];
}
