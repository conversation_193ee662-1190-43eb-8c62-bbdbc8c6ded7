export const isDateEqual = (date1: Date, date2: Date, accuracy: "date" | "hours" | "minutes" | "seconds") => {
    const status1 = date1.getFullYear() === date2.getFullYear();
    const status2 = date1.getMonth() === date2.getMonth();
    const status3 = date1.getDate() === date2.getDate();

    if (accuracy === "date") {
        return status1 && status2 && status3;
    }

    const status4 = date1.getHours() === date2.getHours();
    if (accuracy === "hours") {
        return status1 && status2 && status3 && status4;
    }

    const status5 = date1.getMinutes() === date2.getMinutes();
    if (accuracy === "minutes") {
        return status1 && status2 && status3 && status4 && status5;
    }

    const status6 = date1.getSeconds() === date2.getSeconds();
    if (accuracy === "seconds") {
        return status1 && status2 && status3 && status4 && status5 && status6;
    }

    return false;
};

export const isDateTimeEqual = (dateTime1: number, dateTime2: number, accuracy: "date" | "hours" | "minutes" | "seconds") => {
    const date1 = new Date(dateTime1);
    const date2 = new Date(dateTime2);
    return isDateEqual(date1, date2, accuracy);
};

export const getCountDays = (year: number, month: number) => {
    const curDate = new Date(year, month);
    const curMonth = curDate.getMonth();
    curDate.setMonth(curMonth + 1);
    curDate.setDate(0);
    return curDate.getDate();
};
