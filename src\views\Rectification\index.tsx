import React, {useCallback, useEffect, useState} from "react";
import {useDispatch} from "react-redux";
import {useParams} from "react-router-dom";
import {RectifyTaskStatus} from "./interface";
import RectifyList from "./crud/list";
import RectifyAdd from "./crud/add";
import RectifyDetail from "./crud/detail";
import {setRectifyQueryOptions} from "../../store/rectification/template/actions";


const Rectification = () => {
    const dispatch = useDispatch();
    const {type = "PLAN_REFORM"} = useParams<{type?: string}>();
    const [pageStatus, setPageStatus] = useState<RectifyTaskStatus>("list");

    useEffect(() => () => {
        // dispatch(setRectifyStatus("list"));
        dispatch(setRectifyQueryOptions({processType: undefined}));
    }, [dispatch]);

    const handlePageStatusChange = useCallback(
        () => {
            setPageStatus("list");
        },
        []
    );

    const handleRecordClick = useCallback(
        () => {
            setPageStatus("detail");
        },
        []
    );

    if (pageStatus === "add") {
        return (
            <RectifyAdd back={handlePageStatusChange} moduleType={type} />
        );
    }

    if (pageStatus === "detail") {
        return (
            <RectifyDetail back={handlePageStatusChange} moduleType={type} />
        );
    }

    return (
        <RectifyList onRecordClick={handleRecordClick} moduleType={type} />
    );
};
export default Rectification;
