import React, {useCallback, useContext} from "react";
import {Button} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const RelatePhotoButton = () => {
    const cls = useStyles();
    const {checkoutStatus, onPhoto} = useContext(EditorContext);

    // 打开照片对话框
    const handleRelatePhoto = useCallback(() => {
        if (onPhoto instanceof Function) {
            onPhoto();
        }
    }, [onPhoto]);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-tupian" fontSize={18} />}
            onClick={handleRelatePhoto}
        >
            关联照片
        </Button>
    );
};

export default RelatePhotoButton;
