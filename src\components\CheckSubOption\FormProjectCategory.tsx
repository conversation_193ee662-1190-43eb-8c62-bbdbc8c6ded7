import {FormInstance, Select, SelectProps} from "antd";
import React, {useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {getCheckListProjectCategory} from "../../api/center";
import {RootState} from "../../store/rootReducer";


interface FormProjectCategoryProps extends Omit<SelectProps<string>, "value" | "onChange"> {
    value?: string;
    onChange?: (value: string) => void;
    moduleType: "security" | "quality" | string;
    // 是否自动设置值,新增是自动,编辑不需要
    valueAuto?: boolean;
    form: FormInstance;
    itemNameKey: string;
}


const FormProjectCategory = (props: FormProjectCategoryProps) => {
    const {value, onChange, moduleType, valueAuto = false, form, itemNameKey, ...other} = props;
    const [options, setOptions] = useState<FormProjectCategoryProps["options"]>([]);
    const {orgInfo} = useSelector((state: RootState) => state.commonData);

    useEffect(() => {
        getCheckListProjectCategory(moduleType.toLowerCase())
            .then((res) => {
                const resData = res.result ?? [];
                setOptions(resData.map((el) => ({label: el.name, value: el.id})));
                // 房建
                if (Number(orgInfo.deptDataType) === 1 && valueAuto === true) {
                    const findData = resData.find((el) => el.name === "房建工程");
                    if (findData !== undefined) {
                        form.setFieldsValue({[itemNameKey]: findData.id});
                    }
                }
                // 基建
                if (Number(orgInfo.deptDataType) === 2 && valueAuto === true) {
                    const findData = resData.find((el) => el.name === "公路工程");
                    if (findData !== undefined) {
                        form.setFieldsValue({[itemNameKey]: findData.id});
                    }
                }
            })
            .catch((err) => {
                console.log(err);
            });
    }, [form, itemNameKey, moduleType, orgInfo.deptDataType, valueAuto]);

    const handleSelect: FormProjectCategoryProps["onSelect"] = (val: string) => {
        if (onChange !== undefined) {
            onChange(val);
        }
    };

    return (
        <>
            <Select value={value} options={options} onSelect={handleSelect} {...other} />
        </>
    );
};

export default FormProjectCategory;
