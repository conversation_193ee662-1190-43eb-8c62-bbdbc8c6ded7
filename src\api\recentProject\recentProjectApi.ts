/* eslint-disable @typescript-eslint/no-explicit-any */
import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {FileDownloadInfo, GetFileDownloadUrlParam} from "../graphicModel";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = (window as any).__IWorksConfig__;
const pdscommonUrl = `${baseUrl}/pdscommon`;

//  获取最近打开的沙盘工程
export const getRecentOpenedSandTableProjList = async (deptId: string, top: number): Promise<WebRes<any>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/sand-table/recent-opened-projects/${deptId}/top/${top}`,
});

//  记录最近打开的沙盘工程
export const recordRecentOpenedSandTableProj = async (deptId: string, ppid: number): Promise<WebRes<any>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/sand-table/recent-opened-projects/${deptId}/ppid/${ppid}`,
});

//  获取最近上传的沙盘工程
export const getRecentUploadedSandTableProjList = async (deptId: string, top: number): Promise<WebRes<any>> => Fetch({
    methods: "get",
    url: `${baseUrl}/iworks/sand-table/recent-upload-proj-list/${deptId}/top/${top}`,
});

//  申请文件下载地址
export const getDownloadUrls = async (param: GetFileDownloadUrlParam): Promise<FileDownloadInfo[]> => Fetch({
    methods: "post",
    url: `${pdscommonUrl}/rs/fileaddress/downloadURLs`,
    data: param,
    isDeal: false,
});
