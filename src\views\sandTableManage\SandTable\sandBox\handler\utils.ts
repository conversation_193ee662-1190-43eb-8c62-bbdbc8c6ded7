import moment from "moment";
import StatusColor from "./dataOrInterface";

// eslint-disable-next-line import/prefer-default-export
export const getColorByTime = (curTime: Date, finishedDate: number) => {
    if (finishedDate === 0) {
        return StatusColor.ongoingColor;
    }
    const finishMoment = moment(finishedDate);
    return finishMoment.startOf("day") <= moment(curTime) ? StatusColor.completeColor : StatusColor.ongoingColor;
};
