import {createUseStyles} from "react-jss";

const modelStyle = {
    root: {
        display: "flex",
        width: "100%",
        height: "100%",
    },
    leftBox: {
        position: "relative",
        flexShrink: 0,
        height: "100%",
        background: "#FFFFFF",
    },
    leftPanelBox: {
        height: "100%",
        overflow: "hidden",
        transition: "width 0.3s ease-in-out",
    },
    leftPanel: {
        display: "inline-block",
        width: 320,
        height: "100%",
    },
    content: {
        position: "relative",
        flexGrow: 1,
        flexBasis: 0,
        width: "100vw",
        // padding: "16px 24px",
        overflowY: "auto"
    },
    collapseBtn: {
        position: "absolute",
        right: "-16px",
        top: "50%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "16px",
        height: "32px",
        backgroundColor: "#E1E2E5",
        transform: "translateY(-50%)",
        cursor: "pointer",
    }
};

const useMenuLayoutStyle = createUseStyles(modelStyle);

export default useMenuLayoutStyle;
