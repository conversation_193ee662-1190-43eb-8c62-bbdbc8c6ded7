import React, {Key, useCallback, useEffect, useMemo, useRef, useState} from "react";
import {useSelector, useDispatch} from "react-redux";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SketchPicker} from "react-color";
import {Button, Col, message, Row, Select, Typography} from "antd";
import {CloseOutlined} from "@ant-design/icons";
import ProcessTmplTreeWithFilter from "../../../../../components/ProcessTmplTreeWithFilter";
import {ProcessTemplateInfo, ProcessTmplTreeNode} from "../../../../../api/processManager/type";
import {RootState} from "../../../../../store/rootReducer";
import {getProcessTemplateList, getProcessTemplateTreeInfo, getProjProcessTmplByPpid, saveProjProcessTmpl} from "../../../../../api/processManager";
import {isDefined} from "../../../../../assets/ts/utils";
import useStyles from "./style";
import {enhanceCenterTmplTree, enhanceCenterTmplTreeWithStateKey, getIworksProcessTmplLeafNodeMap, transformCenterTmplTreeToIworks, transformIworksTmplTreeToCenter, updateTmplTreeNodeColor} from "../../helps";
import ComModal from "../../../../../components/ComModal";
import {setCurProcessTmplId, setProcessTmplTree} from "../../../../../store/process/action";
import {setAnalyzedData} from "../../../../../store/sandAnalyzedData/action";

const {Option} = Select;
const {Text} = Typography;

interface ProcessTmplContentProps {
    onClose?: () => void;
}

const ProcessTmplContent = (props: ProcessTmplContentProps) => {
    const cls = useStyles();
    const dispatch = useDispatch();
    const {onClose} = props;
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {curTmplId: sotreCurTmplId} = useSelector((state: RootState) => state.processData);
    const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const [selectedNode, setSelectedNode] = useState<ProcessTmplTreeNode>();
    const [tmplList, setTmplList] = useState<ProcessTemplateInfo[]>([]);
    const [curTmplId, setCurTmplId] = useState<string>();
    const [tmplTree, setTmplTree] = useState<ProcessTmplTreeNode[]>([]);
    const [modalVisible, setModelVisible] = useState<boolean>(false);
    const [resetBtnLoading, setResetBtnLoading] = useState<boolean>(false);
    const [expandedKeys, setExpandedKeys] = useState<string[]>();

    // 模板切换，弹出告警通知框，此时将选中的模板id存起来，点击确定更换后再赋值给curTmplId
    const tmplIdRef = useRef<string>();

    const init = useCallback(
        async () => {
            if (!isDefined(curSandTable)) {
                return;
            }
            const {orgId, orgType} = orgInfo;
            const processTmplListRes = await getProcessTemplateList({orgType, orgid: orgId});
            const projProcessTmplRes = await getProjProcessTmplByPpid(curSandTable.ppid);
            const tempTmplList = processTmplListRes.result?.list ?? [];
            const tempTmplItem = tempTmplList.find((v) => v.id === projProcessTmplRes?.data?.templateId);
            const originTmplTree = projProcessTmplRes?.data?.projStateNode;
            const tempTmplTree = isDefined(originTmplTree) ? transformIworksTmplTreeToCenter([originTmplTree]) : undefined;
            const realTmplTree = tempTmplTree ?? [];
            setTmplList(tempTmplList);
            setCurTmplId(tempTmplItem?.id ?? `${curSandTable.projName}工序模板`);
            setExpandedKeys([realTmplTree[0]?.key ?? ""]);
            setTmplTree(realTmplTree);
        },
        [curSandTable, orgInfo]
    );

    useEffect(
        () => {
            init();
        },
        [init]
    );

    const beforeTmplChange = useCallback(
        (tmplId: string) => {
            tmplIdRef.current = tmplId;
            setModelVisible(true);
        },
        []
    );

    const handleTmplChange = useCallback(
        async () => {
            const {result} = await getProcessTemplateTreeInfo(tmplIdRef.current ?? "");
            if (!isDefined(result)) {
                message.error("当前工序模板无数据！请切换模板重试");
            }
            const tempTmplTree = enhanceCenterTmplTree([result]);
            const realTmplTree = tempTmplTree ?? [];
            setExpandedKeys([realTmplTree[0]?.key ?? ""]);
            setSelectedNode(undefined);
            setTmplTree(tempTmplTree ?? []);
            setCurTmplId(tmplIdRef.current);
            setModelVisible(false);
            tmplIdRef.current = undefined;
        },
        []
    );

    const handleColorPickChange = useCallback<ColorChangeHandler>(
        (color) => {
            if (!isDefined(selectedNode)) {
                return;
            }
            const {rgb} = color;
            const newSelectedNode = {...selectedNode, color: `${rgb.r},${rgb.g},${rgb.b}`};
            setTmplTree((prevTree) => {
                updateTmplTreeNodeColor(prevTree, newSelectedNode);
                return [...prevTree];
            });
            setSelectedNode(newSelectedNode);
        },
        [selectedNode]
    );

    // 工序模板树，色块的点击事件
    const handleColorBlockClick = useCallback(
        (node: ProcessTmplTreeNode) => {
            setSelectedNode((prev) => {
                if (prev?.key !== node.key) {
                    return node;
                }
                return undefined;
            });
        },
        []
    );

    const handleCloseModal = useCallback(
        () => {
            setModelVisible(false);
            tmplIdRef.current = undefined;
        },
        []
    );

    const handleClose = useCallback(
        () => {
            setSelectedNode(undefined);
            if (onClose !== undefined) {
                onClose();
            }
        },
        [onClose]
    );

    const handleSaveTmpl = useCallback(
        async () => {
            if (!isDefined(curSandTable)) {
                return;
            }
            const iworksTmplTree = transformCenterTmplTreeToIworks(tmplTree);
            await saveProjProcessTmpl({
                ppid: curSandTable.ppid,
                projStateNode: iworksTmplTree[0],
                templateId: curTmplId
            });
            const {data: processTmplData} = await getProjProcessTmplByPpid(curSandTable.ppid);
            const originTmplTree = processTmplData?.projStateNode;
            const tempTmplTree = isDefined(originTmplTree) ? transformIworksTmplTreeToCenter([originTmplTree]) : undefined;
            // 保存工序模板时，如果当前工序模板id与store中的工序模板id，则认为工序模板已切换
            if (curTmplId !== sotreCurTmplId) {
                if (analyzedData.handler !== undefined && analyzedData.isReady === true) {
                    dispatch(setAnalyzedData({
                        isReady: false,
                        handler: analyzedData.handler
                    }));
                    analyzedData.handler.refresh().then(() => {
                        dispatch(setAnalyzedData({
                            isReady: true,
                            handler: analyzedData.handler
                        }));
                    });
                }
            }
            dispatch(setCurProcessTmplId(processTmplData?.templateId ?? ""));
            dispatch(setProcessTmplTree(tempTmplTree ?? []));
            message.success("工序模板修改成功！");
            handleClose();
        },
        [analyzedData.handler, analyzedData.isReady, curSandTable, curTmplId, dispatch, handleClose, sotreCurTmplId, tmplTree]
    );

    const handleSearch = useCallback(
        () => {
            setSelectedNode(undefined);
        },
        []
    );

    const handleCloseColorPicker = useCallback(
        () => {
            setSelectedNode(undefined);
        },
        []
    );

    const handleExpand = useCallback(
        (keys: Key[]) => {
            setExpandedKeys(keys as string[]);
        },
        []
    );

    const renderColorPicker = useCallback(
        () => {
            if (!isDefined(selectedNode)) {
                return null;
            }
            const {name, color} = selectedNode;
            return (
                <div className={cls.colorPickerBox}>
                    <Row justify="space-between" className={cls.colorPickerTitle}>
                        <Col span={20}>
                            <Text ellipsis={{tooltip: name}} strong>{name}</Text>
                        </Col>
                        <Col span={4} style={{textAlign: "right"}}>
                            <CloseOutlined onClick={handleCloseColorPicker} />
                        </Col>
                    </Row>
                    <SketchPicker
                        disableAlpha
                        color={`rgb(${color})`}
                        onChange={handleColorPickChange}
                    />
                </div>
            );
        },
        [cls, selectedNode, handleColorPickChange, handleCloseColorPicker]
    );

    // 初始化
    const resetTmplTree = useCallback(
        async () => {
            if (!isDefined(curTmplId) || !isDefined(curSandTable)) {
                return;
            }
            setResetBtnLoading(true);
            const {result} = await getProcessTemplateTreeInfo(curTmplId);
            if (!isDefined(result)) {
                message.error("当前工序模板无数据！请切换模板重试");
                setResetBtnLoading(false);
                return;
            }
            const iworksTmplTreeRes = await getProjProcessTmplByPpid(curSandTable.ppid);
            const root = iworksTmplTreeRes?.data?.projStateNode;
            const stateKeyMap = isDefined(root) ? getIworksProcessTmplLeafNodeMap([root]) : new Map<string, string>();
            const newCenterTmplTree = enhanceCenterTmplTreeWithStateKey(stateKeyMap, [result]);
            setTmplTree(newCenterTmplTree ?? []);
            message.success("当前工序模板已初始化");
            setResetBtnLoading(false);
        },
        [curSandTable, curTmplId]
    );

    const processTmplTreeView = useMemo(() => (
        <ProcessTmplTreeWithFilter
            tmplTree={tmplTree}
            showAlert
            key={curTmplId}
            onSearch={handleSearch}
            onColorBlockClick={handleColorBlockClick}
            expandedKeys={expandedKeys}
            onExpand={handleExpand}
        />
    ), [curTmplId, handleColorBlockClick, handleSearch, tmplTree, expandedKeys, handleExpand]);

    return (
        <>
            <div className={cls.box}>
                <Row style={{marginBottom: 8}}>
                    <Col flex="auto">
                        <Select
                            value={curTmplId}
                            style={{width: "100%"}}
                            onChange={beforeTmplChange}
                        >
                            {tmplList.map((v) => <Option key={v.id}>{v.templateName}</Option>)}
                        </Select>
                    </Col>
                    <Col flex="112px" style={{marginLeft: 8}}>
                        <Button
                            type="primary"
                            ghost
                            style={{width: "100%", fontSize: 14}}
                            onClick={resetTmplTree}
                            disabled={curTmplId !== sotreCurTmplId || curTmplId === `${curSandTable?.projName}工序模板`}
                            loading={resetBtnLoading}
                        >
                            初始化
                        </Button>
                    </Col>
                </Row>
                {processTmplTreeView}
                <Row gutter={1} className={cls.drawerFooter}>
                    <Col span={12}>
                        <Button style={{background: "#33394D", borderColor: "#33394D", fontSize: 14}} onClick={handleClose}>
                            取消
                        </Button>
                    </Col>
                    <Col span={12}>
                        <Button type="primary" onClick={handleSaveTmpl} style={{fontSize: 14}}>
                            保存
                        </Button>
                    </Col>
                </Row>
            </div>
            {renderColorPicker()}
            <ComModal
                title="告警通知"
                visible={modalVisible}
                onCancel={handleCloseModal}
                onOk={handleTmplChange}
            >
                <p style={{padding: "24px 52px"}}>更换模板将清空原有工序数据，确定继续？</p>
            </ComModal>
        </>
    );
};

export default ProcessTmplContent;
