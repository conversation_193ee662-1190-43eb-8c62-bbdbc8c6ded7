import {ProcessActions, SET_PROCESS_TMPL_ID, SET_PROCESS_TMPL_TREE, ProcessInfo, SET_SELECTED_COMPS, SET_VISIBLE_COMPS, SET_PROCESS_SELECT_TYPE} from "./types";
import {ProcessTmplTreeNode} from "../../api/processManager/type";

export const setProcessTmplTree = (payload: ProcessTmplTreeNode[]): ProcessActions => ({
    type: SET_PROCESS_TMPL_TREE,
    payload
});

export const setCurProcessTmplId = (payload: string): ProcessActions => ({
    type: SET_PROCESS_TMPL_ID,
    payload
});

export const setSelectedComps = (payload: ProcessInfo["selectedComps"]): ProcessActions => ({
    type: SET_SELECTED_COMPS,
    payload
});

export const setVisibleCompPaths = (payload: ProcessInfo["visibleCompPaths"]): ProcessActions => ({
    type: SET_VISIBLE_COMPS,
    payload
});

export const setProcessSelectType = (payload: ProcessInfo["selectType"]): ProcessActions => ({
    type: SET_PROCESS_SELECT_TYPE,
    payload
});
