import React, {Dispatch, Mu<PERSON>RefObject, SetStateAction} from "react";
import {PlanInfoDetailType} from "../../../../api/Preparation/type";
import {CalendarInfo, ColumnItem} from "../../api/plan/type";
import {EnterType, FromType} from "../../config/interface";

export interface EditorContextType {
    planInfo: Partial<PlanInfoDetailType>;
    setPlanInfo: Dispatch<SetStateAction<Partial<PlanInfoDetailType>>>;
    parentPlanInfo: Partial<PlanInfoDetailType>;
    setParentPlanInfo: Dispatch<SetStateAction<Partial<PlanInfoDetailType>>>;
    hasParentPlan: boolean;
    fromType: FromType;
    enterType: EnterType;
    isWbs: boolean;
    setIsWbs: Dispatch<SetStateAction<boolean>>;
    scale: string;
    needAutoSave: MutableRefObject<boolean>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
    isShowGantt: boolean;
    setIsShowGantt: Dispatch<SetStateAction<boolean>>;
    checkoutStatus: boolean; // 是否是签出状态
    saveLoading: boolean;
    currentCalendarRef: MutableRefObject<CalendarInfo | undefined>;
    onPhoto: () => void;
    customColumnsRef: MutableRefObject<ColumnItem[] | undefined>;
    fetchAllData: (() => void) | undefined;
    doSyncProcess: (syncType: 1 | 2, silence?: boolean) => (Promise<any>) | undefined; // 同步
    refreshTaskRef: MutableRefObject<(() => void) | undefined>; // 刷新任务
    checkoutMethodRef: MutableRefObject<((checkoutSuccess: boolean) => void) | undefined>; // 签出进度计划
    updatePlanTaskMethodRef: MutableRefObject<(() => Promise<any>) | undefined>; // 保存进度计划
    saveMethodRef: MutableRefObject<((checkinType: 1 | 2) => Promise<any>) | undefined>; // 点击保存按钮-保存进度计划
    onLaunchApproval: ((planInfo: PlanInfoDetailType) => void) | undefined;
}

export const initialContext: EditorContextType = {
    planInfo: {},
    setPlanInfo: () => undefined,
    parentPlanInfo: {},
    setParentPlanInfo: () => undefined,
    hasParentPlan: false,
    fromType: "plan",
    enterType: "view",
    isWbs: false,
    setIsWbs: () => undefined,
    scale: "",
    needAutoSave: {current: true},
    loading: false,
    setLoading: () => undefined,
    isShowGantt: false,
    setIsShowGantt: () => undefined,
    checkoutStatus: false,
    saveLoading: false,
    currentCalendarRef: {current: undefined},
    onPhoto: () => undefined,
    customColumnsRef: {current: undefined},
    fetchAllData: undefined,
    doSyncProcess: () => undefined,
    refreshTaskRef: {current: undefined},
    checkoutMethodRef: {current: undefined},
    updatePlanTaskMethodRef: {current: undefined},
    saveMethodRef: {current: undefined},
    onLaunchApproval: undefined,
};

const EditorContext = React.createContext<EditorContextType>(initialContext);

export default EditorContext;
