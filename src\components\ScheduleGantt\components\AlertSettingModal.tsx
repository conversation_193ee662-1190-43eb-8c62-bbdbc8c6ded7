import React, {FC, memo, useState, useEffect} from "react";
import {Modal, Table} from "antd";
import {ColumnsType} from "antd/lib/table";
import {jss} from "react-jss";

interface AlertItem {
    key: string;
    status?: string;
    name?: string;
    color?: string;
    conditions?: string;
}

interface AlertSettingModalProps {
    visible: boolean;
    onCancel: () => void;
}

// 预警设置对话框组件
const AlertSettingModal: FC<AlertSettingModalProps> = memo((props) => {
    const {visible, onCancel} = props;
    const [modalVisible, setModalVisible] = useState<boolean>(visible);
    const progressAlert1 = "#ffe3df";
    const progressAlert2 = "#f1dde9";
    const notStartAlert1 = "#e4f2ff";
    const notStartAlert2 = "#eeeadc";

    useEffect(() => {
        // 覆盖gantt全局样式
        jss.createStyleSheet({
            "@global": {
                // 预警颜色 start
                ".progress_alert_1, .progress_alert_1.odd": {
                    backgroundColor: "#ffe3df"
                },
                ".progress_alert_2, .progress_alert_2.odd": {
                    backgroundColor: "#f1dde9"
                },
                ".not_start_alert_1, .not_start_alert_1.odd": {
                    backgroundColor: "#e4f2ff"
                },
                ".not_start_alert_2, .not_start_alert_2.odd": {
                    backgroundColor: "#eeeadc"
                },
                // 预警颜色 end
            }
        }).attach();
    }, []);

    useEffect(() => {
        setModalVisible(visible);
    }, [visible]);

    const columns: ColumnsType<AlertItem> = [
        {
            title: "预警等级",
            align: "center",
            width: "33%",
            dataIndex: "name",
            render: (name: string, record: AlertItem, index: number) => {
                if (index === 0 || index === 3) {
                    return {
                        children: record.status,
                        props: {
                            colSpan: 3,
                        },
                    };
                }
                return name;
            },
        },
        {
            title: "预警颜色",
            align: "center",
            width: "33%",
            dataIndex: "color",
            render: (color: string, _record: AlertItem, index: number) => {
                if (index === 0 || index === 3) {
                    return {
                        props: {
                            colSpan: 0,
                        },
                    };
                }
                return <div style={{display: "inline-block", width: "50px", height: "20px", backgroundColor: color}} />;
            },
        },
        {
            title: "预警条件",
            align: "center",
            width: "33%",
            dataIndex: "conditions",
            render: (conditions: string, _record: AlertItem, index: number) => {
                if (index === 0 || index === 3) {
                    return {
                        props: {
                            colSpan: 0,
                        },
                    };
                }
                return conditions;
            },
        },
    ];

    const data: AlertItem[] = [
        {
            key: "1",
            status: "进行中",
        },
        {
            key: "2",
            name: "一级预警",
            color: progressAlert1,
            conditions: "进度滞后 ≥ 5",
        },
        {
            key: "3",
            name: "二级预警",
            color: progressAlert2,
            conditions: "5 > 进度滞后 ≥ 3",
        },
        {
            key: "4",
            status: "未开始",
        },
        {
            key: "5",
            name: "一级预警",
            color: notStartAlert1,
            conditions: "进度滞后 ≥ 5",
        },
        {
            key: "6",
            name: "二级预警",
            color: notStartAlert2,
            conditions: "5 > 进度滞后 ≥ 3",
        },
    ];

    return (
        <>
            <Modal
                title={<span style={{fontWeight: "bold"}}>预警设置</span>}
                visible={modalVisible}
                onCancel={onCancel}
                footer={false}
                destroyOnClose
            >
                <Table
                    columns={columns}
                    dataSource={data}
                    bordered
                    pagination={false}
                />
            </Modal>
        </>
    );
});

export default AlertSettingModal;
