import {Input, InputProps} from "antd";
import React from "react";
import {useDispatch} from "react-redux";
import {setIsShowDrawer} from "./store/action";

const FormInputWBS = (props: InputProps) => {
    const dispatch = useDispatch();

    return (
        <Input
            placeholder="选择WBS分部分项"
            onClick={() => dispatch(setIsShowDrawer(true))}
            {...props}
        />
    );
};

export default FormInputWBS;
