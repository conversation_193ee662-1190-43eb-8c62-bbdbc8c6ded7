const {name, version} = require("../package.json");
let fs = require("fs");
let archiver = require("archiver");
const moment = require("moment");
const path = require("path");
const argv = require("yargs").argv;
const {config} = require("./config.js");
const {deleteFileAll} = require("./utils.js");
let args = process.argv.splice(2);
let time = moment().format("YYYY-MM-DD-HHmmss");
let args0 = args.length ? args[0] : undefined;
let configPath = path.join(__dirname, `./config/${args0}.js`);
const {env} = argv;
// if (args0 === 'prod') {
//     // 正式
//     packageName = `/${name}-${version}-${time}.zip`
// } else if (args0 === "saas-beta") {
//     // 内网saas
//     packageName = `/${name}-${version}-saas-beta-${time}.zip`
// } else if (args0 === "beta" || args0 === "beta-161") {
//     // 测试
//     packageName = `/${name}.zip`
// } else {
//     // 自定义包名，可用于二开项目
//     packageName = `/${name}-${version}-${args0}-${time}.zip`;
// }
packageName = `/${name}-${args0}-${time}.zip`;
/* // 清空压缩包
deleteFileAll({
    rootPath: config.rootPath,
    reg: config.distZipReg
}); */
// 更新配置文件
fs.writeFileSync(path.join(__dirname, "../dist/config.js"), fs.readFileSync(configPath));

// 开始压缩
let output = fs.createWriteStream(path.join(__dirname, `../${packageName}`));

let archive = archiver("zip", {
    zlib: {level: 9},
});

output.on("close", () => {
    console.log("压缩完成");
});

output.on("end", () => {
    console.log("文件处理完成");
});

archive.on("warning", (err) => {
    if (err.code === "ENOENT") {
        console.log(err);
    } else {
        throw err;
    }
});

archive.on("error", (err) => {
    throw err;
});

archive.pipe(output);

archive.directory("dist/", false);

archive.finalize();
