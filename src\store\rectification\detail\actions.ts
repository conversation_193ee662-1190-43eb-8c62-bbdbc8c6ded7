import {createSFAPayloadAction} from "../../utils";
import {QsRecordDetailModel, RectificationActionType} from "../../../components/Rectification/models/rectification";
// eslint-disable-next-line import/no-cycle
import {RectificationDetailModel} from "./reducer";

export const setCurrentCheckInfo = createSFAPayloadAction("SET_CURRENT_CHECK_INFO" as const, (payload: RectificationDetailModel["relatedCheckInfo"]) => payload);

export const setCurrentDetailData = createSFAPayloadAction("SET_CURRENT_DETAIL_DATA" as const, (payload: QsRecordDetailModel) => payload);
export const setVisibleButtons = createSFAPayloadAction("SET_VISIBLE_BUTTONS" as const, (payload: RectificationActionType[]) => payload);

type SetCurrentDetailData = ReturnType<typeof setCurrentDetailData>;
type SetVisibleButtons = ReturnType<typeof setVisibleButtons>;
type SetCurrentCheckInfo = ReturnType<typeof setCurrentCheckInfo>;
type Actions = SetCurrentDetailData | SetVisibleButtons | SetCurrentCheckInfo;
export default Actions;
