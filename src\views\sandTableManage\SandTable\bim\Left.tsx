import React, {useCallback, useMemo, useState} from "react";
import {Col, Radio, RadioChangeEvent, Row} from "antd";
import {useSelector, useDispatch} from "react-redux";
import useStyle from "../style";
import MyIcon from "../../../../components/MyIcon";
import LeftCombineTree from "./LeftCombineTree";
import {RootState} from "../../../../store/rootReducer";
import {setIsLeftExpand} from "../../../../store/sandManage/action";
import Color from "../../../../assets/css/Color";

// const {Option} = Select;
const radioOptions = [
    {label: "EBS", value: "EBS"},
    // {label: "工序列表", value: "processTmpl"}
];
type RadioType = "EBS" | "processTmpl";
// type ProcessBind = "defined" | "all";

const BimLeftView = () => {
    const cls = useStyle();
    const {isLeftExpand} = useSelector((state: RootState) => state.sandManageData);

    const [isExpand, setIsExpand] = useState<boolean>(isLeftExpand);
    // const [checkedKeys, setCheckedKeys] = useState<Set<string>>(new Set());
    const [radioValue, setRadioValue] = useState<RadioType>("EBS");
    // const [processBindType, setProcessBindType] = useState<ProcessBind>("all");
    const dispatch = useDispatch();

    const handleExpandSwitch = useCallback(
        () => {
            setIsExpand((prev) => {
                dispatch(setIsLeftExpand(!prev));
                return !prev;
            });
        },
        [dispatch],
    );

    const handleRadioChange = useCallback(
        (e: RadioChangeEvent) => {
            setRadioValue(e.target.value);
        },
        []
    );

    /* const handleProcessBindTypechange = useCallback(
        (val: ProcessBind) => {
            setProcessBindType(val);
        },
        []
    ); */

    const LeftCombineTreeView = useMemo(() => <LeftCombineTree />, []);

    return (
        <Col
            className={`${cls.left} ${cls.antTreeWrapper}`}
            style={{marginLeft: isExpand ? 0 : -480}}
        >
            <div className={cls.leftSwitch} onClick={handleExpandSwitch}>
                <MyIcon type={isExpand ? "icon-xiangzuo" : "icon-xiangyou"} color={Color.primary} />
            </div>
            <Row justify="space-between">
                <Radio.Group
                    optionType="button"
                    options={radioOptions}
                    buttonStyle="solid"
                    value={radioValue}
                    onChange={handleRadioChange}
                    style={{marginBottom: 16}}
                    className={cls.radioBox}
                />
                {/* <Col flex="160px">
                    <Select
                        style={{width: "100%"}}
                        value={processBindType}
                        onChange={handleProcessBindTypechange}
                    >
                        <Option key="defined">已定义工序部位</Option>
                        <Option key="all">全部部位</Option>
                    </Select>
                </Col> */}
            </Row>
            {LeftCombineTreeView}
        </Col>
    );
};

export default BimLeftView;
