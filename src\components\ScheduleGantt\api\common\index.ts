import {WebRes} from "../../../../api/common.type";
import Fetch from "../../../../service/Fetch";
import {UploadFileInfo} from "../../config/interface";
import {PlanBusinessType, ResourceLockParams, ResourceLockRes} from "./type";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = (window as any).__IWorksConfig__;

export const getServicesUrl = (key: string) => `${baseUrl}/${key}`;

//  上锁（编辑，签出）
export const resourceLock = async (params: ResourceLockParams) => Fetch<WebRes<ResourceLockRes>>({
    url: `${baseUrl}/sphere/rest/resource-lock/lock`,
    methods: "put",
    data: params,
});

//  解锁（保存，签入）
export const resourceUnLock = async (businessType: PlanBusinessType, planId: string) => Fetch<WebRes<boolean>>({
    url: `${baseUrl}/sphere/rest/resource-lock/unLock/${businessType}/${planId}`,
    methods: "put",
});

//  获取图片URL
export const getImageURLs = async (fileUUIDList: string[], fileType: number) => Fetch<any>({
    url: `${baseUrl}/pdscommon/rs/fileaddress/downloadURLs`,
    methods: "post",
    data: {fileUUIDList, fileType}
});

//  上传文件
export const postUploadFile = async (uploadFileInfos: UploadFileInfo[]) => Fetch<any>({
    url: `${baseUrl}/pdscommon/rs/fileaddress/applyUploadUrlEx`,
    methods: "post",
    data: uploadFileInfos
});

export const uploadFileV2 = async (url: string, params: File) => {
    const formData = new FormData();
    formData.append("file", params);
    return fetch(url, {
        body: formData, // must match 'Content-Type' header
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        credentials: "include", // include, same-origin, *omit
        method: "POST", // *GET, POST, PUT, DELETE, etc.
        mode: "cors", // no-cors, cors, *same-origin
        redirect: "follow", // manual, *follow, error
        referrer: "no-referrer", // *client, no-referrer
    }).then(async (response) => response.json());
};
