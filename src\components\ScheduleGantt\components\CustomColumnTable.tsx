import React, {useContext, useState, useEffect, useRef, useCallback} from "react";
import {Table, Button, Form, Select, Input} from "antd";
import {v4 as uuidv4} from "uuid";
import {FormInstance} from "antd/lib/form";
import {ColumnProps, TableProps} from "antd/lib/table";
import {CustomValueType} from "../common/constant";
import MyIconFont from "../../MyIconFont";
import {ColumnItem} from "../api/plan/type";

const EditableContext = React.createContext<FormInstance | null>(null);

export interface TableColumnProps<RecordType> extends ColumnProps<RecordType> {
    editable?: boolean;
}

export interface CustomValueTypeOption {
    type: number;
    name: string;
}

export const customColumnTypeOptions: CustomValueTypeOption[] = [
    {type: CustomValueType.text, name: "文本"},
    {type: CustomValueType.date, name: "日期"},
    {type: CustomValueType.number, name: "数值"}
];

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({index, ...props}) => {
    const [form] = Form.useForm();
    return (
        <Form form={form} component={false}>
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    children: React.ReactNode;
    dataIndex: string;
    record: ColumnItem;
    handleSave: (record: ColumnItem) => void;
}

const EditableCell: React.FC<EditableCellProps> = (props) => {
    const {
        title,
        editable,
        children,
        dataIndex,
        record,
        handleSave,
        ...restProps
    } = props;
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<any>(undefined);// eslint-disable-line
    const form = useContext(EditableContext) as FormInstance;

    useEffect(() => {
        if (editing === true && inputRef.current !== undefined) {
            inputRef.current.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        if (record !== undefined && record !== null) {
            const value = (record as any)[dataIndex];// eslint-disable-line 
            form.setFieldsValue({[dataIndex]: value});
        }
    };

    const save = async () => {
        try {
            const values = await form.validateFields();
            toggleEdit();
            if (values[dataIndex] !== undefined) {
                handleSave({...record, ...values});
            }
        } catch (errInfo) {
            // console.log("Save failed:", errInfo);
        }
    };

    let childNode = children;

    if (editable) {
        if (editing) {
            if (dataIndex === "name") {
                childNode = (
                    <Form.Item
                        style={{margin: 0, width: "100%"}}
                        name={dataIndex}
                    >
                        <Input
                            style={{width: "100%"}}
                            ref={inputRef}
                            onBlur={save}
                        />
                    </Form.Item>
                );
            }
            if (dataIndex === "valueType") {
                childNode = (
                    <Form.Item
                        style={{margin: 0, width: "100%"}}
                        name={dataIndex}
                    >
                        <Select style={{width: "100%"}} ref={inputRef} onSelect={save} onBlur={save}>
                            {customColumnTypeOptions.map((option) => (
                                <Select.Option key={option.type} value={option.type}>
                                    {option.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                );
            }
        }
    }

    return (
        <td
            {...restProps}
            style={{width: "100%"}}
            onClick={() => editing === false && toggleEdit()}
        >
            {childNode}
        </td>
    );
};

interface CustomColumnInputProps<T extends {}> extends TableProps<T> {
    value?: T[];
    editable: boolean;
    onColumnChange?: (customColumns: T[]) => void;
}

const CustomColumnTable: React.FC<CustomColumnInputProps<ColumnItem>> = (props) => {
    const {editable, value, onColumnChange, ...restProps} = props;
    const [customColumns, setCustomColumns] = useState<ColumnItem[]>([]);

    useEffect(() => {
        setCustomColumns(value ?? []);
    }, [value]);

    const triggerChange = useCallback((columns: ColumnItem[]) => {
        setCustomColumns(columns);
        if (onColumnChange instanceof Function) {
            onColumnChange(columns);
        }
    }, [onColumnChange]);

    const handleDelete = (key: string) => {
        triggerChange(customColumns.filter((item) => item.columnId !== key));
    };

    const handleAdd = useCallback(() => {
        const newData: ColumnItem = {columnId: uuidv4().replace(/-/g, ""), name: "自定义列", valueType: CustomValueType.text};
        triggerChange([...customColumns, newData]);
    }, [customColumns, triggerChange]);

    const handleSave = (row: ColumnItem) => {
        const newLinkList: ColumnItem[] = [...customColumns];
        const index = newLinkList.findIndex((item: ColumnItem) => row.columnId === item.columnId);
        const item = newLinkList[index];
        newLinkList.splice(index, 1, {
            ...item,
            ...row,
        });
        triggerChange([...newLinkList]);
    };

    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };

    const originColumns: TableColumnProps<ColumnItem>[] = [
        {
            title: "名称",
            dataIndex: "name",
            editable,
        },
        {
            title: "类型",
            dataIndex: "valueType",
            editable,
            render: (type: number) => customColumnTypeOptions.find((option) => option.type === type)?.name ?? ""
        },
    ];

    if (editable) {
        originColumns.push(
            {
                title: "操作",
                width: "100px",
                render: (_: unknown, record: ColumnItem) => <a onClick={() => handleDelete(record.columnId)}>删除</a>
            }
        );
    }

    const columns: any = originColumns.map((col) => { // eslint-disable-line
        if (col.editable !== true) {
            return col;
        }
        return {
            ...col,
            onCell: (record: ColumnItem) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave,
            }),
        };
    });

    return (
        <div>
            {
                editable && (
                    <div style={{height: "40px"}}>
                        <Button
                            style={{display: "flex", alignItems: "center", padding: "0 4px"}}
                            type="text"
                            icon={<MyIconFont type="icon-tianjia" />}
                            onClick={handleAdd}
                        >
                            添加
                        </Button>
                    </div>
                )
            }
            <Table
                {...restProps}
                rowKey={(record) => record.columnId}
                components={components}
                dataSource={customColumns}
                columns={columns}
                pagination={false}
            />
        </div>
    );
};

export default CustomColumnTable;
