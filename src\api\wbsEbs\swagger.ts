/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/indent */
/* eslint-disable @typescript-eslint/class-name-casing */
/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  "/sphere/plan/actual/page": {
    post: operations["pageUsingPOST_6"];
  };
  "/sphere/plan/actual/syncExport": {
    post: operations["exportUsingPOST_1"];
  };
  "/sphere/plan/actual/warn-list": {
    post: operations["warnListUsingPOST"];
  };
  "/sphere/plan/approval/export": {
    put: operations["exportApprovalUsingPUT"];
  };
  "/sphere/plan/approval/page": {
    put: operations["pageApprovalUsingPUT"];
  };
  "/sphere/plan/approval/tab-num": {
    put: operations["pageApprovalTabNumUsingPUT"];
  };
  "/sphere/plan/batch-delete": {
    delete: operations["batchDeleteUsingDELETE"];
  };
  "/sphere/plan/calendar/list": {
    get: operations["listCalendarTemplateUsingGET"];
  };
  "/sphere/plan/calendar/{id}": {
    get: operations["getCalendarTemplateUsingGET"];
  };
  "/sphere/plan/change-pre-date": {
    put: operations["changePreDateUsingPUT"];
  };
  "/sphere/plan/change/{id}": {
    put: operations["changeUsingPUT"];
  };
  "/sphere/plan/create": {
    post: operations["createUsingPOST_11"];
  };
  "/sphere/plan/detail/{id}": {
    get: operations["detailUsingGET_9"];
  };
  "/sphere/plan/launch-change": {
    put: operations["launchChangeUsingPUT"];
  };
  "/sphere/plan/list-associative-parent-plan/planId/{planId}": {
    get: operations["listAssociativeParentPlanUsingGET"];
  };
  "/sphere/plan/list-used-cycle": {
    get: operations["listUsedCycleUsingGET"];
  };
  "/sphere/plan/manage/getComponentDate/{ppid}": {
    get: operations["getComponentDateUsingGET"];
  };
  "/sphere/plan/manage/getComponentStatistic": {
    post: operations["getComponentStatisticUsingPOST"];
  };
  "/sphere/plan/manage/getwbsComponentState": {
    post: operations["getWbsComponentStateUsingPOST"];
  };
  "/sphere/plan/page": {
    post: operations["pageUsingPOST_7"];
  };
  "/sphere/plan/page/export": {
    post: operations["exportPageUsingPOST_2"];
  };
  "/sphere/plan/statistic/wbsCompleteStatistic": {
    post: operations["wbsCompleteStatisticUsingPOST"];
  };
  "/sphere/plan/statistic/wbsStatisticByName": {
    post: operations["wbsStatisticByNameUsingPOST"];
  };
  "/sphere/plan/statistic/wbsStatisticByParent": {
    post: operations["wbsStatisticByParentUsingPOST"];
  };
  "/sphere/plan/tab-num": {
    post: operations["tabNumUsingPOST"];
  };
  "/sphere/plan/task/all-column/planId/{planId}/module/{module}": {
    get: operations["listAllColumnUsingGET"];
  };
  "/sphere/plan/task/bind-files": {
    post: operations["restBindUsingPOST_1"];
  };
  "/sphere/plan/task/bind-node": {
    post: operations["bindNodeUsingPOST_1"];
  };
  "/sphere/plan/task/changed-project/planId/{planId}": {
    get: operations["listChangedProjectUsingGET"];
  };
  "/sphere/plan/task/check/data": {
    put: operations["checkDateUsingPUT"];
  };
  "/sphere/plan/task/custom-column/upset": {
    post: operations["upsetCustomColumnUsingPOST"];
  };
  "/sphere/plan/task/hidden-columns/planId/{planId}/module/{module}": {
    put: operations["setHiddenColumnsUsingPUT"];
  };
  "/sphere/plan/task/list-bind-files": {
    post: operations["listUsingPOST_12"];
  };
  "/sphere/plan/task/list-bind-node": {
    post: operations["listBindNodeUsingPOST_1"];
  };
  "/sphere/plan/task/list-ebs-node": {
    post: operations["listBindingEbsNodeUsingPOST_1"];
  };
  "/sphere/plan/task/list/{planId}": {
    get: operations["listUsingGET"];
  };
  "/sphere/plan/task/process-inspection-time/sync/start": {
    put: operations["startSyncProcessInspectionTimeUsingPUT"];
  };
  "/sphere/plan/task/upset": {
    post: operations["upsetUsingPOST"];
  };
  "/sphere/plan/update": {
    put: operations["updateUsingPUT_4"];
  };
  "/sphere/plan/wbs/bind-ebs-list": {
    post: operations["bindEbsListUsingPOST_2"];
  };
  "/sphere/plan/wbs/bind-ebs-list/v1": {
    post: operations["bindEbsListV1UsingPOST_1"];
  };
  "/sphere/plan/wbs/bind-node": {
    post: operations["bindNodeUsingPOST_2"];
  };
  "/sphere/plan/wbs/bind-wbs-list": {
    post: operations["bindWbsListUsingPOST_2"];
  };
  "/sphere/plan/wbs/bind-wbs-list/v1": {
    post: operations["bindWbsListV1UsingPOST_1"];
  };
  "/sphere/plan/wbs/list-bind-node": {
    post: operations["listBindNodeUsingPOST_2"];
  };
  "/sphere/plan/wbs/list-ebs-node": {
    get: operations["bindingEbsNodeUsingGET"];
    post: operations["listBindingEbsNodeUsingPOST_2"];
  };
  "/sphere/plan/wbs/list-ifbind-ebs": {
    post: operations["listIfBindingEbsNodeUsingPOST"];
  };
  "/sphere/plan/wbs/path-ebs-list-by-wbs": {
    post: operations["pathEbsListByWbsUsingPOST_1"];
  };
  "/sphere/plan/wbs/path-wbs-list-by-ebs": {
    post: operations["pathWbsListByEbsUsingPOST_1"];
  };
  "/sphere/rules/setting/add": {
    post: operations["addUsingPOST_2"];
  };
  "/sphere/rules/setting/delete": {
    delete: operations["deleteUsingDELETE_2"];
  };
  "/sphere/rules/setting/list": {
    get: operations["listUsingGET_1"];
  };
  "/sphere/rules/setting/update": {
    post: operations["updateUsingPOST_8"];
  };
}

export interface definitions {
  /**
   * ActualPlanItem
   * @description 时间进度信息
   */
  ActualPlanItem: {
    /**
     * Format: date-time
     * @description 实际完成日期，可为空
     */
    actualEndDate?: string;
    /**
     * Format: date-time
     * @description 实际开始日期，可为空
     */
    actualStartDate?: string;
    /**
     * Format: int32
     * @description 执行状态，0-未开始 1-进行中 2-完成
     */
    actualStatus: number;
    /**
     * Format: date-time
     * @description 计划周期
     */
    cycle?: string;
    /**
     * Format: date-time
     * @description 计划完成日期
     */
    endDate?: string;
    /** @description 计划ID */
    id?: string;
    /** @description 计划名称 */
    name?: string;
    /** @description 组织节点ID，空，查询项目部下有权限的数据 */
    nodeId?: string;
    /** @description 组织节点名称 */
    nodeName?: string;
    /**
     * Format: int32
     * @description 组织节点类型，项目部=2，标段=3
     */
    nodeType?: number;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    startDate?: string;
    /**
     * Format: int32
     * @description 计划状态：0-无需审批，1-未申报（未发起审批），2-已申报（审批中），3-审批完成
     */
    status?: number;
    /** @description 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type?: string;
  };
  /** ActualPlanListResult */
  ActualPlanListResult: {
    /** @description 编辑前置任务关联传参 */
    predTaskRelationList?: definitions["PlanPredTaskRelationUpsetParam"][];
    /** @description 任务列表 */
    taskList?: definitions["PlanTaskItem"][];
  };
  /**
   * ActualPlanParam
   * @description 实际进度查询参数
   */
  ActualPlanParam: {
    /** @description 项目部ID */
    deptId: string;
    /** @description 计划名称模糊搜索，空，查询所有 */
    nameKey?: string;
    /** @description 组织节点ID，空，查询项目部下有权限的数据 */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型，项目部=2，标段=3
     */
    nodeType?: number;
    /**
     * Format: int32
     * @description 当前页，从1开始
     */
    pageNum?: number;
    /**
     * Format: int32
     * @description 页大小
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 执行状态，0-未开始 1-进行中 2-完成, 空查询所有
     */
    status?: number;
    /** @description 计划类型，MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划，空，查询所有 */
    type?: string;
  };
  /**
   * ActualWarnTaskItem
   * @description 实际任务报警条目
   */
  ActualWarnTaskItem: {
    /**
     * Format: int32
     * @description 实际工期，单位天
     */
    actualDuration?: number;
    /**
     * Format: date-time
     * @description 实际完成日期，可为空
     */
    actualEndDate?: string;
    /**
     * Format: date-time
     * @description 实际开始日期，可为空
     */
    actualStartDate?: string;
    /**
     * Format: int32
     * @description 执行状态，1-未开始 2-进行中 3-完成
     */
    actualStatus: number;
    /** @description 项目部ID */
    deptId?: string;
    /**
     * Format: int32
     * @description 偏差值，单位天
     */
    deviationDays?: number;
    /**
     * Format: int32
     * @description 已进行工期，单位天
     */
    doDays?: number;
    /** @description 任务id */
    id: string;
    /** @description 任务名称 */
    name: string;
    /** @description 父任务ID */
    parentTaskId?: string;
    /**
     * Format: int32
     * @description 计划工期，单位天
     */
    planDuration?: number;
    /**
     * Format: date-time
     * @description 计划结束日期
     */
    planEndDate?: string;
    /** @description 所属计划id */
    planId: string;
    /** @description 所属计划名称 */
    planName: string;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    planStartDate?: string;
    /**
     * Format: int32
     * @description 报警级别，一、二、三、四，值越小，延期越严重
     */
    warnLevel?: number;
    /** @description wbs序号 */
    wbsNo: string;
  };
  /**
   * ActualWarnTaskParam
   * @description 实际任务延期查询参数
   */
  ActualWarnTaskParam: {
    /** @description 项目部ID */
    deptId: string;
    /** @description 计划名称模糊搜索，空查询全部 */
    nameKey?: string;
    /** @description 组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型
     */
    nodeType?: number;
    /** @description 进度计划ID */
    planId?: string;
    /** @description 计划类型，空查询全部 */
    type?: string;
    /**
     * Format: int32
     * @description 报警级别，一、二、三、四，值越小，延期越严重，空查询所有
     */
    warnLevel?: number;
  };
  /** BindEbsResult */
  BindEbsResult: {
    path?: string[];
    /** Format: int32 */
    ppid?: number;
    projectName?: string;
  };
  /** CalendarTemplateItemResult */
  CalendarTemplateItemResult: {
    /**
     * Format: int32
     * @description 复制的日历类型 0 24 1 标准
     */
    calendarFalg?: number;
    /** @description 复制的id */
    copyid?: string;
    /** @description 模板名称 */
    ctName?: string;
    /** @description 模板id */
    ctid?: string;
  };
  /** CalendarTemplateResult */
  CalendarTemplateResult: {
    /**
     * Format: int32
     * @description 复制的日历类型 0 24 1 标准
     */
    calendarFalg?: number;
    /** @description 复制的id */
    copyid?: string;
    /**
     * Format: date-time
     * @description 添加时间
     */
    createDate?: string;
    /** @description 模板名称 */
    ctName?: string;
    /** @description 模板id */
    ctid?: string;
    /**
     * Format: date-time
     * @description 有效结束时间
     */
    endDate?: string;
    /** @description 休息日 */
    restDates?: string[];
    /**
     * Format: date-time
     * @description 有效开始时间
     */
    startDate?: string;
    /**
     * Format: date-time
     * @description 修改时间
     */
    updateDate?: string;
    /** @description 工作日 */
    workDates?: string[];
  };
  /** ChangedProjectItem */
  ChangedProjectItem: {
    /** @description 变更的任务ID列表 */
    changedTaskIdList?: string[];
    /**
     * Format: int32
     * @description 代理工程ID
     */
    ppid?: number;
    /** @description 工程名称 */
    projName?: string;
    /** @description 未变更的任务ID列表 */
    unchangedTaskIdList?: string[];
  };
  /** CommonBindNodeBase */
  CommonBindNodeBase: {
    /** @description 业务id */
    businessId: string;
    /** @description 需要绑定的ebs节点列表【绑定ebs必传】 */
    ebsNodes?: definitions["EbsNodeRequest"][];
    /** @description 需要绑定的wbs节点id列表【绑定wbs必传】 */
    wbsNodeIds?: string[];
  };
  /**
   * CommonBindNodeRequest
   * @description 绑定wbs/ebs传参
   */
  CommonBindNodeRequest: {
    /**
     * Format: int32
     * @description 绑定类型：-1 wbs绑定，0-未绑定、 >0-eds绑定 (1-工程、 2-构件、 3-类别)
     */
    bindType: number;
    /** @description 业务id */
    businessId: string;
    /**
     * @description 业务类型，security.activity.edu.wbs=岗前教育，security.danger.project.wbs=危大工程，security.danger.source.accessment.wbs=危险源评估，" +
     *             "security.danger.source.control.wbs=危险源管控，security.disclosure.wbs=技术交底，security.inspection.routing.wbs=巡检计划，security.check.info.wbs=安全检查,PLAN_TASK=进度子任务，security.routing.inspection.point.ebs=巡检点台账中巡检点
     */
    businessType: string;
    /** @description 项目部id */
    deptId: string;
    /** @description 需要绑定的ebs节点列表【绑定ebs必传】 */
    ebsNodes?: definitions["EbsNodeRequest"][];
    /** @description 标段id，没有标段则传空字符串 */
    nodeId: string;
    /** @description 需要绑定的wbs节点id列表【绑定wbs必传】 */
    wbsNodeIds?: string[];
  };
  /**
   * CommonBindNodeResponse
   * @description 绑定的wbs/ebs节点
   */
  CommonBindNodeResponse: {
    /**
     * Format: int32
     * @description 绑定类型：-1 wbs绑定，0-未绑定、 >0-eds绑定 (1-工程、 2-构件、 3-类别)
     */
    bindType: number;
    /** @description 业务id */
    businessId: string;
    /** @description 业务类型 */
    businessType: string;
    /** @description 项目部id */
    deptId: string;
    /** @description 绑定的ebs节点列表 */
    ebsNodes: definitions["EbsNodeResponse"][];
    /** @description 标段id，没有标段则传空字符串 */
    nodeId: string;
    /** @description 绑定的wbs节点列表 */
    wbsNodes: definitions["WbsNodeBoResponse"][];
  };
  /**
   * CommonEbsNodeResponse
   * @description 绑定的ebs节点
   */
  CommonEbsNodeResponse: {
    /** @description 业务id */
    businessId: string;
    /** @description 绑定的ebs节点列表 */
    ebsNodes: definitions["EbsNodeBo"][];
  };
  /** CommonFileBindInfo */
  CommonFileBindInfo: {
    /** @description 业务id */
    businessId?: string;
    /** @description 业务类型 */
    businessType?: string;
    /** @description 文件扩展属性 */
    fileExtraInfo?: string;
    /** @description 文件名称 */
    fileName?: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize?: number;
    /** @description 文件uuid */
    fileUuid?: string;
  };
  /**
   * CommonFileBindParam
   * @description 公共-文件绑定传参
   */
  CommonFileBindParam: {
    /** @description 业务id */
    businessId?: string;
    /** @description 业务类型,PLAN_TASK-绑定计划任务 */
    businessType?: string;
    /** @description 最新文件列表 */
    latestFiles?: definitions["CommonFileInfoBase"][];
  };
  /** CommonFileInfoBase */
  CommonFileInfoBase: {
    /** @description 文件扩展属性 */
    fileExtraInfo?: string;
    /** @description 文件名称 */
    fileName?: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize?: number;
    /** @description 文件uuid */
    fileUuid?: string;
  };
  /** ComponentStateBaseResult */
  ComponentStateBaseResult: {
    /**
     * Format: int64
     * @description 实际结束日期
     */
    endDate?: number;
    /**
     * Format: int64
     * @description 计划结束日期
     */
    planEndDate: number;
    /**
     * Format: int64
     * @description 计划开始日期
     */
    planStartDate: number;
    /**
     * Format: int64
     * @description 实际开始日期
     */
    startDate?: number;
  };
  /** ComponentStateParam */
  ComponentStateParam: {
    /**
     * Format: int32
     * @description 当前页
     */
    page: number;
    /**
     * Format: int32
     * @description 工程id
     */
    ppid: number;
    /**
     * Format: int32
     * @description 分页大小
     */
    size: number;
    /** @description 排序，如果是升序，使用sort=field，如果是降序，使用sort=-field，如果参数中包含多个排序条件，则多个排序条件间用’%2c’隔开，如果排序的字段为中文或包含特殊字符，则需要进行base64编码 */
    sort?: string;
  };
  /** ComponentStatisticResult */
  ComponentStatisticResult: {
    /**
     * Format: int64
     * @description 实际结束日期
     */
    endDate?: number;
    /** @description 构件handle */
    handle: string;
    /** @description 构件path */
    path: string;
    /** @description 工序集合 */
    procedures?: definitions["ProcedureStatistic"][];
    /**
     * Format: int64
     * @description 实际开始日期
     */
    startDate?: number;
  };
  /** EbsNode */
  EbsNode: {
    /** @description 构件全路径 */
    ebsNodes: definitions["Element"][];
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /** @description 工程名称 */
    projName?: string;
  };
  /** EbsNodeBo */
  EbsNodeBo: {
    /** @description 构件handle */
    handle: string;
    mergePath?: string;
    /** @description 构件全路径 */
    paths: string[];
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /** @description 工程名称 */
    projName?: string;
  };
  /**
   * EbsNodeRequest
   * @description ebs节点传承
   */
  EbsNodeRequest: {
    /** @description 构件handle */
    handle: string;
    /** @description 构件全路径 */
    paths: string[];
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /** @description 工程名称 */
    projName?: string;
  };
  /**
   * EbsNodeResponse
   * @description ebs节点返回值
   */
  EbsNodeResponse: {
    /** @description 构件handle */
    handle: string;
    mergePath?: string;
    /** @description 构件全路径 */
    paths: string[];
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /** @description 工程名称 */
    projName?: string;
  };
  /** Element */
  Element: {
    /** @description 构件handle */
    handle: string;
    /** @description 构件全路径 */
    paths: string[];
  };
  /** PageResponseData«ComponentStatisticResult» */
  "PageResponseData«ComponentStatisticResult»": {
    /** @description 实体集合 */
    items: definitions["ComponentStatisticResult"][];
    /**
     * Format: int64
     * @description 总记录数
     */
    totalCount: number;
    /**
     * Format: int64
     * @description 总页数
     */
    totalPage: number;
  };
  /** PageResponseData«WbsComponentStateResult» */
  "PageResponseData«WbsComponentStateResult»": {
    /** @description 实体集合 */
    items: definitions["WbsComponentStateResult"][];
    /**
     * Format: int64
     * @description 总记录数
     */
    totalCount: number;
    /**
     * Format: int64
     * @description 总页数
     */
    totalPage: number;
  };
  /** PageResult«ActualPlanItem» */
  "PageResult«ActualPlanItem»": {
    items?: definitions["ActualPlanItem"][];
    /** Format: int64 */
    total?: number;
  };
  /** PageResult«PlanItemResult» */
  "PageResult«PlanItemResult»": {
    items?: definitions["PlanItemResult"][];
    /** Format: int64 */
    total?: number;
  };
  /** PathEbsListByWbsRequest */
  PathEbsListByWbsRequest: {
    /** @description 项目id */
    deptId: string;
    /** @description ebs集合 */
    list: definitions["PathEbsListInfo"][];
    /** @description 标段id */
    nodeId: string;
  };
  /** PathEbsListByWbsResponse */
  PathEbsListByWbsResponse: {
    /** @description path */
    path: string;
    /**
     * Format: int32
     * @description ppid
     */
    ppid: number;
    /** @description 绑定的wbs集合 */
    wbsList: definitions["PathEbsListByWbsResponseExt"][];
  };
  /** PathEbsListByWbsResponseExt */
  PathEbsListByWbsResponseExt: {
    /** @description wbsId */
    id: string;
    /** @description wbs完整路径 */
    pathName: string;
  };
  /** PathEbsListInfo */
  PathEbsListInfo: {
    /** @description path */
    path: string[];
    /**
     * Format: int32
     * @description ppid
     */
    ppid: number;
  };
  /**
   * PathWbsListByEbsRequest
   * @description wbsPath查询请求参数
   */
  PathWbsListByEbsRequest: {
    /** @description 项目id */
    deptId: string;
    /** @description wbsId */
    ids: string[];
    /** @description 标段id */
    nodeId: string;
  };
  /** PathWbsListByEbsResponse */
  PathWbsListByEbsResponse: {
    /** @description 关联ebs集合 */
    ebsList: definitions["PathWbsListByEbsResponseExt"][];
    /** @description wbsId */
    id: string;
  };
  /** PathWbsListByEbsResponseExt */
  PathWbsListByEbsResponseExt: {
    /** @description path */
    path: string;
    /** @description ebs完整路径 */
    pathName: string;
    /**
     * Format: int32
     * @description ppid
     */
    ppid: number;
  };
  /** PlanApprovalPageParam */
  PlanApprovalPageParam: {
    /** @description 计划变更状态：未变更-Unchanged, 已变更-Changed */
    changeStatus?: string;
    /** @description 项目部ID */
    deptId?: string;
    /** @description 导出时传入的具体id */
    ids?: string[];
    /** @description 计划名称模糊搜索 */
    nameKey?: string;
    /** @description 组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型
     */
    nodeType?: number;
    /**
     * Format: int32
     * @description 当前页，从1开始
     */
    pageNum?: number;
    /**
     * Format: int32
     * @description 页大小
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 审批处理类别：0-全部，1-我发起，2-待处理，3-已处理，4-抄送我的
     */
    processType?: number;
    /**
     * Format: int32
     * @description 计划状态
     */
    status?: number;
    /** @description 计划类型 */
    type?: string;
  };
  /** PlanBindNodeRequest */
  PlanBindNodeRequest: {
    /**
     * Format: int32
     * @description 绑定类型：-1 wbs绑定，0-未绑定、 >0-eds绑定 (1-工程、 2-构件、 3-类别)
     */
    bindType: number;
    /**
     * @description 业务类型，security.activity.edu.wbs=岗前教育，security.danger.project.wbs=危大工程，security.danger.source.accessment.wbs=危险源评估，" +
     *             "security.danger.source.control.wbs=危险源管控，security.disclosure.wbs=技术交底，security.inspection.routing.wbs=巡检计划，security.check.info.wbs=安全检查,PLAN_TASK=进度子任务
     */
    businessType: string;
    /** @description 项目部id */
    deptId: string;
    /** @description 绑定关系列表 */
    list: definitions["CommonBindNodeBase"][];
    /** @description 标段id，没有标段则传空字符串 */
    nodeId: string;
    /** @description planId */
    planId: string;
  };
  /** PlanColumnResult */
  PlanColumnResult: {
    /** @description 列ID */
    columnId?: string;
    /** @description true-隐藏 */
    hidden?: boolean;
    /** @description 列名 */
    name?: string;
    /**
     * Format: int32
     * @description 列取值类型1=文本/2=日期/3=数值
     */
    valueType?: number;
  };
  /**
   * PlanCreateParam
   * @description 创建传参
   */
  PlanCreateParam: {
    /** @description 审批模板ID，不传则无需审批 */
    approvalTemplateId?: string;
    /** @description 工作日历：0-24小时日历，1-标准日历，其他值-自定义日历 */
    calendar: string;
    /**
     * Format: date-time
     * @description 计划周期[计划类型:MASTER时不传]
     */
    cycle?: string;
    /** @description 项目部ID */
    deptId: string;
    /**
     * Format: date-time
     * @description 计划完成日期
     */
    endDate: string;
    /** @description 计划名称 */
    name: string;
    /** @description 组织节点ID */
    nodeId: string;
    /**
     * Format: int32
     * @description 组织节点类型
     */
    nodeType: number;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    startDate: string;
    /** @description 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type: string;
  };
  /** PlanCustomColumnUpsetParam */
  PlanCustomColumnUpsetParam: {
    /** @description 列ID */
    columnId?: string;
    /** @description 列显隐 */
    hidden: boolean;
    /** @description 列名 */
    name: string;
    /**
     * Format: int32
     * @description 列取值类型1=文本/2=日期/3=数值
     */
    valueType: number;
  };
  /**
   * PlanCycleResult
   * @description 计划周期
   */
  PlanCycleResult: {
    /**
     * Format: date-time
     * @description 计划周期
     */
    cycle?: string;
    /** @description 计划类型 */
    type?: string;
  };
  /** PlanEbsNode */
  PlanEbsNode: {
    /** Format: int64 */
    endDate?: number;
    /** @description 构件handle */
    handle: string;
    mergePath?: string;
    /** @description 构件全路径 */
    paths: string[];
    /**
     * Format: int32
     * @description 代理工程id
     */
    ppid: number;
    /** Format: int64 */
    preEndDate?: number;
    /** Format: int64 */
    preStartDate?: number;
    /** @description 工程名称 */
    projName?: string;
    /** Format: int64 */
    startDate?: number;
  };
  /**
   * PlanEbsNodeReponse
   * @description 任务是否绑定ebs
   */
  PlanEbsNodeReponse: {
    /** @description 是否绑定了ebs */
    bindEbs: boolean;
    /** @description 业务id */
    businessId: string;
  };
  /** PlanEbsNodeRet */
  PlanEbsNodeRet: {
    /** @description 业务id */
    businessId?: string;
    /** @description 绑定的ebs节点列表 */
    ebsNodes?: definitions["PlanEbsNode"][];
  };
  /** PlanItemResult */
  PlanItemResult: {
    /**
     * Format: date-time
     * @description 审批完成时间
     */
    approvalEndDate?: string;
    /** @description 审批ID */
    approvalId?: string;
    /**
     * Format: date-time
     * @description 审批发起时间
     */
    approvalStartDate?: string;
    /**
     * Format: int32
     * @description 审批状态：1-无审批，2-审批中，3-审批完成，4-审批删除（即审批撤回），5-退回到发起人节点
     */
    approvalStatus?: number;
    /** @description 审批模板ID */
    approvalTemplateId?: string;
    /** @description 审批人列表 */
    approvalUserList?: definitions["UserInfoDTO"][];
    /** @description 是否绑定WBS,false=未绑定，true=绑定 */
    bindWbs?: boolean;
    /** @description 变更状态：未变更-Unchanged，已变更-Changed */
    changeStatus?: string;
    /**
     * Format: date-time
     * @description 计划周期
     */
    cycle?: string;
    /**
     * Format: date-time
     * @description 计划完成日期
     */
    endDate?: string;
    /** @description 计划ID */
    id?: string;
    /** @description 计划名称 */
    name?: string;
    /** @description 组织节点名称 */
    nodeName?: string;
    /** @description 流程状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
    processStatus?: string;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    startDate?: string;
    /**
     * Format: int32
     * @description 计划状态：0-无需审批，1-未申报（未发起审批），2-已申报（审批中），3-审批完成
     */
    status?: number;
    /** @description 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type?: string;
    /**
     * Format: date-time
     * @description 编制时间
     */
    updateAt?: string;
    /** @description 编制人 */
    updateBy?: string;
  };
  /**
   * PlanLaunchChangeParam
   * @description 计划发起变更传参
   */
  PlanLaunchChangeParam: {
    /** @description 变更原因 */
    changeReason?: string;
    /** @description 变更资料 */
    fileList?: definitions["公共文件信息传参"][];
    /** @description 计划ID */
    id?: string;
  };
  /** PlanPageParam */
  PlanPageParam: {
    /** @description 项目id */
    deptId?: string;
    /**
     * Format: int32
     * @description 是否过滤没有报警的计划，0=不过滤，1=过滤，默认为0
     */
    filterNoWarn?: number;
    /** @description 导出时传入的具体id */
    ids?: string[];
    /** @description 计划名称模糊搜索，空，查询所有 */
    nameKey?: string;
    /** @description 组织节点ID */
    nodeId: string;
    /**
     * Format: int32
     * @description 组织节点类型，，项目部=2，标段=3
     */
    nodeType: number;
    /**
     * Format: int32
     * @description 当前页，从1开始
     */
    pageNum?: number;
    /**
     * Format: int32
     * @description 页大小
     */
    pageSize?: number;
    /**
     * Format: int32
     * @description 计划状态
     */
    status?: number;
    /** @description 计划类型，MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划，空，查询所有 */
    type?: string;
  };
  /** PlanPredTaskRelationUpsetParam */
  PlanPredTaskRelationUpsetParam: {
    /**
     * Format: int32
     * @description 时间关系:0 - 'finish_to_start',1 - 'start_to_start',2 - 'finish_to_finish',3 - 'start_to_finish'
     */
    dateRelation?: number;
    /**
     * Format: int32
     * @description 时间间隔（单位天）
     */
    dateSpan?: number;
    /** @description 关联ID，uuid小写 */
    id?: string;
    /** @description 进度计划ID */
    planId?: string;
    /** @description 前置任务ID */
    predTaskId?: string;
    /** @description 任务ID */
    taskId?: string;
  };
  /** PlanResult */
  PlanResult: {
    /** @description 审批ID */
    approvalId?: string;
    /** @description 审批发起人 */
    approvalStartUser?: string;
    /**
     * Format: int32
     * @description 审批状态：1-无审批，2-审批中，3-审批完成，4-审批删除（即审批撤回），5-退回到发起人节点
     */
    approvalStatus?: number;
    /** @description 审批模板ID */
    approvalTemplateId?: string;
    /** @description 工作日历 */
    calendar?: string;
    /** @description 变更资料 */
    changeFileList?: definitions["公共文件信息"][];
    /** @description 变更原因 */
    changeReason?: string;
    /** @description 变更状态：未变更-Unchanged,已变更-Changed */
    changeStatus?: string;
    /**
     * Format: int32
     * @description 当前总工期，单位天
     */
    currentTotalDuration?: number;
    /**
     * Format: date-time
     * @description 计划周期
     */
    cycle?: string;
    /** @description 项目部ID */
    deptId?: string;
    /**
     * Format: date-time
     * @description 计划完成日期
     */
    endDate?: string;
    /** @description 是否有子计划：true-有 */
    hasChildren?: boolean;
    /** @description 计划ID */
    id?: string;
    /** @description 计划名称 */
    name?: string;
    /** @description 组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型，项目部=2，标段=3
     */
    nodeType?: number;
    /** @description 父计划ID */
    parentId?: string;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    startDate?: string;
    /**
     * Format: int32
     * @description 计划状态：0-无需审批，1-未申报（未发起审批），2-已申报（审批中），3-审批完成
     */
    status?: number;
    /** @description 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type?: string;
    /**
     * Format: int32
     * @description 变更前总工期，单位天
     */
    unchangedTotalDuration?: number;
    /**
     * Format: date-time
     * @description 编制时间
     */
    updateAt?: string;
    /** @description 编制人 */
    updateBy?: string;
  };
  /** PlanStatisticsWbsResponse */
  PlanStatisticsWbsResponse: {
    /**
     * Format: date-time
     * @description 实际结束时间
     */
    actualEndDate?: string;
    /**
     * Format: date-time
     * @description 实际开始时间
     */
    actualStartDate?: string;
    /**
     * Format: int32
     * @description 偏差值
     */
    deviation?: number;
    /** @description 执行状态 */
    executionStatus?: string;
    /** @description 是否存在子节点 */
    hasChild?: boolean;
    /** @description wbsID */
    id?: string;
    /** @description wbs节点名称 */
    name?: string;
    /** @description 父节点id */
    pId?: string;
    /**
     * Format: date-time
     * @description 计划完成时间
     */
    planEndDate?: string;
    /**
     * Format: date-time
     * @description 计划开始时间
     */
    planStartDate?: string;
    /**
     * Format: double
     * @description 进度计划
     */
    ratio?: number;
    /** @description 标段id */
    sectionId?: string;
    /**
     * Format: int32
     * @description 排序
     */
    sort?: number;
    /**
     * Format: date-time
     * @description 服务器时间
     */
    systemTime?: string;
  };
  /**
   * PlanTaskBatchUpsetParam
   * @description 任务批量修改覆盖传参
   */
  PlanTaskBatchUpsetParam: {
    /** @description 新增前置任务关联传参 */
    addPredTaskRelationList?: definitions["PlanPredTaskRelationUpsetParam"][];
    /** @description 新增任务传参 */
    addTaskList?: definitions["PlanTaskUpsetParam"][];
    /** @description 删除的前置任务关联ID */
    deletePredTaskRelationIdList?: string[];
    /** @description 删除的任务ID */
    deleteTaskIdList?: string[];
    /** @description 更新任务数据选项，PLAN-更新计划部分 ACTUAL--更新实际部分 */
    opt: string;
    /** @description 计划ID */
    planId: string;
    /** @description 编辑前置任务关联传参 */
    updatePredTaskRelationList?: definitions["PlanPredTaskRelationUpsetParam"][];
    /** @description 编辑任务传参 */
    updateTaskList?: definitions["PlanTaskUpsetParam"][];
  };
  /**
   * PlanTaskItem
   * @description 计划任务
   */
  PlanTaskItem: {
    /**
     * Format: int32
     * @description 实际工期
     */
    actualDuration?: number;
    /**
     * Format: date-time
     * @description 实际结束时间
     */
    actualEndDate?: string;
    /**
     * Format: date-time
     * @description 实际开始时间
     */
    actualStartDate?: string;
    /**
     * Format: int32
     * @description 实际时间是否为手动录入(位运算)：1-实际开始时间为录入, 2-实际结束时间为录入
     */
    actualSyn?: number;
    /**
     * Format: int32
     * @description 模型绑定类型：-1-绑定wbs、0-未绑定、 1-工程、 2-构件、 3-类别
     */
    bindType: number;
    /** @description 变更状态：未变更-Unchanged,已变更-Changed */
    changeStatus: string;
    /** @description 自定义值 */
    customValueMap?: { [key: string]: string };
    /** @description 责任人 */
    dutyPerson: string;
    /** @description 责任单位 */
    dutyUnit?: string;
    /** @description 是否关联了文件（图片） */
    hasFile?: boolean;
    /** @description 任务ID，uuid小写 */
    id: string;
    /**
     * Format: int32
     * @description 是否里程碑，1是，0否
     */
    milestone: number;
    /** @description 任务名称 */
    name: string;
    /** @description 父任务ID */
    parentTaskId?: string;
    /**
     * Format: int32
     * @description 计划工期
     */
    planDuration: number;
    /**
     * Format: date-time
     * @description 计划结束日期
     */
    planEndDate: string;
    /** @description 计划id */
    planId?: string;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    planStartDate: string;
    /** @description 备注 */
    remarks?: string;
    /**
     * Format: int32
     * @description 要求工期
     */
    requestDuration?: number;
    /**
     * Format: date-time
     * @description 要求结束日期
     */
    requestEndDate?: string;
    /**
     * Format: date-time
     * @description 要求开始日期
     */
    requestStartDate?: string;
    /**
     * Format: int32
     * @description 任务顺序
     */
    sort: number;
    /**
     * Format: int32
     * @description 任务状态：1-未开始，2-进行中，3-已完成
     */
    taskStatus: number;
    /** @description wbs序号 */
    wbsNo: string;
  };
  /** PlanTaskUpsetParam */
  PlanTaskUpsetParam: {
    /**
     * Format: int32
     * @description 实际工期
     */
    actualDuration?: number;
    /**
     * Format: date-time
     * @description 实际结束时间
     */
    actualEndDate?: string;
    /**
     * Format: date-time
     * @description 实际开始时间
     */
    actualStartDate?: string;
    /**
     * Format: int32
     * @description 实际时间是否为手动录入(位运算)：1-实际开始时间为录入, 2-实际结束时间为录入
     */
    actualSyn?: number;
    /** @description 变更状态：未变更-Unchanged,已变更-Changed */
    changeStatus: string;
    /** @description 自定义值 */
    customValueMap?: { [key: string]: string };
    /** @description 责任人 */
    dutyPerson: string;
    /** @description 责任单位 */
    dutyUnit?: string;
    /** @description 任务ID，uuid小写 */
    id: string;
    /**
     * Format: int32
     * @description 是否里程碑，1是，0否
     */
    milestone: number;
    /** @description 任务名称 */
    name: string;
    /** @description 父任务ID */
    parentTaskId?: string;
    /**
     * Format: int32
     * @description 计划工期
     */
    planDuration: number;
    /**
     * Format: date-time
     * @description 计划结束日期
     */
    planEndDate: string;
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    planStartDate: string;
    /** @description 备注 */
    remarks?: string;
    /**
     * Format: int32
     * @description 要求工期
     */
    requestDuration?: number;
    /**
     * Format: date-time
     * @description 要求结束日期
     */
    requestEndDate?: string;
    /**
     * Format: date-time
     * @description 要求开始日期
     */
    requestStartDate?: string;
    /**
     * Format: int32
     * @description 任务顺序
     */
    sort: number;
    /**
     * Format: int32
     * @description 任务状态：1-未开始，2-进行中，3-已完成
     */
    taskStatus: number;
    /** @description wbs序号 */
    wbsNo: string;
  };
  /**
   * PlanUpdateParam
   * @description 编辑传参
   */
  PlanUpdateParam: {
    /** @description 审批模板ID，不传不修改，传空字符串表无需审批 */
    approvalTemplateId?: string;
    /** @description 工作日历：0-24小时日历，1-标准日历，其他值-自定义日历 */
    calendar?: string;
    /**
     * Format: int32
     * @description 当前总工期，单位天
     */
    currentTotalDuration?: number;
    /**
     * Format: date-time
     * @description 计划周期
     */
    cycle?: string;
    /**
     * Format: date-time
     * @description 计划完成日期
     */
    endDate?: string;
    /** @description 计划ID */
    id: string;
    /** @description 计划名称 */
    name?: string;
    /** @description  组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description  组织节点类型，2-项目部， 3-标段
     */
    nodeType?: number;
    /** @description 父计划ID */
    parentId?: string;
    /** @description 引用父计划的taskid */
    parentPlanTaskIds?: string[];
    /**
     * Format: date-time
     * @description 计划开始日期
     */
    startDate?: string;
    /** @description 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type?: string;
  };
  /** ProcedureStatistic */
  ProcedureStatistic: {
    /** @description center工序id */
    centerProcedureId?: string;
    /**
     * Format: int64
     * @description 结束时间
     */
    finishTime?: number;
    /** @description 工序名称 */
    procedureName?: string;
    /**
     * Format: int64
     * @description 开始时间
     */
    startTime?: number;
  };
  /** ResponseEntity */
  ResponseEntity: {
    body?: { [key: string]: unknown };
    /** @enum {string} */
    statusCode?:
      | "CONTINUE"
      | "SWITCHING_PROTOCOLS"
      | "PROCESSING"
      | "CHECKPOINT"
      | "OK"
      | "CREATED"
      | "ACCEPTED"
      | "NON_AUTHORITATIVE_INFORMATION"
      | "NO_CONTENT"
      | "RESET_CONTENT"
      | "PARTIAL_CONTENT"
      | "MULTI_STATUS"
      | "ALREADY_REPORTED"
      | "IM_USED"
      | "MULTIPLE_CHOICES"
      | "MOVED_PERMANENTLY"
      | "FOUND"
      | "MOVED_TEMPORARILY"
      | "SEE_OTHER"
      | "NOT_MODIFIED"
      | "USE_PROXY"
      | "TEMPORARY_REDIRECT"
      | "PERMANENT_REDIRECT"
      | "BAD_REQUEST"
      | "UNAUTHORIZED"
      | "PAYMENT_REQUIRED"
      | "FORBIDDEN"
      | "NOT_FOUND"
      | "METHOD_NOT_ALLOWED"
      | "NOT_ACCEPTABLE"
      | "PROXY_AUTHENTICATION_REQUIRED"
      | "REQUEST_TIMEOUT"
      | "CONFLICT"
      | "GONE"
      | "LENGTH_REQUIRED"
      | "PRECONDITION_FAILED"
      | "PAYLOAD_TOO_LARGE"
      | "REQUEST_ENTITY_TOO_LARGE"
      | "URI_TOO_LONG"
      | "REQUEST_URI_TOO_LONG"
      | "UNSUPPORTED_MEDIA_TYPE"
      | "REQUESTED_RANGE_NOT_SATISFIABLE"
      | "EXPECTATION_FAILED"
      | "I_AM_A_TEAPOT"
      | "INSUFFICIENT_SPACE_ON_RESOURCE"
      | "METHOD_FAILURE"
      | "DESTINATION_LOCKED"
      | "UNPROCESSABLE_ENTITY"
      | "LOCKED"
      | "FAILED_DEPENDENCY"
      | "TOO_EARLY"
      | "UPGRADE_REQUIRED"
      | "PRECONDITION_REQUIRED"
      | "TOO_MANY_REQUESTS"
      | "REQUEST_HEADER_FIELDS_TOO_LARGE"
      | "UNAVAILABLE_FOR_LEGAL_REASONS"
      | "INTERNAL_SERVER_ERROR"
      | "NOT_IMPLEMENTED"
      | "BAD_GATEWAY"
      | "SERVICE_UNAVAILABLE"
      | "GATEWAY_TIMEOUT"
      | "HTTP_VERSION_NOT_SUPPORTED"
      | "VARIANT_ALSO_NEGOTIATES"
      | "INSUFFICIENT_STORAGE"
      | "LOOP_DETECTED"
      | "BANDWIDTH_LIMIT_EXCEEDED"
      | "NOT_EXTENDED"
      | "NETWORK_AUTHENTICATION_REQUIRED";
    /** Format: int32 */
    statusCodeValue?: number;
  };
  /** ResponseResult«ActualPlanListResult» */
  "ResponseResult«ActualPlanListResult»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["ActualPlanListResult"];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«ActualWarnTaskItem»» */
  "ResponseResult«List«ActualWarnTaskItem»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["ActualWarnTaskItem"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«BindEbsResult»» */
  "ResponseResult«List«BindEbsResult»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["BindEbsResult"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«CommonBindNodeResponse»» */
  "ResponseResult«List«CommonBindNodeResponse»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["CommonBindNodeResponse"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«CommonFileBindInfo»» */
  "ResponseResult«List«CommonFileBindInfo»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["CommonFileBindInfo"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«PathEbsListByWbsResponse»» */
  "ResponseResult«List«PathEbsListByWbsResponse»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["PathEbsListByWbsResponse"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«PathWbsListByEbsResponse»» */
  "ResponseResult«List«PathWbsListByEbsResponse»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["PathWbsListByEbsResponse"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«PlanColumnResult»» */
  "ResponseResult«List«PlanColumnResult»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["PlanColumnResult"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«PlanStatisticsWbsResponse»» */
  "ResponseResult«List«PlanStatisticsWbsResponse»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["PlanStatisticsWbsResponse"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«List«RulesResponse»» */
  "ResponseResult«List«RulesResponse»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["RulesResponse"][];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«PageResult«ActualPlanItem»» */
  "ResponseResult«PageResult«ActualPlanItem»»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["PageResult«ActualPlanItem»"];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«Set«string»» */
  "ResponseResult«Set«string»»": {
    /** Format: int32 */
    code?: number;
    data?: string[];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«WbsCompleteStatisticsResponse» */
  "ResponseResult«WbsCompleteStatisticsResponse»": {
    /** Format: int32 */
    code?: number;
    data?: definitions["WbsCompleteStatisticsResponse"];
    msg?: string;
    success?: boolean;
  };
  /** ResponseResult«boolean» */
  "ResponseResult«boolean»": {
    /** Format: int32 */
    code?: number;
    data?: boolean;
    msg?: string;
    success?: boolean;
  };
  /**
   * RulesAddRequest
   * @description RulesAddRequest
   */
  RulesAddRequest: {
    /** @description 颜色 */
    color: string;
    /** @description 说明 */
    description: string;
    /** @description 是否允许编辑 */
    edit: boolean;
    /** @description 规则 */
    rules?: definitions["RulesRequest"][];
    /**
     * Format: int32
     * @description 排序
     */
    sort: number;
  };
  /**
   * RulesRequest
   * @description RulesRequest
   */
  RulesRequest: {
    /** @description 是否包含 */
    includes: boolean;
    /**
     * Format: int32
     * @description 数值
     */
    val: number;
  };
  /**
   * RulesResponse
   * @description RulesUpdateResponse
   */
  RulesResponse: {
    /** @description 颜色 */
    color?: string;
    /** @description 说明 */
    description?: string;
    /** @description 是否允许编辑 */
    edit: boolean;
    /** @description id */
    id?: string;
    /** @description 规则 */
    rules?: definitions["RulesRequest"][];
    /**
     * Format: int32
     * @description 排序
     */
    sort?: number;
    /**
     * Format: date-time
     * @description 更新时间
     */
    updateAt?: string;
    /** @description 更新人 */
    updateBy?: string;
  };
  /**
   * RulesUpdateRequest
   * @description RulesUpdateRequest
   */
  RulesUpdateRequest: {
    /** @description 颜色 */
    color: string;
    /** @description 说明 */
    description: string;
    /** @description 是否允许编辑 */
    edit: boolean;
    /** @description id */
    id: string;
    /** @description 规则 */
    rules?: definitions["RulesRequest"][];
    /**
     * Format: int32
     * @description 排序
     */
    sort: number;
  };
  /** SyncProcessInspectionTimeParam */
  SyncProcessInspectionTimeParam: {
    /** @description 计划ID */
    planId?: string;
    /**
     * Format: int32
     * @description 同步类型：1-全覆盖同步，2-仅同步未修改过的
     */
    syncType?: number;
  };
  /** TabNumParam */
  TabNumParam: {
    /** @description 项目id */
    deptId?: string;
    /** @description 组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型，，项目部=2，标段=3
     */
    nodeType?: number;
  };
  /** UserInfoDTO */
  UserInfoDTO: {
    /** @description 是否离职：true-离职，false-在职 */
    leaveOffice?: boolean;
    /** @description 用户名称 */
    realName?: string;
    /** @description 账号名 */
    userName?: string;
  };
  /**
   * WbsCompleteStatisticsResponse
   * @description wbs完成统计response
   */
  WbsCompleteStatisticsResponse: {
    /**
     * Format: int32
     * @description 查询完成数
     */
    queryNumber?: number;
    /**
     * Format: int32
     * @description 累计完成数
     */
    totalNumber?: number;
  };
  /** WbsComponentStateResult */
  WbsComponentStateResult: {
    /** @description 节点业务类型(1单位、2子单位、3分部、4子分部、5分项、6子分项、7构件、8工序、9检验批) */
    businessType?: string;
    /**
     * Format: int64
     * @description 实际结束日期
     */
    endDate?: number;
    /** @description 构件handle */
    handle: string;
    /** @description 构件path */
    path: string;
    /**
     * Format: int64
     * @description 计划结束日期
     */
    planEndDate: number;
    /**
     * Format: int64
     * @description 计划开始日期
     */
    planStartDate: number;
    /**
     * Format: int64
     * @description 实际开始日期
     */
    startDate?: number;
    /** @description wbsId */
    wbsId?: string;
  };
  /**
   * WbsNodeBoResponse
   * @description wbs节点信息
   */
  WbsNodeBoResponse: {
    /**
     * Format: int32
     * @description 层级
     */
    level?: number;
    /** @description 模板名 */
    tempName?: string;
    /** @description wbs的id */
    wbsNodeId: string;
    /** @description wbs的name */
    wbsNodeName: string;
  };
  /**
   * WbsStatisticByNameRequest
   * @description WbsStatisticByNameRequest
   */
  WbsStatisticByNameRequest: {
    /** @description 项目id */
    deptId: string;
    /** @description 是否为项目：项目节点为true，标段为false */
    isDept?: boolean;
    /** @description wbs节点名称 */
    name: string;
    /** @description 标段id */
    sectionId?: string;
  };
  /**
   * WbsStatisticByParentRequest
   * @description WbsStatisticByParentRequest
   */
  WbsStatisticByParentRequest: {
    /** @description 项目id */
    deptId: string;
    /** @description 是否为项目：项目节点为true，标段为false */
    isDept?: boolean;
    /** @description wbs节点名称 */
    name?: string;
    /** @description 计划标段id */
    sectionId?: string;
    /** @description wbsPId(传空则查询第一层) */
    wbsPId?: string;
  };
  /** wbs完成统计参数 */
  wbs完成统计参数: {
    /**
     * Format: int64
     * @description 统计结束时间
     */
    endTime?: number;
    /** @description 项目部id */
    projectId: string;
    /** @description 工程类型(1-房建 2-基建) */
    projectType?: string;
    /** @description 标段id */
    sectionId?: string;
    /**
     * Format: int64
     * @description 统计开始时间
     */
    startTime?: number;
  };
  /** 任务数据校验结果TaskDateCheckResult */
  任务数据校验结果TaskDateCheckResult: {
    /** @description 错误信息 */
    msg?: string;
    /** @description 任务id */
    taskId?: string;
    /** @description 任务名称 */
    taskName?: string;
  };
  /** 公共文件信息 */
  公共文件信息: {
    /**
     * Format: int64
     * @description 上传时间，查询结果中展示，前端不需要传值
     */
    createAt?: number;
    /** @description 文件名称 */
    fileName: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize: number;
    /** @description 文件id */
    fileUuid: string;
    id?: string;
    /** @description app缩略图id,没有则传空字符串 */
    thumbUuid: string;
  };
  /** 公共文件信息传参 */
  公共文件信息传参: {
    /** @description 文件名称 */
    fileName: string;
    /**
     * Format: int64
     * @description 文件大小
     */
    fileSize: number;
    /** @description 文件id */
    fileUuid: string;
    /** @description app缩略图id,没有则传空字符串 */
    thumbUuid: string;
  };
  /** 导出报警列表的参数 */
  导出报警列表的参数: {
    /** @description 导出时选中的ids */
    baseIds?: string[];
    /** @description 项目部ID */
    deptId: string;
    /** @description 计划名称模糊搜索，空查询全部 */
    nameKey?: string;
    /** @description 组织节点ID */
    nodeId?: string;
    /**
     * Format: int32
     * @description 组织节点类型
     */
    nodeType?: number;
    /** @description 进度计划ID */
    planId?: string;
    /** @description 计划类型，空查询全部 */
    type?: string;
    /**
     * Format: int32
     * @description 报警级别，一、二、三、四，值越小，延期越严重，空查询所有
     */
    warnLevel?: number;
  };
  /** 查询已经绑定的wbs/ebs */
  "查询已经绑定的wbs/ebs": {
    /**
     * Format: int32
     * @description 三方标志
     */
    buildType?: number;
    /** @description 业务模块，security.activity.edu.wbs=岗前教育，security.danger.project.wbs=危大工程，security.danger.source.accessment.wbs=危险源评估，security.danger.source.control.wbs=危险源管控，security.disclosure.wbs=技术交底，security.inspection.routing.wbs=巡检计划，security.check.info.wbs=安全检查，security.reform.wbs=整改，plan.check.info.wbs=进度检查 */
    businessType: string;
    /** @description 项目部id */
    deptId: string;
    /**
     * @description 质量安全模块，SECURITY=安全，QUALITY=质量，PLAN=进度模块
     * @enum {string}
     */
    moduleType?: "SECURITY" | "QUALITY" | "PLAN";
    /** @description 标段id，没有标段则传空字符串 */
    nodeId?: string;
  };
  /** 查询已经绑定的wbs/ebs_1 */
  "查询已经绑定的wbs/ebs_1": {
    /**
     * Format: int32
     * @description 三方标志
     */
    buildType?: number;
    /** @description 业务模块，security.activity.edu.wbs=岗前教育，security.danger.project.wbs=危大工程，security.danger.source.accessment.wbs=危险源评估，security.danger.source.control.wbs=危险源管控，security.disclosure.wbs=技术交底，security.inspection.routing.wbs=巡检计划，security.check.info.wbs=安全检查，security.reform.wbs=整改，plan.check.info.wbs=进度检查 */
    businessType: string;
    /** @description 项目部id */
    deptId: string;
    /**
     * @description 质量安全模块，SECURITY=安全，QUALITY=质量，PLAN=进度模块
     * @enum {string}
     */
    moduleType?: "SECURITY" | "QUALITY" | "PLAN";
    /** @description 标段id，没有标段则传空字符串 */
    nodeId?: string;
    /**
     * Format: int32
     * @description 同node_type，项目节点=2，标段节点=3
     */
    nodeType?: number;
  };
}

export interface operations {
  pageUsingPOST_6: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["ActualPlanParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«PageResult«ActualPlanItem»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  exportUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** vo */
        vo: definitions["导出报警列表的参数"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  warnListUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** param */
        param: definitions["ActualWarnTaskParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«ActualWarnTaskItem»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  exportApprovalUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["PlanApprovalPageParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pageApprovalUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["PlanApprovalPageParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PageResult«PlanItemResult»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pageApprovalTabNumUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["TabNumParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: number };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  batchDeleteUsingDELETE: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** ids */
        ids: string[];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  listCalendarTemplateUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["CalendarTemplateItemResult"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getCalendarTemplateUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** id */
        id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["CalendarTemplateResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  changePreDateUsingPUT: {
    parameters: {
      query: {
        /** 计划id */
        planId?: string;
      };
      header: {
        /** user token */
        "access-token": string;
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  changeUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** id */
        id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: boolean;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  createUsingPOST_11: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** createParam */
        createParam: definitions["PlanCreateParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: string;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  detailUsingGET_9: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** id */
        id: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PlanResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  launchChangeUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** param */
        param: definitions["PlanLaunchChangeParam"];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAssociativeParentPlanUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** planId */
        planId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PlanItemResult"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listUsedCycleUsingGET: {
    parameters: {
      query: {
        /** 组织节点ID */
        nodeId?: string;
        /** 组织类型 */
        nodeType?: string;
        /** 计划类型 */
        type?: string;
      };
      header: {
        /** user token */
        "access-token": string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PlanCycleResult"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getComponentDateUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** ppid */
        ppid: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ComponentStateBaseResult"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getComponentStatisticUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** param */
        param: definitions["ComponentStateParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PageResponseData«ComponentStatisticResult»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  getWbsComponentStateUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** param */
        param: definitions["ComponentStateParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PageResponseData«WbsComponentStateResult»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pageUsingPOST_7: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["PlanPageParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PageResult«PlanItemResult»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  exportPageUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["PlanPageParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseEntity"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  wbsCompleteStatisticUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** wbsCompleteParam */
        wbsCompleteParam: definitions["wbs完成统计参数"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«WbsCompleteStatisticsResponse»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  wbsStatisticByNameUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** model */
        model: definitions["WbsStatisticByNameRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«PlanStatisticsWbsResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  wbsStatisticByParentUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** model */
        model: definitions["WbsStatisticByParentRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«PlanStatisticsWbsResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  tabNumUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** pageParam */
        pageParam: definitions["TabNumParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: { [key: string]: number };
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listAllColumnUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** module */
        module: string;
        /** planId */
        planId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«PlanColumnResult»»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  restBindUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** bindParam */
        bindParam: definitions["CommonFileBindParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindNodeUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** bindNodeRequest */
        bindNodeRequest: definitions["PlanBindNodeRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listChangedProjectUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** planId */
        planId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ChangedProjectItem"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  checkDateUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** upsetParam */
        upsetParam: definitions["PlanTaskBatchUpsetParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["任务数据校验结果TaskDateCheckResult"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  upsetCustomColumnUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      query: {
        /** planId */
        planId: string;
      };
      body: {
        /** upsetParams */
        upsetParams: definitions["PlanCustomColumnUpsetParam"][];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  setHiddenColumnsUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** columnIds */
        columnIds: string[];
      };
      path: {
        /** module */
        module: string;
        /** planId */
        planId: string;
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listUsingPOST_12: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** businessIds */
        businessIds: string[];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«CommonFileBindInfo»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listBindNodeUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** businessIds */
        businessIds: string[];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«CommonBindNodeResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listBindingEbsNodeUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** businessIds */
        businessIds: string[];
      };
      query: {
        /** ppid */
        ppid?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PlanEbsNodeRet"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      path: {
        /** planId */
        planId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«ActualPlanListResult»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  startSyncProcessInspectionTimeUsingPUT: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** param */
        param: definitions["SyncProcessInspectionTimeParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: boolean;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  upsetUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** upsetParam */
        upsetParam: definitions["PlanTaskBatchUpsetParam"];
      };
    };
    responses: {
      /** OK */
      200: unknown;
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPUT_4: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** createParam */
        createParam: definitions["PlanUpdateParam"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: string;
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindEbsListUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** wbsBindParam */
        wbsBindParam: definitions["查询已经绑定的wbs/ebs"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«BindEbsResult»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindEbsListV1UsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** wbsBindParam */
        wbsBindParam: definitions["查询已经绑定的wbs/ebs_1"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«BindEbsResult»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindNodeUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** bindNodeRequest */
        bindNodeRequest: definitions["CommonBindNodeRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindWbsListUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** wbsBindParam */
        wbsBindParam: definitions["查询已经绑定的wbs/ebs"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«Set«string»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindWbsListV1UsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** wbsBindParam */
        wbsBindParam: definitions["查询已经绑定的wbs/ebs_1"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«Set«string»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listBindNodeUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** businessIds */
        businessIds: string[];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«CommonBindNodeResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  bindingEbsNodeUsingGET: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      query: {
        /** taskId */
        taskId: string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["EbsNode"][];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listBindingEbsNodeUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** businessIds */
        businessIds: string[];
      };
      query: {
        /** ppid */
        ppid?: number;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["CommonEbsNodeResponse"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  listIfBindingEbsNodeUsingPOST: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      query: {
        /** ppid */
        ppid?: number;
      };
      body: {
        /** taskIds */
        taskIds: string[];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["PlanEbsNodeReponse"][];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pathEbsListByWbsUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** request */
        request: definitions["PathEbsListByWbsRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«PathEbsListByWbsResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  pathWbsListByEbsUsingPOST_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** request */
        request: definitions["PathWbsListByEbsRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«PathWbsListByEbsResponse»»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  addUsingPOST_2: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** model */
        model: definitions["RulesAddRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  deleteUsingDELETE_2: {
    parameters: {
      query: {
        /** id */
        id?: string;
      };
      header: {
        /** user token */
        "access-token": string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** No Content */
      204: never;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
    };
  };
  listUsingGET_1: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«List«RulesResponse»»"];
      };
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
  updateUsingPOST_8: {
    parameters: {
      header: {
        /** user token */
        "access-token": string;
      };
      body: {
        /** model */
        model: definitions["RulesUpdateRequest"];
      };
    };
    responses: {
      /** OK */
      200: {
        schema: definitions["ResponseResult«boolean»"];
      };
      /** Created */
      201: unknown;
      /** Unauthorized */
      401: unknown;
      /** Forbidden */
      403: unknown;
      /** Not Found */
      404: unknown;
    };
  };
}

export interface external {}
