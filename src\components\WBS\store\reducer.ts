import {DataNode} from "antd/lib/tree";
import {Key} from "react";
import {AnyAction} from "redux";
import {WBSNodeType} from "../../../api/center/type";
import {EbsBindWbsType, WbsBindEbsType} from "../../../api/wbsEbs/type";
import {WbsEbsTreeDataNode} from "../data";
import ActionTypes from "./actionTypes";

export interface StateType {
    checkedKeys: Key[];
    selectedKey: string;
    isShow: boolean;
    treeData: DataNode[];
    treeDataObj: {
        [key: string]: DataNode[];
    };
    originData: {
        [key: string]: WBSNodeType[];
    };
    isShowDrawer: boolean;
    drawerCheckedNodes: DataNode[];
    leafIds: string[] | undefined;
    ebsTreeData: WbsEbsTreeDataNode[];
    queryCheckedNodes: any[];
    wbsBindEbs: {[key: string]: WbsBindEbsType};
    ebsBindWbs: {[key: string]: EbsBindWbsType};
}

export const initState: StateType = {
    // 搜索选中的节点的key
    checkedKeys: ["all"],
    // 这个没有用到
    selectedKey: "",
    // 搜索弹窗是否显示
    isShow: true,
    // 树数据
    treeData: [],
    // key是标段id,或项目id的树数据
    treeDataObj: {},
    // key是标段,或项目id的扁平数据
    originData: {},
    // 右侧弹窗的wbs的显隐
    isShowDrawer: false,
    // 右侧弹窗的选中的节点
    drawerCheckedNodes: [],
    // 通过叶节点的id来过滤数据
    leafIds: undefined,
    // 搜索的ebs树
    ebsTreeData: [],
    queryCheckedNodes: [],
    wbsBindEbs: {},
    ebsBindWbs: {}
};

const reducer = (state = initState, action: AnyAction): StateType => {
    switch (action.type) {
        case ActionTypes.CHECKED_KEYS:
            return {
                ...state,
                checkedKeys: action.payload
            };
        case ActionTypes.SELECTEDKEY:
            return {
                ...state,
                selectedKey: action.payload
            };
        case ActionTypes.TREEDATA:
            return {
                ...state,
                treeData: action.payload
            };
        case ActionTypes.TREEDATAOBJ:
            return {
                ...state,
                treeDataObj: action.payload
            };
        case ActionTypes.ISSHOW:
            return {
                ...state,
                isShow: action.payload
            };
        case ActionTypes.ISSHOWDRAWER:
            return {
                ...state,
                isShowDrawer: action.payload
            };
        case ActionTypes.DRAWERCHECKEDNODES:
            return {
                ...state,
                drawerCheckedNodes: action.payload
            };
        case ActionTypes.ORIGIN_DATA:
            return {
                ...state,
                originData: action.payload
            };
        case ActionTypes.LEAF_IDS:
            return {
                ...state,
                leafIds: action.payload
            };
        case ActionTypes.SET_EBS_TREE_DATA:
            return {
                ...state,
                ebsTreeData: action.payload
            };
        case ActionTypes.SET_QUERY_CHECKED_NODE:
            return {
                ...state,
                queryCheckedNodes: action.payload
            };
        case ActionTypes.SET_WBS_BIND_EBS:
            return {
                ...state,
                wbsBindEbs: action.payload
            };
        case ActionTypes.SET_EBS_BIND_WBS:
            return {
                ...state,
                ebsBindWbs: action.payload
            };
        default:
            return state;
    }
};

export default reducer;
