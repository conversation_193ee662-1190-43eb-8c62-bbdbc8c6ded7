/* eslint-disable no-console */
import {Input, InputProps, message, Space, Typography} from "antd";
import React, {memo, useCallback, useEffect, useRef, useState} from "react";
import {useDispatch} from "react-redux";
import {saveAs} from "file-saver";
import {getFileUrl, getPreviewUrl} from "../../api/common.api";
import {GetFileUrlRes, FileType} from "../../api/common.type";
import Color from "../../assets/css/Color";
// import {getFileNameNoSuffix, getSuffix, isImg, isVideo, parseFileSize} from "../../assets/ts/utils";
import {getFileNameNoSuffix, getSuffix, isImg, isVideo} from "../../assets/ts/utils";
import {setPreviewUrl} from "../../store/common/action";
import MyIcon from "../MyIcon";
import {Icons} from "./dataAndType";
import IconPng from "./IconPng";

const {Text} = Typography;
interface ListItemProps {
    file: FileType;
    isEditName: boolean;
    isDelete: boolean;
    isDownload: boolean;
    onFileChange: (val: FileType) => void;
    onDelete: (val: FileType) => void;
}

const ListItem = (props: ListItemProps) => {
    const dispatch = useDispatch();
    const {file, isEditName, isDelete, isDownload, onFileChange, onDelete} = props;
    const inputRef = useRef<Input>(null);
    const [isEdit, setIsEdit] = useState<boolean>(false);
    const [fileName, setFileName] = useState<string>("");
    const [fileSuffix, setFileSuffix] = useState<string>("");

    useEffect(() => {
        if (isEdit) {
            if (inputRef !== null && inputRef.current !== null) {
                inputRef.current.focus({cursor: "end"});
            }
        }
    }, [isEdit]);

    const handleEdit = useCallback(
        () => {
            setIsEdit(true);
            setFileSuffix(getSuffix(file.fileName));
            setFileName(getFileNameNoSuffix(file.fileName));
        },
        [file.fileName],
    );

    const fileCheck = (id: string, nameVal: string) => {
        if (Boolean(id) === false) {
            message.warning("无权限查看!");
        }
        const previewFile = ["pdf", "docx", "xlsx", "doc", "xls", "ppt", "pptx", "txt"];
        if (previewFile.includes(getSuffix(nameVal))) {
            getPreviewUrl({fileName: nameVal, uuid: id}).then((res) => {
                if (typeof res === "string") {
                    dispatch(
                        setPreviewUrl({
                            url: res,
                            name: nameVal
                        })
                    );
                } else {
                    const {message: resMessage} = res as {message: string};
                    message.warning(resMessage);
                }
            });
        } else if (isImg(nameVal) || isVideo(nameVal)) {
            getFileUrl([id])
                .then((res: GetFileUrlRes[]) => {
                    if (res.length > 0 && res[0].downloadUrls.length > 0) {
                        dispatch(
                            setPreviewUrl({
                                url: res[0].downloadUrls[0],
                                name: nameVal
                            })
                        );
                    }
                })
                .catch((err: unknown) => {
                    console.log(err);
                });
        } else {
            getFileUrl([id])
                .then((res: GetFileUrlRes[]) => {
                    if (res.length > 0 && res[0].downloadUrls.length > 0) {
                        saveAs(res[0].downloadUrls[0], nameVal);
                    }
                })
                .catch((err: unknown) => {
                    console.log(err);
                });
        }
    };

    const onFileNameChange: InputProps["onChange"] = (e) => {
        setFileName(e.target.value);
    };

    const onFileNameBlur = () => {
        setIsEdit(false);
        onFileChange({
            ...file,
            fileName: `${fileName}.${fileSuffix}`,
        });
    };

    const fileDownload = (id: string, name: string) => {
        if (Boolean(id) === false) {
            message.warning("无权限查看!");
        }
        getFileUrl([id])
            .then((res: GetFileUrlRes[]) => {
                if (res.length > 0 && res[0].downloadUrls.length > 0) {
                    saveAs(res[0].downloadUrls[0], name);
                }
            })
            .catch((err: unknown) => {
                console.log(err);
            });
    };

    const renderIcon = () => {
        if (isImg(file.fileName)) {
            return Icons.pic;
        }
        if (isVideo(file.fileName)) {
            return Icons.video;
        }
        // 文档
        const fileWord = ["doc", "docx"];
        if (fileWord.includes(getSuffix(file.fileName))) {
            return Icons.word;
        }
        if (Icons[getSuffix(file.fileName)] !== undefined) {
            return Icons[getSuffix(file.fileName)];
        }
        return Icons.unknown;
    };

    const renderStatus = () => {
        if (file.failed === true) {
            return <span style={{color: "#D32D28"}}>上传失败!</span>;
        }
        if (Boolean(file.fileUuid) === false && Boolean(file.timeStamp) === true) {
            return (
                <Space>
                    <span>{`${file.percent}%`}</span>
                    <span style={{color: Color.primary}}>上传中</span>
                </Space>
            );
        }
        // return parseFileSize(file.fileSize).all;
        return null;
    };
    return (
        <div style={{position: "relative", height: "48px", background: "#F5F5F6", width: 320, marginBottom: 16}}>
            <div
                style={{position: "absolute", top: 2, bottom: 0, left: 0, width: 45, marginLeft: 15}}
                onDoubleClick={() => fileCheck(file.fileUuid, file.fileName)}
            >
                {isImg(file.fileName) && file.fileUuid !== "" ? <IconPng thumbUuid={file.fileUuid} /> : renderIcon()}
            </div>
            <div
                style={{padding: "0 12px", position: "absolute", top: 14, bottom: 14, left: 60, right: 100}}
                onDoubleClick={() => fileCheck(file.fileUuid, file.fileName)}
            >
                {isEdit
                    ? <Input ref={inputRef} value={fileName} onBlur={onFileNameBlur} onChange={onFileNameChange} />
                    : (
                        <Text style={{width: "100%", maxWidth: "100%"}} ellipsis={{tooltip: file.fileName}}>
                            {file.fileName}
                        </Text>
                    )}
                <div>{renderStatus()}</div>
            </div>
            <div style={{textAlign: "right", position: "absolute", top: 0, bottom: 0, right: 0, width: 100}}>
                <Space>
                    {isEditName && (
                        <MyIcon
                            type="icon-xiugai"
                            style={{fontSize: 26, color: Color["text-3"]}}
                            onClick={handleEdit}
                        />
                    )}
                    {isDelete && <MyIcon style={{fontSize: 26}} type="icon-lajitong" onClick={() => onDelete(file)} />}
                    {isDownload && Boolean(file.fileUuid) === true && (
                        <MyIcon
                            style={{fontSize: 14, marginTop: 17, marginRight: 20}}
                            type="icon-xiazai"
                            onClick={() => fileDownload(file.fileUuid, file.fileName)}
                        />
                    )}
                </Space>
            </div>
        </div>
    );
};

export default memo(ListItem);
