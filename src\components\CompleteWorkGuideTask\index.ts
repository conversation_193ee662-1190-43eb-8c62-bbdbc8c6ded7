import {message} from "antd";
import completeWgTask, {getIncompleteWgTask, IncompleteTaskData} from "./api";

interface WorkGuideInfo {
    businessType: string;
    completeExtraInfo: {
        code: string;
        resId: string;
        sectionId: string;
    };
    id: string;
    source: string;
}

// 添加成功，调用completeWgTask接口更新任务完成状态
const completeWorkGuideTask = async (workGuideInfo: WorkGuideInfo) => {
    try {
        await completeWgTask(workGuideInfo);
    } catch (error) {
        message.error("获取任务数据失败");
        console.error("获取任务数据错误:", error);
    }
};

/**
 * 更新任务完成状态,根据workGuideInfoStr来判断入口，如果workGuideInfoStr有值那么来源是workbench-工作台，否则是business-业务系统本身
 * @param orgId 组织id
 * @param sectionId 标段id
 * @param leafMenuId 子菜单id
 * @param operateType 操作类型
 */
const updateWorkGuideTask = async (orgId: string, sectionId: string, leafMenuId: string, operateType?: string) => {
    const workGuideInfoStr = sessionStorage.getItem("workGuideInfo");
    if (workGuideInfoStr !== null) {
        try {
            const workGuideInfo = JSON.parse(workGuideInfoStr);
            if (
                workGuideInfo !== null && Object.keys(workGuideInfo).length > 0
                && workGuideInfo.completeExtraInfo.sectionId === sectionId
                && workGuideInfo.operateType === operateType
            ) {
                completeWorkGuideTask(workGuideInfo);
            }
        } catch (error) {
            console.error("Failed to parse workGuideInfo:", error);
        }
    } else {
        // 获取未完成的任务列表
        const incompleteTaskList = await getIncompleteWgTask({
            businessType: "WorkGuide",
            sectionId,
            menuId: leafMenuId,
            resId: orgId,
        });
        // 获取incompleteTaskList中的item中operateType跟传入的operateType相同的item
        const operateTypeList = incompleteTaskList.data.filter((item: IncompleteTaskData) => item.operateType === operateType);
        // 如果operateTypeList的长度大于0，则调用completeWorkGuideTask更新任务完成状态
        if (operateTypeList.length > 0) {
            const workGuideInfo = {
                businessType: "WorkGuide",
                completeExtraInfo: {
                    code: operateTypeList[0].code,
                    resId: operateTypeList[0].resId,
                    sectionId: operateTypeList[0].sectionId,
                },
                id: operateTypeList[0].id,
                source: "business",
            };
            completeWorkGuideTask(workGuideInfo);
        }
    }
};

export default updateWorkGuideTask;
