// npx openapi-typescript http://**************:8888/pdscommon/rs/swagger/swagger.json --output swagger.ts

import {definitions} from "./swagger";

export type UserInfoTypeByOrg = definitions["UsersOfOrgItem"];

export type UserInfoType = definitions["UserPortraitVo"];

export type UserInfoTypeOfRoleByOrg = definitions["UsersOfRoleVo"];

export type SearchUsersOfOrg = definitions["UsersOfOrgItem"];

export type SearchUsersOfRole = definitions["UsersOfRoleVo"];

export type UserMailVo = definitions["UserMailVo"];
