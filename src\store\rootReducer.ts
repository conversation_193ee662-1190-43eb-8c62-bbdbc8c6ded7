/* eslint-disable import/no-cycle */
import {combineReducers} from "redux";
import commonReducer from "./common/reducer";
import statusReducer from "./status/reducer";
import ProcessReducer from "./process/reducer";
import sandManageReducer from "./sandManage/reducer";
import sandAnalyzedDataReducer from "./sandAnalyzedData/reducer";
import wbsTreeReducer from "../components/WBS/store/reducer";
import PersonReducer from "./personSelect/reducer";
import noRegisterReducer from "./no-persist/reducer";
import rectificationDetailReducer from "./rectification/detail/reducer";
import rectificationTempReducer from "./rectification/template/reducer";

const rootReducer = combineReducers({
    commonData: commonReducer,
    statusData: statusReducer,
    processData: ProcessReducer,
    sandManageData: sandManageReducer,
    sandAnalyzedData: sandAnalyzedDataReducer,
    wbsTree: wbsTreeReducer,
    personData: PersonReducer,
    noRegister: noRegisterReducer,
    rectificationDetail: rectificationDetailReducer,
    rectificationTemp: rectificationTempReducer
});

export default rootReducer;

export type RootState = ReturnType<typeof rootReducer>;
