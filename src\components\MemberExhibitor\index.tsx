/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
import React, {FC, useCallback, useState, Dispatch, useEffect, useRef} from "react";
import moment, {Moment} from "moment";
import {cloneDeep} from "lodash-es";
import {Timeline, Row, Col, Button, Slider, DatePicker, Menu, Dropdown, Alert} from "antd";
import {useDispatch, useSelector} from "react-redux";
import useStyle from "./useStyle";
import useInterval from "../../hooks/useInterval";
import BtnIcon from "../MyIcon/BtnIcon";
import {setPlayStatus} from "../../store/sandManage/action";
import {RootState} from "../../store/rootReducer";
import {CompStateParam} from "../../api/processManager/type";
import {sandTableFullScreenDomId} from "../../views/sandTableManage/SandTable/helps";

export interface MemberExhibitorProps {
    minDate?: Moment;
    maxDate?: Moment;
    progress: number;
    curDate: Moment | undefined;
    setCurDate: Dispatch<Moment | undefined>;
    clickReset: () => void;
    isRenderContrast?: boolean;
    hasData?: boolean;
}
export interface TimelineInfoType extends PeriodType{
    startDate: Moment;
    endDate: Moment;
    planStartDate: Moment;
    planEndDate: Moment;
    stateName: string;
}
export interface PeriodType extends CompStateParam {
    startDate: Moment;
    endDate: Moment;
    planStartDate: Moment;
    planEndDate: Moment;
    /** 构件handle */
    handle: string;
    /** 构件path */
    path: string;
    /** 构件楼层 */
    floor: string;
}

const MemberExhibitor: FC<MemberExhibitorProps> = (props) => {
    const dispatch = useDispatch();
    const cls = useStyle();
    const {minDate, maxDate, progress = 0, curDate, setCurDate, clickReset, isRenderContrast = false, hasData} = props;
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const {playPattern, playDimension, playStatus, sandTableType} = useSelector((state: RootState) => state.sandManageData);
    const [speed, setSpeed] = useState<number>(1); // 倍速播放
    const [playing, setPlaying] = useState<boolean>(false); // 播放/暂停
    const [isAlert, setIsAlert] = useState<boolean>(false);
    const [timelineInfoList, setTimelineInfoList] = useState<TimelineInfoType[]>([]);
    const sandBoxPeriodList = useRef<PeriodType[]>([]);
    const [fallback, setFallback] = useState(false);

    useEffect(() => {
        if (curSandTable !== null && analyzedData !== undefined && typeof analyzedData.handler !== "undefined" && playing) {
            const compSandBoxList = analyzedData.handler.getCompSandBoxList();
            let infoList: PeriodType[] = [];
            compSandBoxList.forEach((i) => {
                infoList = [
                    ...infoList,
                    ...i.stateItems.map((j) => ({
                        start: moment(j.lifeCycles.startDate).format("YYYY.MM.DD"),
                        ...j,
                        ...i.compInfo,
                        endDate: moment(j.lifeCycles.endDate),
                        startDate: moment(j.lifeCycles.startDate),
                        planEndDate: moment(j.lifeCycles.planEndDate),
                        planStartDate: moment(j.lifeCycles.planStartDate),
                    }))
                ];
            });
            sandBoxPeriodList.current = infoList;
        }
    }, [analyzedData, curSandTable, playPattern, playing]);

    useInterval(() => {
        if (curDate === undefined || maxDate === undefined || minDate === undefined) {
            return;
        }
        const date = cloneDeep(curDate);
        setFallback(false);
        if (date.add(1, "days") <= maxDate) {
            setCurDate(date);
        } else if (maxDate.diff(curDate, "hour") > 23) {
            setCurDate(maxDate);
            setPlaying(false);
            dispatch(setPlayStatus("paused"));
        } else {
            const antTimelineDom: HTMLElement = document.querySelector(".ant-timeline") as unknown as HTMLElement;
            if (antTimelineDom !== null) {
                // eslint-disable-next-line radix
                const top = parseInt(antTimelineDom.style.top) - 350;
                antTimelineDom.style.top = `${top}px`;
                if (!fallback) {
                    antTimelineDom.style.transition = `top ${Math.ceil(1 / speed)}s linear`;
                }
            }
            setPlaying(false);
            dispatch(setPlayStatus("paused"));
        }
    }, playing, fallback ? 0 : Math.ceil(1000 / speed));

    useEffect(() => {
        if (curSandTable !== null && typeof curDate !== "undefined" && analyzedData !== undefined && analyzedData.isReady && typeof analyzedData.handler !== "undefined" && playing) {
            const curTimelineInfoList = sandBoxPeriodList.current.filter((i) => {
                let date: Moment;
                if (playPattern.length === 1) {
                    date = playPattern.includes("plan") ? i.planStartDate : i.startDate;
                } else {
                    date = i.endDate !== null && i.planStartDate.diff(i.startDate) > 0 ? i.planStartDate : i.startDate;
                }
                return date.isSame(curDate, "day");
            });
            const len = 40 * speed;
            let newTimelineInfoList = curTimelineInfoList;
            if (curTimelineInfoList.length > len) {
                newTimelineInfoList = curTimelineInfoList.slice(0, len);
            }
            setTimelineInfoList((oldList) => [...oldList, ...newTimelineInfoList]);
        }
    }, [analyzedData, curDate, curSandTable, playPattern, playing, speed]);
    useEffect(() => {
        const antTimelineDom: HTMLElement = document.querySelector(".ant-timeline") as unknown as HTMLElement;
        if (antTimelineDom !== null) {
            const top = -(timelineInfoList.length * 70) + 350;
            antTimelineDom.style.top = `${top}px`;
            if (!fallback) {
                antTimelineDom.style.transition = `top ${Math.ceil(1 / speed)}s linear`;
            }
        }
    }, [fallback, speed, timelineInfoList.length]);
    const handleReset = useCallback(() => {
        setTimelineInfoList([]);
        setCurDate(minDate);
        setPlaying(false);
        clickReset();
    }, [clickReset, minDate, setCurDate]);

    useEffect(() => {
        if (progress === 0 && playStatus === "idle") {
            handleReset();
        }
    }, [handleReset, playStatus, progress]);

    const handlePlayback = useCallback(() => {
        if (curDate === undefined || hasData === false) {
            setIsAlert(true);
            setTimeout(() => setIsAlert(false), 3000);
            return;
        }
        if (curDate.isSame(maxDate, "day")) {
            handleReset();
        }
        setPlaying((oldPlay) => {
            dispatch(setPlayStatus(oldPlay ? "paused" : "playing"));
            return !oldPlay;
        });
    }, [curDate, dispatch, handleReset, hasData, maxDate]);

    const handleSpeedChange = useCallback((value) => {
        if (value.key !== undefined) {
            setSpeed(value.key);
        }
    }, []);

    const progressChange = useCallback((value: number) => {
        if (maxDate !== undefined && minDate !== undefined) {
            if (maxDate.isSame(minDate, "day")) {
                setCurDate(value > 50 ? maxDate : minDate);
                return;
            }
            const step = maxDate.diff(minDate, "seconds", true) * value * 0.01;
            const date = cloneDeep(minDate);
            const jumpDate = date.add(step, "seconds");
            if (playStatus === "idle") {
                dispatch(setPlayStatus("paused"));
            }
            if (jumpDate.isSame(curDate, "day") && !jumpDate.isSame(minDate, "day") && !jumpDate.isSame(maxDate, "day")) {
                return;
            }
            if (jumpDate.diff(curDate, "days") < 0) {
                setFallback(true);
            }
            setCurDate(jumpDate);
            setTimelineInfoList((oldList) => oldList.filter((i) => {
                let newDate: Moment;
                if (playPattern.length === 1) {
                    newDate = playPattern.includes("plan") ? i.planStartDate : i.endDate;
                } else {
                    newDate = i.endDate !== null && i.planStartDate.diff(i.endDate) > 0 ? i.planStartDate : i.endDate;
                }
                return jumpDate.diff(newDate, "day") > 0;
            }));
        }
    }, [curDate, dispatch, maxDate, minDate, playPattern, playStatus, setCurDate]);
    const getPopupContainer = useCallback(
        () => {
            const target = document.getElementById(sandTableFullScreenDomId);
            return target ?? document.documentElement;
        },
        []
    );

    const onDateChange = useCallback((value) => {
        if (value !== null) {
            setCurDate(value);
            if (playStatus === "idle") {
                dispatch(setPlayStatus("paused"));
            }
            if (value.diff(curDate, "days") < 0) {
                setFallback(true);
            }
            if (maxDate !== undefined && minDate !== undefined && maxDate.isSame(minDate, "day")) {
                setCurDate(value.diff(curDate, "days") > 0 ? maxDate : minDate);
                return;
            }

            setTimelineInfoList((oldList) => oldList.filter((i) => {
                let newDate: Moment;
                if (playPattern.length === 1) {
                    newDate = playPattern.includes("plan") ? i.planStartDate : i.endDate;
                } else {
                    newDate = i.endDate !== null && i.planStartDate.diff(i.endDate) > 0 ? i.planStartDate : i.endDate;
                }
                return value.diff(newDate, "day") > 0;
            }));
        }
    }, [curDate, dispatch, maxDate, minDate, playPattern, playStatus, setCurDate]);

    return (
        <div className={cls.memberExhibitorWrapper} style={{width: isRenderContrast ? "200%" : "100%"}}>
            <div className={cls.timeLineBox}>
                {timelineInfoList.length > 0 && playDimension === "procedure" && (
                    <Timeline>
                        {timelineInfoList.map((item, ind, arr) => (
                            <Timeline.Item
                                className={cls.timeLineItem}
                                key={`${item.stateKey}-${item.stateName}-${ind}`}
                                style={{
                                    opacity: ind === arr.length - 3 ? 1 : ind === arr.length - 2 || ind === arr.length - 4 ? 0.7 : 0.3,
                                    transition: "opacity 1s linear"
                                }}
                            >
                                <div className={cls.timeLineTitle}>{item.stateName}</div>
                                <div>
                                    计划起止日期：
                                    {item.planStartDate.format("YYYY.MM.DD")}
                                    -
                                    {item.planEndDate.format("YYYY.MM.DD")}
                                </div>
                                <div>
                                    实际起止日期：
                                    {item.startDate.isValid() ? item.startDate.format("YYYY.MM.DD") : ""}
                                    -
                                    {item.endDate.isValid() ? item.endDate.format("YYYY.MM.DD") : "至今"}
                                </div>
                            </Timeline.Item>
                        ))}
                    </Timeline>
                )}
            </div>

            <Row>
                <Col span={1}>
                    <Button
                        className={cls.iconButton}
                        type="text"
                        icon={<BtnIcon type="icon-zhongzhi1" />}
                        onClick={() => handleReset()}
                    />
                </Col>
                <Col span={1}>
                    <Button
                        className={cls.iconButton}
                        type="text"
                        icon={<BtnIcon type={playing ? "icon-zanting" : "icon-kaishi"} />}
                        onClick={() => handlePlayback()}
                    />
                </Col>
                <Col span={1}>
                    <Dropdown
                        trigger={["click"]}
                        overlayStyle={{zIndex: 1500}}
                        overlayClassName={cls.antMenu}
                        getPopupContainer={getPopupContainer}
                        overlay={(
                            <Menu onClick={handleSpeedChange} style={{width: 100}}>
                                {[0.5, 1, 1.5, 2].map((item) => (
                                    <Menu.Item key={item}>
                                        x
                                        {item}
                                    </Menu.Item>
                                ))}
                            </Menu>
                        )}
                    >
                        <Button className={cls.iconButton} color="primary">
                            {`x${speed}`}
                        </Button>
                    </Dropdown>
                </Col>
                <Col span={18} style={{padding: "0 20px"}}>
                    <Slider
                        value={progress}
                        onChange={progressChange}
                        range={false}
                        tipFormatter={() => curDate?.format("YYYY.MM.DD")}
                    />
                    <div className={cls.dateWrapper}>
                        <span>{minDate?.format("YYYY.MM.DD")}</span>
                        <span>{maxDate?.format("YYYY.MM.DD")}</span>
                    </div>
                </Col>
                <Col span={3}>
                    <DatePicker
                        allowClear={false}
                        showToday={false}
                        format="YYYY.MM.DD"
                        disabledDate={(current) => {
                            if (current !== undefined) {
                                return current < moment(minDate) || current > moment(maxDate);
                            }
                            return true;
                        }}
                        value={curDate ?? undefined}
                        onChange={onDateChange}
                    />
                </Col>
            </Row>
            {isAlert && (
                <Alert
                    message="播放失败！"
                    description={sandTableType === "custom" ? "无沙盘数据，请在自定义沙盘模块添加沙盘数据" : "无施工沙盘数据"}
                    type="error"
                    showIcon
                    banner
                    closable
                    icon={<BtnIcon type="icon-cuowu-mianxing" />}
                    afterClose={() => setIsAlert(false)}
                />
            )}
        </div>
    );
};

export default MemberExhibitor;
