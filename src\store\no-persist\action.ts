import {ReactNode} from "react";
import {createSFAPayloadAction} from "../utils";
import ActionTypes from "./actionTypes";
import {StateType} from "./reducer";
import {getState} from "..";

export const saveChild = createSFAPayloadAction(ActionTypes.SAVE_CHILD_API, (payload: StateType["child"]) => payload);

const updateIframeEditStatus = (val: boolean) => {
    const {child} = getState().noRegister;
    return child?.emit("changeIframeStatus", val);
};
export const setIframeEditStatus = createSFAPayloadAction(ActionTypes.SET_IFRAME_EDIT_STATUS, (payload: StateType["iframeEditStatus"]) => {
    updateIframeEditStatus(payload);
    return payload;
});

export const setWorkBenchDetails = createSFAPayloadAction(ActionTypes.SET_WORK_BENCH_DETAILS, (payload: StateType["workBenchDetails"]) => payload);
export const setPersonData = createSFAPayloadAction(ActionTypes.SET_PERSON_DATA, (payload: StateType["personData"]) => payload);
export const setPersonTree = createSFAPayloadAction(ActionTypes.SET_PERSON_TREE, (payload: StateType["personTree"]) => payload);
export const setPersonTreeFlag = createSFAPayloadAction(ActionTypes.SET_PERSON_TREE_FLAG, (payload: StateType["personTreeFlag"]) => payload);
export const setMenuList = createSFAPayloadAction(ActionTypes.SET_MENU_LIST, (payload: StateType["menuList"]) => payload);
export const setShowModelSelect = createSFAPayloadAction(ActionTypes.SET_SHOW_MODEL_SELECT, (payload: StateType["showModelSelect"]) => payload);
export const setShowModelSpin = createSFAPayloadAction(ActionTypes.SET_SHOW_MODEL_SPIN, (payload: StateType["showModelSpin"]) => payload);
export const setBimProjInfo = createSFAPayloadAction(ActionTypes.SET_BIM_PROJ_INFO, (payload: StateType["bimProjInfo"]) => payload);
export const setBindInfo = createSFAPayloadAction(ActionTypes.SET_BIND_INFO, (payload: StateType["bindInfo"]) => payload);
export const setEbsProjectList = createSFAPayloadAction(ActionTypes.SET_EBS_PROJECT_LIST, (payload: StateType["ebsProjectList"]) => payload);
export const setAnchorSkip = createSFAPayloadAction(ActionTypes.SET_ANCHORSKIP, (payload: StateType["anchor"]) => payload);

export const setPageArr = createSFAPayloadAction(ActionTypes.SET_PAGE_ARR, (payload: StateType["pageArr"]) => payload);
export const popPage = createSFAPayloadAction(ActionTypes.POP_PAGE, () => null);
export const addPage = createSFAPayloadAction(ActionTypes.ADD_PAGE, (payload: ReactNode) => payload);

export const changeLeftBoxVisible = createSFAPayloadAction(ActionTypes.CHANGE_LEFT_BOX_VISIBLE, (payload: StateType["leftBoxVisibleObj"]) => payload);

export const resetLeftBoxVisible = createSFAPayloadAction(ActionTypes.RESET_LEFT_BOX_VISIBLE, () => null);
