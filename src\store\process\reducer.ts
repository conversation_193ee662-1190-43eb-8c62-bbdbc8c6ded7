import {SET_PROCESS_TMPL_TREE, ProcessActions, ProcessInfo, SET_PROCESS_TMPL_ID, SET_SELECTED_COMPS, SET_VISIBLE_COMPS, SET_PROCESS_SELECT_TYPE} from "./types";

const InitStatusData: ProcessInfo = {
    tmplTree: [],
    visibleCompPaths: new Set(),
    selectedComps: [],
    selectType: "single"
};

const ProcessReducer = (state = InitStatusData, action: ProcessActions): ProcessInfo => {
    switch (action.type) {
        case SET_PROCESS_TMPL_TREE:
            return {
                ...state,
                tmplTree: action.payload
            };
        case SET_PROCESS_TMPL_ID:
            return {
                ...state,
                curTmplId: action.payload
            };
        case SET_SELECTED_COMPS:
            return {
                ...state,
                selectedComps: action.payload
            };
        case SET_VISIBLE_COMPS:
            return {
                ...state,
                visibleCompPaths: action.payload
            };
        case SET_PROCESS_SELECT_TYPE:
            return {
                ...state,
                selectType: action.payload
            };
        default:
            return state;
    }
};

export default ProcessReducer;
