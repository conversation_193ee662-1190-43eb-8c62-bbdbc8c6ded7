import React from "react";
import useStyle from "./css";

export interface TreeData<T extends TreeData<T>> {
    key: string;
    children: T[];
}

export interface TreeNodeProps<T extends TreeData<T>> {
    style?: React.CSSProperties;
    title: React.ReactNode;
    expandable: boolean;
    nodeClassName?: string | ((data: T) => string);
    onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
    onExpand?: () => void;
    onCheck?: (e: MouseEvent, checked: boolean) => void;
    checkable?: boolean | ((data: T) => boolean);
    data: T;
    expander?: React.ReactNode | ((data: T, isExpanded: boolean) => React.ReactNode);
    expanded: boolean;
    checked: boolean;
}

const TreeNode = <T extends TreeData<T>>(props: TreeNodeProps<T>) => {
    const {checkable, data, nodeClassName, style, onExpand, expanded, expander, checked, title} = props;
    const newCheckable = checkable instanceof Function ? checkable(data) : checkable ?? false;
    const newCls = typeof nodeClassName === "function" ? nodeClassName(data) : nodeClassName;
    const {
        virtualizedTreeNode,
        virtualizedTreeSwitcher,
        virtualizedTreeTitle,
        virtualizedTreeSwitcherOpen,
        virtualizedTreeSwitcherClose
    } = useStyle();

    const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, checkableIn: boolean) => {
        const {onCheck, onClick} = props;
        if (onCheck !== undefined && checkableIn) {
            onCheck(e.nativeEvent, !checked);
        }
        if (onClick !== undefined) {
            onClick(e);
        }
    };

    return (
        <div
            style={style}
            className={`${virtualizedTreeNode} ${newCls}`}
            onClick={(e) => handleClick(e, newCheckable)}
        >
            {
                data.children.length !== 0 && (
                    <span
                        onClick={onExpand}
                        className={`${virtualizedTreeSwitcher} ${expanded ? virtualizedTreeSwitcherOpen : virtualizedTreeSwitcherClose}`}
                    >
                        {
                            typeof expander === "function" ? expander(data, expanded) : expander
                        }
                    </span>
                )
            }
            {
                newCheckable && (
                    <span
                        className={`ant-tree-checkbox ${checked ? "ant-tree-checkbox-checked" : ""}`}
                    >
                        <span
                            className="ant-tree-checkbox-inner"
                        />
                    </span>
                )
            }
            <span
                className={`${virtualizedTreeTitle} ${checked ? "virtualized-tree-title-selected" : ""}`}
            >
                {title}
            </span>
        </div>
    );
};

export default TreeNode;

/* export default class TreeNode<T extends TreeData<T>> extends React.PureComponent<TreeNodeProps<T>> {
    render() {
        const {checkable, data, nodeClassName, style, onExpand, expanded, expander, checked, title} = this.props;
        const newCheckable = checkable instanceof Function ? checkable(data) : checkable;
        const newCls = typeof nodeClassName === "function" ? nodeClassName(data) : nodeClassName;

        return (
            <div
                style={style}
                className={`${useStyles.virtualizedTreeNode} ${newCls}`}
                onClick={(e) => this.handleClick(e, newCheckable!)}
            >
                {
                    data.children.length !== 0 ? (
                        <span
                            onClick={onExpand}
                            className={`${useStyles.virtualizedTreeSwitcher} virtualized-tree-switcher-${expanded ? "open" : "close"}`}
                        >
                            {
                                typeof expander === "function" ? expander(data, expanded) : expander
                            }
                        </span>
                    ) : null
                }
                {
                    newCheckable !== undefined && newCheckable ? (
                        <span
                            className={`ant-tree-checkbox ${checked ? "ant-tree-checkbox-checked" : ""}`}
                        >
                            <span
                                className="ant-tree-checkbox-inner"
                            />
                        </span>
                    ) : null
                }
                <span
                    className={`${useStyles.virtualizedTreeTitle} ${checked ? "virtualized-tree-title-selected" : ""}`}
                >
                    {title}
                </span>
            </div>
        );
    }

    private handleClick(e: React.MouseEvent<HTMLDivElement, MouseEvent>, checkable: boolean) {
        const {onCheck, onClick, checked} = this.props;
        if (onCheck !== undefined && checkable) {
            onCheck(e.nativeEvent, !checked);
        }
        if (onClick !== undefined) {
            onClick(e);
        }
    }
} */
