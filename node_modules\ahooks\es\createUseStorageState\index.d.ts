export interface IFuncUpdater<T> {
    (previousState?: T): T;
}
export interface IFuncStorage {
    (): Storage;
}
export interface Options<T> {
    serializer?: (value: T) => string;
    deserializer?: (value: string) => T;
    defaultValue?: T | IFuncUpdater<T>;
}
export declare function createUseStorageState(getStorage: () => Storage | undefined): <T>(key: string, options?: Options<T> | undefined) => readonly [T, (value: T | IFuncUpdater<T>) => void];
