class MarkerTipMgrClass {
    private tipMap: Map<string, string> = new Map();

    public getTipInfo(id: string): string {
        const find = this.tipMap.get(id);
        return find ?? "";
    }

    public addTipInfo(id: string, value: string): void {
        this.tipMap.set(id, value);
    }

    public clear() {
        this.tipMap.clear();
    }
}

const MarkerTipMgr = new MarkerTipMgrClass();
export default MarkerTipMgr;
