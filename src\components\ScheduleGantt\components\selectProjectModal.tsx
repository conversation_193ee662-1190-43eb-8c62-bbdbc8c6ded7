import React, {use<PERSON><PERSON>back, Key} from "react";
import Modal from "antd/lib/modal/Modal";
import {Tree} from "antd";
import {ProjNameVo} from "../api/planModel";
import {getProjectTypeIcon} from "../tool/project";

const {TreeNode} = Tree;

export interface SelectProjectProps {
    planId: number;
    visible: boolean;

    projList: ProjNameVo[];
    onCancel?: () => void;
    onOk?: (info: ProjNameVo) => void;
}

const SelectProject = (props: SelectProjectProps) => {
    const {visible, onOk, onCancel, projList} = props;

    const TreeNodes = (nodeList: ProjNameVo[] | undefined) => {
        const nodesList: any[] = [];
        if (nodeList !== undefined) {
            nodeList.forEach((el) => {
                nodesList.push(
                    <TreeNode
                        key={el.ppid}
                        title={(
                            <div>
                                <img src={getProjectTypeIcon(el.projType)} alt="" width={15} height={15} style={{marginRight: 8}} />
                                {`${el.projName}.${el.projModel}`}
                            </div>
                        )}
                    />
                );
            });
            return nodesList;
        }
        return undefined;
    };

    const onSelect = useCallback((selectedKeys: Key[]) => {
        const keys = selectedKeys as string[];
        if (keys.length === 0) {
            return;
        }
        const ppid = parseInt(keys[0], 10);
        const info = projList?.find((val) => val.ppid === ppid);
        if (info !== undefined && onOk !== undefined) {
            onOk(info);
        }
    }, [onOk, projList]);
    return (
        <Modal
            width={600}
            title={<span style={{fontWeight: "bold"}}>选择模型</span>}
            visible={visible}
            onCancel={onCancel}
            destroyOnClose
            footer={null}
            maskClosable={false}
            keyboard={false}
        >
            <Tree
                blockNode
                height={300}
                defaultExpandAll
                onSelect={onSelect}
            >
                {TreeNodes(projList)}
            </Tree>
        </Modal>
    );
};


export default SelectProject;
