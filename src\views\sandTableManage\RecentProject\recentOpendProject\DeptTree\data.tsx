import {DataNode} from "antd/lib/tree";
import {OrgNodeInfo} from "../../../../../api/recentProject/orgTree.type";
import {BIMSandTableInfo} from "../../../../../store/status/types";

export interface ProjectTreeNodeData extends DataNode {
    bimInfo: BIMSandTableInfo | null;
    orgInfo: OrgNodeInfo;
    children?: ProjectTreeNodeData[];
}

export enum TreeNodeType {
    Undefined,
    Root,
    Company,
    Dept,
    Tender,
    Mono,
    Unit,
    Model,
    Project,
    TjModel,
    GjModel,
    AzModel,
    RevitModel,
    TeklaModel,
    Civil3dModel,
    BentleyModel,
    RhinoModel,
    IfcModel,
    RemizModel,
    SiteModel,
    PdfModel,
    CatiaModel,
    Pkpm
}

export const majorOptions = [
    {
        label: "全部专业",
        value: 0
    },
    {
        label: "土建预算",
        value: 1
    },
    {
        label: "安装预算",
        value: 2
    },
    {
        label: "钢筋预算",
        value: 3
    },
    {
        label: "Revit",
        value: 4
    },
    {
        label: "Tekla",
        value: 5
    },
    {
        label: "Civil3D",
        value: 10
    },
    {
        label: "<PERSON>",
        value: 11
    },
    {
        label: "Rhino",
        value: 12
    },
    {
        label: "IFC",
        value: 13
    },
    {
        label: "班筑家装",
        value: 8
    },
    {
        label: "场布预算",
        value: 9
    },
    // {
    //     label: "CATIA",
    //     value: TreeNodeType.CatiaModel
    // }
];

export const relateOptions = [
    {
        label: "全部状态",
        value: -1
    },
    {
        label: "未关联",
        value: 0
    },
    {
        label: "已关联",
        value: 1,
    }
];

// ebs节点信息
export interface EBSNodeType {
    handle?: string;
    paths?: string[];
    ppid: number;
    projName?: string;
}
