import {Space} from "antd";
import React, {CSSProperties, ReactNode, useEffect, useState} from "react";
import {createUseStyles} from "react-jss";
import Color from "../../assets/css/Color";

const useStyle = createUseStyles({
    box: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "20px 0",
    },
    iconBox: {
        display: "flex",
        width: 24,
        height: 24,
        alignContent: "center",
        justifyContent: "center",
        borderRadius: "50%",
    },
    selectedIcon: {
        backgroundColor: Color.primary,
        color: "#fff",
    },
    noSelectedIcon: {
        border: `1px solid ${Color["text-2"]}`,
        color: Color["text-2"],
    },
    line: {
        height: 1,
        backgroundColor: Color["dark-line-2"],
        flexGrow: 1,
        margin: "0 20px"
    }
});

interface AnchorListType {
    id: string;
    name: string;
    index?: number;
    render?: (val: string) => ReactNode;
}

interface AnchorSkipProps {
    list: AnchorListType[];
    style?: CSSProperties;
}

type RealListType = AnchorListType | string;

// 页面内锚点跳转
const AnchorSkip = (props: AnchorSkipProps) => {
    const {list, style} = props;
    const cls = useStyle();
    const selectNameInit = () => {
        if (Array.isArray(list) && list.length > 0) {
            return list[0].name;
        }
        return "";
    };
    const [selectName, setSelectName] = useState<string>(selectNameInit);
    const [realList, setRealList] = useState<RealListType[]>([]);

    useEffect(() => {
        const tempRealList: RealListType[] = [];
        list.forEach((el, index) => {
            tempRealList.push({
                ...el,
                index: index + 1
            });
            if ((index + 1) !== list.length) {
                tempRealList.push(`line-${index}`);
            }
        });
        setRealList(tempRealList);
    }, [list]);

    const handleClick = (val: AnchorListType) => {
        setSelectName(val.name);
        const node = document.getElementById(val.id);
        if (node === null) {
            return;
        }
        node.scrollIntoView();
    };

    return (
        <div className={cls.box} style={style}>
            {
                realList.map((el) => {
                    if (typeof el === "string") {
                        return (
                            <div key={el} className={cls.line} />
                        );
                    }
                    if (el.render === undefined) {
                        return (
                            <Space key={el.name} onClick={() => handleClick(el)} style={{cursor: "pointer", width: 56}}>
                                {/* <div className={`${selectName === el.name ? cls.selectedIcon : cls.noSelectedIcon} ${cls.iconBox}`}>
                                    {el.index}
                                </div> */}
                                <div style={{color: Color["text-2"], marginTop: 13, fontWeight: selectName === el.name ? 700 : 400}}>
                                    {el.name}
                                    <div style={{width: "100%", height: 2, background: selectName === el.name ? Color["text-2"] : "transparent", marginTop: 13}} />
                                </div>
                            </Space>
                        );
                    }
                    return (
                        <div key={el.name} onClick={() => handleClick(el)}>
                            {el.render(el.name)}
                        </div>
                    );
                })
            }
        </div>
    );
};

export default AnchorSkip;
