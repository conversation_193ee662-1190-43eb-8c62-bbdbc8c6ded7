import React, {ReactElement, ReactNode, useContext} from "react";
import {Row} from "antd";
import {GanttButtonType} from "../GanttButton";
import MyIconFont from "../../../MyIconFont";
import ButtonList from "../GanttButton/ButtonList";
import EditorContext from "../../views/GanttEditor/context";
import {ApprovalStatus} from "../../../../api/Preparation/type";
import ComTag from "../../../ComTag";

interface GanttToolBarProps {
    showBack: boolean;
    onBack: () => void;
    title: ReactNode;
    rightButtons?: (ReactElement | GanttButtonType)[];
}

const GanttHeader = (props: GanttToolBarProps) => {
    const {showBack, onBack, title, rightButtons} = props;
    const {fromType, planInfo} = useContext(EditorContext);

    return (
        <div style={{display: "flex", justifyContent: "space-between", marginBottom: 24}}>
            <Row align="middle">
                {
                    fromType !== "approval" && (
                        <>
                            {showBack && <MyIconFont type="icon-fanhui" style={{marginRight: 16, fontSize: 24}} onClick={onBack} />}
                            <span
                                style={{
                                    marginRight: 16,
                                    fontWeight: 400,
                                    fontSize: 26,
                                    color: "#061127",
                                }}
                            >
                                {title}
                            </span>
                            {planInfo.approvalStatus === ApprovalStatus.RETRACT && <ComTag color="#717784">撤回</ComTag>}
                            {planInfo.approvalStatus === ApprovalStatus.RETURN && <ComTag color="#D40000">退回</ComTag>}
                            {planInfo.changeStatus === "Changed" && planInfo.approvalStatus !== ApprovalStatus.APPROVAL_COMPLETED && <ComTag color="#FAAB0C">变更</ComTag>}
                        </>
                    )
                }
            </Row>
            <Row align="middle">
                <ButtonList buttons={rightButtons} />
            </Row>
        </div>
    );
};

export default GanttHeader;
