import {Color as MotorColor, Element as MotorElement} from "@motor/core";
import {But<PERSON>, message, Space, Tooltip} from "antd";
import Icon from "@ant-design/icons";
import React, {useCallback, useEffect} from "react";
import {useSelector, useDispatch} from "react-redux";
import {isDefined} from "../../../../assets/ts/utils";
import {PickInfo, useMouseEventListener, MotorContext} from "../../../../reMotor";
import {pathSeparator} from "../../../../reMotor/utils";
import {RootState} from "../../../../store/rootReducer";
import useStyle from "./style";
import {AllChoiceIcon, MultipleChoiceIcon, ResetCameraIcon, SingleChoiceIcon} from "../../../../assets/icons";
import {setProcessSelectType, setSelectedComps} from "../../../../store/process/action";

// type BimSelectType = "single" | "multiple" | "all";
interface BIMToolBarProps {
    onVisibleChange?: (visible: boolean) => void;
    onOpenProcessManage?: () => void;
}

const BIMToolBar = (props: BIMToolBarProps) => {
    const cls = useStyle();
    const dispatch = useDispatch();
    const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);
    const {selectType, selectedComps, visibleCompPaths} = useSelector((state: RootState) => state.processData);
    const {onVisibleChange, onOpenProcessManage} = props;

    // 初次进入页面，设置选择模式为单选
    useEffect(
        () => {
            dispatch(setProcessSelectType("single"));
            dispatch(setSelectedComps([]));
        },
        [dispatch]
    );

    // 组件卸载前清空已选中构件
    useEffect(
        () => () => {
            dispatch(setSelectedComps([]));
            const curProj = MotorContext.getCurBIMProject();
            if (curProj !== null) {
                curProj.deselect();
                curProj.resetColor();
            }
        },
        [dispatch]
    );

    // 定义工序时，构件的显隐状态保持不变（即构件的显隐由左侧的ebs决定），将未定义工序的构件设置为灰色，已定义工序的构件设置为指定颜色
    const getBindProcessCompData = useCallback(
        // eslint-disable-next-line @typescript-eslint/require-await
        async () => {
            const curProj = MotorContext.getCurBIMProject();
            if (!isDefined(analyzedData.handler) || curProj === null) {
                return;
            }
            curProj.deselect();
            curProj.setColor(MotorColor.fromCssColorString("#727272"));
            const hasProcessCompIds = analyzedData.handler.queryAllBindHandleList();
            curProj.setColor(MotorColor.fromCssColorString("#B22222"), hasProcessCompIds);
            // const compBimIds: string[] = [];
            // const compKeysMap: Map<string, StateInfoOfTimePeriod> = new Map();
            // res.forEach((v) => {
            //     const bimId = v.compKey.split(compSeparator)[0] ?? "";
            //     compKeysMap.set(v.compKey, v.timePeriodInfo);
            //     compBimIds.push(bimId);
            // });
            // const tempComps = await curProj.queryElementByBimIds(compBimIds);
            // curProj.setColor(MotorColor.fromCssColorString("rgba(114,114,114,0.2)"), tempComps);
            // tempComps.forEach((el) => {
            //     const curCompKey = `${el.bimId}${compSeparator}${el.dir?.join(pathSeparator)}`;
            //     const curState = compKeysMap.get(curCompKey);
            //     if (curState !== undefined) {
            //         // const {isInterval, stateColor} = curState;
            //         // const color = isInterval ? `rgba(${stateColor},0.3)` : `rgb(${stateColor})`;
            //         curProj.setColor(MotorColor.fromCssColorString("rgba(114,114,114,0.2)"), [el]);
            //     }
            // });
        },
        [analyzedData.handler]
    );

    // 获取全部构件的工序绑定信息
    useEffect(
        () => {
            getBindProcessCompData();
        },
        [getBindProcessCompData]
    );

    const handleModelLeftClick = useCallback(
        (info: PickInfo) => {
            const curProj = MotorContext.getCurBIMProject();
            const pickInfo = info.pickObj;
            if (curProj === null || selectType === undefined || pickInfo === undefined) {
                return;
            }
            if (selectType === "single") {
                curProj.deselect();
                curProj.select(pickInfo.id ?? "");
                dispatch(setSelectedComps([pickInfo]));
            }
            if (selectType === "multiple" || selectType === "all") {
                let isSelected = false;
                for (let i = 0; i < selectedComps.length; i++) {
                    const element = selectedComps[i];
                    if (element.id === pickInfo.id) {
                        isSelected = true;
                        break;
                    }
                }
                if (isSelected) {
                    curProj.deselect(pickInfo.id ?? "");
                    dispatch(setSelectedComps(selectedComps.filter((v) => v.id !== pickInfo.id)));
                } else {
                    curProj.select(pickInfo.id ?? "");
                    dispatch(setSelectedComps([...selectedComps, pickInfo]));
                }
            }
        },
        [dispatch, selectType, selectedComps]
    );

    useMouseEventListener(handleModelLeftClick, "leftClick");

    const handleSingleSelect = useCallback(
        () => {
            if (selectType === "single") {
                return;
            }
            dispatch(setProcessSelectType("single"));
            dispatch(setSelectedComps([]));
            const curProj = MotorContext.getCurBIMProject();
            if (curProj === null) {
                return;
            }
            curProj.deselect();
        },
        [dispatch, selectType]
    );

    const handleMultipleSelect = useCallback(
        () => {
            dispatch(setProcessSelectType("multiple"));
        },
        [dispatch]
    );

    // 点击全选时，只选中右边ebs树选中的构件
    const handleAllSelect = useCallback(
        async () => {
            const curProj = MotorContext.getCurBIMProject();
            if (curProj === null) {
                return;
            }
            if (selectType === "all") {
                curProj.deselect(selectedComps.map((v) => v.id ?? ""));
                dispatch(setProcessSelectType(undefined));
                dispatch(setSelectedComps([]));
                return;
            }
            dispatch(setProcessSelectType("all"));
            const allCompIds = await curProj.queryElement();
            const allComp = await curProj.queryElement(allCompIds);
            const checkedComps: MotorElement[] = [];
            for (let i = 0; i < allComp.length; i++) {
                const comp = allComp[i];
                const compPath = (comp.dir ?? []).join(pathSeparator);
                const isCompChecked = visibleCompPaths.has(compPath);
                if (isCompChecked) {
                    checkedComps.push(comp);
                }
            }
            /** 只高亮ebs树中已选中的构件 */
            curProj.select(checkedComps);
            dispatch(setSelectedComps(checkedComps));
        },
        [dispatch, selectType, selectedComps, visibleCompPaths]
    );

    const handleClose = useCallback(
        () => {
            const curProj = MotorContext.getCurBIMProject();
            if (curProj !== null) {
                curProj.deselect();
            }
            dispatch(setSelectedComps([]));
            if (onVisibleChange !== undefined) {
                onVisibleChange(false);
            }
        },
        [dispatch, onVisibleChange]
    );

    const handleOpenProcessManage = useCallback(
        () => {
            if (selectedComps.length === 0) {
                message.warning("请选中至少一个构件再进行操作！");
                return;
            }
            if (onOpenProcessManage !== undefined) {
                onOpenProcessManage();
            }
        },
        [onOpenProcessManage, selectedComps]
    );

    const handleResetBIMViewport = useCallback(
        () => {
            MotorContext.resetViewport();
        },
        []
    );

    return (
        <>
            <Space size={8} className={cls.btnTopRight}>
                <Button onClick={handleClose} style={{width: 112, background: "#33394D", color: "#fff", fontSize: 14}}>
                    取消
                </Button>
                <Button type="primary" onClick={handleOpenProcessManage} style={{width: 112, fontSize: 14}}>
                    完成选择
                </Button>
            </Space>
            <div className={cls.toolBarBox}>
                <Tooltip placement="bottom" title="相机复位">
                    <Button
                        className={cls.toolBarBtn}
                        icon={<Icon component={ResetCameraIcon} />}
                        onClick={handleResetBIMViewport}
                    />
                </Tooltip>
                <Tooltip placement="bottom" title="单选">
                    <Button
                        className={cls.toolBarBtn}
                        icon={<Icon component={SingleChoiceIcon} />}
                        onClick={handleSingleSelect}
                        style={{color: selectType === "single" ? "#1f54c5" : undefined}}
                    />
                </Tooltip>
                <Tooltip placement="bottom" title="多选">
                    <Button
                        className={cls.toolBarBtn}
                        icon={<Icon component={MultipleChoiceIcon} />}
                        onClick={handleMultipleSelect}
                        style={{color: selectType === "multiple" ? "#1f54c5" : undefined}}
                    />
                </Tooltip>
                <Tooltip placement="bottom" title="全选">
                    <Button
                        className={cls.toolBarBtn}
                        icon={<Icon component={AllChoiceIcon} />}
                        onClick={handleAllSelect}
                        style={{color: selectType === "all" ? "#1f54c5" : undefined}}
                    />
                </Tooltip>
            </div>
        </>
    );
};

export default BIMToolBar;
