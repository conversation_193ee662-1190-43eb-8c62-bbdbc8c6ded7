import {Popover} from "antd";
import React, {CSSProperties, useEffect, useRef, useState} from "react";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {useLocation} from "react-router-dom";
import {RootState} from "../../store/rootReducer";
import MyIcon from "../MyIcon";
import ColumnsControl, {ComColumnsProps as ComColumnsPropsType, dealTitle} from "./ColumnsControl";
import {getKV, setKV} from "../../api/common.api";

const useStyle = createUseStyles({
    popoverOverLay: {
        "& .ant-popover-inner-content": {
            padding: 0
        }
    }
});

export type ComColumnsProps<T> = ComColumnsPropsType<T>;
export interface TableColumnsControlProps<T> {
    columnsList: ComColumnsProps<T>[];
    setColumnsList: (val: ComColumnsProps<T>[]) => void;
    style?: CSSProperties;
    tableKey?: string;
}

const TableColumnsControl = <T extends {}>(props: TableColumnsControlProps<T>) => {
    const cls = useStyle();
    const {pathname} = useLocation();
    const {columnsList, setColumnsList, style, tableKey = ""} = props;
    const {userInfo} = useSelector((state: RootState) => state.commonData);
    const [tableColumnKey, setTableColumnKey] = useState(pathname ?? "");
    const [defaultColumnsShow, setDefaultColumnsShow] = useState<string[]>([]);
    const columnsListRef = useRef(columnsList);
    columnsListRef.current = columnsList;

    useEffect(() => {
        if (columnsList.length !== 0 && defaultColumnsShow.length === 0) {
            setDefaultColumnsShow(columnsList.filter((el) => el.mustShow === false && el.show).map((el) => el.title as unknown as string));
        }
    }, [columnsList, defaultColumnsShow.length]);

    const getCustomList = React.useCallback(async () => {
        try {
            setTableColumnKey(`${pathname}${tableKey}`);
            const {data} = await getKV(`${userInfo.username}_tableControl${pathname.replaceAll("/", "_")}${tableKey}`);
            if (data === null) {
                return;
            }
            const customList = JSON.parse(data) ?? [];
            if (customList.length === 0) {
                return;
            }

            const list = columnsListRef.current;
            const columnsListData = list.map((el: ComColumnsProps<T>) => ({
                ...el,
                show: customList?.includes(dealTitle(el.title, el.titleText))
            }));
            setColumnsList(columnsListData);
        } catch (error) {
            // console.log(error);
        }
    }, [pathname, setColumnsList, tableKey, userInfo.username]);

    useEffect(() => {
        getCustomList();
    }, [getCustomList]);

    const onVisibleChange = React.useCallback((visible: boolean) => {
        if (!visible) {
            const list = columnsList.filter((item) => !item.mustShow && item.show).map((el) => dealTitle(el.title, el.titleText));
            setKV({
                k: `${userInfo.username}_tableControl${pathname.replaceAll("/", "_")}${tableKey}`,
                v: JSON.stringify(list)
            });
        }
    }, [columnsList, pathname, tableKey, userInfo.username]);

    return (
        <div style={style}>
            <Popover
                overlayClassName={cls.popoverOverLay}
                trigger="click"
                placement="bottomRight"
                content={(
                    <ColumnsControl<T>
                        defaultColumnsShow={defaultColumnsShow}
                        setColumnsList={setColumnsList}
                        columnsList={columnsList}
                        tableKey={tableColumnKey}
                    />
                )}
                onVisibleChange={onVisibleChange}
            >
                <MyIcon type="icon-shezhi" style={{fontSize: 20, padding: "6px 0"}} />
            </Popover>
        </div>
    );
};

export default TableColumnsControl;
