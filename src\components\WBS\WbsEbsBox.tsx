/* eslint-disable max-nested-callbacks */
import React, {Key, useState, useCallback, useEffect} from "react";
import {useSelector, useDispatch} from "react-redux";
import {TreeProps, Space, Tooltip} from "antd";
import {EyeOutlined} from "@ant-design/icons";
import WBSTree from "./WBSTree";
import {RootState} from "../../store/rootReducer";
import {toArr, pathSeparator} from "../../assets/ts/utils";
import {WbsEbsTreeDataNode} from "./data";
import {setQueryCheckedNode, setCheckedKeys, setLeafIds, setEbsTreeData, setEbsBindWbs, setWbsBindEbs} from "./store/action";
import {getBindEbs, getBindWBSList, getEbsBindWbs, getMyTaskBIndEBSList, getMyTaskBindWBSList, getReformBindWBSList} from "../../api/wbsEbs";
import {emit} from "../../store/common/action";

export interface WbsEbsBoxProps {
    businessType?: string;
    processType?: string;
}

/** 表格左侧wbsebs */
const WbsEbsBox = (props: WbsEbsBoxProps) => {
    const moduleTypeInit = () => {
        const {hash} = window.location;
        if (hash.includes("SECURITY")) {
            return "SECURITY";
        }
        if (hash.includes("QUALITY")) {
            return "QUALITY";
        }
        return "PLAN";
    };
    const {businessType, processType} = props;
    const dispatch = useDispatch();
    const {checkedKeys, leafIds, wbsBindEbs, ebsBindWbs} = useSelector((state: RootState) => state.wbsTree);
    const [moduleType] = useState(moduleTypeInit());
    const {orgInfo, on, curSectionInfo} = useSelector((state: RootState) => state.commonData);

    const getBindWBSIds = useCallback(
        () => {
            if (curSectionInfo !== null) {
                // 我的任务
                if (businessType === "security.inspection.routing.wbs.myTask") {
                    const params = {
                        businessType: "security.inspection.routing.wbs",
                        deptId: orgInfo.orgId,
                        moduleType,
                        nodeId: curSectionInfo.id,
                        buildType: curSectionInfo.nodeType,
                    };
                    getMyTaskBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                    }).catch(() => {
                        dispatch(setLeafIds([]));
                    });
                    return;
                }
                // 整改
                if (processType !== undefined) {
                    const params = {
                        moduleType: "PLAN_REFORM",
                        nodeId: curSectionInfo.id,
                        nodeType: curSectionInfo.nodeType,
                        processType,
                        // buildType: curSectionInfo.nodeType,
                    };
                    getReformBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                    }).catch(() => {
                        dispatch(setLeafIds([]));
                    });
                    return;
                }
                // 隐患排查,巡检(除我的任务),技术交底,岗前教育,风险,危大工程,技术交底
                if (businessType !== undefined) {
                    const params = {
                        businessType,
                        deptId: orgInfo.orgId,
                        moduleType,
                        nodeId: curSectionInfo.isAll === true ? undefined : curSectionInfo.id,
                        nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeType,
                        // buildType: curSectionInfo.nodeType,
                    };
                    getBindWBSList(params).then((res) => {
                        dispatch(setLeafIds(res.data));
                    }).catch(() => {
                        dispatch(setLeafIds([]));
                    });
                }
            }
        },
        [businessType, curSectionInfo, dispatch, moduleType, orgInfo.orgId, processType],
    );

    const buildComponentTreeWithPaths = useCallback((paths: string[], ppid: number): WbsEbsTreeDataNode[] => {
        // 1.记录根位置
        const resList: WbsEbsTreeDataNode[] | undefined = [];
        toArr(paths).forEach((path) => {
            if (path === null) {
                return;
            }
            // 2.待匹配项
            const pathList = path.split(pathSeparator);
            // 3.将移动指针重置顶层，确保每次从根检索匹配（必须！！！）
            let levelList: WbsEbsTreeDataNode[] | undefined = resList;
            // 4.遍历待询节点
            pathList.forEach((title, index) => {
                if (levelList === undefined) {
                    return;
                }
                // 5.同层同名节点查找匹配
                let obj = levelList.find((item) => item.title === title);
                // 6.若不存在则建立该节点
                if (obj === undefined) {
                    const key = `${ppid}${pathSeparator}${pathList.slice(0, index + 1).join(pathSeparator)}`;
                    obj = {
                        title,
                        key,
                        children: [],
                        ppid,
                        path,
                        type: "ebs",
                    };
                    levelList.push(obj);

                    // 7.若当前被增节点是叶子节点，则裁剪该节点子节点属性
                    if (title === pathList[pathList.length - 1]) {
                        delete obj.children;
                    }
                }
                // 8.已有则进入下一层，继续寻找
                levelList = obj.children;
            });
        });
        return resList;
    }, []);

    const getEbsTreeData = useCallback(
        async () => {
            if (orgInfo.orgId === "" || curSectionInfo === null) {
                return;
            }
            // 我的任务
            if (businessType === "security.inspection.routing.wbs.myTask") {
                const params = {
                    businessType: "security.inspection.routing.wbs",
                    deptId: orgInfo.orgId,
                    moduleType,
                    nodeId: curSectionInfo.nodeId,
                    buildType: curSectionInfo.nodeType,
                };
                getMyTaskBIndEBSList(params).then((res) => {
                    const ebsTreeData: WbsEbsTreeDataNode[] = [];
                    toArr(res.data).forEach((proj) => {
                        const ebsNode = ebsTreeData.find((ebsItem) => ebsItem.ppid === proj.ppid);
                        const pathChildren = buildComponentTreeWithPaths(proj.path ?? [], proj.ppid as number);
                        if (ebsNode === undefined) {
                            ebsTreeData.push({
                                title: proj.projectName,
                                key: proj.ppid as number,
                                children: pathChildren,
                                ppid: proj.ppid,
                                type: "ebs",
                            });
                        } else {
                            ebsNode.children = pathChildren;
                        }
                    });
                    dispatch(setEbsTreeData(ebsTreeData));
                }).catch(() => {
                    dispatch(setEbsTreeData([]));
                });
                return;
            }
            const params = {
                deptId: orgInfo.orgId,
                buildType: curSectionInfo.nodeType,
                nodeId: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeId,
                nodeType: curSectionInfo.isAll === true ? undefined : curSectionInfo.nodeType,
                businessType,
                moduleType,
            };

            const bindEbsRes = await getBindEbs(params);
            if (bindEbsRes.success && bindEbsRes.data !== undefined && Array.isArray(bindEbsRes.data)) {
                if (toArr(bindEbsRes.data).length !== 0) {
                    const ebsBindWbsParams = {
                        deptId: orgInfo.orgId,
                        nodeId: curSectionInfo.nodeId,
                        list: toArr(bindEbsRes.data)
                    };
                    getEbsBindWbs(ebsBindWbsParams)
                        .then((res) => {
                            const tempEbsBindWbs: RootState["wbsTree"]["ebsBindWbs"] = {};
                            toArr(res.data).forEach((el) => {
                                const key = `${el.ppid}${pathSeparator}${el.path?.split(">").join(pathSeparator)}`;
                                tempEbsBindWbs[key] = el;
                            });
                            dispatch(setEbsBindWbs(tempEbsBindWbs));
                        });
                }
                const filterData = toArr(bindEbsRes.data).filter((el) => Boolean(el.path) === true);
                const ebsTreeData: WbsEbsTreeDataNode[] = [];
                filterData.forEach((proj) => {
                    ebsTreeData.push({
                        title: proj.projectName,
                        key: proj.ppid ?? 0,
                        children: buildComponentTreeWithPaths(proj.path as string[], proj.ppid as number),
                        ppid: proj.ppid,
                        type: "ebs",
                    });
                });
                dispatch(setEbsTreeData(ebsTreeData));
            }
        },
        [buildComponentTreeWithPaths, businessType, curSectionInfo, dispatch, moduleType, orgInfo.orgId],
    );

    useEffect(() => {
        getBindWBSIds();
        getEbsTreeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [businessType, curSectionInfo, processType]);

    useEffect(() => {
        if (on?.type === "bindWbsChange") {
            dispatch(emit({type: ""}));
            getBindWBSIds();
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [on]);

    useEffect(() => {
        dispatch(setLeafIds([]));
        return () => {
            dispatch(setWbsBindEbs({}));
        };
    }, [dispatch]);

    const handleCheck: TreeProps["onCheck"] = (_checkedVals, info) => {
        let nodes: WbsEbsTreeDataNode[] = [];
        nodes = info.checkedNodes.filter((item: WbsEbsTreeDataNode) => {
            if (item.type === "ebs") {
                return true;
            }
            return (item.children ?? []).length === 0 || item.key === "all" || item.key === "noRelevance";
        });
        dispatch(setQueryCheckedNode(nodes));
        dispatch(setCheckedKeys(_checkedVals as unknown as Key[]));
    };

    const renderTreeNode: TreeProps["titleRender"] = (nodeData) => {
        if (wbsBindEbs[nodeData.key] !== undefined) {
            const bindData = wbsBindEbs[nodeData.key];
            const renderTitle = toArr(bindData.ebsList).map((el) => <div>{el.pathName}</div>);
            return (
                <Space>
                    {nodeData.title}
                    <Tooltip overlayStyle={{width: 400}} trigger={["hover"]} placement="right" title={renderTitle}>
                        <EyeOutlined />
                    </Tooltip>
                </Space>
            );
        }
        if (ebsBindWbs[nodeData.key] !== undefined) {
            const bindData = ebsBindWbs[nodeData.key];
            const renderTitle = toArr(bindData.wbsList).map((el) => <div>{el.pathName}</div>);
            return (
                <Space>
                    {nodeData.title}
                    <Tooltip overlayStyle={{width: 400}} trigger={["hover"]} placement="right" title={renderTitle}>
                        <EyeOutlined />
                    </Tooltip>
                </Space>
            );
        }
        return (
            <div>{nodeData.title}</div>
        );
    };

    return (
        <div style={{height: "100%"}}>
            <WBSTree
                isShowSearch={false}
                query
                leafIds={leafIds}
                checkable
                checkedKeys={checkedKeys}
                onCheck={handleCheck}
                titleRender={renderTreeNode}
            />
        </div>
    );
};

export default WbsEbsBox;
