/* eslint-disable @typescript-eslint/no-explicit-any */
import {message} from "antd";
import {saveAs} from "file-saver";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl: iworksBaseUrl} = (window as any).__IWorksConfig__;

interface FetchProps {
    url: string;
    baseUrl?: string;
    methods?: "get" | "post" | "put" | "delete";
    data?: unknown;
    formData?: FormData;
    needToken?: boolean;
    token?: string;
    requestConfig?: RequestInit;
    fileName: string;
    finallyFun?: () => void;
}

const FileFetch = (props: FetchProps) => {
    const {
        url,
        baseUrl = iworksBaseUrl as string,
        methods = "get",
        data,
        formData,
        token,
        needToken = true,
        requestConfig,
        fileName,
        finallyFun
    } = props;

    let requestUrl = url.includes("http") ? url : baseUrl + url;
    let requestBody: FormData | string | undefined = formData ?? JSON.stringify(data);
    const requestToken = token ?? localStorage.getItem("token");

    const requestHeader: HeadersInit = new Headers();

    if (Boolean(formData) === false) {
        requestHeader.append("content-type", "application/json");
    }

    if (Boolean(requestToken) && needToken) {
        requestHeader.append("access-token", requestToken ?? "");
    }

    if (methods === "get" && Boolean(data)) {
        requestBody = undefined;
        const getParamsArr: string[] = [];
        Object.entries(data as JSON).forEach(([key, value]) => {
            getParamsArr.push(`${key}=${value}`);
        });
        requestUrl = `${requestUrl}?${getParamsArr.join("&")}`;
    }
    fetch(requestUrl, {
        method: methods,
        headers: requestHeader,
        body: requestBody,
        ...requestConfig
    })
        .then(async (res) => {
            const contenType = res.headers.get("Content-type") ?? "";
            if (contenType.includes("application/octet-stream")) {
                const fileData = await res.blob();
                saveAs(fileData, fileName);
            } else {
                return res.json();
            }
        })
        .then((res) => {
            if (res.code !== 200) {
                message.error(res.msg);
            }
        })
        .catch((err: unknown) => {
            // eslint-disable-next-line no-console
            console.log(err);
        })
        .finally(() => {
            if (finallyFun !== undefined) {
                finallyFun();
            }
        });
};

export default FileFetch;
