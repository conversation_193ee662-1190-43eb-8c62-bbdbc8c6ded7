import {Spin} from "antd";
import {sortBy} from "lodash-es";
import React, {memo, useEffect, useCallback} from "react";
import {useDispatch, useSelector} from "react-redux";
import {renderRoutes, RouteConfigComponentProps} from "react-router-config";
import {getDeptOrgList} from "../../api/common.api";
import {NodeTypeEnum} from "../../assets/ts/globalData";
import ComBreadcrumb from "../../components/ComBreadcrumb";
import useWBSData from "../../components/WBS/useWBSData";
import WBSDrawer from "../../components/WBS/WBSDrawer";
import {setCurSectionInfo, setSectionList} from "../../store/common/action";
import {SectionListType} from "../../store/common/actionTypes";
import {RootState} from "../../store/rootReducer";

const RenderRoutesPage = (props: RouteConfigComponentProps) => {
    const {route} = props;

    const dispatch = useDispatch();
    const {authCodeList, orgInfo, loading} = useSelector((state: RootState) => state.commonData);
    useWBSData();
    // usePersonData();

    const dataInit = useCallback(
        async (curSectionId?: string) => {
            if (Boolean(orgInfo.orgId) === true) {
                const curSectionList: SectionListType[] = [];
                const deptNodeInfo = {
                    nodeId: orgInfo.orgId,
                    nodeName: orgInfo.orgName,
                    classification: 3,
                    supervisorOrg: "",
                    parentId: orgInfo.orgId,
                    totalPerson: 0,
                    authFlag: true,
                    name: orgInfo.orgName,
                    id: orgInfo.orgId,
                    nodeType: NodeTypeEnum.项目
                };
                // 房建项目
                if (orgInfo.deptDataType === 1) {
                    curSectionList.push(deptNodeInfo);
                }
                // 基建项目;
                if (orgInfo.deptDataType === 2) {
                    const orgRes = await getDeptOrgList(orgInfo.orgId);
                    const resSectionList = orgRes.data
                        .filter((el) => el.authFlag)
                        .map((el) => ({...el, name: el.nodeName, id: el.nodeId, nodeType: 3}));
                    const tempSectionList1 = resSectionList.map((el) => ({...el, classification1: -el.classification}));
                    const tempSectionList2 = sortBy(tempSectionList1, ["classification1", "nodeName"]);
                    if (resSectionList.length === orgRes.data.length) {
                        curSectionList.push(deptNodeInfo);
                    }
                    tempSectionList2.forEach((el) => curSectionList.push(el));
                }

                const firstNode = curSectionList[0];
                if (firstNode !== undefined && firstNode.isAll !== true) {
                    // 创建独立的"全部"节点，只包含必要属性
                    const allNode: SectionListType = {
                        nodeId: `${orgInfo.orgId}_all`, // 使用项目ID前缀避免冲突
                        nodeName: "全部",
                        name: "全部",
                        id: `${orgInfo.orgId}_all`,
                        classification: firstNode.classification,
                        nodeType: NodeTypeEnum.项目,
                        parentId: orgInfo.orgId,
                        authFlag: true,
                        supervisorOrg: "",
                        totalPerson: 0,
                        isAll: true,
                    };
                    curSectionList.unshift(allNode);
                }
                // 如果有指定的外部传入的curSectionId，则查找对应的 section
                const selectedSection = curSectionId !== undefined
                    ? curSectionList.find((el) => el.nodeId === curSectionId)
                    : curSectionList[0];
                dispatch(setCurSectionInfo(selectedSection ?? null));
                dispatch(setSectionList(curSectionList));
            }
        },
        [dispatch, orgInfo]
    );

    useEffect(() => {
        // 检查顶层窗口的URL中是否有businessId（工作台-消息提醒）
        const search = window.top.location.href;
        const searchStr = search.split("?")?.[1];
        // 从 URL 参数中获取 sectionId
        const urlParams = new URLSearchParams(searchStr);
        const urlSectionId = urlParams.get("sectionId");
        if (urlSectionId !== null) {
            dataInit(urlSectionId);
        } else {
            dataInit();
        }
    }, [dataInit]);

    if (authCodeList.length === 0) {
        return <></>;
    }

    return (
        // <div className={cls.root}>
        //     <div className={cls.content}>
        //         {renderRoutes(route?.routes)}
        //     </div>
        // </div>
        <div style={{position: "relative", width: "100%", height: "100%"}}>
            <div style={{position: "relative", height: "100%"}}>
                <div
                    className="noScrollBar"
                    style={{
                        position: "absolute",
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                        overflowY: "auto"
                    }}
                >
                    <div>
                        <ComBreadcrumb />
                    </div>
                    <div style={{flexGrow: 1, height: 0}}>{renderRoutes(route?.routes)}</div>
                </div>
            </div>
            {/* <PreviewModal /> */}
            <WBSDrawer />
            <Spin spinning={loading ?? false} size="large" style={{position: "absolute", top: 0, left: 0, right: 0, bottom: 0, padding: 240, zIndex: 9999}} />
        </div>
    );
};

export default memo(RenderRoutesPage);
