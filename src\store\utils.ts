import {Action} from "redux";

export interface SFAPayload<ActionType, Payload> extends Action<ActionType> {
    payload: Payload;
}
/**
 * 生成action
 * @param type action的type
 * @param fn 返回payload的函数
 */
/* eslint-disable import/prefer-default-export */
export const createSFAPayloadAction = <A, P, Args extends unknown[]>(
    type: A,
    fn: (...args: Args) => P
) => (...args: Args): SFAPayload<A, P> => ({type, payload: fn(...args)});
