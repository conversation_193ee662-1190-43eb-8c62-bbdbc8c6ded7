export interface AddDwgPDFDrawingInfoData {
    ppid?: number; // 代理工程id
    pdfName: string; // 名称
    pdfSize: number; // 大小
    pdfKey: string; // uuid
    pdfMd5: string;
    pdfMemo: string; // 备注
    classifyId: number; // 分类id
    source: number; // 设置来源，0:自动生成，1:手动上传
    pdfWidth: number;
    pdfHeight: number;
    nodeType: number;
    nodeId: string; // 新增下拉的节点，是可以改的
    deptId: string; // 项目部id
}

export interface PostTriggerMotorModelTransformData {
    sourceId: string; // 业务资源id，用来与motor模型id绑定关系(比如projId, dwg图纸id)
    sourceName: string; // 资源名称(比如：工程名称，dwg图纸)
    motorSubType: string; // 资源类型：PDS,RVT,IFC,IMGTILES, 卫片DOM 3DTILES, 倾斜摄影 TERTILES, 地形DEM FBX, LBG, DWG,
    fileUuid: string; // 业务资源文件uuid,用于抽取时下载资源（模型文件抽取时必传）
    linkUrl: string; // 链入GIS地址（链入GIS时必传）
}
