import {Typography} from "antd";
import React from "react";

const {Paragraph} = Typography;

export interface FormPersonProps {
    value?: {personName?: string; aimName: string}[];
}

const FormPerson = (props: FormPersonProps) => {
    const {value = []} = props;

    const text = Array.isArray(value) ? value.map((el) => el.personName ?? el.aimName).join(",") : "";

    return (
        <Paragraph ellipsis={{tooltip: text, rows: 3}}>
            {text}
        </Paragraph>
    );
};

export default FormPerson;
