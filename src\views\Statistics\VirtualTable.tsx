/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useRef, useState, useMemo, useImperativeHandle, forwardRef, ForwardedRef} from "react";
import {Table, Empty} from "antd";
import type {TableProps} from "antd";
import ResizeObserver from "rc-resize-observer";
import {VariableSizeGrid as Grid} from "react-window";

interface TreeBase<T> {
    id: string;
    children?: T[];
}

interface ColumnItem {
    columnIndex: number;
    rowIndex: number;
    style: React.CSSProperties;
}

interface Props<RecordType> extends TableProps<RecordType> {
    cRef: React.Ref<unknown>;
}

const minWidth = 100;
const rowHeight = 35;

/**
 *  基于antd Table组件封装的虚拟列表table
 *  支持树状结构渲染、展开收起，props与Table组件保持一致
 *  不支持固定列、不支持行hover效果、
 *  固定行高，当文字过长时，可能会溢出，需要做处理超出部分省略号，添加title
 */
const VirtualTable = <RecordType extends object>(
    props: Props<RecordType>
) => {
    const {columns, scroll, expandable, cRef} = props;
    const [tableWidth, setTableWidth] = useState(0);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const gridRef = useRef<any>();
    const [connectObject] = useState<any>(() => {
        const obj = {};
        Object.defineProperty(obj, "scrollLeft", {
            get: () => {
                if (gridRef.current !== undefined) {
                    return gridRef.current?.state?.scrollLeft;
                }
                return null;
            },
            set: (scrollLeft: number) => {
                if (gridRef.current !== undefined) {
                    gridRef.current.scrollTo({scrollLeft});
                }
            },
        });

        return obj;
    });

    const mergedColumns = useMemo(() => {
        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
        const widthColumnCount = columns!.filter(({width}) => !width).length;
        // 剩余宽度
        const restWidth = columns?.reduce((prev, current) => {
            if (current.width !== undefined) {
                return prev - Number(current.width);
            }
            return prev;
        }, tableWidth) ?? 0;

        const newColumns = columns!.map((column) => {
            let {width} = column;
            // 剩余宽度平分未设置宽度的单元格
            if (restWidth > 0 && widthColumnCount > 0 && width === undefined) {
                const w = Math.floor(restWidth / widthColumnCount);
                if (w >= minWidth) {
                    width = w;
                }
                if (w < minWidth) {
                    width = minWidth;
                }
            }
            // 没有未设置宽度的单元格时，剩余宽度平分到每个单元格
            if (restWidth > 0 && widthColumnCount === 0) {
                const w = Math.floor(restWidth / (columns?.length ?? 0));
                (width as number) += w;
            }

            return {
                ...column,
                width
            };
        });
        return newColumns;
    }, [columns, tableWidth]);

    const resetVirtualGrid = () => {
        const options = {columnIndex: 0, shouldForceUpdate: true};
        if (gridRef.current !== undefined) {
            gridRef.current.resetAfterIndices(options);
        }
    };

    // 用于父组件主动初始化table
    useImperativeHandle(cRef, () => ({
        initTable: () => {
            setExpandedKeys([]);
        }
    }));

    useEffect(() => resetVirtualGrid, [tableWidth, mergedColumns.length]);

    const renderVirtualList = (rawData: object[], {scrollbarSize, ref, onScroll}: any) => {
        ref.current = connectObject;
        const tableList = [] as any[];
        const dfsTree = <T extends TreeBase<T>>(tree: T[]): void => {
            for (let i = 0; i < tree.length; i++) {
                const node = tree[i];
                const key = node.id;
                tableList.push(node);
                if (node.children !== undefined && expandedKeys.includes(key)) {
                    dfsTree(node.children);
                }
            }
        };
        dfsTree(rawData as any);

        const totalHeight = tableList.length * rowHeight;

        // if (tableList.length === 0) {
        //     return (
        //         <>
        //         </>
        //     );
        // }
        return (
            <>
                {tableList.length === 0 ? <Empty style={{marginTop: 20}} /> : null}
                <Grid
                    ref={gridRef}
                    className="virtual-grid"
                    columnCount={mergedColumns.length}
                    columnWidth={(index: number) => {
                        const {width} = mergedColumns[index];
                        return totalHeight > scroll!.y! && index === mergedColumns.length - 1
                            ? (width as number) - scrollbarSize - 1
                            : (width as number);
                    }}
                    height={scroll!.y as number}
                    rowCount={tableList.length}
                    rowHeight={(_rowIndex) => rowHeight}
                    width={tableWidth}
                    onScroll={({scrollLeft}: {scrollLeft: number}) => {
                        onScroll({scrollLeft});
                    }}
                >
                    {({
                        columnIndex,
                        rowIndex,
                        style,
                    }: ColumnItem) => {
                        const columnItem = (mergedColumns as any)[columnIndex];
                        const record = tableList[rowIndex];
                        const key = record.id as string;
                        const text = tableList[rowIndex][columnItem.dataIndex];
                        const expanded = !expandedKeys.includes(key);

                        // 缩进计算
                        const level = record.level ?? 1;
                        const indentSize = expandable?.indentSize ?? 5;
                        const paddingLeft = (level - 1) * indentSize;

                        const handleExpand = () => {
                            let nextExpandedKeys: string[] = [];
                            if (expanded) {
                                nextExpandedKeys = [...expandedKeys, key];
                            } else {
                                nextExpandedKeys = expandedKeys.filter((v) => v !== key);
                            }
                            setExpandedKeys(nextExpandedKeys);
                            if (expandable?.onExpand !== undefined) {
                                expandable.onExpand(expanded, record);
                            }
                        };

                        return (
                            <div
                                style={{
                                    ...style,
                                    padding: "6px 24px",
                                    background: rowIndex % 2 !== 0 ? "#F7F8FA" : "",
                                    display: "flex",
                                    alignItems: "center"
                                }}
                            >
                                {
                                    columnItem.ellipsis === true
                                        ? (
                                            <>
                                                <span style={{height: 1, paddingLeft}} />
                                                {
                                                    record.children !== undefined
                                                        ? (
                                                            <div
                                                                className={`ant-table-row-expand-icon ${expanded ? "ant-table-row-expand-icon-collapsed" : ""}`}
                                                                onClick={handleExpand}
                                                                style={{flexShrink: 0, marginRight: 5}}
                                                            />
                                                        )
                                                        : <div style={{flexShrink: 0, marginRight: 5, height: 1, paddingRight: 16}} />
                                                }
                                            </>
                                        )
                                        : null
                                }
                                {
                                    typeof columnItem.render === "function"
                                        ? columnItem.render(text, record, rowIndex)
                                        : tableList[rowIndex][columnItem.dataIndex]
                                }
                            </div>
                        );
                    }}
                </Grid>
            </>
        );
    };

    return (
        <ResizeObserver
            onResize={({width}) => {
                setTableWidth(width);
            }}
        >
            <Table
                {...props}
                className="virtual-table com-table"
                columns={mergedColumns}
                pagination={false}
                components={{
                    body: renderVirtualList as any,
                }}
            />
        </ResizeObserver>
    );
};

export default VirtualTable;
