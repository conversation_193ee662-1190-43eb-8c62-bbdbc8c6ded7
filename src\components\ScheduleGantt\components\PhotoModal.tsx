import React, {FC, memo, useState, useEffect, useCallback, useMemo, useRef} from "react";
import {Modal, Button, Upload, message} from "antd";
import {LeftOutlined, RightOutlined} from "@ant-design/icons";
import {createUseStyles} from "react-jss";
import cryptoJS from "crypto-js";
import {getImageURLs, postUploadFile, uploadFileV2} from "../api/common";
import {PhotoInfo} from "../config/interface";
import {postTaskBindFiles, postTaskListBindFiles} from "../api/plan";
import {BindFileItem} from "../api/plan/type";
import {GetFileUrlRes} from "../../../api/common.type";

interface PhotoModalProps {
    visible: boolean;
    planId: string;
    taskId: string;
    editable: boolean;
    onUpdateHasPhoto(hasPhoto: boolean): void;
    onCancel(): void;
    onConfirm(): void;
}

const useStyles = createUseStyles({
    content: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        width: "100%"
    },
    image: {
        display: "inline-block",
        width: 480,
        height: 480,
        border: "1px dashed black",
        borderWidth: 1,
        objectFit: "contain"
    },
    imageContaner: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        width: "100%"
    },
    indicator: {
        marginTop: "8px",
        fontSize: "16px"
    },
    noImage: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: 480,
        fontSize: "24px"
    },
    pageButton: {
        width: 48,
        height: 48,
    },
});

const getMd5 = async (file: File) => new Promise<string>((resolve) => {
    const reads = new FileReader();
    reads.readAsArrayBuffer(file);
    reads.onload = () => {
        const md5: string = cryptoJS.MD5(cryptoJS.lib.WordArray.create(reads.result)).toString();
        resolve(md5);
    };
});

// 关联照片对话框组件
const PhotoModal: FC<PhotoModalProps> = memo((props) => {
    const {visible, planId, taskId, editable, onUpdateHasPhoto, onCancel, onConfirm} = props;
    const {content, image, imageContaner, indicator, noImage, pageButton} = useStyles();
    const photoListRef = useRef<PhotoInfo[]>([]); // 在获取照片URL前先保存在photoListRef中，获取URL后再存入photoList，避免显示一张空图片
    const [photoList, setPhotoList] = useState<PhotoInfo[]>([]);
    const [modalVisible, setModalVisible] = useState<boolean>(visible);
    const [currentPhotoIdx, setCurrentPhotoIdx] = useState<number>(0);

    useEffect(() => {
        setModalVisible(visible);
    }, [visible]);

    /**
     * 获取照片URL
     * @param photos 需要获取url的照片数组
     */
    const doGetImageURLList = async (photos: PhotoInfo[]) => {
        const response: GetFileUrlRes[] = await getImageURLs(photos.map((photo) => photo.fileUuid), 0);
        if (response !== null) {
            // 图片列表
            // console.log("doGetImageURLList", response)
            const findPhotoFileInfo = (oldPhoto: PhotoInfo) => response.find((resPhoto) => resPhoto.fileUUID === oldPhoto.fileUuid);
            const newPhototList = photos.map((oldPhoto: PhotoInfo) => {
                const fileInfo = findPhotoFileInfo(oldPhoto);
                if (fileInfo !== undefined) {
                    return {
                        ...oldPhoto,
                        downloadUrl: fileInfo.downloadUrls[0]
                    };
                }
                return oldPhoto;
            });
            return newPhototList;
        }
        return photos;
    };

    //  获取图片列表
    const doGetTaskPhotoList = useCallback(async (): Promise<PhotoInfo[] | undefined> => {
        if ((planId.length > 0 && taskId.length > 0) === false) {
            return undefined;
        }
        const response = await postTaskListBindFiles([taskId]);
        if (response.data !== null) {
            // 图片列表
            // console.log("doGetTaskPhotoList", response.result);
            photoListRef.current = await doGetImageURLList(response.data);
            setPhotoList(photoListRef.current);
            return photoListRef.current;
        }
        return undefined;
    }, [planId, taskId]);

    //  添加任务照片
    const doPostTaskPhoto = useCallback(async (photos: BindFileItem[]) => {
        if ((taskId.length > 0) === false) {
            return;
        }
        const response = await postTaskBindFiles({
            businessId: taskId,
            businessType: "PLAN_TASK",
            latestFiles: photos
        });
        if (response.success) {
            // 添加结果
            // console.log("doPostTaskPhoto", response)
            message.success("添加照片成功");
            const newPhotoList = await doGetTaskPhotoList();
            if (newPhotoList !== undefined) {
                setCurrentPhotoIdx(newPhotoList.length - 1);
            } else {
                setCurrentPhotoIdx(0);
            }
        }
    }, [doGetTaskPhotoList, taskId]);

    //  删除任务照片
    const doDeleteTaskPhotoList = useCallback(async () => {
        console.log("currentPhotoIdx", currentPhotoIdx);
        const deletePhoto = photoList[currentPhotoIdx];
        if (deletePhoto !== undefined) {
            const newPhotoList = [...photoList];
            newPhotoList.splice(currentPhotoIdx, 1);
            const response = await postTaskBindFiles({
                businessId: taskId,
                businessType: "PLAN_TASK",
                latestFiles: newPhotoList
            });
            if (response.success) {
                // 添加结果
                // console.log("doPostTaskPhoto", response)
                message.success("删除照片成功");
                await doGetTaskPhotoList();
                setCurrentPhotoIdx((prev) => {
                    if (prev > 0) {
                        return prev - 1;
                    }
                    return 0;
                });
            }
        }
    }, [currentPhotoIdx, photoList, taskId, doGetTaskPhotoList]);

    //  上传图片文件
    const uploadFile = useCallback(async (file: File, md5: string) => {
        if (taskId === undefined || taskId.length === 0) {
            return;
        }
        const params = [
            {
                fileMd5: md5,
                fileName: file?.name,
                fileSize: file?.size,
                isBPUpload: true,
                isCheckFastUpload: true,
                suportModes: [0]
            }
        ];
        const data = await postUploadFile(params);
        const {fileUUID, uploadUrls} = data[0];

        const photo: BindFileItem = {
            fileName: file?.name,
            fileSize: file?.size,
        };

        if (fileUUID !== null) {
            doPostTaskPhoto([
                ...photoList,
                {
                    ...photo,
                    fileUuid: fileUUID,
                }
            ]);
            return;
        }
        const URL = uploadUrls[0];
        const {uuid} = await uploadFileV2(URL, file);
        doPostTaskPhoto([
            ...photoList,
            {
                ...photo,
                fileUuid: uuid,
            }
        ]);
    }, [doPostTaskPhoto, photoList, taskId]);

    const customRequest = useCallback((options) => {
        const {file} = options;
        const reads = new FileReader();
        reads.readAsDataURL(file);
        reads.onload = async () => {
            const md5 = await getMd5(file);
            uploadFile(file, md5);
        };
    }, [uploadFile]);

    useEffect(() => {
        if (visible) {
            photoListRef.current = [];
            setPhotoList([]);
            setCurrentPhotoIdx(0);
            doGetTaskPhotoList();
        }
    }, [doGetTaskPhotoList, visible]);

    useEffect(() => {
        if (onUpdateHasPhoto instanceof Function) {
            onUpdateHasPhoto(photoList.length > 0);
        }
    }, [onUpdateHasPhoto, photoList]);

    // 上一张
    const handlePrev = (): void => {
        if (currentPhotoIdx > 0) {
            setCurrentPhotoIdx(currentPhotoIdx - 1);
        }
    };

    // 下一张
    const handleNext = (): void => {
        if (currentPhotoIdx < photoList.length - 1) {
            setCurrentPhotoIdx(currentPhotoIdx + 1);
        }
    };

    //  删除照片
    const handleDeletePhoto = (): void => {
        doDeleteTaskPhotoList();
    };

    const imgUrl = useMemo(() => photoList[currentPhotoIdx]?.downloadUrl, [currentPhotoIdx, photoList]);

    return (
        <>
            <Modal
                title={<span style={{fontWeight: "bold"}}>关联照片</span>}
                width={640}
                visible={modalVisible}
                footer={false}
                onCancel={onCancel}
                onOk={onConfirm}
                destroyOnClose
            >
                <div style={{display: "flex", flexDirection: "row", justifyContent: "space-between", marginBottom: "8px"}}>
                    <Upload
                        name="file"
                        accept="image/*"
                        // multiple
                        showUploadList={false}
                        customRequest={customRequest}
                    >
                        <Button disabled={!editable} size="middle">添加</Button>
                    </Upload>
                    <Button size="middle" disabled={!editable || photoList.length === 0} onClick={handleDeletePhoto}>刪除</Button>
                </div>
                {
                    photoList.length > 0
                        ? (
                            <div className={content} key={imgUrl}>
                                <div className={imageContaner}>
                                    <Button className={pageButton} onClick={handlePrev} disabled={currentPhotoIdx === 0}>
                                        <LeftOutlined />
                                    </Button>
                                    <img
                                        className={image}
                                        src={imgUrl}
                                        alt=""
                                    />
                                    <Button className={pageButton} onClick={handleNext} disabled={currentPhotoIdx === photoList.length - 1}>
                                        <RightOutlined />
                                    </Button>
                                </div>
                                <div className={indicator}>{`${currentPhotoIdx + 1}/${photoList.length}`}</div>
                            </div>
                        )
                        : (
                            <div className={noImage}>
                                暂无照片
                            </div>
                        )
                }
            </Modal>
        </>
    );
});

export default PhotoModal;
