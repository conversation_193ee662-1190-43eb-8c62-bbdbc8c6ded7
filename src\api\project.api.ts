/* eslint-disable no-underscore-dangle */

import Fetch from "../service/Fetch";
import {WebRes} from "./common.type";
import {AddProjectParams, GetProjectListParams, GetProjectListRes} from "./project.type";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = (window as any).__IWorksConfig__;
const editorUrl = `${baseUrl}/editor`;

export const addProject = async (params: AddProjectParams): Promise<WebRes<GetProjectListRes[]>> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/scene/project/add`,
    data: params
});

export const deleteProject = async (ids: string[]): Promise<WebRes<string>> => Fetch({
    methods: "put",
    url: `${baseUrl}/builder/project/scene/recycle`,
    data: ids,
});

export const getProjectList = async (params: GetProjectListParams): Promise<WebRes<GetProjectListRes>> => Fetch({
    methods: "post",
    url: `${editorUrl}/v2/scene/project/page`,
    data: params
});
