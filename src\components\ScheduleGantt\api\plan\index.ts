import {WebRes} from "../../../../api/common.type";
import Fetch from "../../../../service/Fetch";
import {FromType} from "../../config/interface";
import {<PERSON>antt<PERSON>erson} from "../../gantt/interface";
import {ColumnItem, GetTaskChangedProjectRes, PlanTaskListRes, PostCustomColumnItem, PostTaskBindFilesParams, PostTaskBindNodeParams, PostTaskListBindFilesRes, PostTaskListBindNodeRes, PutSyncProcessInspectionTimeParams, UpdateTaskParams} from "./type";

// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = (window as any).__IWorksConfig__;

// 更新计划任务
export const updatePlanTask = async (params: UpdateTaskParams) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/task/upset`,
    methods: "post",
    data: params,
});

// 获取计划任务列表
export const getPlanTaskList = async (planId: string) => Fetch<WebRes<PlanTaskListRes>>({
    url: `${baseUrl}/sphere/plan/task/list/${planId}`,
    methods: "get",
});

// 查询所有列
export const getTaskAllColumnList = async (planId: string, module: FromType) => Fetch<WebRes<ColumnItem[]>>({
    url: `${baseUrl}/sphere/plan/task/all-column/planId/${planId}/module/${module}`,
    methods: "get",
});

// 批量创建-编辑自定义列
export const postTaskCustomColumnList = async (planId: string, customColumns: PostCustomColumnItem[]) => Fetch<WebRes>({
    url: `${baseUrl}/sphere/plan/task/custom-column/upset?planId=${planId}`,
    methods: "post",
    data: customColumns,
});

// 设置隐藏的列
export const putTaskHiddenColumnList = async (planId: string, module: FromType, hideColumnIds: string[]) => Fetch<WebRes>({
    url: `${baseUrl}/sphere/plan/task/hidden-columns/planId/${planId}/module/${module}`,
    methods: "put",
    data: hideColumnIds,
});

// 绑定wbs或ebs节点
export const postTaskBindNode = async (params: PostTaskBindNodeParams) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/task/bind-node`,
    methods: "post",
    data: params,
});

// 查询根据业务id绑定的wbs或ebs节点
export const postTaskListBindNode = async (params: string[]) => Fetch<WebRes<PostTaskListBindNodeRes>>({
    url: `${baseUrl}/sphere/plan/task/list-bind-node`,
    methods: "post",
    data: params,
});

// 查询变更影响的工程列表
export const getTaskChangedProject = async (planId: string) => Fetch<WebRes<GetTaskChangedProjectRes>>({
    url: `${baseUrl}/sphere/plan/task/changed-project/planId/${planId}`,
    methods: "get",
});

// 任务绑定文件列表
export const postTaskBindFiles = async (params: PostTaskBindFilesParams) => Fetch<WebRes<string>>({
    url: `${baseUrl}/sphere/plan/task/bind-files`,
    methods: "post",
    data: params,
});

// 查询任务绑定文件列表
export const postTaskListBindFiles = async (params: string[]) => Fetch<WebRes<PostTaskListBindFilesRes>>({
    url: `${baseUrl}/sphere/plan/task/list-bind-files`,
    methods: "post",
    data: params,
});

//  获取项目部下用户列表
export const getTaskOwnerList = async (deptId: string) => Fetch<WebRes<GanttPerson[]>>({
    url: `${baseUrl}/pdscommon/rs/userInfo/listDeptUser/deptId/${deptId}`,
    methods: "get"
});

// 开始，同步工序报验
export const postSyncProcessInspectionTime = async (params: PutSyncProcessInspectionTimeParams) => Fetch<WebRes<boolean>>({
    url: `${baseUrl}/sphere/plan/task/process-inspection-time/sync/start`,
    methods: "put",
    data: params
});
