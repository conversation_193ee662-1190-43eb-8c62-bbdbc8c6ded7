import {Attachment} from "./attachment";
import {FlowTypeDetail} from "./custom-field";

/** 整改操作类型 */
export enum RectificationActionType {
    /** 删除 */
    Delete,
    /** 撤销 */
    Revoke,
    /** 提交 */
    Submit,
    /** 退回 */
    Decline,
    /** 抄送 */
    CopyTo,
    /** 打印 */
    Print,
    /** 转交 */
    HandOver
}

/** 审批角色 */
export enum ApprovalRole {
    /** 发起人 */
    InitialPerson = 1,
    /** 审批人 */
    ApprovalPerson = 2,
    /** 抄送人 */
    CopyPerson = 3
}

/** 审批状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed） */
export enum FlowState {
    /** 退回 */
    Declined = "backed",
    /** 撤销 */
    Revoked = "canceled",
    /** 进行中 */
    InProgress = "in-progress",
    /** 已通过 */
    Done = "passed"
}

export type FlowStateValueType = string;

/** 审批类型 */
export enum ApprovalType {
    /** 指定角色 */
    SpecifyRole,
    /** 指定人员 */
    SpecifyPerson,
    /** 指定岗位 */
    SpecifyPosition,
    /** 发起人指定 */
    InitiatorSpecify,
    /** 发起人自己 */
    OnlyStart
}

interface ApprovalTypeNode {
    label: string;
    value: ApprovalType;
}

export const approvalTypeList: ApprovalTypeNode[] = [
    {
        label: "指定角色",
        value: ApprovalType.SpecifyRole
    },
    {
        label: "指定人员",
        value: ApprovalType.SpecifyPerson
    },
    {
        label: "指定岗位",
        value: ApprovalType.SpecifyPosition
    },
    {
        label: "发起人指定",
        value: ApprovalType.InitiatorSpecify
    },
    {
        label: "发起人自己",
        value: ApprovalType.OnlyStart
    }
];

export type ApprovalRoleValueType = number;

/** 操作类型：1-发起；4-撤销；5-提交；6-退回；7-抄送；8-评论; 9-自动抄送; 10-转交 */
export enum RectificationOperationType {
    Initiate = 1,
    Revoke = 4,
    Submit = 5,
    Decline = 6,
    CopyTo = 7,
    Comment = 8,
    AutoCopyTo = 9,
    handover = 10
}

export const RectifyTypeList = [
    {
        key: 1,
        label: "发起",
        value: "Initiate"
    },
    {
        key: 4,
        label: "撤销",
        value: "Revoke"
    },
    {
        key: 5,
        label: "提交",
        value: "Submit"
    },
    {
        key: 6,
        label: "退回",
        value: "Decline"
    },
    {
        key: 7,
        label: "抄送",
        value: "CopyTo"
    },
    {
        key: 8,
        label: "评论",
        value: "Comment"
    },
    {
        key: 9,
        label: "自动抄送",
        value: "AutoCopyTo"
    },
    {
        key: 10,
        label: "转交",
        value: "handover"
    }
];

export interface Flow {
    id: string;
    name: string;
    defaultPersonIds: string[];
    persons: Person[];
    canSet: boolean;
    disableReason?: string;
    roles: string[];
    positions: string[];
    type: ApprovalType;
}

export interface Person {
    name: string;
    id: string;
    avatar?: string | null;
}
export interface SecurityInfo {
    key: string;
    checkItem: string;
    description: string;
    checkLocation: string;
    checkResult: string;
    securityLevel: string;
    rectifyRequirement: string;
    law: string;
    attachment: Attachment[];
    lawId: string;
    checkItemId: string;
    checkContent: string;
}

export interface QsRecordDetailModel {
    key: string;
    createPerson: string;
    name: string;
    checkDate: Date;
    flowType: FlowTypeDetail;
    rectifyDeadLine: Date | null;
    checkDepartment: string;
    deptName?: string;
    deptId?: string;
    projName?: string;
    currentFlowStep?: string;
    approvalRoles?: ApprovalRoleValueType[];
    flowState?: FlowStateValueType;
    startFlowNode?: boolean;
    examineRecord?: ExamineRecord[];
    comment?: string;
    rectifyNoteId?: string;
    securityInfo?: SecurityInfo[];
    isReturnBack: boolean;
    formTaskId: string;
    hasRectifyInvoice: boolean;
    formId: string;
    formTmplId: string;
    isRootNode?: boolean;
}

interface ExamineRecord {
    name: string;
    role: string;
    operationType: number;
    date: Date;
    avatar: string;
    comment: string;
    attachments: Attachment[];
    copyToName?: string;
    handOverName?: string;
}
