import {Action} from "redux";
import {ParentAPI} from "postmate";
import {ProjectDurationResult} from "../../api/sandManager/type";

export interface BIMSandTableInfo {
    ppid: number; // 代理工程id
    motor3dId: string; // motor模型关联id

    /*
     * 专业（1:土建预算/2:安装预算/3:钢筋预算/4:Revit/5:Tekla/6:造价工程/7:Re<PERSON><PERSON>(班筑客户端上传)/8:班筑家装（PDS客户端）
     * 9:场布预算/10:Civil3D/11:Bentley/12:Rhino/13:ifc/14:CATIA/15:PKPM）
    */
    projType: number;
    projName: string; // 工程名称

    nodeType: number; // 工程归属节点类型 0：项目部 1：标段 2：单项 3：单位
    nodeId: string; // 工程归属节点id
    nodeName: string; // 工程归属名称
}

/**
 * 当前的沙盘数据
*/
export const CUR_SANDTABLE = "CUR_SANDTABLE";

/**
 * 当前工程的进度设置信息
*/
export const CUR_PROJ_SETTING_INFO = "CUR_PROJ_SETTING_INFO";

export const BIM_FULL_SCREEN = "BIM_FULL_SCREEN";

export const SET_MOTOR_FRAME = "SET_MOTOR_FRAME";

export const SET_MOTOR_FRAME_INIT_STATUS = "SET_MOTOR_FRAME_INIT_STATUS";

export interface CurSandTable extends Action<typeof CUR_SANDTABLE> {
    payload: BIMSandTableInfo | null;
}

export interface CurProjSettingInfo extends Action<typeof CUR_PROJ_SETTING_INFO> {
    payload: ProjectDurationResult | null;
}

export interface BimFullScreen extends Action<typeof BIM_FULL_SCREEN> {
    payload: boolean;
}

export interface SetMotorFrame extends Action<typeof SET_MOTOR_FRAME> {
    payload: ParentAPI | null;
}

export interface SetMotorFrameInitStatus extends Action<typeof SET_MOTOR_FRAME_INIT_STATUS> {
    payload: boolean;
}

export interface StatusStateType {
    curSandTable: BIMSandTableInfo | null;
    curProjSettingInfo: ProjectDurationResult | null;
    /** 沙盘驾驶舱，点击全屏 */
    isBimFullscreen?: boolean;
    motorFrame: ParentAPI | null;
    motorFrameInitStatus: boolean;
}


export type StatusActionTypes = CurSandTable | CurProjSettingInfo | BimFullScreen | SetMotorFrame | SetMotorFrameInitStatus;
