import React, {<PERSON>} from "react";
import moment, {Moment} from "moment";
import RenderInput from "./comp/renderInput";
import RenderTextArea from "./comp/renderTextArea";
import RenderSelect from "./comp/renderSelect";
import RenderInputNumber from "./comp/renderInputNumber";
import RenderDateInput from "./comp/renderDateInput";
import RenderAttachment from "./comp/renderAttachment";
import RenderPerson from "./comp/renderPerson";
import TextEllipsis from "../../TextEllipsis";
import {CustomFieldProps, CustomFieldType, OptionType} from "../models/custom-field";
// import ConnectedPersonSelector from "../PersonSelector";

const CustomField: FC<CustomFieldProps> = (props) => {
    const {data, currentValue, onChange, displayOnly} = props;

    // const handleMultiDeselect = (val: string) => {
    //     if (typeof currentValue === "string" || typeof currentValue === "number") {
    //         return;
    //     }
    //     const curData = currentValue ?? [];
    //     const realMultiSelectData = curData.filter((v) => v !== val);
    //     onChange(realMultiSelectData);
    // };

    // const handleMultiSelect = (val: string) => {
    //     if (typeof currentValue === "string" || typeof currentValue === "number") {
    //         return;
    //     }
    //     const curData = currentValue ?? [];
    //     onChange([...curData, val]);
    // };

    const handleMultiBlur = (vals: string[]) => {
        onChange(vals);
    };

    const handleDateChange = (date: Moment | null) => {
        let dateStr = "";
        if (date !== null && date.isValid()) {
            dateStr = date.toISOString();
        }
        onChange(dateStr);
    };

    const renderPersonSelector = () => (
        <>
            <div>wfewfw</div>
        </>
    );

    const renderDisplayOnlyStringValue = (val: string | OptionType[]) => {
        let newStr = "";
        if (typeof val === "string") {
            newStr = val;
        } else {
            const target = val.find((v) => v.id === currentValue);
            newStr = target?.value ?? "";
        }
        return <TextEllipsis text={newStr} />;
    };

    const renderDisplayOnlyMultiValue = (val: OptionType[]) => {
        if (Array.isArray(currentValue)) {
            const value = currentValue.map((cv) => {
                const cur = val.find((v) => v.id === cv);
                return cur?.value ?? "";
            });
            return <TextEllipsis text={value.join("、")} />;
        }
        return null;
    };

    const renderDateValue = () => <span>{moment(currentValue).format("YYYY.MM.DD")}</span>;

    const renderMaps: Map<CustomFieldType, () => React.ReactNode> = new Map([
        [CustomFieldType.Input, () => <RenderInput {...props} />],
        [CustomFieldType.TextArea, () => <RenderTextArea {...props} />],
        [
            CustomFieldType.MultiSelect, () => (
                <RenderSelect
                    {...props}
                    // onMultiDeselect={handleMultiDeselect}
                    // onMultiSelect={handleMultiSelect}
                    onMultiBlur={handleMultiBlur}
                />
            )
        ],
        [CustomFieldType.Select, () => <RenderSelect {...props} onSingleSelect={(val) => onChange(val)} />],
        [CustomFieldType.NumberInput, () => <RenderInputNumber {...props} />],
        [CustomFieldType.DateInput, () => <RenderDateInput {...props} handleDateChange={handleDateChange} />],
        [CustomFieldType.Attachment, () => <RenderAttachment {...props} />],
        [CustomFieldType.REF_PROC, () => <div />],
        [CustomFieldType.Person, () => <RenderPerson {...props} />]
    ]);

    const renderDisplayOnly = () => {
        if ([CustomFieldType.Input, CustomFieldType.TextArea, CustomFieldType.NumberInput].includes(data.type)) {
            return renderDisplayOnlyStringValue(data.data);
        }
        if (data.type === CustomFieldType.MultiSelect) {
            return renderDisplayOnlyMultiValue(data.data as OptionType[]);
        }
        if (data.type === CustomFieldType.Select) {
            return renderDisplayOnlyStringValue(data.data);
        }
        if (data.type === CustomFieldType.DateInput) {
            return renderDateValue();
        }
        if (data.type === CustomFieldType.Attachment) {
            return <RenderAttachment {...props} />;
        }
        if (data.type === CustomFieldType.REF_BIM) {
            return <div />;
        }
        if (data.type === CustomFieldType.REF_DOC) {
            return <div />;
        }
        if (data.type === CustomFieldType.REF_PROC) {
            return <div />;
        }
        if (data.type === CustomFieldType.Person) {
            return renderPersonSelector();
        }

        return <div />;
    };

    const rendererContent = () => {
        if (!data.visible) {
            return null;
        }
        if (displayOnly === true) {
            renderDisplayOnly();
        }
        const renderer = renderMaps.get(data.type);
        if (renderer === undefined) {
            return null;
        }
        return renderer();
    };

    return (
        <>{rendererContent()}</>
    );
};
export default CustomField;
