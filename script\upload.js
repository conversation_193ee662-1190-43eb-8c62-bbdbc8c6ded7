const request = require('request-promise');
const path = require("path");
const fs = require("fs");
const {config} = require("./config");
const {findExitFileZip} = require("./utils");
const {rootPath, distZipReg} = config;
const uploadZipBeta = async () => {
    // const distZipPath = path.resolve(__dirname, `../luban-plan-web-.zip`);
    const fileList = findExitFileZip({rootPath, reg: distZipReg});

    let args = process.argv.splice(2);
    const [name, deployUrl = "http://**************:8282/deploy"] = args;
    if (name === undefined) {
        console.log("缺少项目名!");
        return;
    }
    try {
        const listResp = await request({
            method: "GET",
            url: `${deployUrl}/list`,
        });
        const list = JSON.parse(listResp);
        if (list.data === undefined) {
            console.log("获取项目部署地址失败!");
            return;
        }
        const deployInfo = list.data.find((item) => item.name?.includes(name));
        if (deployInfo === undefined) {
            console.log("获取项目部署地址失败!");
            return;
        }
        const uri = `${deployUrl}/upload/id/${deployInfo.id}`;
        console.log("项目部署地址", uri);
        const options = {
            method: 'POST',
            uri: uri,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36',
                'Content-Type': 'multipart/form-data',
            },
            formData: {
                myfile: fs.createReadStream(fileList[0]),   // 这里是关键
            }
        };
        const res = await request(options);
        console.log("代码上传beta环境成功!");
        console.log('res', res);
    } catch (error) {
        console.log('error', error);
    }
}

uploadZipBeta();
