/* eslint-disable import/prefer-default-export */
import {FileType} from "../common.type";

export interface WindowType extends Window {
    embedPlatform: boolean;
    _PLATFORM_: string;
    __IWorksConfig__: {
        baseUrl: string;
        basePds: string;
        token: string;
        productId: string;
        motorEditor: string;
        motorViewUrl: string;
        devBaseUrl: string;
        shellDownUrl: string;
    };
    PlatformHeaderConfig: {
        baseUrl: string;
        token: string;
        productId: string;
        workbenchUrl: string;
    };
    __ModelUrlConfig__: {
        BaseUrl: string;
        MOTOR_BASE_URL: string;
    };
}
export type ProjNameForUpdatePlan = Pick<ProjNameForPlan, "projModel" | "projType">;
export interface CalendarInfo {
    restDayName: string; // 自定义日历名称（日历类型为自定义类型时必传） ,
    restDays: number[]; // 非工作日集合（这里只记录休息日期，不记录休息日期设置规则）（日历类型为自定义类型时选传） ,
    workDays: number[]; // 工作日（日历类型为自定义类型时选传） ,
    calendarType: number; // 日历类型（必传）
    startDate: number; // 日历有效时间（开始）日历类型为自定义类型时必传） ,
    endDate: number; // 日历有时间（结束）日历类型为自定义类型时必传）
}
export interface CalendarDetailType extends CalendarInfo {
    // 是否为EDS模板
    isTemplate: boolean;
}

export interface CalendarListItem {
    ctid: string; // 模板id
    ctName: string; // 模板名称
    calendarFlag: number; // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）
    copyid: string; // 复制的id
}

export interface CalendarInfoRes {
    ctid: string; // 模板id
    ctName: string; // 模板名称
    calendarFlag: number; // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）
    calendarFalg: number; // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）
    copyid: string | null; // 复制的id
    startDate: string | null; // 日历有效时间（开始）
    endDate: string | null; // 日历有时间（结束）
    restDates: string[] | null; // 休息日列表
    workDates: string[] | null; // 工作日列表
}

// 计划周期类型  MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划
export type PlanCycleType = "MASTER" | "YEAR" | "QUARTER" | "MONTH" | "WEEK";

export interface GetPlanListUsedCycleParams {
    nodeId: string; // 组织节点ID
    nodeType: string; // 组织类型
    type: string; // 计划类型
}

export interface GetPlanListUsedCycleItemType {
    cycle: string;
    type: string;
}

/*
* Unchanged: 计划审批
* Changed: 变更审批
*/
export type PlanChangeStatusType = "Unchanged" | "Changed";

export enum ApprovalStatus {
    /** 1 无审批 */
    NO_APPROVAL = 1,
    /** 2 审批中 */
    APPROVING = 2,
    /** 3 审批完成 */
    APPROVAL_COMPLETED = 3,
    /** 4 审批删除（即审批撤回） */
    RETRACT = 4,
    /** 5 退回到发起人节点 */
    RETURN = 5,
}

export interface PlanInfoDetailType {
    /* 审批ID */
    approvalId: string;
    /* 审批模板ID */
    approvalTemplateId: string;
    /* 工作日历 */
    calendar: string;
    /* 变更原因 */
    changeReason: string;
    /* 变更状态：未变更-Unchanged,已变更-Changed */
    changeStatus: PlanChangeStatusType;
    /* 计划周期 */
    cycle: number;
    /* 计划完成日期 */
    endDate: number;
    /* 计划ID */
    id: string;
    /* 计划名称 */
    name: string;
    /* 组织节点ID */
    nodeId: string;
    /* 组织节点类型，项目部=2，标段=3 */
    nodeType: number;
    /* 父计划ID */
    parentId: string;
    /* 是否有子计划 */
    hasChildren: boolean;
    /* 计划开始日期 */
    startDate: number;
    /* 计划状态：0-无需审批，1-未申报（未发起审批），2-已申报（审批中），3-审批完成 */
    status: number;
    /** 审批发起人 */
    approvalStartUser: string;
    /** 审批状态：1-无审批，2-审批中，3-审批完成，4-审批删除（即审批撤回），5-退回到发起人节点 */
    approvalStatus: ApprovalStatus;
    /* 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type: PlanCycleType;
    // 延期天数 = 当前总工期 - 变更前总工期
    /* 变更前总工期，单位天 */
    currentTotalDuration: number;
    /* 当前总工期，单位天 */
    unchangedTotalDuration: number;
    /* 编制时间 */
    updateAt: number;
    /* 编制人 */
    updateBy: string;
    approvalUserList?: ApprovalUserType[]; // 审批人列表
    approvalEndDate?: number; // 审批通过时间
    changeFileList: FileType[];
}

export interface GetPreparationListParams {
    nameKey?: string; //   计划名称，修改人模糊搜索 ,
    deptId?: string;
    // orgId: string; //  组织节点id，不传为当前企业下【右上角】 ,
    // orgType: number; //  组织节点类型：1-分公司，2-项目部【右上角】 ,
    nodeId?: string; //  项目部id||标段id(节点id)【左边】 ,
    nodeType?: number; //  节点类型：-1=项目部，0=施工标段，1=监理标段【左边】 ,
    pageNum: number;
    pageSize: number;
    status?: number;
    type?: PlanCycleType; //  计划类型, MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划
    filterNoWarn?: number; // 是否过滤没有报警的计划，0=不过滤，1=过滤，默认为0
}
export interface PlanPreparationListReturn {
    total: number;
    items: PlanPreparationItemType[];
}

export interface ApprovalUserType {
    leaveOffice: boolean;
    realName: string;
    userName: string;
}

export interface PlanPreparationItemType {
    id: string; //  计划id ,
    name: string; //  计划名称 ,
    type: PlanCycleType; //  计划类型, MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划
    cycle: number; //  计划周期 ,
    startDate: number; //  计划开始时间，时间戳，毫秒 ,
    endDate: number; //  计划结束日期，时间戳，毫秒 ,
    updateAt: number; //  进度计划更新时间 ,
    updateBy: string; //  进度计划更新人 ,
    approvalTemplateId: string; // 审批模板id
    approvalId: string; // 审批id
    /** 计划状态：0-无需审批，1-未申报（未发起审批），2-已申报（审批中），3-审批完成 */
    status: number;
    /** 审批状态：1-无审批，2-审批中，3-审批完成，4-审批删除（即审批撤回），5-退回到发起人节点 */
    approvalStatus: ApprovalStatus;
    approvalUserList: ApprovalUserType[]; // 审批人列表
    approvalEndDate: number; // 审批通过时间
    // projNameList: ProjNameForPlan[]; //  关联的工程列表 ,
    // chooseCalendarType: number; //  日历类型* 0：24小时日历（默认）1：标准日历（周六周日休息）2:自定义日历（复制24小时）3:自定义日历（复制标准）
    // checkOutUser: string; //  签出人，计划未签出则返回null
    /* 变更状态：未变更-Unchanged，已变更-Changed */
    changeStatus: "Unchanged" | "Changed";
    bindWbs?: boolean; // 是否绑定WBS,false=未绑定，true=绑定
}

export interface ProjNameForPlan {
    //  代理工程id
    ppid: number;
    //  工程名称，用于校验工程
    projName: string;
    //  工程类型
    projType: number;
    //  工程模型（施工或预算）
    projModel: string;
}
export interface UnusedProject extends ProjNameForPlan {
    //  工程大小
    projsize: number;
}
export interface AddPlanInfoType {
    name: string;
    type: string; // 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划
    deptId: string; //  项目部id ,
    nodeId: string;
    nodeType: number;
    approvalTemplateId: string; // 审批模板ID，不传则无需审批
    calendar: string; // 工作日历：0-24小时日历，1-标准日历，其他值-自定义日历
    cycle?: number | null; // 计划周期
    startDate: number; // 计划开始日期
    endDate: number; // 计划完成日期
}
export interface UpdatePlanInfoType extends Partial<AddPlanInfoType> {
    id: string;
    parentId?: string;
    currentTotalDuration?: number;
    parentPlanTaskIds?: string[];
}
export interface PlanInfoReturn {
    planId: string; //  计划id ,
    createTime: string; //   计划创建时间 ,
    failProxyProjIds: number[]; //  失败的代理工程id列表 ,
    status: number; //  0成功 1名称重复 2关联模型失败
}

export interface PlanlaunchChangeParams {
    /* 变更原因 */
    changeReason: string;
    /* 变更资料 */
    fileList: FileType[];
    /* 计划ID */
    id: string;
}

export interface PlanExportParams {
    deptId?: string;
    ids?: string[];
    nameKey?: string;
    nodeId?: string;
    nodeType?: number;
    pageNum?: number;
    pageSize?: number;
    status?: number;
    type?: string;
}
export interface GetPreparationTabNumParams {
    /* 项目id */
    deptId?: string;
    /* 组织节点ID */
    nodeId?: string;
    /* 组织节点类型，，项目部=2，标段=3 */
    nodeType?: number;
}
export type PlanPreparationTabNumReturn = {
    [key in React.Key]: number;
};
