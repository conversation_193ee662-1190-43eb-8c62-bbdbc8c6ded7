import moment, {Moment} from "moment";
import {getProcessListByComps} from "../../../api/processManager";
import {CompProcessItem, CompProcessListParam, CompStateParam, CompWithoutProcess, IworksProcessTemplateLeafNode, IworksProcessTemplateTreeNode, ProcessStatus, ProcessTemplateTreeNode, ProcessTmplTreeNode} from "../../../api/processManager/type";
import {getPercent, isNonEmptyArray} from "../../../assets/ts/utils";

/** 模型 treePaths分隔符 */
export const pathSeparator = "☎";

/** 沙盘驾驶舱全屏的dom节点id */
export const sandTableFullScreenDomId = "sandTableFullScreenDom";

export type ComStateWithProgress = CompStateParam & {progressInfo?: ProcessProgressInfo};

/** 一组工序的最早开始时间和最晚结束时间，以及二者的时间间隔 */
interface ProcessStartAndEnd {
    /** 最早开始时间与最晚结束时间的间隔 */
    interval: number;
    /** 最早开始时间 */
    earliestTime: Moment;
    /** 最晚结束时间 */
    latestTime: Moment;
}

/** 单条工序的进度条信息 */
export interface ProcessProgressInfo {
    /** 实际起止日期的间隔 */
    timeInterval: number;
    /** 实际起止日期间隔在整组工序的时间间隔中的占比 */
    timePer: number;
    /** 计划起止日期的间隔 */
    planTimeInterval: number;
    /** 计划起止日期间隔在整组工序的时间间隔中的占比 */
    planTimePer: number;
    /** 实际时间偏移量 */
    timeOffset: number;
    /** 计划时间偏移量 */
    planTimeOffset: number;
    /** 如果没有实际开始日期，则不展示实际起止日期的进度条 */
    hasStartTime: boolean;
}

export const getIworksProcessTmplLeafNodeMap = (list: IworksProcessTemplateTreeNode[]) => {
    const stateKeyMap: Map<string, string> = new Map();
    const converter = (data: IworksProcessTemplateTreeNode[], level = ""): void => {
        data.forEach((v, index) => {
            const {subNodes, states} = v;
            const key = level === "" ? `${index}` : `${level}-${index}`;
            if (isNonEmptyArray(subNodes)) {
                converter(subNodes, key);
            } else if (isNonEmptyArray(states)) {
                states.forEach((s, sIdx) => {
                    stateKeyMap.set(`${key}-${sIdx}`, s.stateKey ?? "");
                });
            }
        });
    };
    converter(list);
    return stateKeyMap;
};

// eslint-disable-next-line arrow-body-style
const getIworksProcessTmplLeafNodeList = (nodes: ProcessTmplTreeNode[]): IworksProcessTemplateLeafNode[] => {
    return nodes.map((v) => ({
        stateColor: v.color,
        stateName: v.name,
        stateKey: v.stateKey,
        earlyWarn: false
    }));
};

export const getIworksProcessTmplLeafNode = (node: ProcessTmplTreeNode): IworksProcessTemplateLeafNode => ({
    stateColor: node.color,
    stateName: node.name,
    stateKey: node.stateKey,
    earlyWarn: false
});

export const isDirectParentNode = (node: ProcessTemplateTreeNode): boolean => {
    if (node.isParent && isNonEmptyArray(node.children)) {
        return node.children.every((v) => v.isParent !== true);
    }
    return false;
};

const getCenterProcessTmplLeafNode = (
    node: IworksProcessTemplateLeafNode,
    pathStr: string
): ProcessTmplTreeNode => ({
    key: node.stateKey ?? "",
    name: node.stateName ?? "",
    isParent: false,
    color: node.stateColor ?? "",
    pathStr,
    stateKey: node.stateKey
});

// const isLeafNode = (
//     node: IworksProcessTemplateLeafNode | IworksProcessTemplateTreeNode
// ): node is IworksProcessTemplateLeafNode => isDefined((node as IworksProcessTemplateLeafNode).stateColor);

/**
 * 修改工序模板树选中节点的颜色，注意这个方法会直接修改源数据
 * 由于涉及到递归中断，因此方法会返回布尔值
 * @param tree 工序模板树
 * @param node 选中的节点
 */
export const updateTmplTreeNodeColor = (tree: ProcessTmplTreeNode[], node: ProcessTmplTreeNode): boolean => {
    for (let i = 0; i < tree.length; i++) {
        const curNode = tree[i];
        if (curNode.key === node.key) {
            curNode.color = node.color;
            return true;
        }
        if (isNonEmptyArray(curNode.children)) {
            if (updateTmplTreeNodeColor(curNode.children, node)) {
                return true;
            }
        }
    }
    return false;
};

/**
 * 对center后台返回的工序模板树进行功能增强，每个节点加上key和pathStr字段
 * @param tree 工序模板树
 */
export const enhanceCenterTmplTree = (
    tree?: ProcessTemplateTreeNode[],
    level = "",
    pathStr = ""
): ProcessTmplTreeNode[] | undefined => {
    if (isNonEmptyArray(tree)) {
        return tree.map((v, index) => {
            const curPathStr = `${pathStr}${v.name}`;
            const curKey = level === "" ? `${index}` : `${level}-${index}`;
            return {
                ...v,
                key: curKey,
                pathStr: curPathStr,
                children: enhanceCenterTmplTree(v.children, curKey, `${curPathStr}${pathSeparator}`)
            };
        });
    }
    return undefined;
};

/**
 * 对center后台返回的工序模板树进行功能增强，每个节点加上key和pathStr字段，叶子节点加上stateKey
 * @param tree 工序模板树
 */
export const enhanceCenterTmplTreeWithStateKey = (
    map: Map<string, string>,
    tree?: ProcessTemplateTreeNode[],
    level = "",
    pathStr = ""
): ProcessTmplTreeNode[] | undefined => {
    if (isNonEmptyArray(tree)) {
        return tree.map((v, index) => {
            const curPathStr = `${pathStr}${v.name}`;
            const curKey = level === "" ? `${index}` : `${level}-${index}`;
            return {
                ...v,
                key: isNonEmptyArray(v.children) ? curKey : map.get(curKey) ?? "",
                stateKey: isNonEmptyArray(v.children) ? curKey : map.get(curKey) ?? undefined,
                pathStr: curPathStr,
                children: enhanceCenterTmplTreeWithStateKey(map, v.children, curKey, `${curPathStr}${pathSeparator}`)
            };
        });
    }
    return undefined;
};

/**
 * 将center后台返回的工序模板树转化为iworks后台所需的格式
 */
export const transformCenterTmplTreeToIworks = (
    tree?: ProcessTmplTreeNode[]
): IworksProcessTemplateTreeNode[] => {
    const converter = (
        data: ProcessTmplTreeNode[]
    ): IworksProcessTemplateTreeNode[] => data.map((v) => {
        // const isLeafNode = !isNonEmptyArray(v.children) && v.isParent !== true;
        // if (isLeafNode) {
        //     return getIworksProcessTmplLeafNode(v) as unknown as IworksProcessTemplateTreeNode;
        // }
        // 是否为叶子节点
        // const isLeafNode = !isNonEmptyArray(v.children) && v.isParent !== true;
        // // 是否为叶子节点的直系父节点
        // const isLeafNodeDirectParentNode = isDirectParentNode(v);
        // const isStates = isLeafNode || isLeafNodeDirectParentNode;

        const childs = v.children ?? [];
        const leafNodeList: ProcessTmplTreeNode[] = [];
        const parentNodeList: ProcessTmplTreeNode[] = [];
        childs.forEach((c) => {
            if (!isNonEmptyArray(c.children) && c.isParent !== true) {
                leafNodeList.push(c);
            } else {
                parentNodeList.push(c);
            }
        });

        return {
            nodeName: v.isParent ? v.name : undefined,
            subNodes: parentNodeList.length !== 0 ? converter(parentNodeList) : undefined,
            states: leafNodeList.length !== 0 ? getIworksProcessTmplLeafNodeList(leafNodeList) : undefined
        };
    });
    return converter(tree ?? []);
};

/**
 * 将iworks后台返回的工序模板树转化为center后台所需的格式
 */
export const transformIworksTmplTreeToCenter = (
    tree?: IworksProcessTemplateTreeNode[],
    level = "",
    pathStr = ""
): ProcessTmplTreeNode[] | undefined => (tree ?? []).map((v, index) => {
    const curPathStr = `${pathStr}${v.nodeName ?? ""}`;
    const curKey = level === "" ? `${index}` : `${level}-${index}`;
    const subNodes = v.subNodes ?? [];
    const leafNodes = v.states ?? [];

    const subChilds = transformIworksTmplTreeToCenter(subNodes, curKey, `${curPathStr}${pathSeparator}`) ?? [];
    const leafChilds = leafNodes.map((s) => {
        const leafPathStr = `${curPathStr}${pathSeparator}${s.stateName}`;
        return getCenterProcessTmplLeafNode(s, leafPathStr);
    });
    const childs = [...subChilds, ...leafChilds];
    return {
        key: curKey,
        name: v.nodeName ?? "",
        isParent: true,
        pathStr: curPathStr,
        children: childs.length !== 0 ? childs : undefined
    };

    // if (isNonEmptyArray(v.subNodes)) {
    //     return {
    //         key: curKey,
    //         name: v.nodeName ?? "",
    //         isParent: true,
    //         children: transformIworksTmplTreeToCenter(v.subNodes, curKey, `${curPathStr}${pathSeparator}`),
    //         pathStr: curPathStr
    //     };
    // }
    // return {
    //     key: curKey,
    //     name: v.nodeName ?? "",
    //     isParent: true,
    //     children: (v.states ?? []).map((s) => {
    //         const leafPathStr = `${curPathStr}${pathSeparator}${s.stateName}`;
    //         return getCenterProcessTmplLeafNode(s, leafPathStr);
    //     }),
    //     pathStr: curPathStr
    // };
});

/**
 * 循环分页获取全部工序信息
 */
export const getAllProcessList = async (basePageParams: Omit<CompProcessListParam, "page">) => {
    let page = 1;
    const res = await getProcessListByComps({...basePageParams, page});
    const {size} = basePageParams;
    const {totalCount, items} = res.data;
    if (totalCount === undefined || totalCount === null) {
        return [];
    }
    const allProcessList = items ?? [];
    if (totalCount > size) {
        while (true) {
            const promiseList = [];
            for (let i = 0; i < 4; i++) {
                if (page * size >= totalCount) {
                    break;
                }
                page++;
                const pageParams: CompProcessListParam = {...basePageParams, page};
                promiseList.push(getProcessListByComps(pageParams));
            }
            const resList = await Promise.all(promiseList);
            resList.forEach((resItem) => {
                if (resItem.data.items !== undefined) {
                    allProcessList.push(...resItem.data.items ?? []);
                }
            });
            if (page * size >= totalCount) {
                break;
            }
        }
    }
    return allProcessList;
};

/**
 * 对选中构件的工序信息进行验证，只有工序信息完全一致的构件才能进行批量定义工序操作
 * @param allList 选中构件的全部工序信息
 */
export const validateCompProcess = (
    compLen: number,
    allList: CompProcessItem[]
): {passed: boolean; processList: CompStateParam[]; compInfo?: CompWithoutProcess} => {
    // 选中的构件无工序信息，直接校验通过
    if (allList.length === 0) {
        return {passed: true, processList: []};
    }
    if (compLen !== allList.length) {
        return {passed: false, processList: []};
    }
    // 只有一个构件的工序信息，直接校验通过
    if (allList.length === 1) {
        const {infos, ...rest} = allList[0];
        return {passed: true, processList: infos ?? [], compInfo: rest};
    }
    // 后端将每个构件对应的全部工序信息进行编码，生成MD5
    // 如果两个构件的MD5值相同，则可以认为这两个构件绑定的工序信息相同
    // 将全部构件的MD5值放到一个集合里，如果集合有且只有一个值，则可以判断这些构件对应的工序信息完全相同
    const md5Set = new Set<string>();
    allList.forEach((v) => md5Set.add(v.stateMd5));
    if (md5Set.size === 1) {
        const {infos, ...rest} = allList[0];
        return {passed: true, processList: infos ?? [], compInfo: rest};
    }

    // 除以上的几种情况外，其余为校验未通过
    return {passed: false, processList: []};
};

/** 接口返回的时间戳，如果没有值，会返回-1或者0，这里做个兼容处理 */
export const isValidDate = (time?: number): time is number => typeof time === "number" && time !== -1 && time !== 0;


export const getProcessItemStatus = (item: CompStateParam) => {
    const {startDate, endDate} = item.lifeCycles;
    if (isValidDate(startDate) && isValidDate(endDate)) {
        return ProcessStatus.Done;
    }
    if (isValidDate(startDate)) {
        return ProcessStatus.Doing;
    }
    return ProcessStatus.Undo;
};

/** 获取工序状态对应的名称 */
export const getProcessStatusName = (status: ProcessStatus) => {
    let name = "";
    switch (status) {
        case ProcessStatus.Undo:
            name = "未开始";
            break;
        case ProcessStatus.Doing:
            name = "进行中";
            break;
        case ProcessStatus.Done:
            name = "已完成";
            break;
        default:
            name = "未知状态";
            break;
    }
    return name;
};

/** 工序完工状态定义：有实际开始时间且有实际结束时间 */
export const isProcessFinished = (data: CompStateParam) => isValidDate(data.lifeCycles.startDate) && isValidDate(data.lifeCycles.endDate);

/**
 * 获取构件绑定的工序列表中最晚完工的工序
 * @param processData 构件绑定的所有工序
 * @return 当且仅当所有工序都完工时，才返回实际结束时间最晚的工序，否则返回undefined
 */
export const getLatestFinishedProcess = (processData: CompStateParam[]) => {
    if (processData.length === 0) {
        return undefined;
    }
    const isAllProcessFinished = processData.every((v) => isProcessFinished(v));
    if (!isAllProcessFinished) {
        return undefined;
    }
    let latestEndDate = processData[0].lifeCycles.endDate;
    let latestProcess = processData[0];
    for (let i = 0; i < processData.length; i++) {
        const {lifeCycles: {endDate}} = processData[i];
        if (moment(endDate) > moment(latestEndDate)) {
            latestEndDate = endDate;
            latestProcess = processData[i];
        }
    }
    return latestProcess;
};

/** 获取一组工序中最早的开始时间（包含计划开始时间和实际开始时间），和最晚的结束时间（包含计划结束时间和实际结束时间），以及时间间隔 */
export const getMaxAndMinDate = (list: CompStateParam[]): ProcessStartAndEnd => {
    if (list.length === 0) {
        return {interval: 0, earliestTime: moment(), latestTime: moment()};
    }
    const startTimes: Moment[] = [];
    const endTimes: Moment[] = [];
    list.forEach((v) => {
        const {startDate, endDate, planStartDate, planEndDate} = v.lifeCycles;
        if (isValidDate(startDate)) {
            startTimes.push(moment(startDate));
            endTimes.push(isValidDate(endDate) ? moment(endDate) : moment().endOf("day"));
        }
        if (isValidDate(planStartDate)) {
            startTimes.push(moment(planStartDate));
        }
        if (isValidDate(planEndDate)) {
            endTimes.push(moment(planEndDate));
        }
    });
    const earliestTime = moment.min(...startTimes);
    const latestTime = moment.max(...endTimes);
    const interval = latestTime.diff(earliestTime, "days");
    return {interval: Math.abs(interval) + 1, earliestTime, latestTime};
};

const getPercentByInterval = (curInterval: number, totalInterval: number) => {
    if (totalInterval === 0) {
        return 100;
    }
    if (curInterval === 0) {
        return getPercent(0.5, totalInterval);
    }
    return getPercent(curInterval, totalInterval);
};

/**
 * 获取单个工序的进度条相关信息，包含计划时间间隔、实际时间间隔、偏移量
 * @param item 单个工序的具体信息
 * @param startAndEnd 全部工序的最早开始时间、最晚结束时间、间隔天数
 */
export const getProcessProgressInfo = (
    item: CompStateParam,
    startAndEnd: ProcessStartAndEnd
): ProcessProgressInfo => {
    const {lifeCycles: {startDate, endDate, planStartDate, planEndDate}} = item;
    const {interval, earliestTime} = startAndEnd;
    const startTime = isValidDate(startDate) ? moment(startDate) : moment();
    const endTime = isValidDate(endDate) ? moment(endDate) : moment();
    const planStartTime = moment(planStartDate);
    const planEndTime = moment(planEndDate);

    const timeInterval = Math.abs(endTime.diff(startTime, "days")) + 1;
    const planTimeInterval = Math.abs(planEndTime.diff(planStartTime, "days")) + 1;
    const timePer = getPercentByInterval(timeInterval, interval);
    const planTimePer = getPercentByInterval(planTimeInterval, interval);
    const timeOffset = getPercent(startTime.diff(earliestTime, "days"), interval);
    const planTimeOffset = getPercent(planStartTime.diff(earliestTime, "days"), interval);
    return {
        hasStartTime: isValidDate(startDate),
        timeInterval,
        planTimeInterval,
        timePer,
        planTimePer,
        timeOffset,
        planTimeOffset
    };
};
