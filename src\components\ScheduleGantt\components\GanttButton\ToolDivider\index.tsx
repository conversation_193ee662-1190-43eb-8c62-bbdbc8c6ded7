import React from "react";
import {Divider} from "antd";

const ToolDivider = () => <Divider type="vertical" style={{height: 16, background: "#E1E2E5"}} />;
// const ToolDivider = () => {
//     const {checkoutStatus} = useContext(EditorContext);
//     if (checkoutStatus === false) {
//         return null;
//     }
//     return (
//         <Divider type="vertical" style={{height: 16, background: "#E1E2E5"}} />
//     );
// };
export default ToolDivider;
