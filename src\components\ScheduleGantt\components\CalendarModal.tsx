import React, {FC, memo, useState, useEffect, useCallback, useContext} from "react";
import {Modal, Select, Calendar, message} from "antd";
import {createUseStyles} from "react-jss";
import moment from "moment";
import {isNil} from "lodash-es";
import {LabeledValue} from "antd/lib/select";
import {CalendarType} from "../common/calendar";
import {WebRes} from "../../../api/common.type";
import {getCalendarInfo, getCalendarList, updatePlanInfo} from "../../../api/Preparation";
import {CalendarListItem} from "../../../api/Preparation/type";
import EditorContext from "../views/GanttEditor/context";
import {buildCalenderInfo} from "../tool/planUtils";
import {CalendarInfo} from "../api/plan/type";

export interface CalendarModalProps {
    planId: string;
    visible: boolean;
    setVisible: (visible: boolean) => void;
    // calendars: CalendarInfo[];
    // currentCalendar?: CalendarInfo;
    onCancel?(): void;
    onConfirm?(data: CalendarInfo): void;
}

const useStyles = createUseStyles({
    mark: {
        display: "inline-block",
        width: 20,
        height: 20,
        border: "thin black solid",
        borderWidth: 1,
        margin: "0 4px"
    },
    dateCell: {
        display: "flex",
        direction: "row",
        justifyContent: "center",
    }
});

const isDateEqual = (date1: Date, date2: Date) => {
    const status1 = date1.getFullYear() === date2.getFullYear();
    const status2 = date1.getMonth() === date2.getMonth();
    const status3 = date1.getDate() === date2.getDate();
    return status1 && status2 && status3;
};

const isRestday = (date: Date, calendar: CalendarInfo) => {
    if (calendar.calendarType === CalendarType.TYPE_24_7 || calendar.calendarType === CalendarType.TYPE_CUSTOM_24_7) {
        if (isNil(calendar.restDays)) {
            return false;
        }
        return calendar.restDays.find((item) => isDateEqual(new Date(item), date)) !== undefined;
    }

    const status1 = isNil(calendar.restDays) ? false : calendar.restDays.find((item) => isDateEqual(new Date(item), date)) !== undefined;
    const status2 = date.getDay() === 0 || date.getDay() === 6;
    const status3 = isNil(calendar.workDays) || calendar.workDays.find((item) => isDateEqual(new Date(item), date)) === undefined;

    return status1 || (status2 && status3);
};

// const calendarNewName = (calendar: CalendarInfo) => {
//     let {restDayName} = calendar;
//     if (isCustomCalendar(calendar) && calendar.isTemplate) {
//         restDayName += "（复制）";
//     }
//     return restDayName;
// };

const CalendarModal: FC<CalendarModalProps> = memo((props) => {
    const {onCancel, visible, setVisible, onConfirm, planId} = props;
    const {mark, dateCell} = useStyles();
    const {fromType, planInfo, hasParentPlan, currentCalendarRef} = useContext(EditorContext);
    // const [state, setState] = useState<CalendarState>({
    //     calendar: CalendarSelectOptionsWithCalendars(calendars),
    //     selectedCalendarKey: calendarKey(currentCalendar!)
    // });
    const [calendarOptions, setCalendarOptions] = useState<LabeledValue[]>([]);
    const [selectedCalendar, setSelectedCalendar] = useState<CalendarInfo>();

    const fetchCalendarList = useCallback(async () => {
        const res: WebRes<CalendarListItem[]> = await getCalendarList();
        if (res.success) {
            if (res.data !== null) {
                const options = res.data.map((i) => ({...i, label: i.ctName, value: i.ctid}));
                setCalendarOptions(options);
            }
        }
    }, []);

    useEffect(() => {
        fetchCalendarList();
    }, [fetchCalendarList]);

    const onOk = () => {
        if (selectedCalendar !== undefined) {
            if (selectedCalendar.ctid !== currentCalendarRef.current?.ctid) {
                Modal.confirm({
                    title: "进度计划数据按日历日期刷新！",
                    okText: "确定",
                    cancelText: "取消",
                    onOk: () => {
                        updatePlanInfo({
                            id: planId,
                            parentId: planInfo.parentId,
                            calendar: selectedCalendar.ctid,
                        }).then((res) => {
                            if (res.success) {
                                onConfirm!(selectedCalendar);
                            } else {
                                message.info({content: res.msg});
                            }
                        });
                    }
                });
            } else {
                setVisible(false);
            }
        }
    };

    const dateCellRender = (date: moment.Moment) => {
        if (isNil(selectedCalendar)) {
            return;
        }
        const day = date.date();
        const dayStr = day < 10 ? `0${String(day)}` : String(day);
        const colorStyle = isRestday(date.toDate(), selectedCalendar) ? {backgroundColor: "gray"} : {backgroundColor: "white"};
        return (
            <div className={dateCell}>
                <div style={{width: 24, height: 24, ...colorStyle}}>{dayStr}</div>
            </div>
        );
    };

    const handleSelectCalendar = async (value: string) => {
        const {data: calendar} = await getCalendarInfo(value);
        setSelectedCalendar(buildCalenderInfo(calendar));
    };

    useEffect(() => {
        if (visible && currentCalendarRef.current !== undefined) {
            setSelectedCalendar(currentCalendarRef.current);
        }
    }, [visible, currentCalendarRef]);

    return (
        <>
            <Modal title={<span style={{fontWeight: "bold"}}>工作日历</span>} visible={visible} onCancel={onCancel} onOk={onOk} destroyOnClose>
                <div style={{display: "flex", justifyContent: "space-between"}}>
                    <div style={{display: "flex", alignItems: "center"}}>
                        <span>日历名称：</span>
                        <Select
                            disabled={fromType !== "plan" || hasParentPlan} // 如果关联了上级计划，不允许修改日历
                            style={{width: 300}}
                            options={calendarOptions}
                            value={selectedCalendar?.ctid}
                            onChange={handleSelectCalendar}
                        />
                    </div>
                </div>
                <div>
                    <Calendar fullscreen={false} dateFullCellRender={dateCellRender} />
                    <div style={{display: "flex", alignItems: "center"}}>
                        <span>图例：</span>
                        <span className={mark} style={{backgroundColor: "gray"}} />
                        <span>非工作日</span>
                        <span className={mark} style={{backgroundColor: "white"}} />
                        <span>工作日</span>
                    </div>
                </div>
            </Modal>
        </>
    );
});

export default CalendarModal;
