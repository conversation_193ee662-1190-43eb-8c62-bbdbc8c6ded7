{"systemParams": "win32-x64-115", "modulesFolders": ["node_modules"], "flags": ["ignoreScripts"], "linkedModules": [], "topLevelPatterns": ["@ant-design/icons@^4.3.0", "@commitlint/cli@^11.0.0", "@commitlint/config-conventional@^11.0.0", "@daihy8759/protocol-check@^1.0.4", "@iworks/dhtmlx-gantt@^7.0.9", "@iworks/iworksframe@^1.12.6", "@iworks/login@1.10.7", "@iworks/plan@1.11.0-feat-20220330.10", "@loadable/component@^5.14.1", "@luban/devenv@^1.3.0", "@luban/linter@1.10", "@luban/react-open-tab@^1.2.2", "@lubango/webpack@^1.1.14", "@motor/core@3.4.4-2.4.0-Release", "@types/ali-oss@^6.16.4", "@types/backbone@^1.4.15", "@types/dagre@^0.7.47", "@types/file-saver@^2.0.5", "@types/jspdf@^2.0.0", "@types/loadable__component@^5.13.3", "@types/lodash-es@^4.17.6", "@types/postmate@^1.5.2", "@types/react-color@^3.0.6", "@types/react-dom@^17.0.0", "@types/react-helmet@^6.1.5", "@types/react-portal@^4.0.4", "@types/react-router-config@^5.0.2", "@types/react-router-dom@^5.1.6", "@types/react-virtualized@^9.21.21", "@types/react-window@^1.8.5", "@types/react@^17.0.3", "@types/spark-md5@^3.0.2", "@types/video.js@^7.3.44", "ahooks@^3.5.2", "ali-oss@^6.17.1", "antd@4.18.9", "archiver@^5.0.0", "clsx@^1.1.1", "compressorjs@^1.1.1", "cross-env@5.0.5", "crypto-js@^4.1.1", "dagre@^0.8.5", "exceljs@^4.4.0", "file-saver@^2.0.5", "husky@^4.3.7", "increase-memory-limit@1.0.3", "jointjs@^3.5.5", "js-md5@^0.7.3", "jspdf@^2.5.1", "jss-preset-default@^10.9.0", "jss@^10.9.0", "lint-staged@^10.5.3", "lodash-es@^4.17.21", "moment@^2.29.1", "nanoid@^3.3.2", "postmate@^1.5.2", "query-string@^7.1.1", "rc-resize-observer@^1.2.1", "react-color@^2.19.3", "react-dom@^16.13.1", "react-helmet@^6.1.0", "react-jss@^10.6.0", "react-portal@^4.2.2", "react-redux@^7.2.3", "react-router-config@^5.1.1", "react-router-dom@^5.2.0", "react-virtualized@^9.22.3", "react-window@^1.8.8", "react@^16.13.1", "redux-dynamic-modules-thunk@^5.2.3", "redux-dynamic-modules@^5.2.3", "redux-persist@^6.0.0", "redux-thunk@^2.4.1", "redux@^4.0.5", "request-promise@^4.2.6", "request@^2.88.2", "reselect@^4.1.6", "spark-md5@^3.0.2", "typescript@3.9", "uuid@^8.3.2", "video.js@^7.20.2"], "lockfileEntries": {"@ampproject/remapping@^2.1.0": "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.1.2.tgz", "@ant-design/colors@^6.0.0": "https://registry.npmmirror.com/@ant-design/colors/-/colors-6.0.0.tgz", "@ant-design/icons-svg@^4.2.1": "https://registry.npmmirror.com/@ant-design/icons-svg/-/icons-svg-4.2.1.tgz", "@ant-design/icons@^4.2.2": "https://registry.npmmirror.com/@ant-design/icons/-/icons-4.7.0.tgz", "@ant-design/icons@^4.3.0": "https://registry.npmmirror.com/@ant-design/icons/-/icons-4.7.0.tgz", "@ant-design/icons@^4.7.0": "https://registry.npmmirror.com/@ant-design/icons/-/icons-4.7.0.tgz", "@ant-design/react-slick@~0.28.1": "https://registry.npmmirror.com/@ant-design/react-slick/-/react-slick-0.28.4.tgz", "@ant-design/react-slick@~0.29.1": "https://registry.npmmirror.com/@ant-design/react-slick/-/react-slick-0.29.2.tgz", "@babel/cli@^7.11.6": "https://registry.npmmirror.com/@babel/cli/-/cli-7.17.6.tgz", "@babel/code-frame@7.12.11": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.12.11.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.16.7.tgz", "@babel/code-frame@^7.16.7": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.16.7.tgz", "@babel/code-frame@^7.8.3": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.16.7.tgz", "@babel/compat-data@^7.13.11": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.17.7.tgz", "@babel/compat-data@^7.16.8": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.17.7.tgz", "@babel/compat-data@^7.17.0": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.17.7.tgz", "@babel/compat-data@^7.17.7": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.17.7.tgz", "@babel/core@^7.11.6": "https://registry.npmmirror.com/@babel/core/-/core-7.17.8.tgz", "@babel/generator@^7.17.3": "https://registry.npmmirror.com/@babel/generator/-/generator-7.17.7.tgz", "@babel/generator@^7.17.7": "https://registry.npmmirror.com/@babel/generator/-/generator-7.17.7.tgz", "@babel/helper-annotate-as-pure@^7.16.7": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.16.7.tgz", "@babel/helper-builder-binary-assignment-operator-visitor@^7.16.7": "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.16.7.tgz", "@babel/helper-compilation-targets@^7.13.0": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.7.tgz", "@babel/helper-compilation-targets@^7.16.7": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.7.tgz", "@babel/helper-compilation-targets@^7.17.7": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.17.7.tgz", "@babel/helper-create-class-features-plugin@^7.16.10": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz", "@babel/helper-create-class-features-plugin@^7.16.7": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz", "@babel/helper-create-class-features-plugin@^7.17.6": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.17.6.tgz", "@babel/helper-create-regexp-features-plugin@^7.16.7": "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.17.0.tgz", "@babel/helper-define-polyfill-provider@^0.3.1": "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.3.1.tgz", "@babel/helper-environment-visitor@^7.16.7": "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.16.7.tgz", "@babel/helper-explode-assignable-expression@^7.16.7": "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.16.7.tgz", "@babel/helper-function-name@^7.16.7": "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.16.7.tgz", "@babel/helper-get-function-arity@^7.16.7": "https://registry.npmmirror.com/@babel/helper-get-function-arity/-/helper-get-function-arity-7.16.7.tgz", "@babel/helper-hoist-variables@^7.16.7": "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.16.7.tgz", "@babel/helper-member-expression-to-functions@^7.16.7": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.17.7.tgz", "@babel/helper-module-imports@^7.12.13": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz", "@babel/helper-module-imports@^7.16.7": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz", "@babel/helper-module-transforms@^7.16.7": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.17.7.tgz", "@babel/helper-module-transforms@^7.17.7": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.17.7.tgz", "@babel/helper-optimise-call-expression@^7.16.7": "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.16.7.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.13.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.16.7": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-plugin-utils@^7.8.3": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.16.7.tgz", "@babel/helper-remap-async-to-generator@^7.16.8": "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.16.8.tgz", "@babel/helper-replace-supers@^7.16.7": "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.16.7.tgz", "@babel/helper-simple-access@^7.17.7": "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.17.7.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.16.0": "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.16.0.tgz", "@babel/helper-split-export-declaration@^7.16.7": "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.16.7.tgz", "@babel/helper-validator-identifier@^7.16.7": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.16.7.tgz", "@babel/helper-validator-option@^7.16.7": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz", "@babel/helper-wrap-function@^7.16.8": "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.16.8.tgz", "@babel/helpers@^7.17.8": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.17.8.tgz", "@babel/highlight@^7.10.4": "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.16.10.tgz", "@babel/highlight@^7.16.7": "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.16.10.tgz", "@babel/parser@^7.16.7": "https://registry.npmmirror.com/@babel/parser/-/parser-7.17.8.tgz", "@babel/parser@^7.17.3": "https://registry.npmmirror.com/@babel/parser/-/parser-7.17.8.tgz", "@babel/parser@^7.17.8": "https://registry.npmmirror.com/@babel/parser/-/parser-7.17.8.tgz", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.7.tgz", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.7.tgz", "@babel/plugin-proposal-async-generator-functions@^7.16.8": "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.16.8.tgz", "@babel/plugin-proposal-class-properties@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz", "@babel/plugin-proposal-class-properties@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.16.7.tgz", "@babel/plugin-proposal-class-static-block@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-class-static-block/-/plugin-proposal-class-static-block-7.17.6.tgz", "@babel/plugin-proposal-decorators@^7.10.5": "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.17.8.tgz", "@babel/plugin-proposal-dynamic-import@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/-/plugin-proposal-dynamic-import-7.16.7.tgz", "@babel/plugin-proposal-export-default-from@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.16.7.tgz", "@babel/plugin-proposal-export-namespace-from@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-export-namespace-from/-/plugin-proposal-export-namespace-from-7.16.7.tgz", "@babel/plugin-proposal-json-strings@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.16.7.tgz", "@babel/plugin-proposal-logical-assignment-operators@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-logical-assignment-operators/-/plugin-proposal-logical-assignment-operators-7.16.7.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.14.2": "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.16.7.tgz", "@babel/plugin-proposal-numeric-separator@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.16.7.tgz", "@babel/plugin-proposal-object-rest-spread@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.17.3.tgz", "@babel/plugin-proposal-optional-catch-binding@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.16.7.tgz", "@babel/plugin-proposal-optional-chaining@^7.14.2": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.7.tgz", "@babel/plugin-proposal-optional-chaining@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.16.7.tgz", "@babel/plugin-proposal-private-methods@^7.16.11": "https://registry.npmmirror.com/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.16.11.tgz", "@babel/plugin-proposal-private-property-in-object@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.16.7.tgz", "@babel/plugin-proposal-unicode-property-regex@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.7.tgz", "@babel/plugin-proposal-unicode-property-regex@^7.4.4": "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.16.7.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-class-properties@^7.12.13": "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-static-block@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "@babel/plugin-syntax-decorators@^7.17.0": "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.17.0.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-export-default-from@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.16.7.tgz", "@babel/plugin-syntax-export-namespace-from@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.7.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.10.4": "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-private-property-in-object@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.14.5": "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.16.7.tgz", "@babel/plugin-transform-arrow-functions@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.16.7.tgz", "@babel/plugin-transform-async-to-generator@^7.16.8": "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.16.8.tgz", "@babel/plugin-transform-block-scoped-functions@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.16.7.tgz", "@babel/plugin-transform-block-scoping@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.16.7.tgz", "@babel/plugin-transform-classes@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.16.7.tgz", "@babel/plugin-transform-computed-properties@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.16.7.tgz", "@babel/plugin-transform-destructuring@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.17.7.tgz", "@babel/plugin-transform-dotall-regex@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz", "@babel/plugin-transform-dotall-regex@^7.4.4": "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.16.7.tgz", "@babel/plugin-transform-duplicate-keys@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.16.7.tgz", "@babel/plugin-transform-exponentiation-operator@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.16.7.tgz", "@babel/plugin-transform-for-of@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.16.7.tgz", "@babel/plugin-transform-function-name@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.16.7.tgz", "@babel/plugin-transform-literals@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.16.7.tgz", "@babel/plugin-transform-member-expression-literals@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.16.7.tgz", "@babel/plugin-transform-modules-amd@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.16.7.tgz", "@babel/plugin-transform-modules-commonjs@^7.16.8": "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.17.7.tgz", "@babel/plugin-transform-modules-systemjs@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.17.8.tgz", "@babel/plugin-transform-modules-umd@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.16.7.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.16.8": "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.16.8.tgz", "@babel/plugin-transform-new-target@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.16.7.tgz", "@babel/plugin-transform-object-super@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.16.7.tgz", "@babel/plugin-transform-parameters@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.16.7.tgz", "@babel/plugin-transform-property-literals@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.16.7.tgz", "@babel/plugin-transform-react-display-name@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.16.7.tgz", "@babel/plugin-transform-react-jsx-development@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.16.7.tgz", "@babel/plugin-transform-react-jsx@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.17.3.tgz", "@babel/plugin-transform-react-pure-annotations@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.16.7.tgz", "@babel/plugin-transform-regenerator@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.16.7.tgz", "@babel/plugin-transform-reserved-words@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.16.7.tgz", "@babel/plugin-transform-runtime@^7.11.5": "https://registry.npmmirror.com/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.17.0.tgz", "@babel/plugin-transform-shorthand-properties@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.16.7.tgz", "@babel/plugin-transform-spread@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.16.7.tgz", "@babel/plugin-transform-sticky-regex@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.16.7.tgz", "@babel/plugin-transform-template-literals@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.16.7.tgz", "@babel/plugin-transform-typeof-symbol@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.16.7.tgz", "@babel/plugin-transform-typescript@^7.12.1": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz", "@babel/plugin-transform-typescript@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.16.8.tgz", "@babel/plugin-transform-unicode-escapes@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.16.7.tgz", "@babel/plugin-transform-unicode-regex@^7.16.7": "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.16.7.tgz", "@babel/preset-env@^7.11.5": "https://registry.npmmirror.com/@babel/preset-env/-/preset-env-7.16.11.tgz", "@babel/preset-modules@^0.1.5": "https://registry.npmmirror.com/@babel/preset-modules/-/preset-modules-0.1.5.tgz", "@babel/preset-react@^7.10.4": "https://registry.npmmirror.com/@babel/preset-react/-/preset-react-7.16.7.tgz", "@babel/preset-typescript@^7.10.4": "https://registry.npmmirror.com/@babel/preset-typescript/-/preset-typescript-7.16.7.tgz", "@babel/runtime-corejs3@^7.11.2": "https://registry.npmmirror.com/@babel/runtime-corejs3/-/runtime-corejs3-7.17.8.tgz", "@babel/runtime@^7.0.0": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.20.13.tgz#7055ab8a7cff2b8f6058bf6ae45ff84ad2aded4b", "@babel/runtime@^7.1.2": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.10.1": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.10.2": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.10.4": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.11.1": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.11.2": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.12.1": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.12.13": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.12.5": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.14.0": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.9.tgz", "@babel/runtime@^7.15.4": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.18.0": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.20.13.tgz#7055ab8a7cff2b8f6058bf6ae45ff84ad2aded4b", "@babel/runtime@^7.18.3": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.3.tgz", "@babel/runtime@^7.20.7": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.20.13.tgz#7055ab8a7cff2b8f6058bf6ae45ff84ad2aded4b", "@babel/runtime@^7.3.1": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.4.5": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.5.5": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.9.tgz", "@babel/runtime@^7.7.2": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.9.tgz", "@babel/runtime@^7.7.6": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.20.6.tgz", "@babel/runtime@^7.7.7": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.8.3": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.8.4": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/runtime@^7.8.7": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.18.9.tgz", "@babel/runtime@^7.9.2": "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.17.8.tgz", "@babel/template@^7.16.7": "https://registry.npmmirror.com/@babel/template/-/template-7.16.7.tgz", "@babel/traverse@^7.13.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.17.3.tgz", "@babel/traverse@^7.16.7": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.17.3.tgz", "@babel/traverse@^7.16.8": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.17.3.tgz", "@babel/traverse@^7.17.3": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.17.3.tgz", "@babel/types@^7.16.0": "https://registry.npmmirror.com/@babel/types/-/types-7.17.0.tgz", "@babel/types@^7.16.7": "https://registry.npmmirror.com/@babel/types/-/types-7.17.0.tgz", "@babel/types@^7.16.8": "https://registry.npmmirror.com/@babel/types/-/types-7.17.0.tgz", "@babel/types@^7.17.0": "https://registry.npmmirror.com/@babel/types/-/types-7.17.0.tgz", "@babel/types@^7.4.4": "https://registry.npmmirror.com/@babel/types/-/types-7.17.0.tgz", "@commitlint/cli@^11.0.0": "https://registry.npmmirror.com/@commitlint/cli/-/cli-11.0.0.tgz", "@commitlint/config-conventional@^11.0.0": "https://registry.npmmirror.com/@commitlint/config-conventional/-/config-conventional-11.0.0.tgz", "@commitlint/ensure@^11.0.0": "https://registry.npmmirror.com/@commitlint/ensure/-/ensure-11.0.0.tgz", "@commitlint/execute-rule@^11.0.0": "https://registry.npmmirror.com/@commitlint/execute-rule/-/execute-rule-11.0.0.tgz", "@commitlint/format@^11.0.0": "https://registry.npmmirror.com/@commitlint/format/-/format-11.0.0.tgz", "@commitlint/is-ignored@^11.0.0": "https://registry.npmmirror.com/@commitlint/is-ignored/-/is-ignored-11.0.0.tgz", "@commitlint/lint@^11.0.0": "https://registry.npmmirror.com/@commitlint/lint/-/lint-11.0.0.tgz", "@commitlint/load@^11.0.0": "https://registry.npmmirror.com/@commitlint/load/-/load-11.0.0.tgz", "@commitlint/message@^11.0.0": "https://registry.npmmirror.com/@commitlint/message/-/message-11.0.0.tgz", "@commitlint/parse@^11.0.0": "https://registry.npmmirror.com/@commitlint/parse/-/parse-11.0.0.tgz", "@commitlint/read@^11.0.0": "https://registry.npmmirror.com/@commitlint/read/-/read-11.0.0.tgz", "@commitlint/resolve-extends@^11.0.0": "https://registry.npmmirror.com/@commitlint/resolve-extends/-/resolve-extends-11.0.0.tgz", "@commitlint/rules@^11.0.0": "https://registry.npmmirror.com/@commitlint/rules/-/rules-11.0.0.tgz", "@commitlint/to-lines@^11.0.0": "https://registry.npmmirror.com/@commitlint/to-lines/-/to-lines-11.0.0.tgz", "@commitlint/top-level@^11.0.0": "https://registry.npmmirror.com/@commitlint/top-level/-/top-level-11.0.0.tgz", "@commitlint/types@^11.0.0": "https://registry.npmmirror.com/@commitlint/types/-/types-11.0.0.tgz", "@ctrl/tinycolor@^3.4.0": "https://registry.npmmirror.com/@ctrl/tinycolor/-/tinycolor-3.4.0.tgz", "@daihy8759/protocol-check@^1.0.4": "https://registry.npmmirror.com/@daihy8759/protocol-check/-/protocol-check-1.0.4.tgz", "@emotion/is-prop-valid@^0.7.3": "https://registry.npmmirror.com/@emotion/is-prop-valid/-/is-prop-valid-0.7.3.tgz", "@emotion/memoize@0.7.1": "https://registry.npmmirror.com/@emotion/memoize/-/memoize-0.7.1.tgz", "@eslint/eslintrc@^0.4.3": "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-0.4.3.tgz", "@fast-csv/format@4.3.5": "http://nexus3.luban.fit/repository/npm/@fast-csv/format/-/format-4.3.5.tgz#90d83d1b47b6aaf67be70d6118f84f3e12ee1ff3", "@fast-csv/parse@4.3.6": "http://nexus3.luban.fit/repository/npm/@fast-csv/parse/-/parse-4.3.6.tgz#ee47d0640ca0291034c7aa94039a744cfb019264", "@gar/promisify@^1.0.1": "https://registry.npmmirror.com/@gar/promisify/-/promisify-1.1.3.tgz", "@humanwhocodes/config-array@^0.5.0": "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.5.0.tgz", "@humanwhocodes/object-schema@^1.2.0": "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "@icons/material@^0.2.4": "https://registry.npmmirror.com/@icons/material/-/material-0.2.4.tgz", "@iworks/dhtmlx-gantt@^7.0.9": "http://192.168.3.17:8081/repository/npm-lball/@iworks/dhtmlx-gantt/-/dhtmlx-gantt-7.0.9.tgz", "@iworks/iworksframe@^1.12.6": "http://192.168.3.17:8081/repository/npm-lball/@iworks/iworksframe/-/iworksframe-1.12.6.tgz", "@iworks/login@1.10.7": "http://192.168.3.17:8081/repository/npm-lball/@iworks/login/-/login-1.10.7.tgz", "@iworks/plan@1.11.0-feat-20220330.10": "http://192.168.3.17:8081/repository/npm-lball/@iworks/plan/-/plan-1.11.0-feat-20220330.10.tgz", "@jridgewell/resolve-uri@^3.0.3": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.0.5.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.11.tgz", "@jridgewell/trace-mapping@^0.3.0": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.4.tgz", "@jridgewell/trace-mapping@^0.3.4": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.4.tgz", "@loadable/component@5.14.1": "https://registry.npmmirror.com/@loadable/component/-/component-5.14.1.tgz", "@loadable/component@^5.14.1": "https://registry.npmmirror.com/@loadable/component/-/component-5.15.2.tgz", "@luban/devenv@^1.3.0": "http://192.168.3.17:8081/repository/npm-lball/@luban/devenv/-/devenv-1.4.4.tgz", "@luban/linter@1.10": "http://192.168.3.17:8081/repository/npm-lball/@luban/linter/-/linter-1.10.0.tgz", "@luban/react-open-tab@^1.2.2": "http://192.168.3.17:8081/repository/npm-lball/@luban/react-open-tab/-/react-open-tab-1.2.2.tgz", "@lubango/webpack@^1.1.14": "http://192.168.3.17:8081/repository/npm-lball/@lubango/webpack/-/webpack-1.1.14.tgz", "@motor/core@3.4.4-2.4.0-Release": "http://192.168.3.17:8081/repository/npm-lball/@motor/core/-/core-3.4.4-2.4.0-Release.tgz", "@nicolo-ribaudo/chokidar-2@2.1.8-no-fsevents.3": "https://registry.npmmirror.com/@nicolo-ribaudo/chokidar-2/-/chokidar-2-2.1.8-no-fsevents.3.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@npmcli/fs@^1.0.0": "https://registry.npmmirror.com/@npmcli/fs/-/fs-1.1.1.tgz", "@npmcli/move-file@^1.0.1": "https://registry.npmmirror.com/@npmcli/move-file/-/move-file-1.1.2.tgz", "@pmmmwh/react-refresh-webpack-plugin@^0.4.3": "https://registry.npmmirror.com/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.4.3.tgz", "@polka/url@^1.0.0-next.20": "https://registry.npmmirror.com/@polka/url/-/url-1.0.0-next.21.tgz", "@rollup/plugin-image@^2.1.1": "https://registry.npmmirror.com/@rollup/plugin-image/-/plugin-image-2.1.1.tgz", "@rollup/pluginutils@^3.1.0": "https://registry.npmmirror.com/@rollup/pluginutils/-/pluginutils-3.1.0.tgz", "@tootallnate/once@1": "https://registry.npmmirror.com/@tootallnate/once/-/once-1.1.2.tgz", "@types/ali-oss@^6.16.4": "https://registry.npmmirror.com/@types/ali-oss/-/ali-oss-6.16.4.tgz", "@types/backbone@^1.4.15": "https://registry.npmmirror.com/@types/backbone/-/backbone-1.4.15.tgz", "@types/crypto-js@3.1.47": "https://registry.npmmirror.com/@types/crypto-js/-/crypto-js-3.1.47.tgz", "@types/dagre@^0.7.47": "https://registry.npmmirror.com/@types/dagre/-/dagre-0.7.47.tgz", "@types/eslint-visitor-keys@^1.0.0": "https://registry.npmmirror.com/@types/eslint-visitor-keys/-/eslint-visitor-keys-1.0.0.tgz", "@types/estree@0.0.39": "https://registry.npmmirror.com/@types/estree/-/estree-0.0.39.tgz", "@types/file-saver@^2.0.5": "https://registry.npmmirror.com/@types/file-saver/-/file-saver-2.0.5.tgz", "@types/glob@^7.1.1": "https://registry.npmmirror.com/@types/glob/-/glob-7.2.0.tgz", "@types/history@*": "https://registry.npmmirror.com/@types/history/-/history-5.0.0.tgz", "@types/history@^4.7.11": "https://registry.npmmirror.com/@types/history/-/history-4.7.11.tgz", "@types/hoist-non-react-statics@^3.3.0": "https://registry.npmmirror.com/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.1.tgz", "@types/html-minifier-terser@^5.0.0": "https://registry.npmmirror.com/@types/html-minifier-terser/-/html-minifier-terser-5.1.2.tgz", "@types/jquery@*": "https://registry.npmmirror.com/@types/jquery/-/jquery-3.5.14.tgz", "@types/js-cookie@^2.x.x": "https://registry.npmmirror.com/@types/js-cookie/-/js-cookie-2.2.7.tgz", "@types/js-md5@^0.4.2": "https://registry.npmmirror.com/@types/js-md5/-/js-md5-0.4.3.tgz", "@types/json-schema@^7.0.3": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "@types/json-schema@^7.0.4": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "@types/json-schema@^7.0.5": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "@types/json-schema@^7.0.7": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "@types/json-schema@^7.0.8": "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.11.tgz", "@types/jspdf@^2.0.0": "https://registry.npmmirror.com/@types/jspdf/-/jspdf-2.0.0.tgz", "@types/loadable__component@5.13.3": "https://registry.npmmirror.com/@types/loadable__component/-/loadable__component-5.13.3.tgz", "@types/loadable__component@^5.13.3": "https://registry.npmmirror.com/@types/loadable__component/-/loadable__component-5.13.4.tgz", "@types/lodash-es@^4.17.3": "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.6.tgz", "@types/lodash-es@^4.17.6": "https://registry.npmmirror.com/@types/lodash-es/-/lodash-es-4.17.6.tgz", "@types/lodash@*": "https://registry.npmmirror.com/@types/lodash/-/lodash-4.14.181.tgz", "@types/minimatch@*": "https://registry.npmmirror.com/@types/minimatch/-/minimatch-3.0.5.tgz", "@types/minimist@^1.2.0": "https://registry.npmmirror.com/@types/minimist/-/minimist-1.2.2.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/-/node-17.0.23.tgz", "@types/node@^14.0.1": "http://nexus3.luban.fit/repository/npm/@types/node/-/node-14.18.63.tgz#1788fa8da838dbb5f9ea994b834278205db6ca2b", "@types/node@^14.14.14": "https://registry.npmmirror.com/@types/node/-/node-14.18.12.tgz", "@types/normalize-package-data@^2.4.0": "https://registry.npmmirror.com/@types/normalize-package-data/-/normalize-package-data-2.4.1.tgz", "@types/parse-json@^4.0.0": "https://registry.npmmirror.com/@types/parse-json/-/parse-json-4.0.0.tgz", "@types/postmate@^1.5.2": "https://registry.npmmirror.com/@types/postmate/-/postmate-1.5.2.tgz", "@types/prop-types@*": "https://registry.npmmirror.com/@types/prop-types/-/prop-types-15.7.4.tgz", "@types/raf@^3.4.0": "https://registry.npmmirror.com/@types/raf/-/raf-3.4.0.tgz", "@types/react-color@^3.0.6": "https://registry.npmmirror.com/@types/react-color/-/react-color-3.0.6.tgz", "@types/react-dom@^17.0.0": "https://registry.npmmirror.com/@types/react-dom/-/react-dom-17.0.14.tgz", "@types/react-helmet@^6.1.5": "https://registry.npmmirror.com/@types/react-helmet/-/react-helmet-6.1.5.tgz", "@types/react-portal@^4.0.4": "https://registry.npmmirror.com/@types/react-portal/-/react-portal-4.0.4.tgz", "@types/react-redux@^7.1.20": "https://registry.npmmirror.com/@types/react-redux/-/react-redux-7.1.23.tgz", "@types/react-router-config@5.0.2": "https://registry.npmmirror.com/@types/react-router-config/-/react-router-config-5.0.2.tgz", "@types/react-router-config@^5.0.2": "https://registry.npmmirror.com/@types/react-router-config/-/react-router-config-5.0.6.tgz", "@types/react-router-dom@^5.1.6": "https://registry.npmmirror.com/@types/react-router-dom/-/react-router-dom-5.3.3.tgz", "@types/react-router-dom@^5.3.3": "https://registry.npmmirror.com/@types/react-router-dom/-/react-router-dom-5.3.3.tgz", "@types/react-router@*": "https://registry.npmmirror.com/@types/react-router/-/react-router-5.1.18.tgz", "@types/react-virtualized@^9.21.21": "https://registry.npmmirror.com/@types/react-virtualized/-/react-virtualized-9.21.21.tgz", "@types/react-window@^1.8.5": "https://registry.npmmirror.com/@types/react-window/-/react-window-1.8.5.tgz", "@types/react@*": "https://registry.npmmirror.com/@types/react/-/react-17.0.43.tgz", "@types/react@^17": "https://registry.npmmirror.com/@types/react/-/react-17.0.47.tgz", "@types/react@^17.0.3": "https://registry.npmmirror.com/@types/react/-/react-17.0.43.tgz", "@types/reactcss@*": "https://registry.npmmirror.com/@types/reactcss/-/reactcss-1.2.6.tgz", "@types/scheduler@*": "https://registry.npmmirror.com/@types/scheduler/-/scheduler-0.16.2.tgz", "@types/sizzle@*": "https://registry.npmmirror.com/@types/sizzle/-/sizzle-2.3.3.tgz", "@types/source-list-map@*": "https://registry.npmmirror.com/@types/source-list-map/-/source-list-map-0.1.2.tgz", "@types/spark-md5@^3.0.2": "https://registry.npmmirror.com/@types/spark-md5/-/spark-md5-3.0.2.tgz", "@types/tapable@^1": "https://registry.npmmirror.com/@types/tapable/-/tapable-1.0.8.tgz", "@types/tapable@^1.0.5": "https://registry.npmmirror.com/@types/tapable/-/tapable-1.0.8.tgz", "@types/uglify-js@*": "https://registry.npmmirror.com/@types/uglify-js/-/uglify-js-3.13.1.tgz", "@types/underscore@*": "https://registry.npmmirror.com/@types/underscore/-/underscore-1.11.4.tgz", "@types/uuid@^8.3.0": "https://registry.npmmirror.com/@types/uuid/-/uuid-8.3.4.tgz", "@types/video.js@^7.3.44": "https://registry.npmmirror.com/@types/video.js/-/video.js-7.3.44.tgz", "@types/webpack-sources@*": "https://registry.npmmirror.com/@types/webpack-sources/-/webpack-sources-3.2.0.tgz", "@types/webpack@^4.4.31": "https://registry.npmmirror.com/@types/webpack/-/webpack-4.41.32.tgz", "@types/webpack@^4.41.8": "https://registry.npmmirror.com/@types/webpack/-/webpack-4.41.32.tgz", "@typescript-eslint/eslint-plugin@^2.11.0": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-2.34.0.tgz", "@typescript-eslint/eslint-plugin@^4.10.0": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-4.33.0.tgz", "@typescript-eslint/experimental-utils@2.34.0": "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/-/experimental-utils-2.34.0.tgz", "@typescript-eslint/experimental-utils@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/-/experimental-utils-4.33.0.tgz", "@typescript-eslint/parser@^2.11.0": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-2.34.0.tgz", "@typescript-eslint/parser@^4.10.0": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-4.33.0.tgz", "@typescript-eslint/scope-manager@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/scope-manager/-/scope-manager-4.33.0.tgz", "@typescript-eslint/types@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/types/-/types-4.33.0.tgz", "@typescript-eslint/typescript-estree@2.34.0": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-2.34.0.tgz", "@typescript-eslint/typescript-estree@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/-/typescript-estree-4.33.0.tgz", "@typescript-eslint/visitor-keys@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/-/visitor-keys-4.33.0.tgz", "@videojs/http-streaming@2.14.2": "https://registry.npmmirror.com/@videojs/http-streaming/-/http-streaming-2.14.2.tgz", "@videojs/vhs-utils@3.0.5": "https://registry.npmmirror.com/@videojs/vhs-utils/-/vhs-utils-3.0.5.tgz", "@videojs/vhs-utils@^3.0.4": "https://registry.npmmirror.com/@videojs/vhs-utils/-/vhs-utils-3.0.5.tgz", "@videojs/vhs-utils@^3.0.5": "https://registry.npmmirror.com/@videojs/vhs-utils/-/vhs-utils-3.0.5.tgz", "@videojs/xhr@2.6.0": "https://registry.npmmirror.com/@videojs/xhr/-/xhr-2.6.0.tgz", "@webassemblyjs/ast@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.9.0.tgz", "@webassemblyjs/floating-point-hex-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "@webassemblyjs/helper-api-error@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "@webassemblyjs/helper-buffer@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz", "@webassemblyjs/helper-code-frame@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz", "@webassemblyjs/helper-fsm@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "@webassemblyjs/helper-module-context@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz", "@webassemblyjs/helper-wasm-bytecode@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "@webassemblyjs/helper-wasm-section@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "@webassemblyjs/ieee754@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "@webassemblyjs/leb128@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.9.0.tgz", "@webassemblyjs/utf8@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "@webassemblyjs/wasm-edit@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "@webassemblyjs/wasm-gen@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "@webassemblyjs/wasm-opt@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "@webassemblyjs/wasm-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "@webassemblyjs/wast-parser@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz", "@webassemblyjs/wast-printer@1.9.0": "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "@xmldom/xmldom@^0.7.2": "https://registry.npmmirror.com/@xmldom/xmldom/-/xmldom-0.7.5.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz", "JSONStream@^1.0.4": "https://registry.npmmirror.com/JSONStream/-/JSONStream-1.3.5.tgz", "accepts@~1.3.4": "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.5": "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.8": "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz", "acorn-jsx@^5.2.0": "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-jsx@^5.3.1": "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^8.0.0": "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.2.0.tgz", "acorn-walk@^8.2.0": "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.2.0.tgz", "acorn@^6.4.1": "https://registry.npmmirror.com/acorn/-/acorn-6.4.2.tgz", "acorn@^7.1.1": "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz", "acorn@^7.4.0": "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz", "acorn@^8.0.4": "https://registry.npmmirror.com/acorn/-/acorn-8.7.0.tgz", "acorn@^8.5.0": "https://registry.npmmirror.com/acorn/-/acorn-8.7.0.tgz", "acorn@^8.7.0": "https://registry.npmmirror.com/acorn/-/acorn-8.7.1.tgz", "address@>=0.0.1": "https://registry.npmmirror.com/address/-/address-1.2.0.tgz", "address@^1.0.0": "https://registry.npmmirror.com/address/-/address-1.2.0.tgz", "aes-decrypter@3.1.3": "https://registry.npmmirror.com/aes-decrypter/-/aes-decrypter-3.1.3.tgz", "agent-base@6": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz", "agent-base@^6.0.0": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz", "agent-base@^6.0.2": "https://registry.npmmirror.com/agent-base/-/agent-base-6.0.2.tgz", "agentkeepalive@^3.4.1": "https://registry.npmmirror.com/agentkeepalive/-/agentkeepalive-3.5.2.tgz", "aggregate-error@^3.0.0": "https://registry.npmmirror.com/aggregate-error/-/aggregate-error-3.1.0.tgz", "ahooks-v3-count@^1.0.0": "https://registry.npmmirror.com/ahooks-v3-count/-/ahooks-v3-count-1.0.0.tgz", "ahooks@^3.5.2": "https://registry.npmmirror.com/ahooks/-/ahooks-3.5.2.tgz", "ajv-errors@^1.0.0": "https://registry.npmmirror.com/ajv-errors/-/ajv-errors-1.0.1.tgz", "ajv-keywords@^3.1.0": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^3.4.1": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^3.5.2": "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv@^6.1.0": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.10.0": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.10.2": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.2": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.3": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.1": "https://registry.npmmirror.com/ajv/-/ajv-8.11.0.tgz", "ali-oss@^6.17.1": "https://registry.npmmirror.com/ali-oss/-/ali-oss-6.17.1.tgz", "ansi-align@^2.0.0": "https://registry.npmmirror.com/ansi-align/-/ansi-align-2.0.0.tgz", "ansi-colors@^3.0.0": "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-3.2.4.tgz", "ansi-colors@^4.1.1": "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.1.tgz", "ansi-escapes@^4.2.1": "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.0": "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-html-community@0.0.8": "https://registry.npmmirror.com/ansi-html-community/-/ansi-html-community-0.0.8.tgz", "ansi-html@^0.0.7": "https://registry.npmmirror.com/ansi-html/-/ansi-html-0.0.7.tgz", "ansi-regex@^2.0.0": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-2.1.1.tgz", "ansi-regex@^3.0.0": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.1.tgz", "ansi-regex@^4.1.0": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-4.1.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-styles@^2.2.1": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-2.2.1.tgz", "ansi-styles@^3.2.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz", "antd@4.18.9": "http://nexus3.luban.fit/repository/npm/antd/-/antd-4.18.9.tgz#a0d246786d5076ea7a53b67191cd39295fc2322a", "antd@^4.5.4": "https://registry.npmmirror.com/antd/-/antd-4.21.3.tgz", "any-promise@^1.0.0": "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz", "any-promise@^1.3.0": "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz", "anymatch@^2.0.0": "https://registry.npmmirror.com/anymatch/-/anymatch-2.0.0.tgz", "anymatch@^3.0.0": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz", "anymatch@~3.1.2": "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.2.tgz", "aproba@^1.1.1": "https://registry.npmmirror.com/aproba/-/aproba-1.2.0.tgz", "archiver-utils@^2.1.0": "https://registry.npmmirror.com/archiver-utils/-/archiver-utils-2.1.0.tgz", "archiver@^5.0.0": "https://registry.npmmirror.com/archiver/-/archiver-5.3.0.tgz", "argparse@^1.0.7": "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz", "aria-query@^3.0.0": "https://registry.npmmirror.com/aria-query/-/aria-query-3.0.0.tgz", "arr-diff@^4.0.0": "https://registry.npmmirror.com/arr-diff/-/arr-diff-4.0.0.tgz", "arr-flatten@^1.1.0": "https://registry.npmmirror.com/arr-flatten/-/arr-flatten-1.1.0.tgz", "arr-union@^3.1.0": "https://registry.npmmirror.com/arr-union/-/arr-union-3.1.0.tgz", "array-flatten@1.1.1": "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz", "array-flatten@^2.1.0": "https://registry.npmmirror.com/array-flatten/-/array-flatten-2.1.2.tgz", "array-ify@^1.0.0": "https://registry.npmmirror.com/array-ify/-/array-ify-1.0.0.tgz", "array-includes@^3.0.3": "https://registry.npmmirror.com/array-includes/-/array-includes-3.1.4.tgz", "array-includes@^3.1.1": "https://registry.npmmirror.com/array-includes/-/array-includes-3.1.4.tgz", "array-includes@^3.1.3": "https://registry.npmmirror.com/array-includes/-/array-includes-3.1.4.tgz", "array-includes@^3.1.4": "https://registry.npmmirror.com/array-includes/-/array-includes-3.1.4.tgz", "array-tree-filter@^2.1.0": "https://registry.npmmirror.com/array-tree-filter/-/array-tree-filter-2.1.0.tgz", "array-union@^1.0.1": "https://registry.npmmirror.com/array-union/-/array-union-1.0.2.tgz", "array-union@^2.1.0": "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz", "array-uniq@^1.0.1": "https://registry.npmmirror.com/array-uniq/-/array-uniq-1.0.3.tgz", "array-unique@^0.3.2": "https://registry.npmmirror.com/array-unique/-/array-unique-0.3.2.tgz", "array.prototype.flatmap@^1.2.5": "https://registry.npmmirror.com/array.prototype.flatmap/-/array.prototype.flatmap-1.2.5.tgz", "arrify@^1.0.1": "https://registry.npmmirror.com/arrify/-/arrify-1.0.1.tgz", "asn1.js@^5.2.0": "https://registry.npmmirror.com/asn1.js/-/asn1.js-5.4.1.tgz", "asn1@~0.2.3": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "assert-plus@1.0.0": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "assert-plus@^1.0.0": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "assert@^1.1.1": "https://registry.npmmirror.com/assert/-/assert-1.5.0.tgz", "assign-symbols@^1.0.0": "https://registry.npmmirror.com/assign-symbols/-/assign-symbols-1.0.0.tgz", "ast-types-flow@0.0.7": "https://registry.npmmirror.com/ast-types-flow/-/ast-types-flow-0.0.7.tgz", "ast-types-flow@^0.0.7": "https://registry.npmmirror.com/ast-types-flow/-/ast-types-flow-0.0.7.tgz", "ast-types@^0.13.2": "https://registry.npmmirror.com/ast-types/-/ast-types-0.13.4.tgz", "astral-regex@^1.0.0": "https://registry.npmmirror.com/astral-regex/-/astral-regex-1.0.0.tgz", "astral-regex@^2.0.0": "https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz", "async-each@^1.0.1": "https://registry.npmmirror.com/async-each/-/async-each-1.0.3.tgz", "async-limiter@~1.0.0": "https://registry.npmmirror.com/async-limiter/-/async-limiter-1.0.1.tgz", "async-validator@^4.0.2": "https://registry.npmmirror.com/async-validator/-/async-validator-4.0.7.tgz", "async-validator@^4.1.0": "https://registry.npmmirror.com/async-validator/-/async-validator-4.2.5.tgz", "async@1.5.0": "https://registry.npmmirror.com/async/-/async-1.5.0.tgz", "async@^2.6.2": "https://registry.npmmirror.com/async/-/async-2.6.3.tgz", "async@^3.2.0": "https://registry.npmmirror.com/async/-/async-3.2.3.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz", "atob@^2.1.2": "https://registry.npmmirror.com/atob/-/atob-2.1.2.tgz", "autoprefixer@^9.8.6": "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-9.8.8.tgz", "aws-sign2@~0.7.0": "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz", "aws4@^1.8.0": "https://registry.npmmirror.com/aws4/-/aws4-1.11.0.tgz", "axios@^0.21.1": "https://registry.npmmirror.com/axios/-/axios-0.21.4.tgz", "axobject-query@^2.0.2": "https://registry.npmmirror.com/axobject-query/-/axobject-query-2.2.0.tgz", "babel-code-frame@^6.26.0": "https://registry.npmmirror.com/babel-code-frame/-/babel-code-frame-6.26.0.tgz", "babel-loader@^8.1.0": "https://registry.npmmirror.com/babel-loader/-/babel-loader-8.2.4.tgz", "babel-plugin-dynamic-import-node@^2.3.3": "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz", "babel-plugin-polyfill-corejs2@^0.3.0": "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.3.1.tgz", "babel-plugin-polyfill-corejs3@^0.5.0": "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.5.2.tgz", "babel-plugin-polyfill-regenerator@^0.3.0": "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.3.1.tgz", "backbone@~1.4.1": "https://registry.npmmirror.com/backbone/-/backbone-1.4.1.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz", "base64-arraybuffer@^1.0.2": "https://registry.npmmirror.com/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz", "base64-js@^1.0.2": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.3.1": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "base@^0.11.1": "https://registry.npmmirror.com/base/-/base-0.11.2.tgz", "batch@0.6.1": "https://registry.npmmirror.com/batch/-/batch-0.6.1.tgz", "bcrypt-pbkdf@^1.0.0": "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "big-integer@^1.6.17": "http://nexus3.luban.fit/repository/npm/big-integer/-/big-integer-1.6.52.tgz#60a887f3047614a8e1bffe5d7173490a97dc8c85", "big.js@^3.1.3": "https://registry.npmmirror.com/big.js/-/big.js-3.2.0.tgz", "big.js@^5.2.2": "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz", "binary-extensions@^1.0.0": "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-1.13.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.2.0.tgz", "binary@~0.3.0": "http://nexus3.luban.fit/repository/npm/binary/-/binary-0.3.0.tgz#9f60553bc5ce8c3386f3b553cff47462adecaa79", "bindings@^1.5.0": "https://registry.npmmirror.com/bindings/-/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df", "bl@^4.0.3": "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz", "bluebird@^3.5.0": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "bluebird@^3.5.5": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "bluebird@~3.4.1": "http://nexus3.luban.fit/repository/npm/bluebird/-/bluebird-3.4.7.tgz#f72d760be09b7f76d08ed8fae98b289a8d05fab3", "blueimp-canvas-to-blob@^3.29.0": "https://registry.npmmirror.com/blueimp-canvas-to-blob/-/blueimp-canvas-to-blob-3.29.0.tgz", "bn.js@^4.0.0": "https://registry.npmmirror.com/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^4.1.0": "https://registry.npmmirror.com/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^4.11.9": "https://registry.npmmirror.com/bn.js/-/bn.js-4.12.0.tgz", "bn.js@^5.0.0": "https://registry.npmmirror.com/bn.js/-/bn.js-5.2.0.tgz", "bn.js@^5.1.1": "https://registry.npmmirror.com/bn.js/-/bn.js-5.2.0.tgz", "body-parser@1.19.2": "https://registry.npmmirror.com/body-parser/-/body-parser-1.19.2.tgz", "bonjour@^3.5.0": "https://registry.npmmirror.com/bonjour/-/bonjour-3.5.0.tgz", "boolbase@^1.0.0": "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz", "bowser@^1.6.0": "https://registry.npmmirror.com/bowser/-/bowser-1.9.4.tgz", "boxen@^1.2.1": "https://registry.npmmirror.com/boxen/-/boxen-1.3.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz", "braces@^2.3.1": "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz", "braces@^2.3.2": "https://registry.npmmirror.com/braces/-/braces-2.3.2.tgz", "braces@^3.0.2": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "braces@~3.0.2": "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz", "brorand@^1.0.1": "https://registry.npmmirror.com/brorand/-/brorand-1.1.0.tgz", "brorand@^1.1.0": "https://registry.npmmirror.com/brorand/-/brorand-1.1.0.tgz", "browserify-aes@^1.0.0": "https://registry.npmmirror.com/browserify-aes/-/browserify-aes-1.2.0.tgz", "browserify-aes@^1.0.4": "https://registry.npmmirror.com/browserify-aes/-/browserify-aes-1.2.0.tgz", "browserify-cipher@^1.0.0": "https://registry.npmmirror.com/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "browserify-des@^1.0.0": "https://registry.npmmirror.com/browserify-des/-/browserify-des-1.0.2.tgz", "browserify-rsa@^4.0.0": "https://registry.npmmirror.com/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "browserify-rsa@^4.0.1": "https://registry.npmmirror.com/browserify-rsa/-/browserify-rsa-4.1.0.tgz", "browserify-sign@^4.0.0": "https://registry.npmmirror.com/browserify-sign/-/browserify-sign-4.2.1.tgz", "browserify-zlib@^0.2.0": "https://registry.npmmirror.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "browserslist@^4.12.0": "https://registry.npmmirror.com/browserslist/-/browserslist-4.20.2.tgz", "browserslist@^4.17.5": "https://registry.npmmirror.com/browserslist/-/browserslist-4.20.2.tgz", "browserslist@^4.19.1": "https://registry.npmmirror.com/browserslist/-/browserslist-4.20.2.tgz", "btoa@^1.2.1": "https://registry.npmmirror.com/btoa/-/btoa-1.2.1.tgz", "buffer-crc32@^0.2.1": "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "buffer-crc32@^0.2.13": "https://registry.npmmirror.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz", "buffer-indexof-polyfill@~1.0.0": "http://nexus3.luban.fit/repository/npm/buffer-indexof-polyfill/-/buffer-indexof-polyfill-1.0.2.tgz#d2732135c5999c64b277fcf9b1abe3498254729c", "buffer-indexof@^1.0.0": "https://registry.npmmirror.com/buffer-indexof/-/buffer-indexof-1.1.1.tgz", "buffer-xor@^1.0.3": "https://registry.npmmirror.com/buffer-xor/-/buffer-xor-1.0.3.tgz", "buffer@^4.3.0": "https://registry.npmmirror.com/buffer/-/buffer-4.9.2.tgz", "buffer@^5.5.0": "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz", "buffers@~0.1.1": "http://nexus3.luban.fit/repository/npm/buffers/-/buffers-0.1.1.tgz#b24579c3bed4d6d396aeee6d9a8ae7f5482ab7bb", "builtin-status-codes@^3.0.0": "https://registry.npmmirror.com/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "bytes@3.0.0": "https://registry.npmmirror.com/bytes/-/bytes-3.0.0.tgz", "bytes@3.1.2": "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz", "cacache@^12.0.2": "https://registry.npmmirror.com/cacache/-/cacache-12.0.4.tgz", "cacache@^15.0.5": "https://registry.npmmirror.com/cacache/-/cacache-15.3.0.tgz", "cache-base@^1.0.1": "https://registry.npmmirror.com/cache-base/-/cache-base-1.0.1.tgz", "call-bind@^1.0.0": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "call-bind@^1.0.2": "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.2.tgz", "callsites@^3.0.0": "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz", "camel-case@^4.1.1": "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz", "camelcase-keys@^6.2.2": "https://registry.npmmirror.com/camelcase-keys/-/camelcase-keys-6.2.2.tgz", "camelcase@^4.0.0": "https://registry.npmmirror.com/camelcase/-/camelcase-4.1.0.tgz", "camelcase@^5.0.0": "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^5.3.1": "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.0.0": "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz", "caniuse-lite@^1.0.30001109": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001323.tgz", "caniuse-lite@^1.0.30001317": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001323.tgz", "canvg@^3.0.6": "https://registry.npmmirror.com/canvg/-/canvg-3.0.10.tgz", "capture-stack-trace@^1.0.0": "https://registry.npmmirror.com/capture-stack-trace/-/capture-stack-trace-1.0.1.tgz", "caseless@~0.12.0": "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz", "chainsaw@~0.1.0": "http://nexus3.luban.fit/repository/npm/chainsaw/-/chainsaw-0.1.0.tgz#5eab50b28afe58074d0d58291388828b5e5fbc98", "chalk@4.1.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.0.tgz", "chalk@^1.1.3": "https://registry.npmmirror.com/chalk/-/chalk-1.1.3.tgz", "chalk@^2.0.0": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "chalk@^2.0.1": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "chalk@^2.1.0": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmmirror.com/chalk/-/chalk-3.0.0.tgz", "chalk@^4.0.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz", "charcodes@^0.2.0": "https://registry.npmmirror.com/charcodes/-/charcodes-0.2.0.tgz", "chardet@^0.7.0": "https://registry.npmmirror.com/chardet/-/chardet-0.7.0.tgz", "chokidar@>=2.0.0 <4.0.0": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "chokidar@>=3.0.0 <4.0.0": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "chokidar@^2.1.8": "https://registry.npmmirror.com/chokidar/-/chokidar-2.1.8.tgz", "chokidar@^3.4.0": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "chokidar@^3.4.1": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "chokidar@^3.4.2": "https://registry.npmmirror.com/chokidar/-/chokidar-3.5.3.tgz", "chownr@^1.1.1": "https://registry.npmmirror.com/chownr/-/chownr-1.1.4.tgz", "chownr@^2.0.0": "https://registry.npmmirror.com/chownr/-/chownr-2.0.0.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz", "ci-info@^1.5.0": "https://registry.npmmirror.com/ci-info/-/ci-info-1.6.0.tgz", "ci-info@^2.0.0": "https://registry.npmmirror.com/ci-info/-/ci-info-2.0.0.tgz", "cipher-base@^1.0.0": "https://registry.npmmirror.com/cipher-base/-/cipher-base-1.0.4.tgz", "cipher-base@^1.0.1": "https://registry.npmmirror.com/cipher-base/-/cipher-base-1.0.4.tgz", "cipher-base@^1.0.3": "https://registry.npmmirror.com/cipher-base/-/cipher-base-1.0.4.tgz", "class-utils@^0.3.5": "https://registry.npmmirror.com/class-utils/-/class-utils-0.3.6.tgz", "classnames@2.x": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "classnames@^2.2.1": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "classnames@^2.2.3": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "classnames@^2.2.5": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "classnames@^2.2.6": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "classnames@^2.3.1": "https://registry.npmmirror.com/classnames/-/classnames-2.3.1.tgz", "clean-css@^4.2.3": "https://registry.npmmirror.com/clean-css/-/clean-css-4.2.4.tgz", "clean-stack@^2.0.0": "https://registry.npmmirror.com/clean-stack/-/clean-stack-2.2.0.tgz", "clean-webpack-plugin@^3.0.0": "https://registry.npmmirror.com/clean-webpack-plugin/-/clean-webpack-plugin-3.0.0.tgz", "cli-boxes@^1.0.0": "https://registry.npmmirror.com/cli-boxes/-/cli-boxes-1.0.0.tgz", "cli-cursor@^3.1.0": "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz", "cli-truncate@^2.1.0": "https://registry.npmmirror.com/cli-truncate/-/cli-truncate-2.1.0.tgz", "cli-width@^3.0.0": "https://registry.npmmirror.com/cli-width/-/cli-width-3.0.0.tgz", "cliui@^5.0.0": "https://registry.npmmirror.com/cliui/-/cliui-5.0.0.tgz", "cliui@^6.0.0": "https://registry.npmmirror.com/cliui/-/cliui-6.0.0.tgz", "clsx@^1.0.4": "https://registry.npmmirror.com/clsx/-/clsx-1.2.1.tgz", "clsx@^1.1.1": "https://registry.npmmirror.com/clsx/-/clsx-1.1.1.tgz", "collection-visit@^1.0.0": "https://registry.npmmirror.com/collection-visit/-/collection-visit-1.0.0.tgz", "color-convert@^1.9.0": "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz", "colorette@^2.0.16": "https://registry.npmmirror.com/colorette/-/colorette-2.0.16.tgz", "combined-stream@^1.0.6": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "combined-stream@~1.0.6": "https://registry.npmmirror.com/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^2.11.0": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "commander@^2.19.0": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "commander@^2.20.0": "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz", "commander@^4.0.1": "https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz", "commander@^4.1.1": "https://registry.npmmirror.com/commander/-/commander-4.1.1.tgz", "commander@^6.2.0": "https://registry.npmmirror.com/commander/-/commander-6.2.1.tgz", "commander@^7.2.0": "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz", "comment-json@^1.1.3": "https://registry.npmmirror.com/comment-json/-/comment-json-1.1.3.tgz", "commondir@^1.0.1": "https://registry.npmmirror.com/commondir/-/commondir-1.0.1.tgz", "compare-func@^2.0.0": "https://registry.npmmirror.com/compare-func/-/compare-func-2.0.0.tgz", "compare-versions@^3.6.0": "https://registry.npmmirror.com/compare-versions/-/compare-versions-3.6.0.tgz", "component-emitter@^1.2.1": "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.3.0.tgz", "compress-commons@^4.1.0": "https://registry.npmmirror.com/compress-commons/-/compress-commons-4.1.1.tgz", "compressible@~2.0.16": "https://registry.npmmirror.com/compressible/-/compressible-2.0.18.tgz", "compression@^1.7.4": "https://registry.npmmirror.com/compression/-/compression-1.7.4.tgz", "compressorjs@^1.1.1": "https://registry.npmmirror.com/compressorjs/-/compressorjs-1.1.1.tgz", "compute-scroll-into-view@^1.0.17": "https://registry.npmmirror.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.17.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz", "concat-stream@^1.5.0": "https://registry.npmmirror.com/concat-stream/-/concat-stream-1.6.2.tgz", "configstore@^3.0.0": "https://registry.npmmirror.com/configstore/-/configstore-3.1.5.tgz", "confusing-browser-globals@^1.0.10": "https://registry.npmmirror.com/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz", "confusing-browser-globals@^1.0.7": "https://registry.npmmirror.com/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz", "connect-history-api-fallback@^1.6.0": "https://registry.npmmirror.com/connect-history-api-fallback/-/connect-history-api-fallback-1.6.0.tgz", "console-browserify@^1.1.0": "https://registry.npmmirror.com/console-browserify/-/console-browserify-1.2.0.tgz", "constants-browserify@^1.0.0": "https://registry.npmmirror.com/constants-browserify/-/constants-browserify-1.0.0.tgz", "contains-path@^0.1.0": "https://registry.npmmirror.com/contains-path/-/contains-path-0.1.0.tgz", "content-disposition@0.5.4": "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@^1.0.2": "https://registry.npmmirror.com/content-type/-/content-type-1.0.4.tgz", "content-type@~1.0.4": "https://registry.npmmirror.com/content-type/-/content-type-1.0.4.tgz", "conventional-changelog-angular@^5.0.0": "https://registry.npmmirror.com/conventional-changelog-angular/-/conventional-changelog-angular-5.0.13.tgz", "conventional-changelog-conventionalcommits@^4.3.1": "https://registry.npmmirror.com/conventional-changelog-conventionalcommits/-/conventional-changelog-conventionalcommits-4.6.3.tgz", "conventional-commits-parser@^3.0.0": "https://registry.npmmirror.com/conventional-commits-parser/-/conventional-commits-parser-3.2.4.tgz", "convert-source-map@^1.1.0": "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-1.8.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-1.8.0.tgz", "cookie-signature@1.0.6": "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.4.2": "https://registry.npmmirror.com/cookie/-/cookie-0.4.2.tgz", "copy-anything@^2.0.1": "https://registry.npmmirror.com/copy-anything/-/copy-anything-2.0.6.tgz", "copy-concurrently@^1.0.0": "https://registry.npmmirror.com/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "copy-descriptor@^0.1.0": "https://registry.npmmirror.com/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "copy-to-clipboard@^3.2.0": "https://registry.npmmirror.com/copy-to-clipboard/-/copy-to-clipboard-3.3.1.tgz", "copy-to@^2.0.1": "https://registry.npmmirror.com/copy-to/-/copy-to-2.0.1.tgz", "copy-webpack-plugin@^6.3.2": "https://registry.npmmirror.com/copy-webpack-plugin/-/copy-webpack-plugin-6.4.1.tgz", "core-js-compat@^3.20.2": "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.21.1.tgz", "core-js-compat@^3.21.0": "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.21.1.tgz", "core-js-pure@^3.20.2": "https://registry.npmmirror.com/core-js-pure/-/core-js-pure-3.21.1.tgz", "core-js@^3.6.0": "https://registry.npmmirror.com/core-js/-/core-js-3.23.5.tgz", "core-js@^3.6.1": "https://registry.npmmirror.com/core-js/-/core-js-3.21.1.tgz", "core-js@^3.8.3": "https://registry.npmmirror.com/core-js/-/core-js-3.23.5.tgz", "core-util-is@1.0.2": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "core-util-is@^1.0.2": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz", "core-util-is@~1.0.0": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz", "cosmiconfig@^6.0.0": "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-6.0.0.tgz", "cosmiconfig@^7.0.0": "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-7.0.1.tgz", "crc-32@^1.2.0": "https://registry.npmmirror.com/crc-32/-/crc-32-1.2.1.tgz", "crc32-stream@^4.0.2": "https://registry.npmmirror.com/crc32-stream/-/crc32-stream-4.0.2.tgz", "create-ecdh@^4.0.0": "https://registry.npmmirror.com/create-ecdh/-/create-ecdh-4.0.4.tgz", "create-error-class@^3.0.0": "https://registry.npmmirror.com/create-error-class/-/create-error-class-3.0.2.tgz", "create-hash@^1.1.0": "https://registry.npmmirror.com/create-hash/-/create-hash-1.2.0.tgz", "create-hash@^1.1.2": "https://registry.npmmirror.com/create-hash/-/create-hash-1.2.0.tgz", "create-hash@^1.2.0": "https://registry.npmmirror.com/create-hash/-/create-hash-1.2.0.tgz", "create-hmac@^1.1.0": "https://registry.npmmirror.com/create-hmac/-/create-hmac-1.1.7.tgz", "create-hmac@^1.1.4": "https://registry.npmmirror.com/create-hmac/-/create-hmac-1.1.7.tgz", "create-hmac@^1.1.7": "https://registry.npmmirror.com/create-hmac/-/create-hmac-1.1.7.tgz", "cross-env@5.0.5": "https://registry.npmmirror.com/cross-env/-/cross-env-5.0.5.tgz", "cross-env@^7.0.3": "https://registry.npmmirror.com/cross-env/-/cross-env-7.0.3.tgz", "cross-spawn@^5.0.1": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-5.1.0.tgz", "cross-spawn@^5.1.0": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-5.1.0.tgz", "cross-spawn@^6.0.0": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-6.0.5.tgz", "cross-spawn@^6.0.5": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-6.0.5.tgz", "cross-spawn@^7.0.0": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.1": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.2": "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz", "crypto-browserify@^3.11.0": "https://registry.npmmirror.com/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "crypto-js@4.0.0": "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.0.0.tgz", "crypto-js@^4.0.0": "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.1.1.tgz", "crypto-js@^4.1.1": "https://registry.npmmirror.com/crypto-js/-/crypto-js-4.1.1.tgz", "crypto-random-string@^1.0.0": "https://registry.npmmirror.com/crypto-random-string/-/crypto-random-string-1.0.0.tgz", "css-jss@10.9.0": "https://registry.npmmirror.com/css-jss/-/css-jss-10.9.0.tgz", "css-line-break@^2.1.0": "https://registry.npmmirror.com/css-line-break/-/css-line-break-2.1.0.tgz", "css-loader@^4.3.0": "https://registry.npmmirror.com/css-loader/-/css-loader-4.3.0.tgz", "css-select@^4.1.3": "https://registry.npmmirror.com/css-select/-/css-select-4.3.0.tgz", "css-vendor@^2.0.8": "https://registry.npmmirror.com/css-vendor/-/css-vendor-2.0.8.tgz", "css-what@^6.0.1": "https://registry.npmmirror.com/css-what/-/css-what-6.0.1.tgz", "cssesc@^3.0.0": "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz", "csstype@^3.0.2": "https://registry.npmmirror.com/csstype/-/csstype-3.0.11.tgz", "cyclist@^1.0.1": "https://registry.npmmirror.com/cyclist/-/cyclist-1.0.1.tgz", "dagre@^0.8.5": "https://registry.npmmirror.com/dagre/-/dagre-0.8.5.tgz", "dagre@~0.8.5": "https://registry.npmmirror.com/dagre/-/dagre-0.8.5.tgz", "damerau-levenshtein@^1.0.4": "https://registry.npmmirror.com/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz", "dargs@^7.0.0": "https://registry.npmmirror.com/dargs/-/dargs-7.0.0.tgz", "dart-sass@^1.25.0": "https://registry.npmmirror.com/dart-sass/-/dart-sass-1.25.0.tgz", "dashdash@^1.12.0": "https://registry.npmmirror.com/dashdash/-/dashdash-1.14.1.tgz", "data-uri-to-buffer@3": "https://registry.npmmirror.com/data-uri-to-buffer/-/data-uri-to-buffer-3.0.1.tgz", "date-fns@2.x": "https://registry.npmmirror.com/date-fns/-/date-fns-2.28.0.tgz", "dateformat@^2.0.0": "https://registry.npmmirror.com/dateformat/-/dateformat-2.2.0.tgz", "dayjs@1.x": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.0.tgz", "dayjs@^1.8.34": "http://nexus3.luban.fit/repository/npm/dayjs/-/dayjs-1.11.13.tgz#92430b0139055c3ebb60150aa13e860a4b5a366c", "dayjs@^1.9.1": "https://registry.npmmirror.com/dayjs/-/dayjs-1.11.4.tgz", "debug@2.6.9": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^2.2.0": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@^2.3.3": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@^2.6.9": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "debug@^3.1.1": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "debug@^3.2.7": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "debug@^4.0.1": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.1.1": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.2.0": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "debug@^4.3.1": "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz", "decamelize-keys@^1.1.0": "https://registry.npmmirror.com/decamelize-keys/-/decamelize-keys-1.1.0.tgz", "decamelize@^1.1.0": "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz", "decamelize@^1.2.0": "https://registry.npmmirror.com/decamelize/-/decamelize-1.2.0.tgz", "decode-uri-component@^0.2.0": "https://registry.npmmirror.com/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "dedent@^0.7.0": "https://registry.npmmirror.com/dedent/-/dedent-0.7.0.tgz", "deep-equal@^1.0.1": "https://registry.npmmirror.com/deep-equal/-/deep-equal-1.1.1.tgz", "deep-extend@^0.6.0": "https://registry.npmmirror.com/deep-extend/-/deep-extend-0.6.0.tgz", "deep-is@^0.1.3": "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz", "deep-is@~0.1.3": "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz", "deepmerge@^4.2.2": "https://registry.npmmirror.com/deepmerge/-/deepmerge-4.2.2.tgz", "default-gateway@^4.2.0": "https://registry.npmmirror.com/default-gateway/-/default-gateway-4.2.0.tgz", "default-user-agent@^1.0.0": "https://registry.npmmirror.com/default-user-agent/-/default-user-agent-1.0.0.tgz", "define-properties@^1.1.2": "https://registry.npmmirror.com/define-properties/-/define-properties-1.1.3.tgz", "define-properties@^1.1.3": "https://registry.npmmirror.com/define-properties/-/define-properties-1.1.3.tgz", "define-property@^0.2.5": "https://registry.npmmirror.com/define-property/-/define-property-0.2.5.tgz", "define-property@^1.0.0": "https://registry.npmmirror.com/define-property/-/define-property-1.0.0.tgz", "define-property@^2.0.2": "https://registry.npmmirror.com/define-property/-/define-property-2.0.2.tgz", "degenerator@^3.0.2": "https://registry.npmmirror.com/degenerator/-/degenerator-3.0.2.tgz", "del@^4.1.1": "https://registry.npmmirror.com/del/-/del-4.1.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-1.0.0.tgz", "depd@2.0.0": "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz", "depd@~1.1.2": "https://registry.npmmirror.com/depd/-/depd-1.1.2.tgz", "des.js@^1.0.0": "https://registry.npmmirror.com/des.js/-/des.js-1.0.1.tgz", "destroy@^1.0.4": "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz", "destroy@~1.0.4": "https://registry.npmmirror.com/destroy/-/destroy-1.0.4.tgz", "detect-file@^1.0.0": "https://registry.npmmirror.com/detect-file/-/detect-file-1.0.0.tgz", "detect-indent@^5.0.0": "https://registry.npmmirror.com/detect-indent/-/detect-indent-5.0.0.tgz", "detect-node@^2.0.4": "https://registry.npmmirror.com/detect-node/-/detect-node-2.1.0.tgz", "diffie-hellman@^5.0.0": "https://registry.npmmirror.com/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "digest-header@^0.0.1": "https://registry.npmmirror.com/digest-header/-/digest-header-0.0.1.tgz", "dir-glob@^3.0.1": "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz", "dns-equal@^1.0.0": "https://registry.npmmirror.com/dns-equal/-/dns-equal-1.0.0.tgz", "dns-packet@^1.3.1": "https://registry.npmmirror.com/dns-packet/-/dns-packet-1.3.4.tgz", "dns-txt@^2.0.2": "https://registry.npmmirror.com/dns-txt/-/dns-txt-2.0.2.tgz", "doctrine@1.5.0": "https://registry.npmmirror.com/doctrine/-/doctrine-1.5.0.tgz", "doctrine@^2.1.0": "https://registry.npmmirror.com/doctrine/-/doctrine-2.1.0.tgz", "doctrine@^3.0.0": "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz", "dom-align@^1.7.0": "https://registry.npmmirror.com/dom-align/-/dom-align-1.12.2.tgz", "dom-converter@^0.2.0": "https://registry.npmmirror.com/dom-converter/-/dom-converter-0.2.0.tgz", "dom-helpers@^5.1.3": "https://registry.npmmirror.com/dom-helpers/-/dom-helpers-5.2.1.tgz", "dom-serializer@^1.0.1": "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-1.3.2.tgz", "dom-walk@^0.1.0": "https://registry.npmmirror.com/dom-walk/-/dom-walk-0.1.2.tgz", "domain-browser@^1.1.1": "https://registry.npmmirror.com/domain-browser/-/domain-browser-1.2.0.tgz", "domelementtype@^2.0.1": "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.2.0.tgz", "domelementtype@^2.2.0": "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.2.0.tgz", "domhandler@^4.0.0": "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.2.0": "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.3.1": "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz", "dompurify@^2.2.0": "https://registry.npmmirror.com/dompurify/-/dompurify-2.3.10.tgz", "domutils@^2.5.2": "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz", "domutils@^2.8.0": "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz", "dot-case@^3.0.4": "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz", "dot-prop@^4.2.1": "https://registry.npmmirror.com/dot-prop/-/dot-prop-4.2.1.tgz", "dot-prop@^5.1.0": "https://registry.npmmirror.com/dot-prop/-/dot-prop-5.3.0.tgz", "duplexer2@~0.1.4": "http://nexus3.luban.fit/repository/npm/duplexer2/-/duplexer2-0.1.4.tgz#8b12dab878c0d69e3e7891051662a32fc6bddcc1", "duplexer3@^0.1.4": "https://registry.npmmirror.com/duplexer3/-/duplexer3-0.1.4.tgz", "duplexer@^0.1.2": "https://registry.npmmirror.com/duplexer/-/duplexer-0.1.2.tgz", "duplexify@^3.4.2": "https://registry.npmmirror.com/duplexify/-/duplexify-3.7.1.tgz", "duplexify@^3.6.0": "https://registry.npmmirror.com/duplexify/-/duplexify-3.7.1.tgz", "ecc-jsbn@~0.1.1": "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "editorconfig-to-prettier@0.0.6": "https://registry.npmmirror.com/editorconfig-to-prettier/-/editorconfig-to-prettier-0.0.6.tgz", "editorconfig@^0.15.0": "https://registry.npmmirror.com/editorconfig/-/editorconfig-0.15.3.tgz", "ee-first@1.1.1": "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz", "ee-first@~1.1.1": "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz", "electron-to-chromium@^1.4.84": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.103.tgz", "elliptic@^6.5.3": "https://registry.npmmirror.com/elliptic/-/elliptic-6.5.4.tgz", "emoji-regex@^7.0.1": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz", "emoji-regex@^7.0.2": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-7.0.3.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz", "emojis-list@^2.0.0": "https://registry.npmmirror.com/emojis-list/-/emojis-list-2.1.0.tgz", "emojis-list@^3.0.0": "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz", "encodeurl@~1.0.2": "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz", "end-of-stream@^1.0.0": "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-of-stream@^1.1.0": "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-of-stream@^1.4.1": "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz", "end-or-error@^1.0.1": "https://registry.npmmirror.com/end-or-error/-/end-or-error-1.0.1.tgz", "enhanced-resolve@^4.0.0": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "enhanced-resolve@^4.1.1": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "enhanced-resolve@^4.5.0": "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-4.5.0.tgz", "enquirer@^2.3.5": "https://registry.npmmirror.com/enquirer/-/enquirer-2.3.6.tgz", "enquirer@^2.3.6": "https://registry.npmmirror.com/enquirer/-/enquirer-2.3.6.tgz", "entities@^2.0.0": "https://registry.npmmirror.com/entities/-/entities-2.2.0.tgz", "errno@^0.1.1": "https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz", "errno@^0.1.3": "https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz", "errno@~0.1.7": "https://registry.npmmirror.com/errno/-/errno-0.1.8.tgz", "error-ex@^1.2.0": "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz", "error-ex@^1.3.1": "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmmirror.com/error-stack-parser/-/error-stack-parser-2.0.7.tgz", "es-abstract@^1.19.0": "https://registry.npmmirror.com/es-abstract/-/es-abstract-1.19.2.tgz", "es-abstract@^1.19.1": "https://registry.npmmirror.com/es-abstract/-/es-abstract-1.19.2.tgz", "es-to-primitive@^1.2.1": "https://registry.npmmirror.com/es-to-primitive/-/es-to-primitive-1.2.1.tgz", "escalade@^3.1.1": "https://registry.npmmirror.com/escalade/-/escalade-3.1.1.tgz", "escape-html@^1.0.3": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "escape-html@~1.0.3": "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.2": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "escodegen@^1.8.1": "https://registry.npmmirror.com/escodegen/-/escodegen-1.14.3.tgz", "eslint-config-airbnb-base@14.0.0": "https://registry.npmmirror.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-14.0.0.tgz", "eslint-config-airbnb-base@^14.0.0": "https://registry.npmmirror.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-14.2.1.tgz", "eslint-config-airbnb@18.0.1": "https://registry.npmmirror.com/eslint-config-airbnb/-/eslint-config-airbnb-18.0.1.tgz", "eslint-config-prettier@^7.0.0": "https://registry.npmmirror.com/eslint-config-prettier/-/eslint-config-prettier-7.2.0.tgz", "eslint-import-resolver-node@^0.3.2": "https://registry.npmmirror.com/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.6.tgz", "eslint-module-utils@^2.4.0": "https://registry.npmmirror.com/eslint-module-utils/-/eslint-module-utils-2.7.3.tgz", "eslint-plugin-chai-friendly@^0.5.0": "https://registry.npmmirror.com/eslint-plugin-chai-friendly/-/eslint-plugin-chai-friendly-0.5.0.tgz", "eslint-plugin-import@2.18.2": "https://registry.npmmirror.com/eslint-plugin-import/-/eslint-plugin-import-2.18.2.tgz", "eslint-plugin-jsx-a11y@6.2.3": "https://registry.npmmirror.com/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.2.3.tgz", "eslint-plugin-prettier@^3.3.0": "https://registry.npmmirror.com/eslint-plugin-prettier/-/eslint-plugin-prettier-3.4.1.tgz", "eslint-plugin-react-hooks@2.0.1": "https://registry.npmmirror.com/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-2.0.1.tgz", "eslint-plugin-react-hooks@^4.2.0": "https://registry.npmmirror.com/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.4.0.tgz", "eslint-plugin-react@7.14.3": "https://registry.npmmirror.com/eslint-plugin-react/-/eslint-plugin-react-7.14.3.tgz", "eslint-plugin-react@^7.21.5": "https://registry.npmmirror.com/eslint-plugin-react/-/eslint-plugin-react-7.29.4.tgz", "eslint-scope@^4.0.3": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-4.0.3.tgz", "eslint-scope@^5.0.0": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-utils@^1.4.3": "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-1.4.3.tgz", "eslint-utils@^2.0.0": "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-2.1.0.tgz", "eslint-utils@^2.1.0": "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-2.1.0.tgz", "eslint-utils@^3.0.0": "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz", "eslint-visitor-keys@^1.1.0": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^1.3.0": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^2.0.0": "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint@^6.7.2": "https://registry.npmmirror.com/eslint/-/eslint-6.8.0.tgz", "eslint@^7.15.0": "https://registry.npmmirror.com/eslint/-/eslint-7.32.0.tgz", "espree@^6.1.2": "https://registry.npmmirror.com/espree/-/espree-6.2.1.tgz", "espree@^7.3.0": "https://registry.npmmirror.com/espree/-/espree-7.3.1.tgz", "espree@^7.3.1": "https://registry.npmmirror.com/espree/-/espree-7.3.1.tgz", "esprima@^2.7.0": "https://registry.npmmirror.com/esprima/-/esprima-2.7.3.tgz", "esprima@^4.0.0": "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz", "esquery@^1.0.1": "https://registry.npmmirror.com/esquery/-/esquery-1.4.0.tgz", "esquery@^1.4.0": "https://registry.npmmirror.com/esquery/-/esquery-1.4.0.tgz", "esrecurse@^4.1.0": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "esrecurse@^4.3.0": "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^4.2.0": "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.3.0": "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz", "estree-walker@^1.0.1": "https://registry.npmmirror.com/estree-walker/-/estree-walker-1.0.1.tgz", "esutils@^2.0.2": "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz", "eventemitter3@^4.0.0": "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-4.0.7.tgz", "events@^3.0.0": "https://registry.npmmirror.com/events/-/events-3.3.0.tgz", "eventsource@^1.1.0": "https://registry.npmmirror.com/eventsource/-/eventsource-1.1.0.tgz", "evp_bytestokey@^1.0.0": "https://registry.npmmirror.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "evp_bytestokey@^1.0.3": "https://registry.npmmirror.com/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "exceljs@^4.4.0": "http://nexus3.luban.fit/repository/npm/exceljs/-/exceljs-4.4.0.tgz#cfb1cb8dcc82c760a9fc9faa9e52dadab66b0156", "execa@^0.7.0": "https://registry.npmmirror.com/execa/-/execa-0.7.0.tgz", "execa@^1.0.0": "https://registry.npmmirror.com/execa/-/execa-1.0.0.tgz", "execa@^4.1.0": "https://registry.npmmirror.com/execa/-/execa-4.1.0.tgz", "exit-on-epipe@~1.0.1": "https://registry.npmmirror.com/exit-on-epipe/-/exit-on-epipe-1.0.1.tgz", "expand-brackets@^2.1.4": "https://registry.npmmirror.com/expand-brackets/-/expand-brackets-2.1.4.tgz", "expand-tilde@^2.0.0": "https://registry.npmmirror.com/expand-tilde/-/expand-tilde-2.0.2.tgz", "expand-tilde@^2.0.2": "https://registry.npmmirror.com/expand-tilde/-/expand-tilde-2.0.2.tgz", "express@^4.17.1": "https://registry.npmmirror.com/express/-/express-4.17.3.tgz", "extend-shallow@^2.0.1": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-2.0.1.tgz", "extend-shallow@^3.0.0": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz", "extend-shallow@^3.0.2": "https://registry.npmmirror.com/extend-shallow/-/extend-shallow-3.0.2.tgz", "extend@^3.0.0": "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz", "extend@~3.0.2": "https://registry.npmmirror.com/extend/-/extend-3.0.2.tgz", "external-editor@^3.0.3": "https://registry.npmmirror.com/external-editor/-/external-editor-3.1.0.tgz", "extglob@^2.0.4": "https://registry.npmmirror.com/extglob/-/extglob-2.0.4.tgz", "extsprintf@1.3.0": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz", "extsprintf@^1.2.0": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.4.1.tgz", "fast-csv@^4.3.1": "http://nexus3.luban.fit/repository/npm/fast-csv/-/fast-csv-4.3.6.tgz#70349bdd8fe4d66b1130d8c91820b64a21bc4a63", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmmirror.com/fast-diff/-/fast-diff-1.2.0.tgz", "fast-glob@^3.2.4": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.11.tgz", "fast-glob@^3.2.9": "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.2.11.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-levenshtein@~2.0.6": "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fastq@^1.6.0": "https://registry.npmmirror.com/fastq/-/fastq-1.13.0.tgz", "faye-websocket@^0.11.3": "https://registry.npmmirror.com/faye-websocket/-/faye-websocket-0.11.4.tgz", "faye-websocket@^0.11.4": "https://registry.npmmirror.com/faye-websocket/-/faye-websocket-0.11.4.tgz", "fflate@^0.4.8": "https://registry.npmmirror.com/fflate/-/fflate-0.4.8.tgz", "figgy-pudding@^3.5.1": "https://registry.npmmirror.com/figgy-pudding/-/figgy-pudding-3.5.2.tgz", "figures@^3.0.0": "https://registry.npmmirror.com/figures/-/figures-3.2.0.tgz", "file-entry-cache@^5.0.1": "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-5.0.1.tgz", "file-entry-cache@^6.0.1": "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "file-loader@^6.1.0": "https://registry.npmmirror.com/file-loader/-/file-loader-6.2.0.tgz", "file-saver@^2.0.5": "https://registry.npmmirror.com/file-saver/-/file-saver-2.0.5.tgz", "file-uri-to-path@1.0.0": "https://registry.npmmirror.com/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd", "file-uri-to-path@2": "https://registry.npmmirror.com/file-uri-to-path/-/file-uri-to-path-2.0.0.tgz", "fill-range@^4.0.0": "https://registry.npmmirror.com/fill-range/-/fill-range-4.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz", "filter-obj@^1.1.0": "https://registry.npmmirror.com/filter-obj/-/filter-obj-1.1.0.tgz", "finalhandler@~1.1.2": "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.1.2.tgz", "find-cache-dir@^2.1.0": "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "find-cache-dir@^3.3.1": "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "find-up@^2.0.0": "https://registry.npmmirror.com/find-up/-/find-up-2.1.0.tgz", "find-up@^2.1.0": "https://registry.npmmirror.com/find-up/-/find-up-2.1.0.tgz", "find-up@^3.0.0": "https://registry.npmmirror.com/find-up/-/find-up-3.0.0.tgz", "find-up@^4.0.0": "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmmirror.com/find-up/-/find-up-5.0.0.tgz", "find-versions@^4.0.0": "https://registry.npmmirror.com/find-versions/-/find-versions-4.0.0.tgz", "findup-sync@^3.0.0": "https://registry.npmmirror.com/findup-sync/-/findup-sync-3.0.0.tgz", "flat-cache@^2.0.1": "https://registry.npmmirror.com/flat-cache/-/flat-cache-2.0.1.tgz", "flat-cache@^3.0.4": "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.0.4.tgz", "flatted@^2.0.0": "https://registry.npmmirror.com/flatted/-/flatted-2.0.2.tgz", "flatted@^3.1.0": "https://registry.npmmirror.com/flatted/-/flatted-3.2.5.tgz", "flush-write-stream@^1.0.0": "https://registry.npmmirror.com/flush-write-stream/-/flush-write-stream-1.1.1.tgz", "follow-redirects@^1.0.0": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.14.9.tgz", "follow-redirects@^1.14.0": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.14.9.tgz", "for-in@^1.0.2": "https://registry.npmmirror.com/for-in/-/for-in-1.0.2.tgz", "forever-agent@~0.6.1": "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz", "fork-ts-checker-notifier-webpack-plugin@3.0.0": "https://registry.npmmirror.com/fork-ts-checker-notifier-webpack-plugin/-/fork-ts-checker-notifier-webpack-plugin-3.0.0.tgz", "fork-ts-checker-webpack-plugin@6.0.5": "https://registry.npmmirror.com/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.0.5.tgz", "form-data@~2.3.2": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "formstream@^1.1.0": "https://registry.npmmirror.com/formstream/-/formstream-1.1.1.tgz", "forwarded@0.2.0": "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz", "fragment-cache@^0.2.1": "https://registry.npmmirror.com/fragment-cache/-/fragment-cache-0.2.1.tgz", "fresh@0.5.2": "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz", "from2@^2.1.0": "https://registry.npmmirror.com/from2/-/from2-2.3.0.tgz", "fs-constants@^1.0.0": "https://registry.npmmirror.com/fs-constants/-/fs-constants-1.0.0.tgz", "fs-exists-sync@^0.1.0": "https://registry.npmmirror.com/fs-exists-sync/-/fs-exists-sync-0.1.0.tgz", "fs-extra@^7.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-7.0.1.tgz", "fs-extra@^8.1.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-8.1.0.tgz", "fs-extra@^9.0.0": "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz", "fs-minipass@^2.0.0": "https://registry.npmmirror.com/fs-minipass/-/fs-minipass-2.1.0.tgz", "fs-monkey@1.0.3": "https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.3.tgz", "fs-readdir-recursive@^1.1.0": "https://registry.npmmirror.com/fs-readdir-recursive/-/fs-readdir-recursive-1.1.0.tgz", "fs-write-stream-atomic@^1.0.8": "https://registry.npmmirror.com/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^1.2.7": "https://registry.npmmirror.com/fsevents/-/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38", "fsevents@~2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a", "fstream@^1.0.12": "http://nexus3.luban.fit/repository/npm/fstream/-/fstream-1.0.12.tgz#4e8ba8ee2d48be4f7d0de505455548eae5932045", "ftp@^0.3.10": "https://registry.npmmirror.com/ftp/-/ftp-0.3.10.tgz", "function-bind@^1.1.1": "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.1.tgz", "functional-red-black-tree@^1.0.1": "https://registry.npmmirror.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.1": "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.0.2": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "get-intrinsic@^1.1.0": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "get-intrinsic@^1.1.1": "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "get-own-enumerable-property-symbols@^3.0.0": "https://registry.npmmirror.com/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz", "get-ready@^1.0.0": "https://registry.npmmirror.com/get-ready/-/get-ready-1.0.0.tgz", "get-ready@~1.0.0": "https://registry.npmmirror.com/get-ready/-/get-ready-1.0.0.tgz", "get-stdin@8.0.0": "https://registry.npmmirror.com/get-stdin/-/get-stdin-8.0.0.tgz", "get-stream@^3.0.0": "https://registry.npmmirror.com/get-stream/-/get-stream-3.0.0.tgz", "get-stream@^4.0.0": "https://registry.npmmirror.com/get-stream/-/get-stream-4.1.0.tgz", "get-stream@^5.0.0": "https://registry.npmmirror.com/get-stream/-/get-stream-5.2.0.tgz", "get-symbol-description@^1.0.0": "https://registry.npmmirror.com/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "get-uri@3": "https://registry.npmmirror.com/get-uri/-/get-uri-3.0.2.tgz", "get-value@^2.0.3": "https://registry.npmmirror.com/get-value/-/get-value-2.0.6.tgz", "get-value@^2.0.6": "https://registry.npmmirror.com/get-value/-/get-value-2.0.6.tgz", "getpass@^0.1.1": "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz", "git-config-path@^1.0.1": "https://registry.npmmirror.com/git-config-path/-/git-config-path-1.0.1.tgz", "git-raw-commits@^2.0.0": "https://registry.npmmirror.com/git-raw-commits/-/git-raw-commits-2.0.11.tgz", "git-username@^1.0.0": "https://registry.npmmirror.com/git-username/-/git-username-1.0.0.tgz", "glob-parent@^3.1.0": "https://registry.npmmirror.com/glob-parent/-/glob-parent-3.1.0.tgz", "glob-parent@^5.0.0": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^5.1.1": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@~5.1.2": "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz", "glob@^7.0.0": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.0.3": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.1.1": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.1.2": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.1.3": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.1.4": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "glob@^7.1.6": "https://registry.npmmirror.com/glob/-/glob-7.2.0.tgz", "global-dirs@^0.1.0": "https://registry.npmmirror.com/global-dirs/-/global-dirs-0.1.1.tgz", "global-dirs@^0.1.1": "https://registry.npmmirror.com/global-dirs/-/global-dirs-0.1.1.tgz", "global-modules@^1.0.0": "https://registry.npmmirror.com/global-modules/-/global-modules-1.0.0.tgz", "global-modules@^2.0.0": "https://registry.npmmirror.com/global-modules/-/global-modules-2.0.0.tgz", "global-prefix@^1.0.1": "https://registry.npmmirror.com/global-prefix/-/global-prefix-1.0.2.tgz", "global-prefix@^3.0.0": "https://registry.npmmirror.com/global-prefix/-/global-prefix-3.0.0.tgz", "global@^4.3.1": "https://registry.npmmirror.com/global/-/global-4.4.0.tgz", "global@^4.4.0": "https://registry.npmmirror.com/global/-/global-4.4.0.tgz", "global@~4.4.0": "https://registry.npmmirror.com/global/-/global-4.4.0.tgz", "globals@^11.1.0": "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz", "globals@^12.1.0": "https://registry.npmmirror.com/globals/-/globals-12.4.0.tgz", "globals@^13.6.0": "https://registry.npmmirror.com/globals/-/globals-13.13.0.tgz", "globals@^13.9.0": "https://registry.npmmirror.com/globals/-/globals-13.13.0.tgz", "globby@^11.0.1": "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz", "globby@^11.0.3": "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz", "globby@^6.1.0": "https://registry.npmmirror.com/globby/-/globby-6.1.0.tgz", "got@^6.7.1": "https://registry.npmmirror.com/got/-/got-6.7.1.tgz", "graceful-fs@^4.1.11": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz", "graceful-fs@^4.1.15": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz", "graceful-fs@^4.1.2": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.9.tgz", "graceful-fs@^4.2.2": "http://nexus3.luban.fit/repository/npm/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3", "graphlib@^2.1.8": "https://registry.npmmirror.com/graphlib/-/graphlib-2.1.8.tgz", "graphlib@~2.1.8": "https://registry.npmmirror.com/graphlib/-/graphlib-2.1.8.tgz", "growly@^1.3.0": "https://registry.npmmirror.com/growly/-/growly-1.3.0.tgz", "gzip-size@^6.0.0": "https://registry.npmmirror.com/gzip-size/-/gzip-size-6.0.0.tgz", "handle-thing@^2.0.0": "https://registry.npmmirror.com/handle-thing/-/handle-thing-2.0.1.tgz", "happypack@^5.0.1": "https://registry.npmmirror.com/happypack/-/happypack-5.0.1.tgz", "har-schema@^2.0.0": "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz", "har-validator@~5.1.3": "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz", "hard-rejection@^2.1.0": "https://registry.npmmirror.com/hard-rejection/-/hard-rejection-2.1.0.tgz", "has-ansi@^2.0.0": "https://registry.npmmirror.com/has-ansi/-/has-ansi-2.0.0.tgz", "has-bigints@^1.0.1": "https://registry.npmmirror.com/has-bigints/-/has-bigints-1.0.1.tgz", "has-flag@^3.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz", "has-symbols@^1.0.1": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "has-symbols@^1.0.2": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "has-symbols@^1.0.3": "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz", "has-tostringtag@^1.0.0": "https://registry.npmmirror.com/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "has-value@^0.3.1": "https://registry.npmmirror.com/has-value/-/has-value-0.3.1.tgz", "has-value@^1.0.0": "https://registry.npmmirror.com/has-value/-/has-value-1.0.0.tgz", "has-values@^0.1.4": "https://registry.npmmirror.com/has-values/-/has-values-0.1.4.tgz", "has-values@^1.0.0": "https://registry.npmmirror.com/has-values/-/has-values-1.0.0.tgz", "has@^1.0.3": "https://registry.npmmirror.com/has/-/has-1.0.3.tgz", "hash-base@^3.0.0": "https://registry.npmmirror.com/hash-base/-/hash-base-3.1.0.tgz", "hash.js@^1.0.0": "https://registry.npmmirror.com/hash.js/-/hash.js-1.1.7.tgz", "hash.js@^1.0.3": "https://registry.npmmirror.com/hash.js/-/hash.js-1.1.7.tgz", "he@^1.2.0": "https://registry.npmmirror.com/he/-/he-1.2.0.tgz", "history@*": "https://registry.npmmirror.com/history/-/history-5.3.0.tgz", "history@^4.9.0": "https://registry.npmmirror.com/history/-/history-4.10.1.tgz", "hmac-drbg@^1.0.1": "https://registry.npmmirror.com/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "hoist-non-react-statics@^3.1.0": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoist-non-react-statics@^3.2.0": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoist-non-react-statics@^3.3.0": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoist-non-react-statics@^3.3.1": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoist-non-react-statics@^3.3.2": "https://registry.npmmirror.com/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "homedir-polyfill@^1.0.0": "https://registry.npmmirror.com/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "homedir-polyfill@^1.0.1": "https://registry.npmmirror.com/homedir-polyfill/-/homedir-polyfill-1.0.3.tgz", "hosted-git-info@^2.1.4": "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "hosted-git-info@^4.0.1": "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "hpack.js@^2.1.6": "https://registry.npmmirror.com/hpack.js/-/hpack.js-2.1.6.tgz", "html-entities@^1.2.1": "https://registry.npmmirror.com/html-entities/-/html-entities-1.4.0.tgz", "html-entities@^1.3.1": "https://registry.npmmirror.com/html-entities/-/html-entities-1.4.0.tgz", "html-minifier-terser@^5.0.1": "https://registry.npmmirror.com/html-minifier-terser/-/html-minifier-terser-5.1.1.tgz", "html-webpack-plugin@^4.4.1": "https://registry.npmmirror.com/html-webpack-plugin/-/html-webpack-plugin-4.5.2.tgz", "html2canvas@^1.0.0-rc.5": "https://registry.npmmirror.com/html2canvas/-/html2canvas-1.4.1.tgz", "htmlparser2@^6.1.0": "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-6.1.0.tgz", "http-deceiver@^1.2.7": "https://registry.npmmirror.com/http-deceiver/-/http-deceiver-1.2.7.tgz", "http-errors@1.8.1": "https://registry.npmmirror.com/http-errors/-/http-errors-1.8.1.tgz", "http-errors@2.0.0": "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz", "http-errors@~1.6.2": "https://registry.npmmirror.com/http-errors/-/http-errors-1.6.3.tgz", "http-parser-js@>=0.5.1": "https://registry.npmmirror.com/http-parser-js/-/http-parser-js-0.5.6.tgz", "http-proxy-agent@^4.0.0": "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "http-proxy-agent@^4.0.1": "https://registry.npmmirror.com/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "http-proxy-middleware@0.19.1": "https://registry.npmmirror.com/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz", "http-proxy@^1.17.0": "https://registry.npmmirror.com/http-proxy/-/http-proxy-1.18.1.tgz", "http-signature@~1.2.0": "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz", "https-browserify@^1.0.0": "https://registry.npmmirror.com/https-browserify/-/https-browserify-1.0.0.tgz", "https-proxy-agent@5": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "https-proxy-agent@^5.0.0": "https://registry.npmmirror.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "human-signals@^1.1.1": "https://registry.npmmirror.com/human-signals/-/human-signals-1.1.1.tgz", "humanize-ms@^1.2.0": "https://registry.npmmirror.com/humanize-ms/-/humanize-ms-1.2.1.tgz", "humanize-ms@^1.2.1": "https://registry.npmmirror.com/humanize-ms/-/humanize-ms-1.2.1.tgz", "husky@^4.3.7": "https://registry.npmmirror.com/husky/-/husky-4.3.8.tgz", "hyphenate-style-name@^1.0.3": "https://registry.npmmirror.com/hyphenate-style-name/-/hyphenate-style-name-1.0.4.tgz", "iconv-lite@0.4.24": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.4.15": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.4.24": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz", "icss-utils@^4.0.0": "https://registry.npmmirror.com/icss-utils/-/icss-utils-4.1.1.tgz", "icss-utils@^4.1.1": "https://registry.npmmirror.com/icss-utils/-/icss-utils-4.1.1.tgz", "ieee754@^1.1.13": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "ieee754@^1.1.4": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "iferr@^0.1.5": "https://registry.npmmirror.com/iferr/-/iferr-0.1.5.tgz", "ignore@^4.0.6": "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz", "ignore@^5.1.8": "https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz", "ignore@^5.2.0": "https://registry.npmmirror.com/ignore/-/ignore-5.2.0.tgz", "image-size@~0.5.0": "https://registry.npmmirror.com/image-size/-/image-size-0.5.5.tgz", "immediate@~3.0.5": "http://nexus3.luban.fit/repository/npm/immediate/-/immediate-3.0.6.tgz#9db1dbd0faf8de6fbe0f5dd5e56bb606280de69b", "immutable@^4.0.0": "https://registry.npmmirror.com/immutable/-/immutable-4.0.0.tgz", "import-fresh@^3.0.0": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz", "import-fresh@^3.1.0": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz", "import-fresh@^3.2.1": "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz", "import-lazy@^2.1.0": "https://registry.npmmirror.com/import-lazy/-/import-lazy-2.1.0.tgz", "import-local@^2.0.0": "https://registry.npmmirror.com/import-local/-/import-local-2.0.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz", "increase-memory-limit@1.0.3": "https://registry.npmmirror.com/increase-memory-limit/-/increase-memory-limit-1.0.3.tgz", "indent-string@^4.0.0": "https://registry.npmmirror.com/indent-string/-/indent-string-4.0.0.tgz", "individual@^2.0.0": "https://registry.npmmirror.com/individual/-/individual-2.0.0.tgz", "infer-owner@^1.0.3": "https://registry.npmmirror.com/infer-owner/-/infer-owner-1.0.4.tgz", "infer-owner@^1.0.4": "https://registry.npmmirror.com/infer-owner/-/infer-owner-1.0.4.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.1": "https://registry.npmmirror.com/inherits/-/inherits-2.0.1.tgz", "inherits@2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.3.tgz", "inherits@2.0.4": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.4": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.0": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.1": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz", "ini@^1.3.4": "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz", "ini@^1.3.5": "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz", "ini@~1.3.0": "https://registry.npmmirror.com/ini/-/ini-1.3.8.tgz", "inquirer@^7.0.0": "https://registry.npmmirror.com/inquirer/-/inquirer-7.3.3.tgz", "internal-ip@^4.3.0": "https://registry.npmmirror.com/internal-ip/-/internal-ip-4.3.0.tgz", "internal-slot@^1.0.3": "https://registry.npmmirror.com/internal-slot/-/internal-slot-1.0.3.tgz", "interpret@^1.4.0": "https://registry.npmmirror.com/interpret/-/interpret-1.4.0.tgz", "intersection-observer@^0.12.0": "https://registry.npmmirror.com/intersection-observer/-/intersection-observer-0.12.2.tgz", "ip-regex@^2.1.0": "https://registry.npmmirror.com/ip-regex/-/ip-regex-2.1.0.tgz", "ip@^1.1.0": "https://registry.npmmirror.com/ip/-/ip-1.1.5.tgz", "ip@^1.1.5": "https://registry.npmmirror.com/ip/-/ip-1.1.5.tgz", "ip@^2.0.0": "https://registry.npmmirror.com/ip/-/ip-2.0.0.tgz", "ipaddr.js@1.9.1": "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "ipaddr.js@^1.9.0": "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-absolute-url@^3.0.3": "https://registry.npmmirror.com/is-absolute-url/-/is-absolute-url-3.0.3.tgz", "is-accessor-descriptor@^0.1.6": "https://registry.npmmirror.com/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "is-accessor-descriptor@^1.0.0": "https://registry.npmmirror.com/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "is-arguments@^1.0.4": "https://registry.npmmirror.com/is-arguments/-/is-arguments-1.1.1.tgz", "is-arrayish@^0.2.1": "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-bigint@^1.0.1": "https://registry.npmmirror.com/is-bigint/-/is-bigint-1.0.4.tgz", "is-binary-path@^1.0.0": "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-1.0.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-blob@^2.1.0": "https://registry.npmmirror.com/is-blob/-/is-blob-2.1.0.tgz", "is-boolean-object@^1.1.0": "https://registry.npmmirror.com/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "is-buffer@^1.1.5": "https://registry.npmmirror.com/is-buffer/-/is-buffer-1.1.6.tgz", "is-callable@^1.1.4": "https://registry.npmmirror.com/is-callable/-/is-callable-1.2.4.tgz", "is-callable@^1.2.4": "https://registry.npmmirror.com/is-callable/-/is-callable-1.2.4.tgz", "is-ci@^1.0.10": "https://registry.npmmirror.com/is-ci/-/is-ci-1.2.1.tgz", "is-class-hotfix@~0.0.6": "https://registry.npmmirror.com/is-class-hotfix/-/is-class-hotfix-0.0.6.tgz", "is-core-module@^2.2.0": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.8.1.tgz", "is-core-module@^2.5.0": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.8.1.tgz", "is-core-module@^2.8.1": "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.8.1.tgz", "is-data-descriptor@^0.1.4": "https://registry.npmmirror.com/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "is-data-descriptor@^1.0.0": "https://registry.npmmirror.com/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "is-date-object@^1.0.1": "https://registry.npmmirror.com/is-date-object/-/is-date-object-1.0.5.tgz", "is-descriptor@^0.1.0": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-0.1.6.tgz", "is-descriptor@^1.0.0": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "is-descriptor@^1.0.2": "https://registry.npmmirror.com/is-descriptor/-/is-descriptor-1.0.2.tgz", "is-directory@^0.3.1": "https://registry.npmmirror.com/is-directory/-/is-directory-0.3.1.tgz", "is-docker@^2.0.0": "https://registry.npmmirror.com/is-docker/-/is-docker-2.2.1.tgz", "is-extendable@^0.1.0": "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^0.1.1": "https://registry.npmmirror.com/is-extendable/-/is-extendable-0.1.1.tgz", "is-extendable@^1.0.1": "https://registry.npmmirror.com/is-extendable/-/is-extendable-1.0.1.tgz", "is-extglob@^2.1.0": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "is-extglob@^2.1.1": "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^2.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-function@^1.0.1": "https://registry.npmmirror.com/is-function/-/is-function-1.0.2.tgz", "is-glob@^3.1.0": "https://registry.npmmirror.com/is-glob/-/is-glob-3.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz", "is-in-browser@^1.0.2": "https://registry.npmmirror.com/is-in-browser/-/is-in-browser-1.1.3.tgz", "is-in-browser@^1.1.3": "https://registry.npmmirror.com/is-in-browser/-/is-in-browser-1.1.3.tgz", "is-installed-globally@^0.1.0": "https://registry.npmmirror.com/is-installed-globally/-/is-installed-globally-0.1.0.tgz", "is-negative-zero@^2.0.2": "https://registry.npmmirror.com/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "is-npm@^1.0.0": "https://registry.npmmirror.com/is-npm/-/is-npm-1.0.0.tgz", "is-number-object@^1.0.4": "https://registry.npmmirror.com/is-number-object/-/is-number-object-1.0.6.tgz", "is-number@^3.0.0": "https://registry.npmmirror.com/is-number/-/is-number-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz", "is-obj@^1.0.0": "https://registry.npmmirror.com/is-obj/-/is-obj-1.0.1.tgz", "is-obj@^1.0.1": "https://registry.npmmirror.com/is-obj/-/is-obj-1.0.1.tgz", "is-obj@^2.0.0": "https://registry.npmmirror.com/is-obj/-/is-obj-2.0.0.tgz", "is-path-cwd@^2.0.0": "https://registry.npmmirror.com/is-path-cwd/-/is-path-cwd-2.2.0.tgz", "is-path-in-cwd@^2.0.0": "https://registry.npmmirror.com/is-path-in-cwd/-/is-path-in-cwd-2.1.0.tgz", "is-path-inside@^1.0.0": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-1.0.1.tgz", "is-path-inside@^2.1.0": "https://registry.npmmirror.com/is-path-inside/-/is-path-inside-2.1.0.tgz", "is-plain-obj@^1.1.0": "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-1.1.0.tgz", "is-plain-object@^2.0.3": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-plain-object@^2.0.4": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-redirect@^1.0.0": "https://registry.npmmirror.com/is-redirect/-/is-redirect-1.0.0.tgz", "is-regex@^1.0.4": "https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz", "is-regex@^1.1.4": "https://registry.npmmirror.com/is-regex/-/is-regex-1.1.4.tgz", "is-regexp@^1.0.0": "https://registry.npmmirror.com/is-regexp/-/is-regexp-1.0.0.tgz", "is-retry-allowed@^1.0.0": "https://registry.npmmirror.com/is-retry-allowed/-/is-retry-allowed-1.2.0.tgz", "is-shared-array-buffer@^1.0.1": "https://registry.npmmirror.com/is-shared-array-buffer/-/is-shared-array-buffer-1.0.1.tgz", "is-stream@^1.0.0": "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz", "is-stream@^1.1.0": "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz", "is-stream@^2.0.0": "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz", "is-string@^1.0.5": "https://registry.npmmirror.com/is-string/-/is-string-1.0.7.tgz", "is-string@^1.0.7": "https://registry.npmmirror.com/is-string/-/is-string-1.0.7.tgz", "is-symbol@^1.0.2": "https://registry.npmmirror.com/is-symbol/-/is-symbol-1.0.4.tgz", "is-symbol@^1.0.3": "https://registry.npmmirror.com/is-symbol/-/is-symbol-1.0.4.tgz", "is-text-path@^1.0.1": "https://registry.npmmirror.com/is-text-path/-/is-text-path-1.0.1.tgz", "is-type-of@^1.0.0": "https://registry.npmmirror.com/is-type-of/-/is-type-of-1.2.1.tgz", "is-typedarray@~1.0.0": "https://registry.npmmirror.com/is-typedarray/-/is-typedarray-1.0.0.tgz", "is-unicode-supported@^0.1.0": "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "is-weakref@^1.0.2": "https://registry.npmmirror.com/is-weakref/-/is-weakref-1.0.2.tgz", "is-what@^3.14.1": "https://registry.npmmirror.com/is-what/-/is-what-3.14.1.tgz", "is-windows@^1.0.0": "https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz", "is-windows@^1.0.1": "https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz", "is-windows@^1.0.2": "https://registry.npmmirror.com/is-windows/-/is-windows-1.0.2.tgz", "is-wsl@^1.1.0": "https://registry.npmmirror.com/is-wsl/-/is-wsl-1.1.0.tgz", "is-wsl@^2.1.1": "https://registry.npmmirror.com/is-wsl/-/is-wsl-2.2.0.tgz", "isarray@0.0.1": "https://registry.npmmirror.com/isarray/-/isarray-0.0.1.tgz", "isarray@1.0.0": "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz", "isarray@^1.0.0": "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz", "isarray@~1.0.0": "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz", "isobject@^2.0.0": "https://registry.npmmirror.com/isobject/-/isobject-2.1.0.tgz", "isobject@^3.0.0": "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz", "isobject@^3.0.1": "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz", "isstream@~0.1.2": "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz", "jest-worker@^26.5.0": "https://registry.npmmirror.com/jest-worker/-/jest-worker-26.6.2.tgz", "jointjs@^3.5.5": "https://registry.npmmirror.com/jointjs/-/jointjs-3.5.5.tgz", "jquery@~3.6.0": "https://registry.npmmirror.com/jquery/-/jquery-3.6.0.tgz", "js-base64@^2.5.2": "https://registry.npmmirror.com/js-base64/-/js-base64-2.6.4.tgz", "js-base64@^3.6.0": "https://registry.npmmirror.com/js-base64/-/js-base64-3.7.2.tgz", "js-cookie@^2.x.x": "https://registry.npmmirror.com/js-cookie/-/js-cookie-2.2.1.tgz", "js-md5@^0.7.3": "https://registry.npmmirror.com/js-md5/-/js-md5-0.7.3.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^3.0.2": "https://registry.npmmirror.com/js-tokens/-/js-tokens-3.0.2.tgz", "js-tokens@^4.0.0": "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^3.13.0": "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^3.13.1": "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz", "jsbn@~0.1.0": "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz", "jsesc@^2.5.1": "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz", "jsesc@~0.5.0": "https://registry.npmmirror.com/jsesc/-/jsesc-0.5.0.tgz", "json-parse-better-errors@^1.0.2": "https://registry.npmmirror.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parser@^1.0.0": "https://registry.npmmirror.com/json-parser/-/json-parser-1.1.5.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-schema@0.4.0": "https://registry.npmmirror.com/json-schema/-/json-schema-0.4.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json-stringify-safe@5.0.1": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json-stringify-safe@~5.0.1": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "json2mq@^0.2.0": "https://registry.npmmirror.com/json2mq/-/json2mq-0.2.0.tgz", "json5@^0.5.0": "https://registry.npmmirror.com/json5/-/json5-0.5.1.tgz", "json5@^1.0.1": "https://registry.npmmirror.com/json5/-/json5-1.0.1.tgz", "json5@^2.1.2": "https://registry.npmmirror.com/json5/-/json5-2.2.1.tgz", "jsonfile@^4.0.0": "https://registry.npmmirror.com/jsonfile/-/jsonfile-4.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz", "jsonparse@^1.2.0": "https://registry.npmmirror.com/jsonparse/-/jsonparse-1.3.1.tgz", "jspdf@*": "https://registry.npmmirror.com/jspdf/-/jspdf-2.5.1.tgz", "jspdf@^2.5.1": "https://registry.npmmirror.com/jspdf/-/jspdf-2.5.1.tgz", "jsprim@^1.2.2": "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz", "jss-plugin-camel-case@10.9.0": "https://registry.npmmirror.com/jss-plugin-camel-case/-/jss-plugin-camel-case-10.9.0.tgz", "jss-plugin-compose@10.9.0": "https://registry.npmmirror.com/jss-plugin-compose/-/jss-plugin-compose-10.9.0.tgz", "jss-plugin-default-unit@10.9.0": "https://registry.npmmirror.com/jss-plugin-default-unit/-/jss-plugin-default-unit-10.9.0.tgz", "jss-plugin-expand@10.9.0": "https://registry.npmmirror.com/jss-plugin-expand/-/jss-plugin-expand-10.9.0.tgz", "jss-plugin-extend@10.9.0": "https://registry.npmmirror.com/jss-plugin-extend/-/jss-plugin-extend-10.9.0.tgz", "jss-plugin-global@10.9.0": "https://registry.npmmirror.com/jss-plugin-global/-/jss-plugin-global-10.9.0.tgz", "jss-plugin-nested@10.9.0": "https://registry.npmmirror.com/jss-plugin-nested/-/jss-plugin-nested-10.9.0.tgz", "jss-plugin-props-sort@10.9.0": "https://registry.npmmirror.com/jss-plugin-props-sort/-/jss-plugin-props-sort-10.9.0.tgz", "jss-plugin-rule-value-function@10.9.0": "https://registry.npmmirror.com/jss-plugin-rule-value-function/-/jss-plugin-rule-value-function-10.9.0.tgz", "jss-plugin-rule-value-observable@10.9.0": "https://registry.npmmirror.com/jss-plugin-rule-value-observable/-/jss-plugin-rule-value-observable-10.9.0.tgz", "jss-plugin-template@10.9.0": "https://registry.npmmirror.com/jss-plugin-template/-/jss-plugin-template-10.9.0.tgz", "jss-plugin-vendor-prefixer@10.9.0": "https://registry.npmmirror.com/jss-plugin-vendor-prefixer/-/jss-plugin-vendor-prefixer-10.9.0.tgz", "jss-preset-default@10.9.0": "https://registry.npmmirror.com/jss-preset-default/-/jss-preset-default-10.9.0.tgz", "jss-preset-default@^10.4.0": "https://registry.npmmirror.com/jss-preset-default/-/jss-preset-default-10.9.0.tgz", "jss-preset-default@^10.9.0": "https://registry.npmmirror.com/jss-preset-default/-/jss-preset-default-10.9.0.tgz", "jss@10.9.0": "https://registry.npmmirror.com/jss/-/jss-10.9.0.tgz", "jss@^10.4.0": "https://registry.npmmirror.com/jss/-/jss-10.9.0.tgz", "jss@^10.9.0": "https://registry.npmmirror.com/jss/-/jss-10.9.0.tgz", "jstoxml@^2.0.0": "https://registry.npmmirror.com/jstoxml/-/jstoxml-2.2.9.tgz", "jsx-ast-utils@^2.1.0": "https://registry.npmmirror.com/jsx-ast-utils/-/jsx-ast-utils-2.4.1.tgz", "jsx-ast-utils@^2.2.1": "https://registry.npmmirror.com/jsx-ast-utils/-/jsx-ast-utils-2.4.1.tgz", "jsx-ast-utils@^2.4.1 || ^3.0.0": "https://registry.npmmirror.com/jsx-ast-utils/-/jsx-ast-utils-3.2.1.tgz", "jszip@^3.10.1": "http://nexus3.luban.fit/repository/npm/jszip/-/jszip-3.10.1.tgz#34aee70eb18ea1faec2f589208a157d1feb091c2", "keycode@^2.2.0": "https://registry.npmmirror.com/keycode/-/keycode-2.2.1.tgz", "killable@^1.0.1": "https://registry.npmmirror.com/killable/-/killable-1.0.1.tgz", "kind-of@^3.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.0.3": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^3.2.0": "https://registry.npmmirror.com/kind-of/-/kind-of-3.2.2.tgz", "kind-of@^4.0.0": "https://registry.npmmirror.com/kind-of/-/kind-of-4.0.0.tgz", "kind-of@^5.0.0": "https://registry.npmmirror.com/kind-of/-/kind-of-5.1.0.tgz", "kind-of@^6.0.0": "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz", "kind-of@^6.0.2": "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz", "kind-of@^6.0.3": "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz", "kleur@^1.0.0": "https://registry.npmmirror.com/kleur/-/kleur-1.0.2.tgz", "kleur@^2.0.2": "https://registry.npmmirror.com/kleur/-/kleur-2.0.2.tgz", "klona@^2.0.4": "https://registry.npmmirror.com/klona/-/klona-2.0.5.tgz", "ko-sleep@^1.0.3": "https://registry.npmmirror.com/ko-sleep/-/ko-sleep-1.1.4.tgz", "latest-version@^3.0.0": "https://registry.npmmirror.com/latest-version/-/latest-version-3.1.0.tgz", "lazystream@^1.0.0": "https://registry.npmmirror.com/lazystream/-/lazystream-1.0.1.tgz", "less-loader@^7.0.1": "https://registry.npmmirror.com/less-loader/-/less-loader-7.3.0.tgz", "less@^3.12.2": "https://registry.npmmirror.com/less/-/less-3.13.1.tgz", "levn@^0.3.0": "https://registry.npmmirror.com/levn/-/levn-0.3.0.tgz", "levn@^0.4.1": "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz", "levn@~0.3.0": "https://registry.npmmirror.com/levn/-/levn-0.3.0.tgz", "lie@~3.3.0": "http://nexus3.luban.fit/repository/npm/lie/-/lie-3.3.0.tgz#dcf82dee545f46074daf200c7c1c5a08e0f40f6a", "lines-and-columns@^1.1.6": "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "lint-staged@^10.5.3": "https://registry.npmmirror.com/lint-staged/-/lint-staged-10.5.4.tgz", "listenercount@~1.0.1": "http://nexus3.luban.fit/repository/npm/listenercount/-/listenercount-1.0.1.tgz#84c8a72ab59c4725321480c975e6508342e70937", "listify@^1.0.0": "https://registry.npmmirror.com/listify/-/listify-1.0.3.tgz", "listr2@^3.2.2": "https://registry.npmmirror.com/listr2/-/listr2-3.14.0.tgz", "load-json-file@^2.0.0": "https://registry.npmmirror.com/load-json-file/-/load-json-file-2.0.0.tgz", "loader-runner@^2.4.0": "https://registry.npmmirror.com/loader-runner/-/loader-runner-2.4.0.tgz", "loader-utils@1.1.0": "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.1.0.tgz", "loader-utils@^1.2.3": "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.0.tgz", "loader-utils@^1.4.0": "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.0.tgz", "loader-utils@^2.0.0": "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.2.tgz", "locate-path@^2.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-2.0.0.tgz", "locate-path@^3.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-3.0.0.tgz", "locate-path@^5.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmmirror.com/locate-path/-/locate-path-6.0.0.tgz", "lodash-es@^4.17.15": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "lodash-es@^4.17.21": "https://registry.npmmirror.com/lodash-es/-/lodash-es-4.17.21.tgz", "lodash.debounce@^4.0.8": "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.defaults@^4.2.0": "https://registry.npmmirror.com/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "lodash.difference@^4.5.0": "https://registry.npmmirror.com/lodash.difference/-/lodash.difference-4.5.0.tgz", "lodash.escaperegexp@^4.1.2": "http://nexus3.luban.fit/repository/npm/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz#64762c48618082518ac3df4ccf5d5886dae20347", "lodash.flatten@^4.4.0": "https://registry.npmmirror.com/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "lodash.groupby@^4.6.0": "http://nexus3.luban.fit/repository/npm/lodash.groupby/-/lodash.groupby-4.6.0.tgz#0b08a1dcf68397c397855c3239783832df7403d1", "lodash.isboolean@^3.0.3": "http://nexus3.luban.fit/repository/npm/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz#6c2e171db2a257cd96802fd43b01b20d5f5870f6", "lodash.isequal@^4.5.0": "http://nexus3.luban.fit/repository/npm/lodash.isequal/-/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0", "lodash.isfunction@^3.0.9": "http://nexus3.luban.fit/repository/npm/lodash.isfunction/-/lodash.isfunction-3.0.9.tgz#06de25df4db327ac931981d1bdb067e5af68d051", "lodash.isnil@^4.0.0": "http://nexus3.luban.fit/repository/npm/lodash.isnil/-/lodash.isnil-4.0.0.tgz#49e28cd559013458c814c5479d3c663a21bfaa6c", "lodash.isplainobject@^4.0.6": "https://registry.npmmirror.com/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "lodash.isundefined@^3.0.1": "http://nexus3.luban.fit/repository/npm/lodash.isundefined/-/lodash.isundefined-3.0.1.tgz#23ef3d9535565203a66cefd5b830f848911afb48", "lodash.merge@^4.6.2": "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.truncate@^4.4.2": "https://registry.npmmirror.com/lodash.truncate/-/lodash.truncate-4.4.2.tgz", "lodash.union@^4.6.0": "https://registry.npmmirror.com/lodash.union/-/lodash.union-4.6.0.tgz", "lodash.uniq@^4.5.0": "http://nexus3.luban.fit/repository/npm/lodash.uniq/-/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773", "lodash@^4.0.1": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.10": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.11": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.14": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.15": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.19": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.20": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "lodash@~4.17.21": "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz", "log-symbols@^4.0.0": "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz", "log-update@^4.0.0": "https://registry.npmmirror.com/log-update/-/log-update-4.0.0.tgz", "loglevel@^1.6.8": "https://registry.npmmirror.com/loglevel/-/loglevel-1.8.0.tgz", "longest@^2.0.1": "https://registry.npmmirror.com/longest/-/longest-2.0.1.tgz", "loose-envify@^1.1.0": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.2.0": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.3.1": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.4.0": "https://registry.npmmirror.com/loose-envify/-/loose-envify-1.4.0.tgz", "lower-case@^2.0.2": "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz", "lowercase-keys@^1.0.0": "https://registry.npmmirror.com/lowercase-keys/-/lowercase-keys-1.0.1.tgz", "lru-cache@^4.0.1": "https://registry.npmmirror.com/lru-cache/-/lru-cache-4.1.5.tgz", "lru-cache@^4.1.5": "https://registry.npmmirror.com/lru-cache/-/lru-cache-4.1.5.tgz", "lru-cache@^5.1.1": "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz", "lru-cache@^6.0.0": "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz", "m3u8-parser@4.7.1": "https://registry.npmmirror.com/m3u8-parser/-/m3u8-parser-4.7.1.tgz", "make-dir@^1.0.0": "https://registry.npmmirror.com/make-dir/-/make-dir-1.3.0.tgz", "make-dir@^2.0.0": "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz", "make-dir@^2.1.0": "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz", "make-dir@^3.0.2": "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^3.1.0": "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz", "map-cache@^0.2.2": "https://registry.npmmirror.com/map-cache/-/map-cache-0.2.2.tgz", "map-obj@^1.0.0": "https://registry.npmmirror.com/map-obj/-/map-obj-1.0.1.tgz", "map-obj@^4.0.0": "https://registry.npmmirror.com/map-obj/-/map-obj-4.3.0.tgz", "map-visit@^1.0.0": "https://registry.npmmirror.com/map-visit/-/map-visit-1.0.0.tgz", "material-colors@^1.2.1": "https://registry.npmmirror.com/material-colors/-/material-colors-1.2.6.tgz", "md5.js@^1.3.4": "https://registry.npmmirror.com/md5.js/-/md5.js-1.3.5.tgz", "media-typer@0.3.0": "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz", "memfs@^3.1.2": "https://registry.npmmirror.com/memfs/-/memfs-3.4.1.tgz", "memoize-one@>=3.1.1 <6": "https://registry.npmmirror.com/memoize-one/-/memoize-one-5.2.1.tgz", "memoize-one@^6.0.0": "https://registry.npmmirror.com/memoize-one/-/memoize-one-6.0.0.tgz", "memory-fs@^0.4.1": "https://registry.npmmirror.com/memory-fs/-/memory-fs-0.4.1.tgz", "memory-fs@^0.5.0": "https://registry.npmmirror.com/memory-fs/-/memory-fs-0.5.0.tgz", "meow@^8.0.0": "https://registry.npmmirror.com/meow/-/meow-8.1.2.tgz", "merge-descriptors@1.0.1": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "merge-descriptors@^1.0.1": "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "merge-stream@^2.0.0": "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz", "methods@~1.1.2": "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz", "micromatch@^3.0.4": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.10.tgz", "micromatch@^3.1.10": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.10.tgz", "micromatch@^3.1.4": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.10.tgz", "micromatch@^4.0.0": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "micromatch@^4.0.2": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "micromatch@^4.0.4": "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz", "middleearth-names@^1.1.0": "https://registry.npmmirror.com/middleearth-names/-/middleearth-names-1.1.0.tgz", "miller-rabin@^4.0.0": "https://registry.npmmirror.com/miller-rabin/-/miller-rabin-4.0.1.tgz", "mime-db@1.52.0": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.27": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.17": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.19": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.24": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz", "mime@^1.4.1": "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz", "mime@^2.4.4": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "mime@^2.4.5": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "mime@^2.5.2": "https://registry.npmmirror.com/mime/-/mime-2.6.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz", "min-document@^2.19.0": "https://registry.npmmirror.com/min-document/-/min-document-2.19.0.tgz", "min-indent@^1.0.0": "https://registry.npmmirror.com/min-indent/-/min-indent-1.0.1.tgz", "mini-create-react-context@^0.4.0": "https://registry.npmmirror.com/mini-create-react-context/-/mini-create-react-context-0.4.1.tgz", "mini-css-extract-plugin@^1.3.3": "https://registry.npmmirror.com/mini-css-extract-plugin/-/mini-css-extract-plugin-1.6.2.tgz", "mini-svg-data-uri@^1.2.3": "https://registry.npmmirror.com/mini-svg-data-uri/-/mini-svg-data-uri-1.4.4.tgz", "minimalistic-assert@^1.0.0": "https://registry.npmmirror.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimalistic-assert@^1.0.1": "https://registry.npmmirror.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimalistic-crypto-utils@^1.0.1": "https://registry.npmmirror.com/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "minimatch@^3.0.4": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz", "minimist-options@4.1.0": "https://registry.npmmirror.com/minimist-options/-/minimist-options-4.1.0.tgz", "minimist@^1.1.0": "https://registry.npmmirror.com/minimist/-/minimist-1.2.6.tgz", "minimist@^1.2.0": "https://registry.npmmirror.com/minimist/-/minimist-1.2.6.tgz", "minimist@^1.2.6": "https://registry.npmmirror.com/minimist/-/minimist-1.2.6.tgz", "minipass-collect@^1.0.2": "https://registry.npmmirror.com/minipass-collect/-/minipass-collect-1.0.2.tgz", "minipass-flush@^1.0.5": "https://registry.npmmirror.com/minipass-flush/-/minipass-flush-1.0.5.tgz", "minipass-pipeline@^1.2.2": "https://registry.npmmirror.com/minipass-pipeline/-/minipass-pipeline-1.2.4.tgz", "minipass@^3.0.0": "https://registry.npmmirror.com/minipass/-/minipass-3.1.6.tgz", "minipass@^3.1.1": "https://registry.npmmirror.com/minipass/-/minipass-3.1.6.tgz", "minizlib@^2.1.1": "https://registry.npmmirror.com/minizlib/-/minizlib-2.1.2.tgz", "mississippi@^3.0.0": "https://registry.npmmirror.com/mississippi/-/mississippi-3.0.0.tgz", "mixin-deep@^1.2.0": "https://registry.npmmirror.com/mixin-deep/-/mixin-deep-1.3.2.tgz", "mkdirp@>=0.5 0": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^0.5.1": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^0.5.3": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^0.5.5": "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz", "mkdirp@^1.0.3": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz", "mkdirp@^1.0.4": "https://registry.npmmirror.com/mkdirp/-/mkdirp-1.0.4.tgz", "moment@^2.24.0": "https://registry.npmmirror.com/moment/-/moment-2.29.1.tgz", "moment@^2.25.3": "https://registry.npmmirror.com/moment/-/moment-2.29.1.tgz", "moment@^2.27.0": "https://registry.npmmirror.com/moment/-/moment-2.29.3.tgz", "moment@^2.29.1": "https://registry.npmmirror.com/moment/-/moment-2.29.1.tgz", "moment@^2.29.2": "https://registry.npmmirror.com/moment/-/moment-2.29.3.tgz", "move-concurrently@^1.0.1": "https://registry.npmmirror.com/move-concurrently/-/move-concurrently-1.0.1.tgz", "mpd-parser@0.21.1": "https://registry.npmmirror.com/mpd-parser/-/mpd-parser-0.21.1.tgz", "mrm-core@^3.1.2": "https://registry.npmmirror.com/mrm-core/-/mrm-core-3.3.4.tgz", "mrm-core@^3.3.0": "https://registry.npmmirror.com/mrm-core/-/mrm-core-3.3.4.tgz", "mrm-core@^3.3.4": "https://registry.npmmirror.com/mrm-core/-/mrm-core-3.3.4.tgz", "mrm-preset-default@^1.8.0": "https://registry.npmmirror.com/mrm-preset-default/-/mrm-preset-default-1.12.0.tgz", "mrm-task-codecov@^1.1.0": "https://registry.npmmirror.com/mrm-task-codecov/-/mrm-task-codecov-1.1.0.tgz", "mrm-task-contributing@^1.1.0": "https://registry.npmmirror.com/mrm-task-contributing/-/mrm-task-contributing-1.1.0.tgz", "mrm-task-editorconfig@^1.1.0": "https://registry.npmmirror.com/mrm-task-editorconfig/-/mrm-task-editorconfig-1.1.0.tgz", "mrm-task-eslint@^1.2.1": "https://registry.npmmirror.com/mrm-task-eslint/-/mrm-task-eslint-1.2.1.tgz", "mrm-task-gitignore@^1.1.3": "https://registry.npmmirror.com/mrm-task-gitignore/-/mrm-task-gitignore-1.1.3.tgz", "mrm-task-jest@^1.2.5": "https://registry.npmmirror.com/mrm-task-jest/-/mrm-task-jest-1.2.5.tgz", "mrm-task-license@^1.2.3": "https://registry.npmmirror.com/mrm-task-license/-/mrm-task-license-1.2.3.tgz", "mrm-task-lint-staged@^1.4.1": "https://registry.npmmirror.com/mrm-task-lint-staged/-/mrm-task-lint-staged-1.4.1.tgz", "mrm-task-package@^1.1.2": "https://registry.npmmirror.com/mrm-task-package/-/mrm-task-package-1.1.2.tgz", "mrm-task-prettier@^1.2.5": "https://registry.npmmirror.com/mrm-task-prettier/-/mrm-task-prettier-1.2.5.tgz", "mrm-task-readme@^1.1.3": "https://registry.npmmirror.com/mrm-task-readme/-/mrm-task-readme-1.1.3.tgz", "mrm-task-semantic-release@^2.2.0": "https://registry.npmmirror.com/mrm-task-semantic-release/-/mrm-task-semantic-release-2.2.0.tgz", "mrm-task-styleguidist@^1.0.3": "https://registry.npmmirror.com/mrm-task-styleguidist/-/mrm-task-styleguidist-1.0.3.tgz", "mrm-task-stylelint@^2.0.3": "https://registry.npmmirror.com/mrm-task-stylelint/-/mrm-task-stylelint-2.0.3.tgz", "mrm-task-travis@^1.1.2": "https://registry.npmmirror.com/mrm-task-travis/-/mrm-task-travis-1.1.2.tgz", "mrm-task-typescript@^1.0.3": "https://registry.npmmirror.com/mrm-task-typescript/-/mrm-task-typescript-1.0.3.tgz", "mrm@^1.2.2": "https://registry.npmmirror.com/mrm/-/mrm-1.2.3.tgz", "mrmime@^1.0.0": "https://registry.npmmirror.com/mrmime/-/mrmime-1.0.0.tgz", "ms@*": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "ms@2.0.0": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "ms@2.1.2": "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz", "ms@2.1.3": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "ms@^2.0.0": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz", "multicast-dns-service-types@^1.1.0": "https://registry.npmmirror.com/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "multicast-dns@^6.0.1": "https://registry.npmmirror.com/multicast-dns/-/multicast-dns-6.2.3.tgz", "mute-stream@0.0.8": "https://registry.npmmirror.com/mute-stream/-/mute-stream-0.0.8.tgz", "mux.js@6.0.1": "https://registry.npmmirror.com/mux.js/-/mux.js-6.0.1.tgz", "mz-modules@^2.1.0": "https://registry.npmmirror.com/mz-modules/-/mz-modules-2.1.0.tgz", "mz@^2.7.0": "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz", "nan@^2.12.1": "https://registry.npmmirror.com/nan/-/nan-2.17.0.tgz#c0150a2368a182f033e9aa5195ec76ea41a199cb", "nanoid@^3.3.2": "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.2.tgz", "nanomatch@^1.2.9": "https://registry.npmmirror.com/nanomatch/-/nanomatch-1.2.13.tgz", "native-request@^1.0.5": "https://registry.npmmirror.com/native-request/-/native-request-1.1.0.tgz", "native-url@^0.2.6": "https://registry.npmmirror.com/native-url/-/native-url-0.2.6.tgz", "natural-compare@^1.4.0": "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz", "negotiator@0.6.3": "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz", "neo-async@^2.5.0": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "neo-async@^2.6.1": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "neo-async@^2.6.2": "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz", "nested-error-stacks@~2.0.1": "https://registry.npmmirror.com/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz", "netmask@^2.0.2": "https://registry.npmmirror.com/netmask/-/netmask-2.0.2.tgz", "nice-try@^1.0.4": "https://registry.npmmirror.com/nice-try/-/nice-try-1.0.5.tgz", "no-case@^3.0.4": "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz", "node-forge@^0.10.0": "https://registry.npmmirror.com/node-forge/-/node-forge-0.10.0.tgz", "node-libs-browser@^2.2.1": "https://registry.npmmirror.com/node-libs-browser/-/node-libs-browser-2.2.1.tgz", "node-notifier@^6.0.0": "https://registry.npmmirror.com/node-notifier/-/node-notifier-6.0.0.tgz", "node-releases@^2.0.2": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.2.tgz", "normalize-package-data@^2.3.2": "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "normalize-package-data@^2.5.0": "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "normalize-package-data@^3.0.0": "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-3.0.3.tgz", "normalize-path@^2.1.1": "https://registry.npmmirror.com/normalize-path/-/normalize-path-2.1.1.tgz", "normalize-path@^3.0.0": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz", "npm-run-path@^2.0.0": "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-2.0.2.tgz", "npm-run-path@^4.0.0": "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz", "nth-check@^2.0.1": "https://registry.npmmirror.com/nth-check/-/nth-check-2.0.1.tgz", "num2fraction@^1.2.2": "https://registry.npmmirror.com/num2fraction/-/num2fraction-1.2.2.tgz", "oauth-sign@~0.9.0": "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz", "object-assign@^4.0.1": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz", "object-copy@^0.1.0": "https://registry.npmmirror.com/object-copy/-/object-copy-0.1.0.tgz", "object-inspect@^1.12.0": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.0.tgz", "object-inspect@^1.9.0": "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.12.0.tgz", "object-is@^1.0.1": "https://registry.npmmirror.com/object-is/-/object-is-1.1.5.tgz", "object-keys@^1.0.12": "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz", "object-keys@^1.1.1": "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz", "object-visit@^1.0.0": "https://registry.npmmirror.com/object-visit/-/object-visit-1.0.1.tgz", "object.assign@^4.1.0": "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.2.tgz", "object.assign@^4.1.2": "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.2.tgz", "object.entries@^1.1.0": "https://registry.npmmirror.com/object.entries/-/object.entries-1.1.5.tgz", "object.entries@^1.1.2": "https://registry.npmmirror.com/object.entries/-/object.entries-1.1.5.tgz", "object.entries@^1.1.5": "https://registry.npmmirror.com/object.entries/-/object.entries-1.1.5.tgz", "object.fromentries@^2.0.0": "https://registry.npmmirror.com/object.fromentries/-/object.fromentries-2.0.5.tgz", "object.fromentries@^2.0.5": "https://registry.npmmirror.com/object.fromentries/-/object.fromentries-2.0.5.tgz", "object.getownpropertydescriptors@^2.0.3": "https://registry.npmmirror.com/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.3.tgz", "object.hasown@^1.1.0": "https://registry.npmmirror.com/object.hasown/-/object.hasown-1.1.0.tgz", "object.pick@^1.3.0": "https://registry.npmmirror.com/object.pick/-/object.pick-1.3.0.tgz", "object.values@^1.1.0": "https://registry.npmmirror.com/object.values/-/object.values-1.1.5.tgz", "object.values@^1.1.5": "https://registry.npmmirror.com/object.values/-/object.values-1.1.5.tgz", "obuf@^1.0.0": "https://registry.npmmirror.com/obuf/-/obuf-1.1.2.tgz", "obuf@^1.1.2": "https://registry.npmmirror.com/obuf/-/obuf-1.1.2.tgz", "on-finished@~2.3.0": "https://registry.npmmirror.com/on-finished/-/on-finished-2.3.0.tgz", "on-headers@~1.0.2": "https://registry.npmmirror.com/on-headers/-/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmmirror.com/once/-/once-1.4.0.tgz", "onetime@^5.1.0": "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz", "opencollective-postinstall@^2.0.2": "https://registry.npmmirror.com/opencollective-postinstall/-/opencollective-postinstall-2.0.3.tgz", "opener@^1.5.2": "https://registry.npmmirror.com/opener/-/opener-1.5.2.tgz", "opn@^5.5.0": "https://registry.npmmirror.com/opn/-/opn-5.5.0.tgz", "optionator@^0.8.1": "https://registry.npmmirror.com/optionator/-/optionator-0.8.3.tgz", "optionator@^0.8.3": "https://registry.npmmirror.com/optionator/-/optionator-0.8.3.tgz", "optionator@^0.9.1": "https://registry.npmmirror.com/optionator/-/optionator-0.9.1.tgz", "original@^1.0.0": "https://registry.npmmirror.com/original/-/original-1.0.2.tgz", "os-browserify@^0.3.0": "https://registry.npmmirror.com/os-browserify/-/os-browserify-0.3.0.tgz", "os-homedir@^1.0.0": "https://registry.npmmirror.com/os-homedir/-/os-homedir-1.0.2.tgz", "os-name@~1.0.3": "https://registry.npmmirror.com/os-name/-/os-name-1.0.3.tgz", "os-tmpdir@~1.0.2": "https://registry.npmmirror.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "osx-release@^1.0.0": "https://registry.npmmirror.com/osx-release/-/osx-release-1.1.0.tgz", "p-finally@^1.0.0": "https://registry.npmmirror.com/p-finally/-/p-finally-1.0.0.tgz", "p-limit@^1.1.0": "https://registry.npmmirror.com/p-limit/-/p-limit-1.3.0.tgz", "p-limit@^2.0.0": "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^2.2.0": "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmmirror.com/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^2.0.0": "https://registry.npmmirror.com/p-locate/-/p-locate-2.0.0.tgz", "p-locate@^3.0.0": "https://registry.npmmirror.com/p-locate/-/p-locate-3.0.0.tgz", "p-locate@^4.1.0": "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmmirror.com/p-locate/-/p-locate-5.0.0.tgz", "p-map@^2.0.0": "https://registry.npmmirror.com/p-map/-/p-map-2.1.0.tgz", "p-map@^4.0.0": "https://registry.npmmirror.com/p-map/-/p-map-4.0.0.tgz", "p-retry@^3.0.1": "https://registry.npmmirror.com/p-retry/-/p-retry-3.0.1.tgz", "p-try@^1.0.0": "https://registry.npmmirror.com/p-try/-/p-try-1.0.0.tgz", "p-try@^2.0.0": "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz", "pac-proxy-agent@^5.0.0": "https://registry.npmmirror.com/pac-proxy-agent/-/pac-proxy-agent-5.0.0.tgz", "pac-resolver@^5.0.0": "https://registry.npmmirror.com/pac-resolver/-/pac-resolver-5.0.1.tgz", "package-json@^4.0.0": "https://registry.npmmirror.com/package-json/-/package-json-4.0.1.tgz", "pako@~1.0.2": "https://registry.npmmirror.com/pako/-/pako-1.0.11.tgz", "pako@~1.0.5": "https://registry.npmmirror.com/pako/-/pako-1.0.11.tgz", "parallel-transform@^1.1.0": "https://registry.npmmirror.com/parallel-transform/-/parallel-transform-1.2.0.tgz", "param-case@^3.0.3": "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz", "parent-module@^1.0.0": "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz", "parse-asn1@^5.0.0": "https://registry.npmmirror.com/parse-asn1/-/parse-asn1-5.1.6.tgz", "parse-asn1@^5.1.5": "https://registry.npmmirror.com/parse-asn1/-/parse-asn1-5.1.6.tgz", "parse-git-config@^1.1.1": "https://registry.npmmirror.com/parse-git-config/-/parse-git-config-1.1.1.tgz", "parse-github-url@^1.0.2": "https://registry.npmmirror.com/parse-github-url/-/parse-github-url-1.0.2.tgz", "parse-json@^2.2.0": "https://registry.npmmirror.com/parse-json/-/parse-json-2.2.0.tgz", "parse-json@^5.0.0": "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz", "parse-passwd@^1.0.0": "https://registry.npmmirror.com/parse-passwd/-/parse-passwd-1.0.0.tgz", "parseurl@~1.3.2": "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz", "parseurl@~1.3.3": "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz", "pascal-case@^3.1.2": "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz", "pascalcase@^0.1.1": "https://registry.npmmirror.com/pascalcase/-/pascalcase-0.1.1.tgz", "path-browserify@0.0.1": "https://registry.npmmirror.com/path-browserify/-/path-browserify-0.0.1.tgz", "path-dirname@^1.0.0": "https://registry.npmmirror.com/path-dirname/-/path-dirname-1.0.2.tgz", "path-exists@^3.0.0": "https://registry.npmmirror.com/path-exists/-/path-exists-3.0.0.tgz", "path-exists@^4.0.0": "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-is-inside@^1.0.1": "https://registry.npmmirror.com/path-is-inside/-/path-is-inside-1.0.2.tgz", "path-is-inside@^1.0.2": "https://registry.npmmirror.com/path-is-inside/-/path-is-inside-1.0.2.tgz", "path-key@^2.0.0": "https://registry.npmmirror.com/path-key/-/path-key-2.0.1.tgz", "path-key@^2.0.1": "https://registry.npmmirror.com/path-key/-/path-key-2.0.1.tgz", "path-key@^3.0.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.5": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "path-parse@^1.0.6": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "path-parse@^1.0.7": "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz", "path-to-regexp@0.1.7": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "path-to-regexp@^1.7.0": "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-1.8.0.tgz", "path-type@^2.0.0": "https://registry.npmmirror.com/path-type/-/path-type-2.0.0.tgz", "path-type@^4.0.0": "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz", "pause-stream@~0.0.11": "https://registry.npmmirror.com/pause-stream/-/pause-stream-0.0.11.tgz", "pbkdf2@^3.0.3": "https://registry.npmmirror.com/pbkdf2/-/pbkdf2-3.1.2.tgz", "performance-now@^2.1.0": "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz", "picocolors@^0.2.1": "https://registry.npmmirror.com/picocolors/-/picocolors-0.2.1.tgz", "picocolors@^1.0.0": "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz", "picomatch@^2.0.4": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.2": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz", "pify@^2.0.0": "https://registry.npmmirror.com/pify/-/pify-2.3.0.tgz", "pify@^3.0.0": "https://registry.npmmirror.com/pify/-/pify-3.0.0.tgz", "pify@^4.0.1": "https://registry.npmmirror.com/pify/-/pify-4.0.1.tgz", "pinkie-promise@^2.0.0": "https://registry.npmmirror.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "pinkie@^2.0.0": "https://registry.npmmirror.com/pinkie/-/pinkie-2.0.4.tgz", "pkcs7@^1.0.4": "https://registry.npmmirror.com/pkcs7/-/pkcs7-1.0.4.tgz", "pkg-dir@^3.0.0": "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-3.0.0.tgz", "pkg-dir@^4.1.0": "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz", "pkg-dir@^5.0.0": "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-5.0.0.tgz", "platform@^1.3.1": "https://registry.npmmirror.com/platform/-/platform-1.3.6.tgz", "please-upgrade-node@^3.2.0": "https://registry.npmmirror.com/please-upgrade-node/-/please-upgrade-node-3.2.0.tgz", "portfinder@^1.0.26": "https://registry.npmmirror.com/portfinder/-/portfinder-1.0.28.tgz", "posix-character-classes@^0.1.0": "https://registry.npmmirror.com/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "postcss-loader@^4.0.1": "https://registry.npmmirror.com/postcss-loader/-/postcss-loader-4.3.0.tgz", "postcss-modules-extract-imports@^2.0.0": "https://registry.npmmirror.com/postcss-modules-extract-imports/-/postcss-modules-extract-imports-2.0.0.tgz", "postcss-modules-local-by-default@^3.0.3": "https://registry.npmmirror.com/postcss-modules-local-by-default/-/postcss-modules-local-by-default-3.0.3.tgz", "postcss-modules-scope@^2.2.0": "https://registry.npmmirror.com/postcss-modules-scope/-/postcss-modules-scope-2.2.0.tgz", "postcss-modules-values@^3.0.0": "https://registry.npmmirror.com/postcss-modules-values/-/postcss-modules-values-3.0.0.tgz", "postcss-selector-parser@^6.0.0": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz", "postcss-selector-parser@^6.0.2": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^7.0.14": "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.32": "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.5": "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz", "postcss@^7.0.6": "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz", "postmate@^1.5.2": "https://registry.npmmirror.com/postmate/-/postmate-1.5.2.tgz", "prelude-ls@^1.2.1": "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz", "prelude-ls@~1.1.2": "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.1.2.tgz", "prepend-http@^1.0.1": "https://registry.npmmirror.com/prepend-http/-/prepend-http-1.0.4.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmmirror.com/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "prettier@^2.2.1": "https://registry.npmmirror.com/prettier/-/prettier-2.6.1.tgz", "pretty-error@^2.1.1": "https://registry.npmmirror.com/pretty-error/-/pretty-error-2.1.2.tgz", "printj@~1.3.1": "https://registry.npmmirror.com/printj/-/printj-1.3.1.tgz", "process-nextick-args@~2.0.0": "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "process@^0.11.10": "https://registry.npmmirror.com/process/-/process-0.11.10.tgz", "progress-bar-webpack-plugin@^2.1.0": "https://registry.npmmirror.com/progress-bar-webpack-plugin/-/progress-bar-webpack-plugin-2.1.0.tgz", "progress@^2.0.0": "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz", "progress@^2.0.3": "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz", "promise-inflight@^1.0.1": "https://registry.npmmirror.com/promise-inflight/-/promise-inflight-1.0.1.tgz", "prop-ini@^0.0.2": "https://registry.npmmirror.com/prop-ini/-/prop-ini-0.0.2.tgz", "prop-types@^15.5.10": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.5.8": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.6.0": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.6.2": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.7.2": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.8.1": "https://registry.npmmirror.com/prop-types/-/prop-types-15.8.1.tgz", "proxy-addr@~2.0.7": "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-agent@^5.0.0": "https://registry.npmmirror.com/proxy-agent/-/proxy-agent-5.0.0.tgz", "proxy-from-env@^1.0.0": "https://registry.npmmirror.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "prr@~1.0.1": "https://registry.npmmirror.com/prr/-/prr-1.0.1.tgz", "pseudomap@^1.0.2": "https://registry.npmmirror.com/pseudomap/-/pseudomap-1.0.2.tgz", "psl@^1.1.28": "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz", "public-encrypt@^4.0.0": "https://registry.npmmirror.com/public-encrypt/-/public-encrypt-4.0.3.tgz", "pump@^2.0.0": "https://registry.npmmirror.com/pump/-/pump-2.0.1.tgz", "pump@^3.0.0": "https://registry.npmmirror.com/pump/-/pump-3.0.0.tgz", "pumpify@^1.3.3": "https://registry.npmmirror.com/pumpify/-/pumpify-1.5.1.tgz", "punycode@1.3.2": "https://registry.npmmirror.com/punycode/-/punycode-1.3.2.tgz", "punycode@^1.2.4": "https://registry.npmmirror.com/punycode/-/punycode-1.4.1.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/-/punycode-2.1.1.tgz", "punycode@^2.1.1": "https://registry.npmmirror.com/punycode/-/punycode-2.1.1.tgz", "q@^1.5.1": "https://registry.npmmirror.com/q/-/q-1.5.1.tgz", "qr.js@0.0.0": "https://registry.npmmirror.com/qr.js/-/qr.js-0.0.0.tgz", "qrcode.react@^1.0.0": "https://registry.npmmirror.com/qrcode.react/-/qrcode.react-1.0.1.tgz", "qs@6.9.7": "https://registry.npmmirror.com/qs/-/qs-6.9.7.tgz", "qs@^6.4.0": "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz", "qs@~6.5.2": "https://registry.npmmirror.com/qs/-/qs-6.5.3.tgz", "query-string@^6.13.1": "https://registry.npmmirror.com/query-string/-/query-string-6.14.1.tgz", "query-string@^7.1.1": "https://registry.npmmirror.com/query-string/-/query-string-7.1.1.tgz", "querystring-es3@^0.2.0": "https://registry.npmmirror.com/querystring-es3/-/querystring-es3-0.2.1.tgz", "querystring@0.2.0": "https://registry.npmmirror.com/querystring/-/querystring-0.2.0.tgz", "querystring@^0.2.0": "https://registry.npmmirror.com/querystring/-/querystring-0.2.1.tgz", "querystringify@^2.1.1": "https://registry.npmmirror.com/querystringify/-/querystringify-2.2.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz", "quick-lru@^4.0.1": "https://registry.npmmirror.com/quick-lru/-/quick-lru-4.0.1.tgz", "raf@^3.4.1": "https://registry.npmmirror.com/raf/-/raf-3.4.1.tgz", "randombytes@^2.0.0": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.0.1": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.0.5": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "randombytes@^2.1.0": "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz", "randomfill@^1.0.3": "https://registry.npmmirror.com/randomfill/-/randomfill-1.0.4.tgz", "range-parser@^1.2.1": "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz", "range-parser@~1.2.1": "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.4.3": "https://registry.npmmirror.com/raw-body/-/raw-body-2.4.3.tgz", "raw-body@^2.2.0": "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.1.tgz", "rc-align@^4.0.0": "https://registry.npmmirror.com/rc-align/-/rc-align-4.0.11.tgz", "rc-cascader@~3.2.1": "https://registry.npmmirror.com/rc-cascader/-/rc-cascader-3.2.9.tgz", "rc-cascader@~3.6.0": "https://registry.npmmirror.com/rc-cascader/-/rc-cascader-3.6.0.tgz", "rc-checkbox@~2.3.0": "https://registry.npmmirror.com/rc-checkbox/-/rc-checkbox-2.3.2.tgz", "rc-collapse@~3.1.0": "https://registry.npmmirror.com/rc-collapse/-/rc-collapse-3.1.2.tgz", "rc-collapse@~3.3.0": "https://registry.npmmirror.com/rc-collapse/-/rc-collapse-3.3.0.tgz", "rc-dialog@~8.6.0": "https://registry.npmmirror.com/rc-dialog/-/rc-dialog-8.6.0.tgz", "rc-dialog@~8.9.0": "https://registry.npmmirror.com/rc-dialog/-/rc-dialog-8.9.0.tgz", "rc-drawer@~4.4.2": "https://registry.npmmirror.com/rc-drawer/-/rc-drawer-4.4.3.tgz", "rc-dropdown@^3.2.0": "https://registry.npmmirror.com/rc-dropdown/-/rc-dropdown-3.4.0.tgz", "rc-dropdown@~3.2.5": "https://registry.npmmirror.com/rc-dropdown/-/rc-dropdown-3.2.5.tgz", "rc-dropdown@~4.0.0": "https://registry.npmmirror.com/rc-dropdown/-/rc-dropdown-4.0.1.tgz", "rc-field-form@~1.23.0": "https://registry.npmmirror.com/rc-field-form/-/rc-field-form-1.23.1.tgz", "rc-field-form@~1.26.1": "https://registry.npmmirror.com/rc-field-form/-/rc-field-form-1.26.7.tgz", "rc-image@~5.2.5": "https://registry.npmmirror.com/rc-image/-/rc-image-5.2.5.tgz", "rc-image@~5.7.0": "https://registry.npmmirror.com/rc-image/-/rc-image-5.7.0.tgz", "rc-input-number@~7.3.0": "https://registry.npmmirror.com/rc-input-number/-/rc-input-number-7.3.4.tgz", "rc-input@~0.0.1-alpha.5": "https://registry.npmmirror.com/rc-input/-/rc-input-0.0.1-alpha.7.tgz", "rc-mentions@~1.6.1": "https://registry.npmmirror.com/rc-mentions/-/rc-mentions-1.6.5.tgz", "rc-mentions@~1.8.0": "https://registry.npmmirror.com/rc-mentions/-/rc-mentions-1.8.0.tgz", "rc-menu@~9.2.1": "https://registry.npmmirror.com/rc-menu/-/rc-menu-9.2.1.tgz", "rc-menu@~9.3.2": "https://registry.npmmirror.com/rc-menu/-/rc-menu-9.3.2.tgz", "rc-menu@~9.6.0": "https://registry.npmmirror.com/rc-menu/-/rc-menu-9.6.0.tgz", "rc-motion@^2.0.0": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.0.1": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.2.0": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.3.0": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.3.4": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.4.3": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.4.4": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.4.6.tgz", "rc-motion@^2.5.1": "https://registry.npmmirror.com/rc-motion/-/rc-motion-2.6.0.tgz", "rc-notification@~4.5.7": "https://registry.npmmirror.com/rc-notification/-/rc-notification-4.5.7.tgz", "rc-notification@~4.6.0": "https://registry.npmmirror.com/rc-notification/-/rc-notification-4.6.0.tgz", "rc-overflow@^1.0.0": "https://registry.npmmirror.com/rc-overflow/-/rc-overflow-1.2.4.tgz", "rc-overflow@^1.2.0": "https://registry.npmmirror.com/rc-overflow/-/rc-overflow-1.2.4.tgz", "rc-pagination@~3.1.16": "https://registry.npmmirror.com/rc-pagination/-/rc-pagination-3.1.17.tgz#91e690aa894806e344cea88ea4a16d244194a1bd", "rc-pagination@~3.1.9": "https://registry.npmmirror.com/rc-pagination/-/rc-pagination-3.1.15.tgz", "rc-picker@~2.5.17": "https://registry.npmmirror.com/rc-picker/-/rc-picker-2.5.19.tgz", "rc-picker@~2.6.8": "https://registry.npmmirror.com/rc-picker/-/rc-picker-2.6.10.tgz", "rc-progress@~3.2.1": "https://registry.npmmirror.com/rc-progress/-/rc-progress-3.2.4.tgz", "rc-progress@~3.3.2": "https://registry.npmmirror.com/rc-progress/-/rc-progress-3.3.3.tgz", "rc-rate@~2.9.0": "https://registry.npmmirror.com/rc-rate/-/rc-rate-2.9.1.tgz", "rc-resize-observer@^1.0.0": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-1.2.0.tgz", "rc-resize-observer@^1.1.0": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-1.2.0.tgz", "rc-resize-observer@^1.2.0": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-1.2.0.tgz", "rc-resize-observer@^1.2.1": "https://registry.npmmirror.com/rc-resize-observer/-/rc-resize-observer-1.2.1.tgz#7f9715b5d1afe126ade3c107aafd2cebf8a57a99", "rc-segmented@~2.1.0": "https://registry.npmmirror.com/rc-segmented/-/rc-segmented-2.1.0.tgz", "rc-select@~14.0.0-alpha.15": "https://registry.npmmirror.com/rc-select/-/rc-select-14.0.6.tgz", "rc-select@~14.0.0-alpha.23": "https://registry.npmmirror.com/rc-select/-/rc-select-14.0.6.tgz", "rc-select@~14.0.0-alpha.8": "https://registry.npmmirror.com/rc-select/-/rc-select-14.0.6.tgz", "rc-select@~14.1.0": "https://registry.npmmirror.com/rc-select/-/rc-select-14.1.7.tgz", "rc-select@~14.1.1": "https://registry.npmmirror.com/rc-select/-/rc-select-14.1.7.tgz", "rc-slider@~10.0.0": "https://registry.npmmirror.com/rc-slider/-/rc-slider-10.0.1.tgz#7058c68ff1e1aa4e7c3536e5e10128bdbccb87f9", "rc-slider@~9.7.4": "https://registry.npmmirror.com/rc-slider/-/rc-slider-9.7.5.tgz", "rc-steps@~4.1.0": "https://registry.npmmirror.com/rc-steps/-/rc-steps-4.1.4.tgz", "rc-switch@~3.2.0": "https://registry.npmmirror.com/rc-switch/-/rc-switch-3.2.2.tgz", "rc-table@~7.23.0": "https://registry.npmmirror.com/rc-table/-/rc-table-7.23.2.tgz", "rc-table@~7.24.0": "https://registry.npmmirror.com/rc-table/-/rc-table-7.24.2.tgz", "rc-tabs@~11.10.0": "https://registry.npmmirror.com/rc-tabs/-/rc-tabs-11.10.8.tgz", "rc-tabs@~11.16.0": "https://registry.npmmirror.com/rc-tabs/-/rc-tabs-11.16.0.tgz", "rc-textarea@^0.3.0": "https://registry.npmmirror.com/rc-textarea/-/rc-textarea-0.3.7.tgz", "rc-textarea@~0.3.0": "https://registry.npmmirror.com/rc-textarea/-/rc-textarea-0.3.7.tgz", "rc-tooltip@^5.0.1": "https://registry.npmmirror.com/rc-tooltip/-/rc-tooltip-5.1.1.tgz", "rc-tooltip@~5.1.1": "https://registry.npmmirror.com/rc-tooltip/-/rc-tooltip-5.1.1.tgz", "rc-tree-select@~5.1.1": "https://registry.npmmirror.com/rc-tree-select/-/rc-tree-select-5.1.5.tgz", "rc-tree-select@~5.4.0": "https://registry.npmmirror.com/rc-tree-select/-/rc-tree-select-5.4.0.tgz", "rc-tree@~5.4.3": "https://registry.npmmirror.com/rc-tree/-/rc-tree-5.4.4.tgz", "rc-tree@~5.6.1": "https://registry.npmmirror.com/rc-tree/-/rc-tree-5.6.5.tgz", "rc-tree@~5.6.3": "https://registry.npmmirror.com/rc-tree/-/rc-tree-5.6.5.tgz", "rc-tree@~5.6.5": "https://registry.npmmirror.com/rc-tree/-/rc-tree-5.6.5.tgz", "rc-trigger@^5.0.0": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-5.2.11.tgz", "rc-trigger@^5.0.4": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-5.2.11.tgz", "rc-trigger@^5.1.2": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-5.2.11.tgz", "rc-trigger@^5.2.10": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-5.2.11.tgz", "rc-trigger@^5.3.1": "https://registry.npmmirror.com/rc-trigger/-/rc-trigger-5.3.1.tgz", "rc-upload@~4.3.0": "https://registry.npmmirror.com/rc-upload/-/rc-upload-4.3.3.tgz", "rc-util@^5.0.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.0.6": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.0.7": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.12.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.14.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.15.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.16.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.17.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.18.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.21.5.tgz", "rc-util@^5.19.2": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.2.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.2.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.20.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.21.5.tgz", "rc-util@^5.20.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.21.5.tgz", "rc-util@^5.21.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.21.5.tgz", "rc-util@^5.27.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.27.1.tgz#d12f02b9577b04299c0f1a235c8acbcf56e2824b", "rc-util@^5.3.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.4.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.5.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.6.1": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.7.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.8.0": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.9.4": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-util@^5.9.8": "https://registry.npmmirror.com/rc-util/-/rc-util-5.19.3.tgz", "rc-virtual-list@^3.2.0": "https://registry.npmmirror.com/rc-virtual-list/-/rc-virtual-list-3.4.4.tgz", "rc-virtual-list@^3.4.2": "https://registry.npmmirror.com/rc-virtual-list/-/rc-virtual-list-3.4.4.tgz", "rc-virtual-list@^3.4.8": "https://registry.npmmirror.com/rc-virtual-list/-/rc-virtual-list-3.4.8.tgz", "rc@^1.0.1": "https://registry.npmmirror.com/rc/-/rc-1.2.8.tgz", "rc@^1.1.6": "https://registry.npmmirror.com/rc/-/rc-1.2.8.tgz", "rc@^1.2.1": "https://registry.npmmirror.com/rc/-/rc-1.2.8.tgz", "rc@~1.2.7": "https://registry.npmmirror.com/rc/-/rc-1.2.8.tgz", "react-color@^2.19.3": "https://registry.npmmirror.com/react-color/-/react-color-2.19.3.tgz", "react-display-name@^0.2.4": "https://registry.npmmirror.com/react-display-name/-/react-display-name-0.2.5.tgz", "react-dom@^16.13.1": "https://registry.npmmirror.com/react-dom/-/react-dom-16.14.0.tgz", "react-fast-compare@^3.1.1": "https://registry.npmmirror.com/react-fast-compare/-/react-fast-compare-3.2.0.tgz", "react-helmet@^6.1.0": "https://registry.npmmirror.com/react-helmet/-/react-helmet-6.1.0.tgz", "react-is@^16.12.0": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "react-is@^16.13.1": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "react-is@^16.6.0": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "react-is@^16.7.0": "https://registry.npmmirror.com/react-is/-/react-is-16.13.1.tgz", "react-is@^17.0.2": "https://registry.npmmirror.com/react-is/-/react-is-17.0.2.tgz", "react-jss@^10.3.0": "https://registry.npmmirror.com/react-jss/-/react-jss-10.9.0.tgz", "react-jss@^10.6.0": "https://registry.npmmirror.com/react-jss/-/react-jss-10.9.0.tgz", "react-lifecycles-compat@^3.0.4": "https://registry.npmmirror.com/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "react-portal@^4.2.2": "https://registry.npmmirror.com/react-portal/-/react-portal-4.2.2.tgz", "react-redux@^7.2.1": "https://registry.npmmirror.com/react-redux/-/react-redux-7.2.6.tgz", "react-redux@^7.2.3": "https://registry.npmmirror.com/react-redux/-/react-redux-7.2.6.tgz", "react-refresh@^0.9.0": "https://registry.npmmirror.com/react-refresh/-/react-refresh-0.9.0.tgz", "react-router-config@^5.1.1": "https://registry.npmmirror.com/react-router-config/-/react-router-config-5.1.1.tgz", "react-router-dom@5.2.0": "https://registry.npmmirror.com/react-router-dom/-/react-router-dom-5.2.0.tgz", "react-router-dom@^5.2.0": "https://registry.npmmirror.com/react-router-dom/-/react-router-dom-5.3.0.tgz", "react-router@5.2.0": "https://registry.npmmirror.com/react-router/-/react-router-5.2.0.tgz", "react-router@5.2.1": "https://registry.npmmirror.com/react-router/-/react-router-5.2.1.tgz", "react-side-effect@^2.1.0": "https://registry.npmmirror.com/react-side-effect/-/react-side-effect-2.1.1.tgz", "react-virtualized@^9.22.3": "https://registry.npmmirror.com/react-virtualized/-/react-virtualized-9.22.3.tgz", "react-window@^1.8.8": "https://registry.npmmirror.com/react-window/-/react-window-1.8.8.tgz", "react@^16.13.1": "https://registry.npmmirror.com/react/-/react-16.14.0.tgz", "reactcss@^1.2.0": "https://registry.npmmirror.com/reactcss/-/reactcss-1.2.3.tgz", "read-pkg-up@^2.0.0": "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-2.0.0.tgz", "read-pkg-up@^7.0.1": "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "read-pkg@^2.0.0": "https://registry.npmmirror.com/read-pkg/-/read-pkg-2.0.0.tgz", "read-pkg@^5.2.0": "https://registry.npmmirror.com/read-pkg/-/read-pkg-5.2.0.tgz", "readable-stream@1 || 2": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@1.1.x": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz", "readable-stream@3": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^2.0.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.0.1": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.0.2": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.0.5": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.1.5": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.2.2": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.3.3": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^2.3.6": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readable-stream@^3.0.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.0.6": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.1.1": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.4.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@^3.6.0": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.0.tgz", "readable-stream@~2.3.6": "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.7.tgz", "readdir-glob@^1.0.0": "https://registry.npmmirror.com/readdir-glob/-/readdir-glob-1.1.1.tgz", "readdirp@^2.2.1": "https://registry.npmmirror.com/readdirp/-/readdirp-2.2.1.tgz", "readdirp@~3.6.0": "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz", "readme-badger@^0.3.0": "https://registry.npmmirror.com/readme-badger/-/readme-badger-0.3.0.tgz", "redent@^3.0.0": "https://registry.npmmirror.com/redent/-/redent-3.0.0.tgz", "redux-devtools-extension@^2.13.7": "https://registry.npmmirror.com/redux-devtools-extension/-/redux-devtools-extension-2.13.9.tgz", "redux-dynamic-middlewares@^1.0.0": "https://registry.npmmirror.com/redux-dynamic-middlewares/-/redux-dynamic-middlewares-1.0.0.tgz", "redux-dynamic-modules-core@^5.2.3": "https://registry.npmmirror.com/redux-dynamic-modules-core/-/redux-dynamic-modules-core-5.2.3.tgz", "redux-dynamic-modules-react@^5.2.3": "https://registry.npmmirror.com/redux-dynamic-modules-react/-/redux-dynamic-modules-react-5.2.3.tgz", "redux-dynamic-modules-thunk@^5.2.3": "https://registry.npmmirror.com/redux-dynamic-modules-thunk/-/redux-dynamic-modules-thunk-5.2.3.tgz", "redux-dynamic-modules@^5.2.3": "https://registry.npmmirror.com/redux-dynamic-modules/-/redux-dynamic-modules-5.2.3.tgz", "redux-persist@^6.0.0": "https://registry.npmmirror.com/redux-persist/-/redux-persist-6.0.0.tgz", "redux-thunk@2.4.1": "https://registry.npmmirror.com/redux-thunk/-/redux-thunk-2.4.1.tgz", "redux-thunk@^2.4.1": "https://registry.npmmirror.com/redux-thunk/-/redux-thunk-2.4.1.tgz", "redux@^4.0.0": "https://registry.npmmirror.com/redux/-/redux-4.1.2.tgz", "redux@^4.0.5": "https://registry.npmmirror.com/redux/-/redux-4.1.2.tgz", "regenerate-unicode-properties@^10.0.1": "https://registry.npmmirror.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.0.1.tgz", "regenerate@^1.4.2": "https://registry.npmmirror.com/regenerate/-/regenerate-1.4.2.tgz", "regenerator-runtime@^0.13.11": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-runtime@^0.13.4": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "regenerator-runtime@^0.13.7": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.13.9.tgz", "regenerator-transform@^0.14.2": "https://registry.npmmirror.com/regenerator-transform/-/regenerator-transform-0.14.5.tgz", "regex-not@^1.0.0": "https://registry.npmmirror.com/regex-not/-/regex-not-1.0.2.tgz", "regex-not@^1.0.2": "https://registry.npmmirror.com/regex-not/-/regex-not-1.0.2.tgz", "regexp.prototype.flags@^1.2.0": "https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz", "regexp.prototype.flags@^1.4.1": "https://registry.npmmirror.com/regexp.prototype.flags/-/regexp.prototype.flags-1.4.1.tgz", "regexpp@^2.0.1": "https://registry.npmmirror.com/regexpp/-/regexpp-2.0.1.tgz", "regexpp@^3.0.0": "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz", "regexpp@^3.1.0": "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz", "regexpu-core@^5.0.1": "https://registry.npmmirror.com/regexpu-core/-/regexpu-core-5.0.1.tgz", "registry-auth-token@^3.0.1": "https://registry.npmmirror.com/registry-auth-token/-/registry-auth-token-3.4.0.tgz", "registry-url@^3.0.3": "https://registry.npmmirror.com/registry-url/-/registry-url-3.1.0.tgz", "regjsgen@^0.6.0": "https://registry.npmmirror.com/regjsgen/-/regjsgen-0.6.0.tgz", "regjsparser@^0.8.2": "https://registry.npmmirror.com/regjsparser/-/regjsparser-0.8.4.tgz", "relateurl@^0.2.7": "https://registry.npmmirror.com/relateurl/-/relateurl-0.2.7.tgz", "remote-origin-url@^1.0.0": "https://registry.npmmirror.com/remote-origin-url/-/remote-origin-url-1.0.0.tgz", "remove-trailing-separator@^1.0.1": "https://registry.npmmirror.com/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "renderkid@^2.0.4": "https://registry.npmmirror.com/renderkid/-/renderkid-2.0.7.tgz", "repeat-element@^1.1.2": "https://registry.npmmirror.com/repeat-element/-/repeat-element-1.1.4.tgz", "repeat-string@^1.6.1": "https://registry.npmmirror.com/repeat-string/-/repeat-string-1.6.1.tgz", "request-promise-core@1.1.4": "https://registry.npmmirror.com/request-promise-core/-/request-promise-core-1.1.4.tgz", "request-promise@^4.2.6": "https://registry.npmmirror.com/request-promise/-/request-promise-4.2.6.tgz", "request@^2.88.2": "https://registry.npmmirror.com/request/-/request-2.88.2.tgz", "require-directory@^2.1.1": "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz", "require-main-filename@^2.0.0": "https://registry.npmmirror.com/require-main-filename/-/require-main-filename-2.0.0.tgz", "requireg@^0.1.8": "https://registry.npmmirror.com/requireg/-/requireg-0.1.8.tgz", "requires-port@^1.0.0": "https://registry.npmmirror.com/requires-port/-/requires-port-1.0.0.tgz", "reselect@^4.1.6": "https://registry.npmmirror.com/reselect/-/reselect-4.1.6.tgz", "resize-observer-polyfill@^1.5.0": "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "resize-observer-polyfill@^1.5.1": "https://registry.npmmirror.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "resolve-cwd@^2.0.0": "https://registry.npmmirror.com/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "resolve-dir@^1.0.0": "https://registry.npmmirror.com/resolve-dir/-/resolve-dir-1.0.1.tgz", "resolve-dir@^1.0.1": "https://registry.npmmirror.com/resolve-dir/-/resolve-dir-1.0.1.tgz", "resolve-from@5.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-from@^3.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmmirror.com/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-global@1.0.0": "https://registry.npmmirror.com/resolve-global/-/resolve-global-1.0.0.tgz", "resolve-global@^1.0.0": "https://registry.npmmirror.com/resolve-global/-/resolve-global-1.0.0.tgz", "resolve-pathname@^3.0.0": "https://registry.npmmirror.com/resolve-pathname/-/resolve-pathname-3.0.0.tgz", "resolve-url@^0.2.1": "https://registry.npmmirror.com/resolve-url/-/resolve-url-0.2.1.tgz", "resolve@^1.10.0": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "resolve@^1.10.1": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "resolve@^1.11.0": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "resolve@^1.14.2": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "resolve@^1.20.0": "https://registry.npmmirror.com/resolve/-/resolve-1.22.0.tgz", "resolve@^2.0.0-next.3": "https://registry.npmmirror.com/resolve/-/resolve-2.0.0-next.3.tgz", "resolve@~1.7.1": "https://registry.npmmirror.com/resolve/-/resolve-1.7.1.tgz", "restore-cursor@^3.1.0": "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz", "ret@~0.1.10": "https://registry.npmmirror.com/ret/-/ret-0.1.15.tgz", "retry@^0.12.0": "https://registry.npmmirror.com/retry/-/retry-0.12.0.tgz", "reusify@^1.0.4": "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz", "rfdc@^1.3.0": "https://registry.npmmirror.com/rfdc/-/rfdc-1.3.0.tgz", "rgbcolor@^1.0.1": "https://registry.npmmirror.com/rgbcolor/-/rgbcolor-1.0.1.tgz", "rimraf@2": "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz", "rimraf@2.6.3": "https://registry.npmmirror.com/rimraf/-/rimraf-2.6.3.tgz", "rimraf@^2.5.4": "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^2.6.1": "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^2.6.3": "https://registry.npmmirror.com/rimraf/-/rimraf-2.7.1.tgz", "rimraf@^3.0.2": "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz", "ripemd160@^2.0.0": "https://registry.npmmirror.com/ripemd160/-/ripemd160-2.0.2.tgz", "ripemd160@^2.0.1": "https://registry.npmmirror.com/ripemd160/-/ripemd160-2.0.2.tgz", "run-async@^2.4.0": "https://registry.npmmirror.com/run-async/-/run-async-2.4.1.tgz", "run-parallel@^1.1.9": "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz", "run-queue@^1.0.0": "https://registry.npmmirror.com/run-queue/-/run-queue-1.0.3.tgz", "run-queue@^1.0.3": "https://registry.npmmirror.com/run-queue/-/run-queue-1.0.3.tgz", "rust-result@^1.0.0": "https://registry.npmmirror.com/rust-result/-/rust-result-1.0.0.tgz", "rxjs@^6.6.0": "https://registry.npmmirror.com/rxjs/-/rxjs-6.6.7.tgz", "rxjs@^7.5.1": "https://registry.npmmirror.com/rxjs/-/rxjs-7.5.5.tgz", "safe-buffer@5.1.2": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@5.2.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@>=5.1.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.0.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.2": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.2.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-json-parse@4.0.0": "https://registry.npmmirror.com/safe-json-parse/-/safe-json-parse-4.0.0.tgz", "safe-regex@^1.1.0": "https://registry.npmmirror.com/safe-regex/-/safe-regex-1.1.0.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@^2.0.2": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@^2.1.0": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@~2.1.0": "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz", "sass-loader@^10.0.2": "https://registry.npmmirror.com/sass-loader/-/sass-loader-10.2.1.tgz", "sass@^1.26.10": "https://registry.npmmirror.com/sass/-/sass-1.49.10.tgz", "sax@>=0.6.0": "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz", "saxes@^5.0.1": "http://nexus3.luban.fit/repository/npm/saxes/-/saxes-5.0.1.tgz#eebab953fa3b7608dbe94e5dadb15c888fa6696d", "scheduler@^0.19.1": "https://registry.npmmirror.com/scheduler/-/scheduler-0.19.1.tgz", "schema-utils@2.7.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.0.tgz", "schema-utils@^1.0.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-1.0.0.tgz", "schema-utils@^2.6.5": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^2.7.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^2.7.1": "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^3.0.0": "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.1.1.tgz", "screenfull@^5.0.0": "https://registry.npmmirror.com/screenfull/-/screenfull-5.2.0.tgz", "scroll-into-view-if-needed@^2.2.25": "https://registry.npmmirror.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.29.tgz", "sdk-base@^2.0.1": "https://registry.npmmirror.com/sdk-base/-/sdk-base-2.0.1.tgz", "select-hose@^2.0.0": "https://registry.npmmirror.com/select-hose/-/select-hose-2.0.0.tgz", "selfsigned@^1.10.8": "https://registry.npmmirror.com/selfsigned/-/selfsigned-1.10.14.tgz", "semver-compare@^1.0.0": "https://registry.npmmirror.com/semver-compare/-/semver-compare-1.0.0.tgz", "semver-diff@^2.0.0": "https://registry.npmmirror.com/semver-diff/-/semver-diff-2.1.0.tgz", "semver-regex@^3.1.2": "https://registry.npmmirror.com/semver-regex/-/semver-regex-3.1.3.tgz", "semver-utils@^1.1.2": "https://registry.npmmirror.com/semver-utils/-/semver-utils-1.1.4.tgz", "semver@2 || 3 || 4 || 5": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@7.0.0": "https://registry.npmmirror.com/semver/-/semver-7.0.0.tgz", "semver@7.3.2": "https://registry.npmmirror.com/semver/-/semver-7.3.2.tgz", "semver@^5.0.1": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^5.0.3": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^5.1.0": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^5.5.0": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^5.5.1": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^5.6.0": "https://registry.npmmirror.com/semver/-/semver-5.7.1.tgz", "semver@^6.0.0": "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz", "semver@^6.1.1": "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz", "semver@^6.1.2": "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz", "semver@^6.3.0": "https://registry.npmmirror.com/semver/-/semver-6.3.0.tgz", "semver@^7.2.1": "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz", "semver@^7.3.2": "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz", "semver@^7.3.4": "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz", "semver@^7.3.5": "https://registry.npmmirror.com/semver/-/semver-7.3.5.tgz", "send@0.17.2": "https://registry.npmmirror.com/send/-/send-0.17.2.tgz", "serialize-error@^2.1.0": "https://registry.npmmirror.com/serialize-error/-/serialize-error-2.1.0.tgz", "serialize-javascript@^4.0.0": "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "serialize-javascript@^5.0.1": "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-5.0.1.tgz", "serve-index@^1.9.1": "https://registry.npmmirror.com/serve-index/-/serve-index-1.9.1.tgz", "serve-static@1.14.2": "https://registry.npmmirror.com/serve-static/-/serve-static-1.14.2.tgz", "set-blocking@^2.0.0": "https://registry.npmmirror.com/set-blocking/-/set-blocking-2.0.0.tgz", "set-value@^2.0.0": "https://registry.npmmirror.com/set-value/-/set-value-2.0.1.tgz", "set-value@^2.0.1": "https://registry.npmmirror.com/set-value/-/set-value-2.0.1.tgz", "setimmediate@^1.0.4": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "setimmediate@^1.0.5": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "setimmediate@~1.0.4": "https://registry.npmmirror.com/setimmediate/-/setimmediate-1.0.5.tgz", "setprototypeof@1.1.0": "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.1.0.tgz", "setprototypeof@1.2.0": "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz", "sha.js@^2.4.0": "https://registry.npmmirror.com/sha.js/-/sha.js-2.4.11.tgz", "sha.js@^2.4.8": "https://registry.npmmirror.com/sha.js/-/sha.js-2.4.11.tgz", "shallow-equal@^1.2.0": "https://registry.npmmirror.com/shallow-equal/-/shallow-equal-1.2.1.tgz", "shallowequal@^1.1.0": "https://registry.npmmirror.com/shallowequal/-/shallowequal-1.1.0.tgz", "shebang-command@^1.2.0": "https://registry.npmmirror.com/shebang-command/-/shebang-command-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^1.0.0": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-1.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz", "shellwords@^0.1.1": "https://registry.npmmirror.com/shellwords/-/shellwords-0.1.1.tgz", "side-channel@^1.0.4": "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.4.tgz", "sigmund@^1.0.1": "https://registry.npmmirror.com/sigmund/-/sigmund-1.0.1.tgz", "signal-exit@^3.0.0": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.2": "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz", "sirv@^1.0.7": "https://registry.npmmirror.com/sirv/-/sirv-1.0.19.tgz", "slash@^2.0.0": "https://registry.npmmirror.com/slash/-/slash-2.0.0.tgz", "slash@^3.0.0": "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz", "slice-ansi@^2.1.0": "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-2.1.0.tgz", "slice-ansi@^3.0.0": "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-3.0.0.tgz", "slice-ansi@^4.0.0": "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz", "smart-buffer@^4.2.0": "https://registry.npmmirror.com/smart-buffer/-/smart-buffer-4.2.0.tgz", "smpltmpl@^1.0.2": "https://registry.npmmirror.com/smpltmpl/-/smpltmpl-1.0.2.tgz", "snapdragon-node@^2.0.1": "https://registry.npmmirror.com/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "snapdragon-util@^3.0.1": "https://registry.npmmirror.com/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "snapdragon@^0.8.1": "https://registry.npmmirror.com/snapdragon/-/snapdragon-0.8.2.tgz", "sockjs-client@^1.5.0": "https://registry.npmmirror.com/sockjs-client/-/sockjs-client-1.6.0.tgz", "sockjs@^0.3.21": "https://registry.npmmirror.com/sockjs/-/sockjs-0.3.24.tgz", "socks-proxy-agent@5": "https://registry.npmmirror.com/socks-proxy-agent/-/socks-proxy-agent-5.0.1.tgz", "socks-proxy-agent@^5.0.0": "https://registry.npmmirror.com/socks-proxy-agent/-/socks-proxy-agent-5.0.1.tgz", "socks@^2.3.3": "https://registry.npmmirror.com/socks/-/socks-2.7.0.tgz", "source-list-map@^2.0.0": "https://registry.npmmirror.com/source-list-map/-/source-list-map-2.0.1.tgz", "source-map-js@>=0.6.2 <2.0.0": "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-resolve@^0.5.0": "https://registry.npmmirror.com/source-map-resolve/-/source-map-resolve-0.5.3.tgz", "source-map-support@~0.5.12": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.20": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-url@^0.4.0": "https://registry.npmmirror.com/source-map-url/-/source-map-url-0.4.1.tgz", "source-map@^0.5.0": "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz", "source-map@^0.5.6": "https://registry.npmmirror.com/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmmirror.com/source-map/-/source-map-0.7.3.tgz", "source-map@~0.6.0": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@~0.6.1": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "source-map@~0.7.2": "https://registry.npmmirror.com/source-map/-/source-map-0.7.3.tgz", "spark-md5@^3.0.2": "https://registry.npmmirror.com/spark-md5/-/spark-md5-3.0.2.tgz", "spdx-correct@^3.0.0": "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.1.1.tgz", "spdx-exceptions@^2.1.0": "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.3.0.tgz", "spdx-expression-parse@^3.0.0": "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "spdx-license-ids@^3.0.0": "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.11.tgz", "spdy-transport@^3.0.0": "https://registry.npmmirror.com/spdy-transport/-/spdy-transport-3.0.0.tgz", "spdy@^4.0.2": "https://registry.npmmirror.com/spdy/-/spdy-4.0.2.tgz", "speed-measure-webpack-plugin@^1.3.3": "https://registry.npmmirror.com/speed-measure-webpack-plugin/-/speed-measure-webpack-plugin-1.5.0.tgz", "split-lines@^2.0.0": "https://registry.npmmirror.com/split-lines/-/split-lines-2.1.0.tgz", "split-on-first@^1.0.0": "https://registry.npmmirror.com/split-on-first/-/split-on-first-1.1.0.tgz", "split-string@^3.0.1": "https://registry.npmmirror.com/split-string/-/split-string-3.1.0.tgz", "split-string@^3.0.2": "https://registry.npmmirror.com/split-string/-/split-string-3.1.0.tgz", "split2@^3.0.0": "https://registry.npmmirror.com/split2/-/split2-3.2.2.tgz", "sprintf-js@~1.0.2": "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz", "sshpk@^1.7.0": "https://registry.npmmirror.com/sshpk/-/sshpk-1.17.0.tgz", "ssri@^6.0.1": "https://registry.npmmirror.com/ssri/-/ssri-6.0.2.tgz", "ssri@^8.0.1": "https://registry.npmmirror.com/ssri/-/ssri-8.0.1.tgz", "stackblur-canvas@^2.0.0": "https://registry.npmmirror.com/stackblur-canvas/-/stackblur-canvas-2.5.0.tgz", "stackframe@^1.1.1": "https://registry.npmmirror.com/stackframe/-/stackframe-1.2.1.tgz", "static-extend@^0.1.1": "https://registry.npmmirror.com/static-extend/-/static-extend-0.1.2.tgz", "statuses@2.0.1": "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz", "statuses@>= 1.4.0 < 2": "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz", "statuses@>= 1.5.0 < 2": "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz", "statuses@^1.3.1": "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz", "statuses@~1.5.0": "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz", "stealthy-require@^1.1.1": "https://registry.npmmirror.com/stealthy-require/-/stealthy-require-1.1.1.tgz", "stream-browserify@^2.0.1": "https://registry.npmmirror.com/stream-browserify/-/stream-browserify-2.0.2.tgz", "stream-each@^1.1.0": "https://registry.npmmirror.com/stream-each/-/stream-each-1.2.3.tgz", "stream-http@2.8.2": "https://registry.npmmirror.com/stream-http/-/stream-http-2.8.2.tgz", "stream-http@^2.7.2": "https://registry.npmmirror.com/stream-http/-/stream-http-2.8.3.tgz", "stream-shift@^1.0.0": "https://registry.npmmirror.com/stream-shift/-/stream-shift-1.0.1.tgz", "stream-wormhole@^1.0.4": "https://registry.npmmirror.com/stream-wormhole/-/stream-wormhole-1.1.0.tgz", "strict-uri-encode@^2.0.0": "https://registry.npmmirror.com/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "string-argv@0.3.1": "https://registry.npmmirror.com/string-argv/-/string-argv-0.3.1.tgz", "string-convert@^0.2.0": "https://registry.npmmirror.com/string-convert/-/string-convert-0.2.1.tgz", "string-width@^2.0.0": "https://registry.npmmirror.com/string-width/-/string-width-2.1.1.tgz", "string-width@^2.1.1": "https://registry.npmmirror.com/string-width/-/string-width-2.1.1.tgz", "string-width@^3.0.0": "https://registry.npmmirror.com/string-width/-/string-width-3.1.0.tgz", "string-width@^3.1.0": "https://registry.npmmirror.com/string-width/-/string-width-3.1.0.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz", "string.prototype.matchall@^4.0.6": "https://registry.npmmirror.com/string.prototype.matchall/-/string.prototype.matchall-4.0.7.tgz", "string.prototype.trimend@^1.0.4": "https://registry.npmmirror.com/string.prototype.trimend/-/string.prototype.trimend-1.0.4.tgz", "string.prototype.trimstart@^1.0.4": "https://registry.npmmirror.com/string.prototype.trimstart/-/string.prototype.trimstart-1.0.4.tgz", "string_decoder@^1.0.0": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@^1.1.1": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@~0.10.x": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "string_decoder@~1.1.1": "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz", "stringify-object@^3.3.0": "https://registry.npmmirror.com/stringify-object/-/stringify-object-3.3.0.tgz", "strip-ansi@^3.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "strip-ansi@^3.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-3.0.1.tgz", "strip-ansi@^4.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-4.0.0.tgz", "strip-ansi@^5.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^5.1.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^5.2.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-bom@^3.0.0": "https://registry.npmmirror.com/strip-bom/-/strip-bom-3.0.0.tgz", "strip-eof@^1.0.0": "https://registry.npmmirror.com/strip-eof/-/strip-eof-1.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-indent@^3.0.0": "https://registry.npmmirror.com/strip-indent/-/strip-indent-3.0.0.tgz", "strip-json-comments@^3.0.1": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.0": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.1": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "strip-json-comments@~2.0.1": "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "style-loader@^1.2.1": "https://registry.npmmirror.com/style-loader/-/style-loader-1.3.0.tgz", "supports-color@^2.0.0": "https://registry.npmmirror.com/supports-color/-/supports-color-2.0.0.tgz", "supports-color@^5.3.0": "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^6.1.0": "https://registry.npmmirror.com/supports-color/-/supports-color-6.1.0.tgz", "supports-color@^7.0.0": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "svg-pathdata@^6.0.3": "https://registry.npmmirror.com/svg-pathdata/-/svg-pathdata-6.0.3.tgz", "symbol-observable@^1.2.0": "https://registry.npmmirror.com/symbol-observable/-/symbol-observable-1.2.0.tgz", "table@^5.2.3": "https://registry.npmmirror.com/table/-/table-5.4.6.tgz", "table@^6.0.9": "https://registry.npmmirror.com/table/-/table-6.8.0.tgz", "tapable@^1.0.0": "https://registry.npmmirror.com/tapable/-/tapable-1.1.3.tgz", "tapable@^1.1.3": "https://registry.npmmirror.com/tapable/-/tapable-1.1.3.tgz", "tar-stream@^2.2.0": "https://registry.npmmirror.com/tar-stream/-/tar-stream-2.2.0.tgz", "tar@^6.0.2": "https://registry.npmmirror.com/tar/-/tar-6.1.11.tgz", "term-size@^1.2.0": "https://registry.npmmirror.com/term-size/-/term-size-1.2.0.tgz", "terser-webpack-plugin@4.2.3": "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-4.2.3.tgz", "terser-webpack-plugin@^1.4.3": "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-1.4.5.tgz", "terser@^4.1.2": "https://registry.npmmirror.com/terser/-/terser-4.8.0.tgz", "terser@^4.6.3": "https://registry.npmmirror.com/terser/-/terser-4.8.0.tgz", "terser@^5.3.4": "https://registry.npmmirror.com/terser/-/terser-5.12.1.tgz", "text-extensions@^1.0.0": "https://registry.npmmirror.com/text-extensions/-/text-extensions-1.9.0.tgz", "text-segmentation@^1.0.3": "https://registry.npmmirror.com/text-segmentation/-/text-segmentation-1.0.3.tgz", "text-table@^0.2.0": "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz", "theming@^3.3.0": "https://registry.npmmirror.com/theming/-/theming-3.3.0.tgz", "thenify-all@^1.0.0": "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz", "through2@^2.0.0": "https://registry.npmmirror.com/through2/-/through2-2.0.5.tgz", "through2@^4.0.0": "https://registry.npmmirror.com/through2/-/through2-4.0.2.tgz", "through@>=2.2.7 <3": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "through@^2.3.6": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "through@^2.3.8": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "through@~2.3": "https://registry.npmmirror.com/through/-/through-2.3.8.tgz", "thunky@^1.0.2": "https://registry.npmmirror.com/thunky/-/thunky-1.1.0.tgz", "timed-out@^4.0.0": "https://registry.npmmirror.com/timed-out/-/timed-out-4.0.1.tgz", "timers-browserify@^2.0.4": "https://registry.npmmirror.com/timers-browserify/-/timers-browserify-2.0.12.tgz", "tiny-invariant@^1.0.2": "https://registry.npmmirror.com/tiny-invariant/-/tiny-invariant-1.2.0.tgz", "tiny-warning@^1.0.0": "https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz", "tiny-warning@^1.0.2": "https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz", "tiny-warning@^1.0.3": "https://registry.npmmirror.com/tiny-warning/-/tiny-warning-1.0.3.tgz", "tinycolor2@^1.4.1": "https://registry.npmmirror.com/tinycolor2/-/tinycolor2-1.4.2.tgz", "tmp@^0.0.33": "https://registry.npmmirror.com/tmp/-/tmp-0.0.33.tgz", "tmp@^0.2.0": "http://nexus3.luban.fit/repository/npm/tmp/-/tmp-0.2.3.tgz#eb783cc22bc1e8bebd0671476d46ea4eb32a79ae", "to-arraybuffer@^1.0.0": "https://registry.npmmirror.com/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "to-fast-properties@^2.0.0": "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "to-object-path@^0.3.0": "https://registry.npmmirror.com/to-object-path/-/to-object-path-0.3.0.tgz", "to-regex-range@^2.1.0": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-2.1.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz", "to-regex@^3.0.1": "https://registry.npmmirror.com/to-regex/-/to-regex-3.0.2.tgz", "to-regex@^3.0.2": "https://registry.npmmirror.com/to-regex/-/to-regex-3.0.2.tgz", "toggle-selection@^1.0.6": "https://registry.npmmirror.com/toggle-selection/-/toggle-selection-1.0.6.tgz", "toidentifier@1.0.1": "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz", "totalist@^1.0.0": "https://registry.npmmirror.com/totalist/-/totalist-1.1.0.tgz", "tough-cookie@^2.3.3": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz", "tough-cookie@~2.5.0": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz", "traverse@>=0.3.0 <0.4": "http://nexus3.luban.fit/repository/npm/traverse/-/traverse-0.3.9.tgz#717b8f220cc0bb7b44e40514c22b2e8bbc70d8b9", "trim-newlines@^3.0.0": "https://registry.npmmirror.com/trim-newlines/-/trim-newlines-3.0.1.tgz", "ts-loader@^8.0.11": "https://registry.npmmirror.com/ts-loader/-/ts-loader-8.3.0.tgz", "tslib@^1.10.0": "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz", "tslib@^1.8.1": "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz", "tslib@^1.9.0": "https://registry.npmmirror.com/tslib/-/tslib-1.14.1.tgz", "tslib@^2.0.1": "https://registry.npmmirror.com/tslib/-/tslib-2.4.0.tgz", "tslib@^2.0.3": "https://registry.npmmirror.com/tslib/-/tslib-2.3.1.tgz", "tslib@^2.1.0": "https://registry.npmmirror.com/tslib/-/tslib-2.3.1.tgz", "tsutils@^3.17.1": "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz", "tsutils@^3.21.0": "https://registry.npmmirror.com/tsutils/-/tsutils-3.21.0.tgz", "tty-browserify@0.0.0": "https://registry.npmmirror.com/tty-browserify/-/tty-browserify-0.0.0.tgz", "tunnel-agent@^0.6.0": "https://registry.npmmirror.com/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "tweetnacl@^0.14.3": "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "tweetnacl@~0.14.0": "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "type-check@^0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-check@~0.3.2": "https://registry.npmmirror.com/type-check/-/type-check-0.3.2.tgz", "type-check@~0.4.0": "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz", "type-fest@^0.18.0": "https://registry.npmmirror.com/type-fest/-/type-fest-0.18.1.tgz", "type-fest@^0.20.2": "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmmirror.com/type-fest/-/type-fest-0.21.3.tgz", "type-fest@^0.6.0": "https://registry.npmmirror.com/type-fest/-/type-fest-0.6.0.tgz", "type-fest@^0.8.1": "https://registry.npmmirror.com/type-fest/-/type-fest-0.8.1.tgz", "type-is@~1.6.18": "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz", "typedarray@^0.0.6": "https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz", "typescript@3.9": "https://registry.npmmirror.com/typescript/-/typescript-3.9.10.tgz", "unbox-primitive@^1.0.1": "https://registry.npmmirror.com/unbox-primitive/-/unbox-primitive-1.0.1.tgz", "underscore@>=1.8.3": "https://registry.npmmirror.com/underscore/-/underscore-1.13.4.tgz", "unescape@^1.0.1": "https://registry.npmmirror.com/unescape/-/unescape-1.0.1.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.0.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.0.0.tgz", "union-value@^1.0.0": "https://registry.npmmirror.com/union-value/-/union-value-1.0.1.tgz", "unique-filename@^1.1.1": "https://registry.npmmirror.com/unique-filename/-/unique-filename-1.1.1.tgz", "unique-random-array@1.0.0": "https://registry.npmmirror.com/unique-random-array/-/unique-random-array-1.0.0.tgz", "unique-random@^1.0.0": "https://registry.npmmirror.com/unique-random/-/unique-random-1.0.0.tgz", "unique-slug@^2.0.0": "https://registry.npmmirror.com/unique-slug/-/unique-slug-2.0.2.tgz", "unique-string@^1.0.0": "https://registry.npmmirror.com/unique-string/-/unique-string-1.0.0.tgz", "universalify@^0.1.0": "https://registry.npmmirror.com/universalify/-/universalify-0.1.2.tgz", "universalify@^2.0.0": "https://registry.npmmirror.com/universalify/-/universalify-2.0.0.tgz", "unpipe@1.0.0": "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz", "unset-value@^1.0.0": "https://registry.npmmirror.com/unset-value/-/unset-value-1.0.0.tgz", "unzip-response@^2.0.1": "https://registry.npmmirror.com/unzip-response/-/unzip-response-2.0.1.tgz", "unzipper@^0.10.11": "http://nexus3.luban.fit/repository/npm/unzipper/-/unzipper-0.10.14.tgz#d2b33c977714da0fbc0f82774ad35470a7c962b1", "upath@^1.1.1": "https://registry.npmmirror.com/upath/-/upath-1.2.0.tgz", "update-notifier@^2.5.0": "https://registry.npmmirror.com/update-notifier/-/update-notifier-2.5.0.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "urix@^0.1.0": "https://registry.npmmirror.com/urix/-/urix-0.1.0.tgz", "url-loader@^4.1.0": "https://registry.npmmirror.com/url-loader/-/url-loader-4.1.1.tgz", "url-parse-lax@^1.0.0": "https://registry.npmmirror.com/url-parse-lax/-/url-parse-lax-1.0.0.tgz", "url-parse@^1.4.3": "https://registry.npmmirror.com/url-parse/-/url-parse-1.5.10.tgz", "url-parse@^1.5.10": "https://registry.npmmirror.com/url-parse/-/url-parse-1.5.10.tgz", "url-toolkit@^2.2.1": "https://registry.npmmirror.com/url-toolkit/-/url-toolkit-2.2.5.tgz", "url@^0.11.0": "https://registry.npmmirror.com/url/-/url-0.11.0.tgz", "urllib@^2.33.1": "https://registry.npmmirror.com/urllib/-/urllib-2.38.1.tgz", "use@^3.1.0": "https://registry.npmmirror.com/use/-/use-3.1.1.tgz", "user-home@^2.0.0": "https://registry.npmmirror.com/user-home/-/user-home-2.0.0.tgz", "user-meta@^1.0.0": "https://registry.npmmirror.com/user-meta/-/user-meta-1.0.0.tgz", "util-deprecate@^1.0.1": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@^1.0.2": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz", "util.promisify@1.0.0": "https://registry.npmmirror.com/util.promisify/-/util.promisify-1.0.0.tgz", "util@0.10.3": "https://registry.npmmirror.com/util/-/util-0.10.3.tgz", "util@^0.11.0": "https://registry.npmmirror.com/util/-/util-0.11.1.tgz", "utila@~0.4": "https://registry.npmmirror.com/utila/-/utila-0.4.0.tgz", "utility@0.1.11": "https://registry.npmmirror.com/utility/-/utility-0.1.11.tgz", "utility@^1.16.1": "https://registry.npmmirror.com/utility/-/utility-1.17.0.tgz", "utility@^1.8.0": "https://registry.npmmirror.com/utility/-/utility-1.17.0.tgz", "utils-merge@1.0.1": "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz", "utrie@^1.0.2": "https://registry.npmmirror.com/utrie/-/utrie-1.0.2.tgz", "uuid@^3.3.2": "https://registry.npmmirror.com/uuid/-/uuid-3.4.0.tgz", "uuid@^8.3.0": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "uuid@^8.3.2": "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz", "v8-compile-cache@^2.0.3": "https://registry.npmmirror.com/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "v8-compile-cache@^2.1.1": "https://registry.npmmirror.com/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "validate-npm-package-license@^3.0.1": "https://registry.npmmirror.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "value-equal@^1.0.1": "https://registry.npmmirror.com/value-equal/-/value-equal-1.0.1.tgz", "vary@~1.1.2": "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz", "verror@1.10.0": "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz", "video.js@^6 || ^7": "https://registry.npmmirror.com/video.js/-/video.js-7.20.2.tgz", "video.js@^7.20.2": "https://registry.npmmirror.com/video.js/-/video.js-7.20.2.tgz", "videojs-font@3.2.0": "https://registry.npmmirror.com/videojs-font/-/videojs-font-3.2.0.tgz", "videojs-vtt.js@^0.15.3": "https://registry.npmmirror.com/videojs-vtt.js/-/videojs-vtt.js-0.15.3.tgz", "vm-browserify@^1.0.1": "https://registry.npmmirror.com/vm-browserify/-/vm-browserify-1.1.2.tgz", "vm2@^3.9.8": "https://registry.npmmirror.com/vm2/-/vm2-3.9.10.tgz", "watchpack-chokidar2@^2.0.1": "https://registry.npmmirror.com/watchpack-chokidar2/-/watchpack-chokidar2-2.0.1.tgz", "watchpack@^1.7.4": "https://registry.npmmirror.com/watchpack/-/watchpack-1.7.5.tgz", "wbuf@^1.1.0": "https://registry.npmmirror.com/wbuf/-/wbuf-1.7.3.tgz", "wbuf@^1.7.3": "https://registry.npmmirror.com/wbuf/-/wbuf-1.7.3.tgz", "webpack-bundle-analyzer@^4.3.0": "https://registry.npmmirror.com/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.5.0.tgz", "webpack-cli@^3.3.12": "https://registry.npmmirror.com/webpack-cli/-/webpack-cli-3.3.12.tgz", "webpack-dev-middleware@^3.7.2": "https://registry.npmmirror.com/webpack-dev-middleware/-/webpack-dev-middleware-3.7.3.tgz", "webpack-dev-server@^3.11.0": "https://registry.npmmirror.com/webpack-dev-server/-/webpack-dev-server-3.11.3.tgz", "webpack-log@^2.0.0": "https://registry.npmmirror.com/webpack-log/-/webpack-log-2.0.0.tgz", "webpack-merge@^4.2.1": "https://registry.npmmirror.com/webpack-merge/-/webpack-merge-4.2.2.tgz", "webpack-merge@^4.2.2": "https://registry.npmmirror.com/webpack-merge/-/webpack-merge-4.2.2.tgz", "webpack-sources@^1.1.0": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.0": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.1": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^1.4.3": "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack@^4.44.1": "https://registry.npmmirror.com/webpack/-/webpack-4.46.0.tgz", "websocket-driver@>=0.5.1": "https://registry.npmmirror.com/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-driver@^0.7.4": "https://registry.npmmirror.com/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-extensions@>=0.1.1": "https://registry.npmmirror.com/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "which-boxed-primitive@^1.0.2": "https://registry.npmmirror.com/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "which-module@^2.0.0": "https://registry.npmmirror.com/which-module/-/which-module-2.0.0.tgz", "which-pm-runs@^1.0.0": "https://registry.npmmirror.com/which-pm-runs/-/which-pm-runs-1.1.0.tgz", "which@^1.2.14": "https://registry.npmmirror.com/which/-/which-1.3.1.tgz", "which@^1.2.9": "https://registry.npmmirror.com/which/-/which-1.3.1.tgz", "which@^1.3.1": "https://registry.npmmirror.com/which/-/which-1.3.1.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/-/which-2.0.2.tgz", "widest-line@^2.0.0": "https://registry.npmmirror.com/widest-line/-/widest-line-2.0.1.tgz", "win-release@^1.0.0": "https://registry.npmmirror.com/win-release/-/win-release-1.1.1.tgz", "word-wrap@^1.2.3": "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz", "word-wrap@~1.2.3": "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.3.tgz", "worker-farm@^1.7.0": "https://registry.npmmirror.com/worker-farm/-/worker-farm-1.7.0.tgz", "wrap-ansi@^5.1.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-5.1.0.tgz", "wrap-ansi@^6.2.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@^2.0.0": "https://registry.npmmirror.com/write-file-atomic/-/write-file-atomic-2.4.3.tgz", "write@1.0.3": "https://registry.npmmirror.com/write/-/write-1.0.3.tgz", "ws@^6.2.1": "https://registry.npmmirror.com/ws/-/ws-6.2.2.tgz", "ws@^7.3.1": "https://registry.npmmirror.com/ws/-/ws-7.5.7.tgz", "xdg-basedir@^3.0.0": "https://registry.npmmirror.com/xdg-basedir/-/xdg-basedir-3.0.0.tgz", "xml2js@^0.4.16": "https://registry.npmmirror.com/xml2js/-/xml2js-0.4.23.tgz", "xmlbuilder@~11.0.0": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "xmlchars@^2.2.0": "http://nexus3.luban.fit/repository/npm/xmlchars/-/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb", "xregexp@2.0.0": "https://registry.npmmirror.com/xregexp/-/xregexp-2.0.0.tgz", "xtend@^4.0.0": "https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz", "xtend@~4.0.1": "https://registry.npmmirror.com/xtend/-/xtend-4.0.2.tgz", "y18n@^4.0.0": "https://registry.npmmirror.com/y18n/-/y18n-4.0.3.tgz", "yallist@^2.1.2": "https://registry.npmmirror.com/yallist/-/yallist-2.1.2.tgz", "yallist@^3.0.2": "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz", "yallist@^4.0.0": "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz", "yaml@^1.10.0": "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz", "yaml@^1.7.2": "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz", "yargs-parser@^13.1.2": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-13.1.2.tgz", "yargs-parser@^18.1.2": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-18.1.3.tgz", "yargs-parser@^20.2.3": "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs@^13.3.2": "https://registry.npmmirror.com/yargs/-/yargs-13.3.2.tgz", "yargs@^15.1.0": "https://registry.npmmirror.com/yargs/-/yargs-15.4.1.tgz", "yocto-queue@^0.1.0": "https://registry.npmmirror.com/yocto-queue/-/yocto-queue-0.1.0.tgz", "zip-stream@^4.1.0": "https://registry.npmmirror.com/zip-stream/-/zip-stream-4.1.0.tgz"}, "files": [], "artifacts": {}}