// import React from "react";
import {Moment} from "moment";
import {LabeledValue} from "antd/lib/select";
import {GetHiddenDangerCheckListType} from "../../../api/hiddenDangerCheck/type";
import {ComColumnsProps} from "../../../components/TableColumnsControl/ColumnsControl";
import {ComFormItemProps} from "../../../components/FormItem";
import renderTableText from "../../../components/renderTableText";
import {momentText} from "../../../assets/ts/utils";
// import QueryFormBtn from "../../../components/WBS/queryFormBtn";
import {CheckSubOptionValueType} from "../../../components/CheckSubOption/MultipleChoice";
import {renderWbsEbs} from "../../../components/FormWbsEbs/data";
import {getNodeNameById} from "../../../assets/ts/form";

export const reformStatusType = {
    REFORM_NOT_NEED: 0, // 无需整改
    REFORM_NOT_START: 1, // 待整改
    REFORMING: 2, // 整改中
    REFORM_END: 3, // 已整改
    REFORM_NEED: 4, // 待整改
};

export const reformStatusList: LabeledValue[] = [
    {value: reformStatusType.REFORM_NOT_NEED, label: "无需整改"},
    {value: reformStatusType.REFORM_NOT_START, label: "待整改"},
    {value: reformStatusType.REFORMING, label: "整改中"},
    {value: reformStatusType.REFORM_END, label: "已整改"},
    {value: reformStatusType.REFORM_NEED, label: "待整改"},
];
export interface QueryFormType {
    "buildType": number;
    "checkResult": number;
    "checkTime": Moment[];
    "checkTimeEnd": string;
    "checkTimeStart": string;
    "checkType": number;
    "checkUser": string;
    "dangerLevel": number;
    "deptId": string;
    "hasReform": number;
    "moduleType": string;
    "nodeId": string;
    "page": number;
    "position": string;
    "searchWord": string;
    "size": number;
    "sort": string;
    "wbsId": string;
    "wbsIds": string[];
    projectCategoryId: string;
    subOptionContent: CheckSubOptionValueType;
    checkPlanId?: string[];
}

export const buildTypeList: LabeledValue[] = [
    {value: 0, label: "施工自查"},
    {value: 1, label: "监理检查"},
    {value: 3, label: "业主检查"},
];
/* export const checkResultList: LabeledValue[] = [
    {value: 0, label: "合格"},
    {value: 1, label: "口头警告"},
    {value: 2, label: "书面整改"},
]; */
export const dangerLevelList: LabeledValue[] = [
    {value: 0, label: "一般"},
    // {value: 1, label: "较大"},
    {value: 2, label: "重大"},
    {value: 3, label: "特大"},
];
export const checkTypeList: LabeledValue[] = [
    {value: 0, label: "日常检查"},
    {value: 1, label: "专项检查"},
    {value: 2, label: "不定期检查"},
    {value: 3, label: "节假日检查"},
    {value: 4, label: "综合性检查"},
    {value: 5, label: "其他"},
    /* {value: 6, label: "按天检查"},
    {value: 7, label: "按周检查"},
    {value: 8, label: "按月检查"},
    {value: 9, label: "风险检查"}, */
];

export const businessType = "plan.check.info.wbs";

export const queryItemList: ComFormItemProps[] = [
    /* {
        key: "wbsValue",
        type: "custom",
        typeConfig: {},
        itemConfig: {name: "wbsValue", label: "分部分项"},
        customNode: <QueryFormBtn businessType={businessType} />,
        colConfig: {span: 6}
    }, */
    {
        key: "checkPlanId",
        type: "cascader",
        typeConfig: {options: [], placeholder: "全部", dropdownClassName: "customAntCascader"},
        itemConfig: {name: "checkPlanId", label: "被检查计划"},
        colConfig: {span: 6},
    },
    {
        key: "buildType",
        type: "select",
        typeConfig: {options: buildTypeList, placeholder: "全部"},
        itemConfig: {name: "buildType", label: "检查单位"},
        colConfig: {span: 6},
    },
    {
        key: "dangerLevel",
        type: "select",
        typeConfig: {
            options: dangerLevelList,
            placeholder: "全部"
        },
        itemConfig: {name: "dangerLevel", label: "异常级别"},
        colConfig: {span: 6}
    },
    /* {
        key: "checkResult",
        type: "select",
        typeConfig: {
            options: checkResultList,
            placeholder: "全部"
        },
        itemConfig: {name: "checkResult", label: "检查结果"},
        colConfig: {span: 6}
    }, */
    {
        key: "hasReform",
        type: "select",
        typeConfig: {
            options: reformStatusList.filter((status) => status.value !== 1),
            placeholder: "全部"
        },
        itemConfig: {name: "hasReform", label: "整改状态"},
        colConfig: {span: 6}
    },
    {
        key: "checkTime",
        type: "rangePicker",
        typeConfig: {},
        itemConfig: {name: "checkTime", label: "检查日期"},
        colConfig: {span: 6}
    },
    {
        key: "checkType",
        type: "select",
        typeConfig: {
            options: checkTypeList,
            placeholder: "全部"
        },
        itemConfig: {name: "checkType", label: "检查形式"},
        colConfig: {span: 6}
    },
    {
        key: "projectCategoryId",
        type: "custom",
        typeConfig: {},
        itemConfig: {name: "projectCategoryId", label: "工程类型"},
        colConfig: {span: 6}
    },
    {
        key: "subOptionContent",
        type: "custom",
        typeConfig: {placeholder: "全部"},
        itemConfig: {name: "subOptionContent", label: "检查分项"},
        colConfig: {span: 6}
    },
    {
        key: "searchWord",
        type: "input",
        typeConfig: {placeholder: "请输入检查编号、检查人进行搜索"},
        itemConfig: {name: "searchWord", label: "关键字搜索"},
        colConfig: {span: 6}
    },
];

export type ComColumnsType = ComColumnsProps<GetHiddenDangerCheckListType>;

export const columnsInit: ComColumnsType[] = [
    {
        title: "序号",
        align: "center",
        mustShow: true,
        show: true,
        width: 80,
        fixed: "left",
        render: (_text: unknown, _record: GetHiddenDangerCheckListType, index: number) => index + 1
    },
    {
        key: "checkPlanId",
        title: "被检查计划",
        dataIndex: "checkPlanId",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        fixed: "left",
        render: renderTableText,
    },
    {
        key: "wbsEbs",
        title: "关联WBS/EBS",
        dataIndex: "wbsEbs",
        align: "left",
        mustShow: false,
        show: false,
        // width: 120,
        width: 160,
        // fixed: "left",
        render: renderWbsEbs,
    },
    {
        key: "number",
        title: "检查编号",
        dataIndex: "number",
        align: "left",
        mustShow: false,
        show: false,
        width: 150,
        // fixed: "left",
        render: renderTableText,
    },
    {
        key: "buildType",
        title: "检查单位",
        dataIndex: "buildType",
        align: "left",
        mustShow: false,
        show: true,
        // width: 100,
        width: 140,
        render: (_text: string, _record: unknown) => {
            const buildTypeListFilter = buildTypeList.filter((item) => item.value === _text);
            const text = Array.isArray(buildTypeListFilter) && buildTypeListFilter.length > 0 ? buildTypeListFilter[0].label : "";
            return text;
        }
    },
    {
        key: "checkNodeId",
        title: "项目",
        dataIndex: "checkNodeId",
        align: "left",
        mustShow: false,
        show: false,
        width: 150,
        render: (val: string) => renderTableText(getNodeNameById(val)),
    },
    {
        key: "checkUser",
        title: "检查人",
        dataIndex: "checkUser",
        align: "left",
        mustShow: false,
        show: false,
        width: 120,
        render: renderTableText,
        // render: (_text: unknown, _record: any) => _record.checkUserName
    },
    {
        key: "projectCategory",
        title: "工程类型",
        dataIndex: "projectCategory",
        align: "left",
        mustShow: false,
        show: false,
        width: 120,
        render: renderTableText,
        // render: (_text: unknown, _record: any) => _record.checkUserName
    },
    {
        key: "checkType",
        title: "检查形式",
        dataIndex: "checkType",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (_text: unknown, _record: unknown) => {
            const checkTypeListFilter = checkTypeList.filter((item) => item.value === _text);
            const text = Array.isArray(checkTypeListFilter) && checkTypeListFilter.length > 0 ? checkTypeListFilter[0].label : "";
            return text;
        }
    },
    {
        key: "subOptionContentList",
        title: "检查分项",
        dataIndex: "subOptionContentList",
        align: "left",
        mustShow: false,
        show: true,
        width: 120,
        render: (_text: string[]) => renderTableText(Array.isArray(_text) ? _text.join(";") : ""),
    },
    /* {
        key: "checkResult",
        title: "检查结果",
        dataIndex: "checkResult",
        align: "center",
        mustShow: false,
        show: true,
        width: 100,
        render: (_text: unknown, _record: unknown) => {
            const checkResultListFilter = checkResultList.filter((item) => item.value === _text);
            const text = Array.isArray(checkResultListFilter) && checkResultListFilter.length > 0 ? checkResultListFilter[0].label : "";
            return text;
        }
    }, */
    {
        key: "dangerLevel",
        title: "异常级别",
        dataIndex: "dangerLevel",
        align: "center",
        mustShow: false,
        show: true,
        width: 120,
        render: (_text: unknown, _record: unknown) => {
            const dangerLevelListFilter = dangerLevelList.filter((item) => item.value === _text);
            const text = Array.isArray(dangerLevelListFilter) && dangerLevelListFilter.length > 0 ? dangerLevelListFilter[0].label : "无隐患";
            return text;
        }
    },
    {
        key: "checkTime",
        title: "检查日期",
        dataIndex: "checkTime",
        align: "center",
        mustShow: false,
        show: true,
        // width: 100,
        width: 140,
        render: (_text: string | number) => momentText(_text, "YYYY.MM.DD")
    },
    {
        key: "position",
        title: "检查部位",
        dataIndex: "position",
        align: "center",
        mustShow: false,
        show: true,
        width: 120,
        render: renderTableText,
    },
    /* {
        key: "lawBasis",
        title: "法规依据",
        dataIndex: "lawBasis",
        align: "left",
        mustShow: false,
        show: false,
        width: 200,
        render: renderTableText,
    }, */
    {
        key: "updateBy",
        title: "编辑人",
        dataIndex: "updateBy",
        align: "center",
        mustShow: false,
        show: false,
        width: 120,
    },
    {
        key: "updateAt",
        title: "编辑日期",
        dataIndex: "updateAt",
        align: "left",
        mustShow: false,
        show: false,
        width: 120,
        render: (text: string | number) => (text !== null ? momentText(text) : "")
    },
    {
        key: "reformStatus",
        title: "整改状态",
        dataIndex: "reformStatus",
        align: "center",
        mustShow: false,
        show: true,
        width: 140,
        render: renderTableText,
    },
    {
        key: "operate",
        title: "操作",
        mustShow: true,
        show: true,
        width: 240
    }
];
