import Fetch from "../../service/Fetch";
import * as Types from "./type";

/**
 * 获取项目部下用户列表（iworksWeb1.1.1新增）
 */
export const getListDeptUser = async (deptId: string) => Fetch<Types.UserMailVo[]>({
    url: `/pdscommon/rs/userInfo/listDeptUser/deptId/${deptId}`,
    methods: "get"
});

/**
 * 按组织分组获取用户列表,根据项目筛选
 */
export const getUserInfoByFilterOrg = async (params: {}) => Fetch<Types.UserInfoTypeByOrg[]>({
    url: "/pdscommon/rs/userInfo/listUsersOfFilterOrg",
    methods: "post",
    data: params
});

export const getUserInfoByOrg = async (params: {}) => Fetch<Types.UserInfoTypeByOrg[]>({
    url: "/pdscommon/rs/userInfo/listUsersOfOrg",
    methods: "get",
    data: params
});

/**
 * 按角色获取用户列表, 根据项目公司筛选
 */
export const getUserInfoOfRoleByFilterOrg = async (params: {}) => Fetch<Types.UserInfoTypeOfRoleByOrg[]>({
    url: "/pdscommon/rs/userInfo/listUsersOfRoleFilterOrg",
    methods: "post",
    data: params
});

export const getUserInfoOfRoleByOrg = async (params: {}) => Fetch<Types.UserInfoTypeOfRoleByOrg[]>({
    url: "/pdscommon/rs/userInfo/listUsersOfRole",
    methods: "get",
    data: params
});
/**
 * 按组织机构搜索； center人员列表
 */
export const getSearchUsersOfOrg = async (params: {}) => Fetch<Types.SearchUsersOfOrg[]>({
    url: "/pdscommon/rs/userInfo/searchUsersOfOrg",
    methods: "post",
    data: params
});
/**
 * 按用户角色搜索; center人员列表
 */
export const getSearchUsersOfRole = async (params: {}) => Fetch<Types.SearchUsersOfRole[]>({
    url: "/pdscommon/rs/userInfo/searchUsersOfRole",
    methods: "post",
    data: params
});
