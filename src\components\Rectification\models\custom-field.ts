import {CSSProperties} from "react";

export interface FlowTypeDetail {
    id: string;
    customFields: CustomFieldData[];
    procTmplId: string;
    name?: string;
    authEntityId: string;
}

export interface FileInfo {
    fileName: string;
    fileSysMd5: string;
    fileSize: number;
    fileExtension: string;
    fileSysUuid: string;
    fileType?: number;
}

export interface CustomFieldProps {
    data: CustomFieldData;
    disabled?: boolean;
    value?: string | string[] | number;
    onChange: (data: string | string[]) => void;
    onBlur?: (val?: string) => void;
    currentValue?: string | string[] | number;
    hint?: string;
    maxLength?: number;
    displayOnly?: boolean;
    style?: CSSProperties;
    onLoadProjInfo?: () => void;
    onPreview?: (uuid: string, name: string) => void;
    relateDoc?: (visible: boolean) => void;
    nowrap?: boolean;
    nodeId?: string;
    nodeType?: number;
}


/**
 * 自定义表单类型，暂不支持关联BIM
 */
export enum CustomFieldType {
    Input,
    TextArea,
    Select,
    MultiSelect,
    Person,
    NumberInput,
    DateInput,
    Attachment,
    REF_BIM,
    REF_DOC,
    REF_PROC
}

export enum CustomFieldMode {
    Readable = 0x02,
    Writable = 0x04
}

export interface OptionType<TId = string, TValue = string> {
    id: TId;
    value: TValue;
}

export interface CustomFieldData {
    id: string;
    name: string;
    mode: CustomFieldMode;
    visible: boolean;
    type: CustomFieldType;
    data: string | OptionType[];
    required: boolean;
    defaultValue?: string | string[] | number;
    maxLength: number;
    hint: string;
    valueId: string;
    hasHistory: boolean;
}

export interface RelateBimInfo {
    bimInfo: BimProjInfo;
    binds?: CheckBind[];
    bindType: string;
}

export interface RefDetailInfo {
    descName: string;
    descValue: string;
}
export interface RefInfo {
    refKey: string;
    refValueDescs: RefDetailInfo[];
}
export interface ServerBimInfo {
    refType: string;
    valueElements: RefInfo[];
}

export interface RelateDocInfo {
    docId: string;
    docName: string;
    docMd5: string;
    size: number;
    extension: string;
    docUuid: string;
    docType: number;
}

/**
 * BIM绑定信息
 */
export interface CheckBind {
    /**
     * 代理工程ID
     */
    ppid?: number;
    /**
     * 楼层
     */
    floor?: string;
    /**
     * 工程类型（1-土建预算，2-安装预算，3-钢筋预算，4-Revit，5-Tekla，6-造价工程，7-REMIZ，8-班筑家装，9-场布预算，10-Civil3D，11-Bentley，12-Rhino，13-IFC
     */
    projType?: number;
    /**
     * 专业
     */
    spec?: string;
    /**
     * 大类
     */
    compClass?: string;
    /**
     * 小类
     */
    subClass?: string;
    /**
     * 属性名称
     */
    attrname?: string;
    /**
     * 构件handle
     */
    handle?: string;
    /**
     * 属性目录
     */
    treenode?: string;
    /**
     * 部位树path
     */
    treePaths?: string[];
}

export interface BimProjInfo {
    nPPid: number;
    nodeId?: string;
    projType: TreeNodeType;
    projName: string;
    bindType: number;
    bindBws?: boolean;
}

export enum TreeNodeType {
    Undefined,
    Root,
    Company,
    Dept,
    Tender,
    Mono,
    Unit,
    Model,
    Project,
    TjModel,
    GjModel,
    AzModel,
    RevitModel,
    TeklaModel,
    Civil3dModel,
    BentleyModel,
    RhinoModel,
    IfcModel,
    RemizModel,
    SiteModel,
    PdfModel,
    CatiaModel,
}

export const projectTypeMapping: [number, TreeNodeType][] = [
    [1, TreeNodeType.TjModel],
    [2, TreeNodeType.AzModel],
    [3, TreeNodeType.GjModel],
    [4, TreeNodeType.RevitModel],
    [5, TreeNodeType.TeklaModel],
    // [6, TreeNodeType.],
    [7, TreeNodeType.RemizModel],
    [8, TreeNodeType.RemizModel],
    [9, TreeNodeType.SiteModel],
    [10, TreeNodeType.Civil3dModel],
    [11, TreeNodeType.BentleyModel],
    [12, TreeNodeType.RhinoModel],
    [13, TreeNodeType.IfcModel],
    [14, TreeNodeType.CatiaModel]
];
