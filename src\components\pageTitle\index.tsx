import React, {memo, useCallback, FC, ReactNode, useState} from "react";
// import {Breadcrumb} from "antd";
import {useLocation} from "react-router-dom";
import {createUseStyles} from "react-jss";
import {useSelector} from "react-redux";
import {cloneDeep} from "lodash-es";
import MyIcon from "../MyIcon";
import {RootState} from "../../store/rootReducer";
import {MenuListProps} from "../../api/common.type";

interface BreadcrumbType {
    route?: string;
    path: string;
    label: string;
}
interface Props {
    title?: string;
    onBack?: () => void;
    titleContent?: ReactNode;
}
const useStyle = createUseStyles({
    header: {
        padding: "16px 24px 0"
    },
    breadcrumbBox: {
        "& .ant-breadcrumb .ant-breadcrumb-link": {
            fontSize: 14,
            lineHeight: "20px",
            color: "#717784",
        },
    },
    title: {
        lineHeight: "28px",
        fontSize: 20,
        fontWeight: "bold",
        color: "#061127",
        marginTop: 6,
        "& .anticon": {
            marginRight: 10,
        },
        // "& div:last-child .anticon": {
        //     marginLeft: 20
        // }
    },

});
const PageTitle: FC<Props> = (props) => {
    const {title, onBack, titleContent} = props;
    const {menuList, lastSelectedMenu} = useSelector((state: RootState) => state.commonData);
    const [dataList, setDataList] = useState<BreadcrumbType[]>([]);
    const [curName, setCurName] = useState("");
    const cls = useStyle();
    // const history = useHistory();
    const location = useLocation();

    const dealMenuList = useCallback(() => {
        if (location === undefined) {
            return;
        }
        if (lastSelectedMenu?.parentId === 2691) {
            setDataList([{label: lastSelectedMenu.label, path: lastSelectedMenu.path}]);
            setCurName(lastSelectedMenu.label);
            return;
        }
        type menuFlatType = BreadcrumbType & {infoList: BreadcrumbType[]};
        const menuFlatArr: menuFlatType[] = [];
        const dealData = (list: MenuListProps[], parent: BreadcrumbType[]) => {
            list.forEach((el) => {
                const info = {label: el.label, path: el.path, route: el.parentId !== 2691 ? el.path : undefined};
                const newParent = [...parent, info];
                menuFlatArr.push({...info, infoList: newParent});
                if (Array.isArray(el.children)) {
                    dealData(el.children, newParent);
                }
            });
        };
        dealData(cloneDeep(menuList), []);
        const findedMenu = menuFlatArr.find((el) => el.path === location.pathname);
        if (findedMenu !== undefined) {
            const tempNameList = cloneDeep(findedMenu.infoList ?? []);
            setCurName(tempNameList[tempNameList.length - 1].label);
            if (tempNameList.length > 1) {
                tempNameList.pop();
            }
            setDataList(tempNameList);
        }
    }, [lastSelectedMenu, location, menuList]);

    React.useEffect(() => {
        dealMenuList();
    }, [dealMenuList]);
    // const handleBreadcrumb = useCallback((item) => {
    //     if (item.route === undefined) {
    //         return;
    //     }
    //     history.replace(item.route);
    //     if (onBack !== undefined) {
    //         onBack();
    //     }
    // }, [history, onBack]);

    const handleBack = useCallback(
        () => {
            window.history.back();
            if (onBack !== undefined) {
                onBack();
            }
        },
        [onBack]
    );


    return (
        <div className={cls.header}>
            <div className={cls.title}>
                {(dataList ?? []).length > 1 && <MyIcon type="icon-fanhui" onClick={handleBack} />}
                {title !== undefined ? title : curName}
                {titleContent !== undefined && titleContent}
            </div>
        </div>
    );
};

export default memo(PageTitle);
