import {Reducer} from "redux";
import {TypedUseSelectorHook, useSelector} from "react-redux";
import {persistReducer, persistStore} from "redux-persist";
import {IModule, IModuleStore, createStore} from "redux-dynamic-modules";
import storage from "redux-persist/lib/storage";
import {getThunkExtension} from "redux-dynamic-modules-thunk";
import commonReducer from "./common/reducer";
import statusReducer from "./status/reducer";
import ProcessReducer from "./process/reducer";
import sandManageReducer from "./sandManage/reducer";
import rootReducer, {RootState} from "./rootReducer";
import sandAnalyzedDataReducer from "./sandAnalyzedData/reducer";
import wbsTreeReducer from "../components/WBS/store/reducer";
import PersonReducer from "./personSelect/reducer";
import noRegisterReducer from "./no-persist/reducer";
import rectificationDetailReducer from "./rectification/detail/reducer";
import rectificationTempReducer from "./rectification/template/reducer";

const persistConfig = {
    key: "plan",
    storage,
    blacklist: ["sandAnalyzedData", "sandManageData", "processData", "wbsTree", "personData", "noRegister", "rectificationDetail", "rectificationTemp"]
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export type StateType<T extends Reducer> = T extends Reducer<infer S> ? S : never;

const wrapReducerModule = function wrapReducerModule<
    T extends Reducer>(name: string, reducer: T): IModule<{[key in typeof name]: StateType<T>}> {
    return {
        id: name,
        reducerMap: {
            [name]: reducer,
        }
    };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const store: IModuleStore<any> = createStore(
    {
        initialState: {},
        extensions: [getThunkExtension()],
    },
    wrapReducerModule("commonData", commonReducer as Reducer),
    wrapReducerModule("statusData", statusReducer as Reducer),
    wrapReducerModule("processData", ProcessReducer as Reducer),
    wrapReducerModule("persistInfo", persistedReducer as Reducer),
    wrapReducerModule("sandManageData", sandManageReducer as Reducer),
    wrapReducerModule("sandAnalyzedData", sandAnalyzedDataReducer as Reducer),

    wrapReducerModule("wbsTree", wbsTreeReducer as Reducer),
    wrapReducerModule("personData", PersonReducer as Reducer),
    wrapReducerModule("noRegister", noRegisterReducer as Reducer),
    wrapReducerModule("rectificationDetail", rectificationDetailReducer as Reducer),
    wrapReducerModule("rectificationTemp", rectificationTempReducer as Reducer),
);

const persistor = persistStore(store);
const {dispatch, getState} = store;

export {
    persistor,
    dispatch,
    getState
};
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export default store;
