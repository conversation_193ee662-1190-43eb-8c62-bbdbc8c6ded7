import Fetch from "../../service/Fetch";

export interface Res<T = unknown> {
    msg: string;
    code: number;
    data: T;
    success: boolean;
}

interface CompleteExtraInfo {
    code: string;
    resId: string;
    sectionId: string;
}

interface WorkGuideInfo {
    businessType: string;
    completeExtraInfo: CompleteExtraInfo;
    id: string;
    source: string;
}

export interface IncompleteTaskParams {
    businessType: string;
    menuId: string;
    resId: string;
    sectionId: string;
}

export interface IncompleteTaskData {
    businessType: string;
    code: string;
    id: string;
    operateType: string;
    resId: string;
    sectionId: string;
}

/**
 * 获取数据
 */
const completeWgTask = async (params: WorkGuideInfo) => Fetch<Res>({
    url: "/lkPurchase/wg/task/complete",
    methods: "post",
    data: params,
});

/**
 * 获取未完成的任务列表
 */
const getIncompleteWgTask = async (params: IncompleteTaskParams) => Fetch<Res<IncompleteTaskData[]>>({
    url: "/lkPurchase/wg/task/incomplete",
    methods: "get",
    data: params,
});

export default completeWgTask;
export {getIncompleteWgTask};
