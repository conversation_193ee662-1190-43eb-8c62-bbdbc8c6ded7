import {ColProps, Form, FormItemProps, Grid} from "antd";
import React, {useEffect, useState} from "react";

const {Item} = Form;
const {useBreakpoint} = Grid;

const ComItem = (props: FormItemProps) => {
    const {
        labelCol,
        wrapperCol,
        ...other
    } = props;
    const [curLabelCol, setCurLabelCol] = useState<ColProps|undefined>(labelCol);
    const [curWrapperCol, setWrapperCol] = useState<ColProps|undefined>(wrapperCol);
    const screens = useBreakpoint();

    useEffect(() => {
        if (screens.xxl === true && labelCol?.xxl !== undefined) {
            setCurLabelCol(labelCol.xxl as ColProps);
        } else if (screens.xl === true && labelCol?.xl !== undefined) {
            setCurLabelCol(labelCol.xl as ColProps);
        } else if (screens.lg === true && labelCol?.lg !== undefined) {
            setCurLabelCol(labelCol.lg as ColProps);
        } else if (screens.md === true && labelCol?.md !== undefined) {
            setCurLabelCol(labelCol.md as ColProps);
        } else if (screens.sm === true && labelCol?.sm !== undefined) {
            setCurLabelCol(labelCol.sm as ColProps);
        } else if (screens.xs === true && labelCol?.xs !== undefined) {
            setCurLabelCol(labelCol.xs as ColProps);
        }
        if (screens.xxl === true && wrapperCol?.xxl !== undefined) {
            setWrapperCol(wrapperCol.xxl as ColProps);
        } else if (screens.xl === true && wrapperCol?.xl !== undefined) {
            setWrapperCol(wrapperCol.xl as ColProps);
        } else if (screens.lg === true && wrapperCol?.lg !== undefined) {
            setWrapperCol(wrapperCol.lg as ColProps);
        } else if (screens.md === true && wrapperCol?.md !== undefined) {
            setWrapperCol(wrapperCol.md as ColProps);
        } else if (screens.sm === true && wrapperCol?.sm !== undefined) {
            setWrapperCol(wrapperCol.sm as ColProps);
        } else if (screens.xs === true && wrapperCol?.xs !== undefined) {
            setWrapperCol(wrapperCol.xs as ColProps);
        }
    }, [labelCol, screens, wrapperCol]);

    return (
        <Item {...other} labelCol={curLabelCol} wrapperCol={curWrapperCol} />
    );
};

export default ComItem;
