import {Modal} from "antd";
import React from "react";
import {useDispatch, useSelector} from "react-redux";
import {isImg, isVideo} from "../../assets/ts/utils";
import {setPreviewUrl} from "../../store/common/action";
import {RootState} from "../../store/rootReducer";
import VideoPlayer from "../VideoPlayer";

const PreviewModal = () => {
    const {previewInfo} = useSelector((state: RootState) => state.commonData);
    const dispatch = useDispatch();

    const closeModal = () => {
        dispatch(
            setPreviewUrl({
                url: "",
                name: ""
            })
        );
    };

    const render = () => {
        if (isImg(previewInfo.name)) {
            return <img src={previewInfo.url} style={{width: "100%"}} />;
        }
        if (isVideo(previewInfo.name)) {
            return <VideoPlayer src={previewInfo.url} />;
        }
        return (
            <div style={{height: "70vh", width: "100%"}}>
                <iframe
                    src={previewInfo.url}
                    frameBorder="0"
                    height="100%"
                    width="100%"
                />
            </div>
        );
    };

    if (Boolean(previewInfo) === false) {
        return null;
    }

    return (
        <Modal
            title="文件预览"
            width="70%"
            footer={null}
            visible={Boolean(previewInfo.url) === true}
            onCancel={closeModal}
            centered
            destroyOnClose
        >
            <div className="centerX">
                {render()}
            </div>
        </Modal>
    );
};

export default PreviewModal;
