import {DataNode} from "antd/lib/tree";
import {<PERSON><PERSON><PERSON><PERSON>, Tooltip, Typography} from "antd";
import React from "react";
import {flatten, sortedUniq} from "lodash-es";
import {OrgProjNodeVo, ProjNameVo, TreeNodeVO, WBSType} from "../../api/common.type";
import {toArr, pathSeparator} from "../../assets/ts/utils";
import * as comApi from "../../api/wbsEbs";

export enum RelateTabType {
    /** 关联项目 */
    RelateProj,
    /** 关联构件 */
    RelateComp,
    /** 构件类别 */
    RelateCompCategory,
    /** 关联构件组, 暂时不用 */
    // RelateCompGroup
}

export const defaultTabs = [
    {
        name: "构件",
        type: RelateTabType.RelateComp,
    },
    {
        name: "构件类别",
        type: RelateTabType.RelateCompCategory,
    },
    {
        name: "工程",
        type: RelateTabType.RelateProj,
    },
];

export interface ProjectTreeNodeData extends DataNode {
    originData: OrgProjNodeVo | ProjNameVo;
    children?: ProjectTreeNodeData[];
}

export interface CompCategoryTreeNodeData extends DataNode {
    originData: TreeNodeVO;
    treePath: string[];
    children?: CompCategoryTreeNodeData[];
}

export enum TreeNodeType {
    Undefined,
    Root,
    Company,
    Dept,
    Tender,
    Mono,
    Unit,
    Model,
    Project,
    TjModel,
    GjModel,
    AzModel,
    RevitModel,
    TeklaModel,
    Civil3dModel,
    BentleyModel,
    RhinoModel,
    IfcModel,
    RemizModel,
    SiteModel,
    PdfModel,
    CatiaModel,
    Pkpm
}

export const majorOptions = [
    {
        label: "全部专业",
        value: 0
    },
    {
        label: "土建预算",
        value: 1
    },
    {
        label: "安装预算",
        value: 2
    },
    {
        label: "钢筋预算",
        value: 3
    },
    {
        label: "Revit",
        value: 4
    },
    {
        label: "Tekla",
        value: 5
    },
    {
        label: "Civil3D",
        value: 10
    },
    {
        label: "Bentley",
        value: 11
    },
    {
        label: "Rhino",
        value: 12
    },
    {
        label: "IFC",
        value: 13
    },
    {
        label: "班筑家装",
        value: 8
    },
    {
        label: "场布预算",
        value: 9
    },
    // {
    //     label: "CATIA",
    //     value: TreeNodeType.CatiaModel
    // }
];

export const relateOptions = [
    {
        label: "全部状态",
        value: -1
    },
    {
        label: "未关联",
        value: 0
    },
    {
        label: "已关联",
        value: 1,
    }
];

// ebs节点信息
export interface EBSNodeType {
    handle?: string;
    paths?: string[];
    ppid: number;
    projName?: string;
}
/**
 * -1-绑定wbs、0-未绑定、 1-工程、 2-构件、 3-类别
 */
export interface WbsEbsNodeType {
    bindType: number;
    businessId?: string;
    businessType?: string;
    deptId?: string;
    ebsNodes: EBSNodeType[] | null;
    wbsNodes: WBSType[] | null;
    wbsNodeIds?: string[];
}


export type WbsEbsType = WBSType | EBSNodeType;
/**
 * -1-绑定wbs、0-未绑定、 1-工程、 2-构件、 3-类别
 */
export const getWBSEBSType = (val: WbsEbsType[] | undefined): number => {
    const firstNode = toArr(val as unknown as EBSNodeType[])[0];
    if (firstNode === undefined) {
        return 0;
    }
    if (Boolean(firstNode.ppid) === false) {
        return -1;
    }
    if (Boolean(firstNode.ppid) === true && toArr(firstNode.paths ?? []).length === 0 && Boolean(firstNode.handle) === false) {
        return 1;
    }
    if (Boolean(firstNode.handle) === false) {
        return 3;
    }
    return 2;
};

export const transformTree = (val: TreeNodeVO, treePath: string[]): CompCategoryTreeNodeData => {
    const {treeNodeList, ...other} = val;
    const tempTreePath = [...treePath, val.name];
    return {
        key: tempTreePath.join(pathSeparator),
        title: val.name,
        treePath: tempTreePath,
        originData: other,
        children: toArr(val.treeNodeList ?? []).map((el) => transformTree(el, tempTreePath))
    };
};

export const filterTreeByKeys = (vals: CompCategoryTreeNodeData[], keys: string[]) => {
    const filterNode = (val: CompCategoryTreeNodeData) => {
        const isShow = keys.find((el) => el.startsWith(val.key as unknown as string));
        if (toArr(val.children ?? []).length > 0) {
            val.children = toArr(val.children ?? []).filter((el) => filterNode(el));
        }
        return Boolean(isShow);
    };
    return vals.filter((el) => filterNode(el));
};

export const handleBindWbsEbs = (params: {}, cb: () => void) => {
    if (params === undefined) {
        cb();
        return;
    }
    const tempParams = params as unknown as WbsEbsNodeType;
    if (tempParams.bindType === 0 || tempParams.bindType === undefined) {
        cb();
        return;
    }
    const val = {
        ...tempParams,
        wbsNodeIds: toArr(tempParams.wbsNodes ?? []).map((el) => el.wbsNodeId),
    };
    comApi.bindWbsEbs(val)
        .then(() => {
            cb();
        });
};

interface HandleGetWbsEbsType {
    form?: FormInstance;
    businessId: string;
}

export const handleGetWbsEbs = async (val: HandleGetWbsEbsType) => {
    const {form, businessId} = val;
    if (Boolean(businessId) === false) {
        return undefined;
    }
    const {success, data} = await comApi.getBindWbsEbsById([businessId]);
    if (success === false || toArr(data).length === 0) {
        return undefined;
    }
    if (form !== undefined) {
        form.setFieldsValue({wbsEbs: data[0]});
    }
    return data[0];
};

export const handleGetWbsEbs2 = async (val: HandleGetWbsEbsType) => {
    const {form, businessId} = val;
    if (Boolean(businessId) === false) {
        return undefined;
    }
    const {success, data} = await comApi.getBindWbsEbsById([businessId]);
    if (success === false || toArr(data).length === 0) {
        return undefined;
    }
    if (form !== undefined) {
        form.setFieldsValue({wbsEbs: data[0]});
    }
    return data[0];
};

interface DealTableDataType {
    tableData: any[];
    idKey?: string;
}

export const dealTableData = async (val: DealTableDataType) => {
    const {tableData, idKey = "id"} = val;
    const businessIds = toArr(tableData).map((el: any) => el[idKey]);
    if (businessIds.length === 0) {
        return tableData;
    }
    const {success, data} = await comApi.getBindWbsEbsById(businessIds);
    if (success === false) {
        return tableData;
    }
    const dataObj: {[key: string]: comApi.BindWbsEbsType} = {};
    toArr(data).forEach((el) => {
        dataObj[el.businessId] = el;
    });
    const newTableData = tableData.map((el: any) => ({
        ...el,
        wbsEbs: dataObj[el[idKey]]
    }));
    return newTableData;
};


interface DealTableDataByIdsType {
    tableData: any[];
    idKey?: string;
}
export const dealTableDataKeysArr = async (val: DealTableDataByIdsType) => {
    const {tableData, idKey = "id"} = val;
    const tableKeyIdArr = toArr(tableData).map((el: any) => el[idKey]);
    const businessIds = flatten(tableKeyIdArr);
    if (businessIds.length === 0) {
        return tableData;
    }
    const {success, data} = await comApi.getBindWbsEbsById(businessIds);
    if (success === false) {
        return tableData;
    }
    const dataObj: {[key: string]: comApi.BindWbsEbsType} = {};
    toArr(data).forEach((el) => {
        dataObj[el.businessId] = el;
    });
    const newTableData = tableData.map((el: any) => {
        const tempWbsEbs: comApi.BindWbsEbsType[] = [];
        toArr(el[idKey]).forEach((e: string) => {
            if (dataObj[e] !== undefined) {
                tempWbsEbs.push(dataObj[e]);
            }
        });
        return {
            ...el,
            wbsEbs: tempWbsEbs
        };
    });
    return newTableData;
};

export const renderWbsEbs = (val: comApi.BindWbsEbsType) => {
    if (Boolean(val) === false) {
        return "";
    }
    let text = "";
    let textList: string[] = [];
    if (val.bindType === -1) {
        text = toArr(val.wbsNodes)
            .map((el) => {
                textList.push(el.wbsNodeName);
                return el.wbsNodeName;
            })
            .join(",");
    }
    if (val.bindType === 1) {
        text = toArr(val.ebsNodes)
            .map((el) => {
                if (el.projName !== undefined) {
                    textList.push(el.projName);
                }
                return el.projName;
            })
            .join(",");
    }
    if (val.bindType === 2 || val.bindType === 3) {
        text = toArr(val.ebsNodes)
            .map((el) => {
                const fullPaths = [el.projName, ...el.paths ?? []];
                const fullPathStr = fullPaths.join(">");
                textList.push(fullPathStr);
                return fullPathStr;
            })
            .join(",");
    }
    textList = sortedUniq(textList);
    return (
        <Tooltip placement="right" trigger={["hover"]} title={textList.map((el) => <div key={el}>{el}</div>)}>
            <Typography.Text style={{width: "100%"}} ellipsis>
                {text}
            </Typography.Text>
        </Tooltip>
    );
};

export const renderWbsEbsArr = (val: comApi.BindWbsEbsType[]) => {
    if (toArr(val).length === 0) {
        return "";
    }
    const textList: string[] = [];
    const titleTextList: string[] = [];
    toArr(val).forEach((item) => {
        if (item.bindType === -1) {
            const tempText = item.wbsNodes
                .map((el) => {
                    titleTextList.push(el.wbsNodeName);
                    return el.wbsNodeName;
                })
                .join(",");
            textList.push(tempText);
        }
        if (item.bindType === 1) {
            const tempText = item.ebsNodes
                .map((el) => {
                    if (el.projName !== undefined) {
                        titleTextList.push(el.projName);
                    }
                    return el.projName;
                })
                .join(",");
            textList.push(tempText);
        }
        if (item.bindType === 2 || item.bindType === 3) {
            const tempText = item.ebsNodes
                .map((el) => {
                    const fullPaths = [el.projName, ...el.paths];
                    const fullPathStr = fullPaths.join(">");
                    titleTextList.push(fullPathStr);
                    return fullPathStr;
                })
                .join(",");
            textList.push(tempText);
        }
    });
    return (
        <Tooltip placement="right" trigger={["hover"]} title={titleTextList.map((el) => <div key={el}>{el}</div>)}>
            <Typography.Text style={{width: "100%"}} ellipsis>
                {textList}
            </Typography.Text>
        </Tooltip>
    );
};


export const handleBindWbsEbsAll = async (val: {}[]) => {
    const promiseList: Promise<any>[] = [];
    const tempVal = val as unknown as WbsEbsNodeType[];
    tempVal
        .filter((el) => el.bindType !== undefined)
        .forEach((el) => {
            const params = {
                ...el,
                wbsNodeIds: toArr(el.wbsNodes ?? []).map((e) => e.wbsNodeId),
            };
            promiseList.push(comApi.bindWbsEbs(params));
        });
    return Promise.all(promiseList);
};
