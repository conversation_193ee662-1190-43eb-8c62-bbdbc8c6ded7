/* eslint-disable @typescript-eslint/camelcase */
import React, {useCallback, useEffect, useMemo, useState} from "react";
import {Col, DatePicker, Form, InputNumber, Row} from "antd";
import moment, {Moment} from "moment";
import {FormInstance} from "antd/lib/form";
import {gantt} from "@iworks/dhtmlx-gantt";
import {DurationDateValue, GanttTask} from "../gantt/interface";
import ganttManager from "../gantt/ganttManager";
import {calculateTaskChangeDurationDate, getConstraintDurationValue} from "../gantt/calendarUtils";

interface DurationInputProps {
    defaultvalue: DurationDateValue;
    editType?: "plan" | "request";
    disabled?: boolean;
    form: FormInstance;
    editTask: GanttTask | undefined;
    autoInit?: boolean; // 初始化时是否自动进行填充
    taskType: string | undefined;
}

const prefixName = (originName: string, prefix: "plan" | "request") => {
    if (prefix !== undefined && prefix.length > 0) {
        return `${prefix}_${originName}`;
    }
    return originName;
};

const DurationInputItems: React.FC<DurationInputProps> = (props) => {
    const {defaultvalue, editType = "plan", disabled, form, editTask, taskType} = props;
    const [durationValue, setDurationValue] = useState<DurationDateValue>({});

    const prefixLabel = useCallback((originLabel) => {
        let editTypeLabel = "计划";
        switch (editType) {
            case "request":
                editTypeLabel = "要求";
                break;
            default:
                break;
        }
        return `${editTypeLabel}${originLabel}`;
    }, [editType]);


    const durationName = useMemo(() => prefixName("duration", editType), [editType]);
    const startDateName = useMemo(() => prefixName("start_date", editType), [editType]);
    const endDateName = useMemo(() => prefixName("end_date", editType), [editType]);

    useEffect(() => {
        // 只在第一次初始化时使用
        if (editTask !== undefined
            && defaultvalue !== undefined
            && defaultvalue.duration !== undefined
            && Number.isNaN(defaultvalue.duration) === false
            && defaultvalue.startDate !== undefined
            && defaultvalue.endDate !== undefined) {
            const duration = typeof defaultvalue.duration === "number"
                ? Math.ceil(defaultvalue.duration <= 0 ? 1 : defaultvalue.duration)
                : 1;
            const startDate = defaultvalue.startDate ?? gantt.date.day_start(new Date());
            const endDate = defaultvalue.endDate ?? gantt.getCalendar("custom").calculateEndDate({
                start_date: startDate,
                duration,
            });
            setDurationValue(getConstraintDurationValue({duration, startDate, endDate}));
        }
    }, [form, editTask, defaultvalue]);

    useEffect(() => {
        // 根据durationValue更新相关数据
        const fieldsValue: any = {}; // eslint-disable-line
        if (typeof durationValue.duration === "number") {
            fieldsValue[durationName] = Number.parseFloat(ganttManager.durationFormatter.format(`${Math.ceil(durationValue.duration)}`));
        }
        if (durationValue.startDate !== undefined) {
            fieldsValue[startDateName] = moment(durationValue.startDate);
        }
        if (durationValue.endDate !== undefined) {
            fieldsValue[endDateName] = moment(durationValue.endDate);
        }
        if (editType === "request" && taskType === gantt.config.types.task) {
            // 叶子结点任务的要求时间编辑后需要同步至计划时间
            if (
                fieldsValue[durationName] !== undefined
                && fieldsValue[startDateName] !== undefined
                && fieldsValue[endDateName] !== undefined
            ) {
                fieldsValue[prefixName("duration", "plan")] = fieldsValue[durationName];
                fieldsValue[prefixName("start_date", "plan")] = fieldsValue[startDateName];
                fieldsValue[prefixName("end_date", "plan")] = fieldsValue[endDateName];
            }
        }
        form.setFieldsValue(fieldsValue);
    }, [form, durationValue, durationName, startDateName, endDateName, editType, taskType]);

    /**
     * 修改工期
     */
    const handleDurationChange = useCallback((value: number | null) => {
        if (value === undefined || value === null) {
            return;
        }
        const duration = ganttManager.durationFormatter.parse(value?.toString());
        if (durationValue?.startDate !== undefined || durationValue?.endDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {duration});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, duration}));
        }
    }, [durationValue]);

    /**
     * 修改开始时间
     */
    const handleStartDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        if (durationValue?.duration !== undefined || durationValue?.endDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {startDate: date.toDate()});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, startDate: date.toDate()}));
        }
    }, [durationValue]);

    /**
     * 修改结束时间
     */
    const handleEndDateChange = useCallback((date: Moment | null) => {
        if (date === null) {
            return;
        }
        if (durationValue?.duration !== undefined || durationValue?.startDate !== undefined) {
            const newValue = calculateTaskChangeDurationDate(durationValue, {endDate: date.toDate()});
            setDurationValue(newValue);
        } else {
            setDurationValue((old) => ({...old, endDate: date.toDate()}));
        }
    }, [durationValue]);

    return (
        <Row>
            <Col span={12}>
                <Form.Item
                    label={prefixLabel("开始")}
                    name={startDateName}
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={disabled}
                        allowClear={editType !== "plan"}
                        onChange={handleStartDateChange}
                        placeholder={`请选择${prefixLabel("开始时间")}`}
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    label={prefixLabel("完成")}
                    name={endDateName}
                    labelCol={{span: 8}}
                    wrapperCol={{span: 16}}
                >
                    <DatePicker
                        style={{width: "100%"}}
                        disabled={disabled}
                        allowClear={editType !== "plan"}
                        onChange={handleEndDateChange}
                        placeholder={`请选择${prefixLabel("完成时间")}`}
                    />
                </Form.Item>
            </Col>
            <Col span={12}>
                <Form.Item
                    label={prefixLabel("工期")}
                    labelCol={{span: 8}}
                    wrapperCol={{span: 14}}
                    style={{position: "relative"}}
                >
                    <Form.Item
                        noStyle
                        name={durationName}
                    >
                        <InputNumber
                            style={{width: "100%"}}
                            onChange={handleDurationChange}
                            min={1}
                            precision={0}
                            disabled={disabled}
                            placeholder={`请输入${prefixLabel("工期")}`}
                        />
                    </Form.Item>
                    <span style={{position: "absolute", right: "-20px", lineHeight: "32px"}}>天</span>
                </Form.Item>
            </Col>
        </Row>
    );
};

export default DurationInputItems;
