/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable no-param-reassign */
import {gantt} from "@iworks/dhtmlx-gantt";
import moment from "moment";
import {GanttTask, GanttLink, GanttScale, DurationDateValue} from "./interface";
import {CalendarType, is24HourCalendar, isStandardCalendar} from "../common/calendar";
import {TaskStatus} from "../common/constant";
import ganttManager from "./ganttManager";
import {CalendarInfo} from "../api/plan/type";
import {updateGanttConfig} from "./ganttConfig";

export const getPlanStartDate = (date: Date): Date => {
    const {currentCalendarInfo} = ganttManager;
    const newDate = moment(date).startOf("day");
    if (is24HourCalendar(currentCalendarInfo)) {
        return newDate.toDate();
    }
    return newDate.add(8, "hours").toDate();
};

export const getPlanEndDate = (date: Date): Date => {
    const {currentCalendarInfo} = ganttManager;
    const newDate = moment(date).startOf("day");
    if (is24HourCalendar(currentCalendarInfo)) {
        return newDate.toDate();
    }
    // if (date.getHours() === 8) {
    //     // let newDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1, 17, 0, 0);
    //     // newDate = gantt.getCalendar("custom").getClosestWorkTime({date: newDate, dir: "future"});
    //     return newDate.subtract(1, "day").add(17, "hours").toDate();
    // }
    return newDate.add(17, "hours").toDate();
};

const updateTaskByWorkTime = (calendarInfo: CalendarInfo) => {
    // console.log("updateTaskByWorkTime", calendarInfo);
    let multiple = 1;
    const {currentCalendarInfo} = ganttManager;
    gantt.batchUpdate(() => {
        gantt.eachTask((task: GanttTask) => {
            if (task.start_date === undefined || task.end_date === undefined) {
                return;
            }
            if (isStandardCalendar(calendarInfo)) {
                if (is24HourCalendar(currentCalendarInfo)) {
                    multiple = 8;
                }
                // 标准日历
                task.start_date = gantt.getClosestWorkTime({
                    dir: "future",
                    date: gantt.date.day_start(task.start_date),
                    unit: gantt.config.duration_unit,
                    task
                });
                // duration_unit由day变成hour，需乘8
                task.duration *= multiple;
                if (task.actual_start !== undefined) {
                    task.actual_start = gantt.getClosestWorkTime({
                        dir: "future",
                        date: gantt.date.day_start(task.actual_start),
                        unit: gantt.config.duration_unit,
                        task
                    });
                    // duration_unit由day变成hour，需乘8
                    task.actual_duration = task.actual_duration as number * multiple;
                }
            } else {
                if (isStandardCalendar(currentCalendarInfo)) {
                    multiple = 8;
                }
                // 24小时日历
                task.start_date = gantt.date.date_part(gantt.getClosestWorkTime({
                    dir: "future",
                    date: gantt.date.day_start(task.start_date),
                    unit: gantt.config.duration_unit,
                    task
                }));
                // duration_unit由hour变成day，需除8
                task.duration /= multiple;
                if (task.actual_start !== undefined) {
                    task.actual_start = gantt.date.date_part(gantt.getClosestWorkTime({
                        dir: "future",
                        date: gantt.date.day_start(task.actual_start),
                        unit: gantt.config.duration_unit,
                        task
                    }));
                    // duration_unit由hour变成day，需除8，并向上取整
                    task.actual_duration = Math.ceil(task.actual_duration as number / multiple);
                }
            }
            task.end_date = gantt.calculateEndDate(task);
            if (task.actual_end !== undefined) {
                task.actual_end = gantt.calculateEndDate({
                    start_date: task.actual_start,
                    duration: task.actual_duration,
                    task
                });
            }
            if (task.taskStatus === TaskStatus.IN_PROGRESS) {
                // 进行中，计算预计时间
                task.actual_duration = -1;
                task.actual_end = undefined;
            }

            gantt.updateTask(task.id, task);
        });

        gantt.getLinks().forEach((link: GanttLink) => {
            if (isStandardCalendar(calendarInfo)) {
                link.lag *= multiple;
            } else {
                link.lag /= multiple;
            }
            gantt.updateLink(link.id);
        });
    });
    ganttManager.currentCalendarInfo = calendarInfo;
    updateGanttConfig();
};

// 设置工作日历，fix bug: 此处gantt.setWorkTime会导致Project导出失败，原因未知，暂时移除，只通过addCalendar设置日历
export const setWorkTime = (calendarInfo: CalendarInfo) => {
    gantt.deleteCalendar("custom");
    switch (calendarInfo.calendarType) {
        case CalendarType.TYPE_STANDARD: // 标准日历（周六周日休息）
            gantt.addCalendar({
                id: "custom",
                worktime: {
                    hours: ["8:00-12:00", "13:00-17:00"],
                    days: [0, 1, 1, 1, 1, 1, 0]
                }
            });
            break;
        case CalendarType.TYPE_CUSTOM_24_7: { // 自定义日历（复制24小时）
            gantt.addCalendar({
                id: "custom",
                worktime: {
                    days: [1, 1, 1, 1, 1, 1, 1]
                }
            });
            const calendar = gantt.getCalendar("custom");
            if (calendarInfo.restDays !== null) {
                calendarInfo.restDays.forEach((day) => {
                    calendar.setWorkTime({date: new Date(day), hours: false});
                });
            }
        }
            break;
        case CalendarType.TYPE_CUSTOM_STANDARD: { // 自定义日历（复制标准）
            gantt.addCalendar({
                id: "custom",
                worktime: {
                    hours: ["8:00-12:00", "13:00-17:00"],
                    days: [0, 1, 1, 1, 1, 1, 0]
                }
            });
            const calendar = gantt.getCalendar("custom");
            if (calendarInfo.restDays !== null) {
                calendarInfo.restDays.forEach((day) => {
                    calendar.setWorkTime({date: new Date(day), hours: false});
                });
            }
            if (calendarInfo.workDays !== null) {
                calendarInfo.workDays.forEach((day) => {
                    calendar.setWorkTime({
                        hours: ["8:00-12:00", "13:00-17:00"],
                        date: new Date(day)
                    });
                });
            }
        }
            break;
        default: // 24小时日历（默认）
            gantt.addCalendar({
                id: "custom",
                worktime: {
                    days: [1, 1, 1, 1, 1, 1, 1]
                }
            });
            break;
    }
    updateTaskByWorkTime(calendarInfo);
};

export const setTimeScale = (calendar: CalendarInfo) => {
    switch (calendar.calendarType) {
        case CalendarType.TYPE_STANDARD:
        case CalendarType.TYPE_CUSTOM_STANDARD: {
            const durationFormatter = gantt.ext.formatters.durationFormatter({
                enter: "day",
                store: "hour",
                format: "day",
                hoursPerDay: 8,
                short: true
            });
            const linksFormatter = gantt.ext.formatters.linkFormatter({durationFormatter});
            ganttManager.durationFormatter = durationFormatter;
            ganttManager.linksFormatter = linksFormatter;
            gantt.config.duration_unit = "hour";
            // gantt.config.scales = [
            //     {unit: "day", step: 1, format: "%Y年%n月%j日 周%D"},
            //     {unit: "hour", step: 1, format: "%G"}
            // ];
            gantt.config.scales = [
                {unit: "month", step: 1, format: "%Y年%n月"},
                {unit: "day", step: 1, format: "%j%D"}
            ];
            break;
        }
        default: {
            const durationFormatter = gantt.ext.formatters.durationFormatter({
                enter: "day",
                store: "day",
                format: "day",
                hoursPerDay: 24,
                short: true
            });
            const linksFormatter = gantt.ext.formatters.linkFormatter({durationFormatter});
            ganttManager.durationFormatter = durationFormatter;
            ganttManager.linksFormatter = linksFormatter;
            gantt.config.duration_unit = "day";
            gantt.config.scales = [
                {unit: "month", step: 1, format: "%Y年%n月"},
                {unit: "day", step: 1, format: "%j%D"}
            ];
            break;
        }
    }
};

export const setGanttCalender = (calendar?: CalendarInfo) => {
    // console.log("setGanttCalender", calendar);
    if (calendar !== undefined) {
        // 按不同工作日历设置不同时间刻度
        setTimeScale(calendar);
        // 设置工作日历
        setWorkTime(calendar);
    }
};

// 时间刻度相关方法
// 小时
export const scaleHour = () => {
    gantt.ext.zoom.setLevel("hour");
};

// 季度天数
export const scaleQuarterDay = () => {
    // gantt.ext.zoom.setLevel("quarterDay");
};

// 天
export const scaleDay = () => {
    gantt.ext.zoom.setLevel("day");
};

// 周
export const scaleWeek = () => {
    gantt.ext.zoom.setLevel("week");
};

// 旬
export const scaleTenDay = () => {
    // gantt.ext.zoom.setLevel("tenDay");
};

// 月
export const scaleMonth = () => {
    gantt.ext.zoom.setLevel("month");
};

// 季度
export const scaleQuarter = () => {
    // gantt.ext.zoom.setLevel("quarter");
};

// 半年
export const scaleHalfYear = () => {
    // gantt.ext.zoom.setLevel("halfYear");
};

// 年
export const scaleYear = () => {
    gantt.ext.zoom.setLevel("year");
};

export const isScaleWorkTime = (task: GanttTask, date: Date) => {
    const calendar = gantt.getTaskCalendar(task);
    // 获取当前时间刻度
    const scale: GanttScale = gantt.getScale();
    // 获取当前时间最近的一个工作日
    const closestDate: Date = calendar.getClosestWorkTime({
        date,
        dir: "future",
        unit: "hour"
    });
    // 通过比较closestDate和下一个时间刻度起始时间来确定是否是工作日
    return closestDate.getTime() < gantt.date.add(date, 1, scale.unit).getTime();
};

/**
 * 根据日期获取开始时间，不考虑工作日
 */
export const getTaskStartDate = (date: Date) => gantt.date.add(
    gantt.date.day_start(date),
    isStandardCalendar(ganttManager.currentCalendarInfo) ? 8 : 0,
    "hour"
);

/**
 * 根据日期获取开始时间，考虑工作日
 */
export const getTaskWorkStartDate = (date: Date) => {
    let startDate = getTaskStartDate(date);
    startDate = gantt.getCalendar("custom").getClosestWorkTime({date: startDate});
    return startDate;
};

/**
 * 根据日期获取完成时间，不考虑工作日
 */
export const getTaskEndDate = (date: Date) => gantt.date.add(
    gantt.date.day_start(date),
    isStandardCalendar(ganttManager.currentCalendarInfo) ? 17 : 0,
    "hour"
);

/**
 * 根据日期获取完成时间，考虑工作日
 */
export const getTaskWorkEndDate = (date: Date) => {
    let endDate = getTaskEndDate(date);
    endDate = gantt.getCalendar("custom").getClosestWorkTime({date: endDate});
    return endDate;
};

/**
 * 计算工期
 */
export const calculateDuration = ({
    start_date,
    end_date
}: {
    start_date: Date;
    end_date: Date;
}) => {
    const startDate = getTaskWorkStartDate(start_date);
    const endDate = getTaskWorkEndDate(end_date);
    const duration = gantt.getCalendar("custom").calculateDuration({start_date: startDate, end_date: endDate});
    return {startDate, endDate, duration};
};

/**
 * 计算开始时间
 */
export const calculateStartDate = ({
    end_date,
    duration
}: {
    end_date: Date;
    duration: number;
}) => {
    const endDate = getTaskWorkEndDate(end_date);
    const durationNum = Number.parseFloat(ganttManager.durationFormatter.format(duration));
    // 将结束时间传参给开始时间，工期为负，返回结束时间就是开始时间
    const startDate = gantt.getCalendar("custom").calculateEndDate({
        start_date: endDate,
        duration: ganttManager.durationFormatter.parse(`${-durationNum}`),
    });
    return {startDate, endDate};
};

/**
 * 计算完成时间
 */
export const calculateEndDate = ({
    start_date,
    duration
}: {
    start_date: Date;
    duration: number;
}) => {
    const startDate = getTaskWorkStartDate(start_date);
    const endDate = gantt.getCalendar("custom").calculateEndDate({start_date: startDate, duration});
    return {startDate, endDate};
};

// 根据日历计算约束时间 结束时间不能小于开始时间
export const getConstraintDurationValue = (value: DurationDateValue): DurationDateValue => {
    // console.log("getConstraintDurationValue", value);
    let {duration} = value;
    const {startDate} = value;
    if (duration === undefined || startDate === undefined) {
        return value;
    }
    let endDate = gantt.getCalendar("custom").calculateEndDate({
        start_date: startDate,
        duration,
    });
    if (startDate !== undefined && endDate < startDate) {
        duration = ganttManager.durationFormatter.parse("1");
        endDate = gantt.getCalendar("custom").calculateEndDate({
            start_date: startDate,
            duration,
        });
    }
    return {duration, startDate, endDate};
};

/**
 * 根据工期，计算开始或完成时间
 */
export const calculateChangeWithDuration = (oldValue: DurationDateValue, changeValue: DurationDateValue): DurationDateValue => {
    // console.log("calculateChangeWithDuration", oldValue, changeValue);
    if (changeValue.duration !== undefined) {
        // 修改了工期，优先检查有没有开始时间
        if (oldValue.startDate !== undefined) {
            // return 工期 + 开始时间 => 完成时间
            const res = calculateEndDate({
                duration: changeValue.duration,
                start_date: oldValue.startDate,
            });
            return {...oldValue, ...changeValue, ...res};
        }
        if (oldValue.endDate !== undefined) {
            // return 工期 + 完成时间 => 开始时间
            const res = calculateStartDate({
                duration: changeValue.duration,
                end_date: oldValue.endDate,
            });
            return {...oldValue, ...changeValue, ...res};
        }
    }
    return {...oldValue, ...changeValue};
};

/**
 * 根据开始时间，计算工期或完成时间
 */
export const calculateChangeWithStartDate = (oldValue: DurationDateValue, changeValue: DurationDateValue): DurationDateValue => {
    // console.log("calculateChangeWithStartDate", oldValue, changeValue);
    if (changeValue.startDate !== undefined) {
        // 修改了开始时间，优先检查有没有工期
        if (oldValue.duration !== undefined) {
            // return 开始时间 + 工期 => 完成时间
            const res = calculateEndDate({
                start_date: changeValue.startDate,
                duration: ganttManager.durationFormatter.parse(`${oldValue.duration}`),
            });
            return {...oldValue, ...changeValue, ...res};
        }
        if (oldValue.endDate !== undefined) {
            // return 开始时间 + 完成时间 => 工期
            const res = calculateDuration({
                start_date: changeValue.startDate,
                end_date: oldValue.endDate,
            });
            return {...oldValue, ...changeValue, ...res};
        }
    }
    return {...oldValue, ...changeValue};
};

/**
 * 根据完成时间，计算工期或开始时间
 */
export const calculateChangeWithEndDate = (oldValue: DurationDateValue, changeValue: DurationDateValue): DurationDateValue => {
    // console.log("calculateChangeWithEndDate", oldValue, changeValue);
    if (changeValue.endDate !== undefined) {
        // 修改了完成时间，优先检查有没有开始时间
        if (oldValue.startDate !== undefined) {
            // return 完成时间 + 开始时间 => 工期
            const res = calculateDuration({
                end_date: changeValue.endDate,
                start_date: oldValue.startDate,
            });
            return {...oldValue, ...changeValue, ...res};
        }
        if (oldValue.duration !== undefined) {
            // return 完成时间 + 工期 => 开始时间
            const res = calculateStartDate({
                end_date: changeValue.endDate,
                duration: ganttManager.durationFormatter.parse(`${oldValue.duration}`),
            });
            return {...oldValue, ...changeValue, ...res};
        }
    }
    return {...oldValue, ...changeValue};
};

/**
 * 根据传入修改计算任务的工期或时间
 */
export const calculateTaskChangeDurationDate = (oldValue: DurationDateValue, changeValue: DurationDateValue): DurationDateValue => {
    // console.log("calculateTaskChangeDurationDate", oldValue, changeValue);
    let changeAvailableValueCount = 0;
    if (changeValue.duration !== undefined) {
        changeAvailableValueCount++;
    }
    if (changeValue.startDate !== undefined) {
        changeAvailableValueCount++;
    }
    if (changeValue.endDate !== undefined) {
        changeAvailableValueCount++;
    }
    if (changeAvailableValueCount !== 1) {
        throw new Error("calculateTaskChangeDurationDate changeValue只能传一个修改属性");
    }
    let resultValue = {...oldValue, ...changeValue};
    if (changeValue.duration !== undefined) {
        resultValue = calculateChangeWithDuration(oldValue, changeValue);
    }
    if (changeValue.startDate !== undefined) {
        resultValue = calculateChangeWithStartDate(oldValue, changeValue);
    }
    if (changeValue.endDate !== undefined) {
        resultValue = calculateChangeWithEndDate(oldValue, changeValue);
    }
    resultValue = getConstraintDurationValue(resultValue);
    // console.log("calculateTaskChangeDurationDate resultValue", resultValue);
    return resultValue;
};

/**
 * 根据传入时间重新计算
 */
export const reCalculateDurationTime = (oldValue: DurationDateValue): DurationDateValue => {
    // 根据任意两个值计算第三个值
    // 只要有工期和开始时间，就重新计算完成时间
    if (oldValue.duration !== undefined && oldValue.startDate !== undefined) {
        const res = calculateEndDate({
            start_date: oldValue.startDate,
            duration: ganttManager.durationFormatter.parse(`${oldValue.duration}`),
        });
        return {...oldValue, ...res};
    }
    // 如果只有开始，完成时间，没有工期，则计算工期
    if (oldValue.startDate !== undefined && oldValue.endDate !== undefined && oldValue.duration === undefined) {
        const res = calculateDuration({
            end_date: oldValue.endDate,
            start_date: oldValue.startDate,
        });
        return {...oldValue, ...res};
    }
    // 如果只有工期，完成时间，没有开始时间，则计算开始时间
    if (oldValue.duration !== undefined && oldValue.endDate !== undefined && oldValue.startDate === undefined) {
        const res = calculateStartDate({
            end_date: oldValue.endDate,
            duration: ganttManager.durationFormatter.parse(`${oldValue.duration}`),
        });
        return {...oldValue, ...res};
    }
    return {};
};
