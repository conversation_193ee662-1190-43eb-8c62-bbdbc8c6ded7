import React, {FC} from "react";
import {createUseStyles} from "react-jss";

interface ComTitleProps {
    text?: string;
}
const useStyle = createUseStyles({
    title: {
        background: "#3777F2",
        lineHeight: "48px",
        textAlign: "center",
        color: "#fff",
        fontSize: "15px",
        fontWeight: 700,
        letterSpacing: 1
    }
});
const ComTitle: FC<ComTitleProps> = (props) => {
    const {text} = props;
    const cls = useStyle();
    return (
        <div className={cls.title}>{text}</div>
    );
};

export default ComTitle;
