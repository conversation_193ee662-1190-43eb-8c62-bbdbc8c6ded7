import React, {useCallback, useMemo, useState} from "react";
import {useSelector} from "react-redux";
import {Button, Drawer, notification, Space, Tooltip} from "antd";
import Icon from "@ant-design/icons";
import BtnIcon from "../../../../components/MyIcon/BtnIcon";
import useStyle from "../style";
import BIMToolBar from "../common/BIMToolBar";
import ProcessManage from "./processManage";
import ProcessTmplManage from "./processTmplManage";
import CompAttrManage from "./CompAttrManage";
import ProjectSettings from "./projectSettings";
import {RootState} from "../../../../store/rootReducer";
import {isDefined} from "../../../../assets/ts/utils";
import {ProcessManagementIcon, ProjSettingsIcon} from "../../../../assets/icons";
import PermissionCode from "../../../../components/PermissionCode";

// type BtnActionType = "projSetting" | "pcoessTmplManage" | "processManage";

const BimBtnControl = () => {
    const cls = useStyle();
    const {curProjSettingInfo} = useSelector((state: RootState) => state.statusData);
    const [processTmplManageVisible, setProcessTmplManageVisible] = useState<boolean>(false);
    const [processManageVisible, setProcessManageVisible] = useState<boolean>(false);
    const [showBIMToolbar, setShowBIMToolbar] = useState<boolean>(false);
    const [projectSettingsVisible, setProjectSettingsVisible] = useState<boolean>(false);

    const handleOpenProcessTmplManageDrawer = useCallback(
        () => {
            setProcessTmplManageVisible(true);
            setShowBIMToolbar(false);
        },
        []
    );

    const handleCloseProcessTmplManageDrawer = useCallback(
        () => {
            setProcessTmplManageVisible(false);
        },
        []
    );

    const handleOpenProcessManageDrawer = useCallback(
        () => {
            setProcessManageVisible(true);
        },
        []
    );


    const handleCloseProcessManageDrawer = useCallback(
        () => {
            setProcessManageVisible(false);
        },
        []
    );

    const handleOpenToolbar = useCallback(
        () => {
            if (!isDefined(curProjSettingInfo)) {
                notification.error({
                    message: "操作失败！",
                    description: "当前工程还没有设置开工和竣工日期，请先设置再进行定义工序操作"
                });
                return;
            }
            setShowBIMToolbar(true);
        },
        [curProjSettingInfo]
    );

    const handleProjectSettingsOk = useCallback(
        () => {
            setProjectSettingsVisible(false);
        },
        []
    );

    const handleOpenProjSettings = useCallback(
        () => {
            setProjectSettingsVisible(true);
            setShowBIMToolbar(false);
        },
        []
    );

    const CompAttrView = useMemo(() => <CompAttrManage isInDefineProcessStatus={showBIMToolbar} />, [showBIMToolbar]);

    const ProjectSettingsModel = useMemo(() => (
        <ProjectSettings
            setVisible={setProjectSettingsVisible}
            visible={projectSettingsVisible}
            onOk={handleProjectSettingsOk}
        />
    ), [handleProjectSettingsOk, projectSettingsVisible]);

    const ProcessTmplManageView = useMemo(() => (
        <ProcessTmplManage
            visible={processTmplManageVisible}
            onClose={handleCloseProcessTmplManageDrawer}
        />
    ), [processTmplManageVisible, handleCloseProcessTmplManageDrawer]);

    const ProcessManageView = useMemo(() => (
        <Drawer
            title="定义工序"
            visible={processManageVisible}
            onClose={handleCloseProcessManageDrawer}
            width={720}
            destroyOnClose
        >
            <ProcessManage />
        </Drawer>
    ), [handleCloseProcessManageDrawer, processManageVisible]);

    const BIMToolBarView = useMemo(
        () => {
            if (showBIMToolbar) {
                return (
                    <BIMToolBar
                        onVisibleChange={setShowBIMToolbar}
                        onOpenProcessManage={handleOpenProcessManageDrawer}
                    />
                );
            }
            return null;
        },
        [showBIMToolbar, handleOpenProcessManageDrawer]
    );

    return (
        <>
            <Space className={cls.btnSpace} direction="vertical" size={16}>
                <PermissionCode authcode="ProjectPlatform-Plan-SandTable-Self-Defining:ProjectSettings">
                    <Tooltip title="工程设置">
                        <Button
                            type={projectSettingsVisible ? "primary" : "default"}
                            icon={<Icon component={ProjSettingsIcon} />}
                            onClick={handleOpenProjSettings}
                        />
                    </Tooltip>
                </PermissionCode>
                <PermissionCode authcode="ProjectPlatform-Plan-SandTable-Self-Defining:ProcessSettings">
                    <Tooltip title="工序管理">
                        <Button
                            type={processTmplManageVisible ? "primary" : "default"}
                            icon={<Icon component={ProcessManagementIcon} />}
                            onClick={handleOpenProcessTmplManageDrawer}
                        />
                    </Tooltip>
                </PermissionCode>
                <PermissionCode authcode="ProjectPlatform-Plan-SandTable-Self-Defining:DefineProcess">
                    <Tooltip title="定义工序">
                        <Button
                            type={showBIMToolbar ? "primary" : "default"}
                            icon={<BtnIcon type="icon-gongxu" />}
                            onClick={handleOpenToolbar}
                            className={cls.focusBtn}
                        />
                    </Tooltip>
                </PermissionCode>
            </Space>
            {BIMToolBarView}
            {ProjectSettingsModel}
            {CompAttrView}
            {ProcessTmplManageView}
            {ProcessManageView}
        </>
    );
};

export default BimBtnControl;
