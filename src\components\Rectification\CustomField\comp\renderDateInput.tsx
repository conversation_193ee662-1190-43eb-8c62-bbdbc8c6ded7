import React from "react";
import {DatePicker} from "antd";
import moment, {Moment} from "moment";
import {CustomFieldProps} from "../../models/custom-field";

interface RenderDateInputProps extends CustomFieldProps {
    handleDateChange: (date: Moment | null) => void;
}

const RenderDateInput = (props: RenderDateInputProps) => {
    const {data, style, hint, onBlur, disabled, handleDateChange} = props;

    return (
        <>
            {
                ["string", "undefined", "number"].includes(typeof data.defaultValue)
                    ? (
                        <DatePicker
                            style={style}
                            placeholder={hint}
                            onChange={(e) => handleDateChange(e)}
                            defaultValue={data.defaultValue !== undefined ? moment(data.defaultValue) : undefined}
                            onOpenChange={(e) => e && onBlur !== undefined && onBlur()}
                            disabled={disabled}
                            format="YYYY.MM.DD"
                        />
                    )
                    : null
            }
        </>
    );
};
export default RenderDateInput;
