import {createUseStyles} from "react-jss";
import Color from "../../../../assets/css/Color";

const useStyles = createUseStyles({
    buttonList: {
        display: "flex",
        alignItems: "center",
        // gap: 16,
        gap: 8,
    },
    button: {
        flexShrink: "0",
        height: "40px",
        fontSize: "14px",
        "&.ant-btn-default:not([disabled])": {
            color: Color["primary-1"],
            borderColor: Color["primary-1"]
        }
    },
    textButton: {
        display: "flex",
        flexShrink: "0",
        alignItems: "center",
        height: "40px",
        padding: "0 4px",
        fontSize: "14px",
    },
    dropDown: {
        fontSize: "14px"
    },
    modal: {
        position: "relative",
        left: "800px !important",
        top: 185,
        "&.ant-modal-confirm .ant-modal-body": {
            padding: "16px !important",
            height: "126px !important",
            textAlign: "left !important",
        },
        "&.ant-modal-confirm .ant-modal-confirm-btns": {
            width: "192px !important",
            // marginTop: "10px !important",
            position: "absolute",
            bottom: 0,
            paddingLeft: "12px !important",
            display: "flex",
            justifyContent: "space-between",
            "& button": {
                background: "none !important",
                border: "none !important",
                color: "#1F54C5 !important",
                boxShadow: "none",
            }
        }
    },
    infoModal: {
        // top: 435,
        top: 375,
        "&.ant-modal-confirm .ant-modal-body": {
            padding: "0px !important",
            height: "180px !important",
            textAlign: "left !important",
        },
        "&.ant-modal-confirm .ant-modal-confirm-btns": {
            width: "480px !important",
            // marginTop: "10px !important",
            // paddingLeft: "12px !important",
            // display: "flex",
            // justifyContent: "space-between",
            "& button": {
                // background: "none !important",
                width: "50%",
                height: 48,
                marginTop: 26,
                color: "#FFFFFF !important",
                boxShadow: "none",
                border: "none",
            },
            "& button.ant-btn-default": {
                background: "#33394D !important",
                width: "239px !important",
                display: "inline-block !important",
                borderRadius: "none !important",
            },
            "& button.ant-btn-primary": {
                float: "right",
                width: "240px !important",
                marginTop: -48,
                borderRadius: "none !important",
            }
        }
    },
});

export default useStyles;
