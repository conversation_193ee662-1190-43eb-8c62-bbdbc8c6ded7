import React, {useState, useEffect} from "react";
import {useControllableValue} from "ahooks";
import {TreeProps} from "antd";
import ComTree, {ComTreeProps} from "../../../../../uikit/Components/ComTree";
import {ValueType} from "./data";
import * as PdsApi from "../../../../api/pds";
import {toArr} from "../../../../assets/ts/utils";
import {TreeNodeType} from "../../../../../uikit/ts/type";


interface PersonTreeRoleProps extends ComTreeProps {
    originPersonData?: PdsApi.UserInfoTypeOfRoleByOrg[];
    value?: ValueType[];
    onChange?: (val: ValueType[]) => void;
    onSelectPerson?: (val: ValueType) => void;
    disabledKeys?: string[];
}

interface OriginType extends PdsApi.UserInfoType {
    role: string;
}

type TreeDataType = TreeNodeType<OriginType>;

const PersonTreeRole = (props: PersonTreeRoleProps) => {
    const {disabledKeys, searchKey, originPersonData = [], ...otherProps} = props;
    const [state, setState] = useControllableValue<ValueType[]>(props);
    const [treeData, setTreeData] = useState<TreeDataType[]>([]);

    useEffect(() => {
        const tempTree = toArr(originPersonData).map((el) => ({
            title: el.role,
            key: `role-${el.role}`,
            originData: {role: el.role ?? ""},
            children: toArr(el.users ?? []).map((e) => ({
                title: e.realName !== undefined ? `${e.realName}(${e.userName})` : e.userName,
                key: e.userName ?? "",
                disabled: toArr(disabledKeys ?? []).includes(e.userName as string),
                originData: {...e}
            }))
        }));
        setTreeData(tempTree);
    }, [disabledKeys, originPersonData]);

    const handleCheck: TreeProps["onCheck"] = (_checked, checkInfo) => {
        const checkedNodes = checkInfo.checkedNodes as unknown as TreeDataType[];
        const userNames = checkedNodes
            .filter((el) => el.originData.userName !== undefined)
            .map((el) => ({
                deptId: el.originData.deptIds,
                userName: el.originData.userName ?? "",
                realName: el.originData.realName ?? "",
            }));
        const unSearchPersons = state.filter((el) => {
            const title = el.realName !== undefined ? `${el.realName}(${el.userName})` : el.userName;
            if (searchKey !== undefined && title.includes(searchKey) === false) {
                return true;
            }
            return false;
        });
        // 在旧数据基础上，移除被删除的数据
        const newState = state.filter((item) => {
            if (unSearchPersons.some((p) => p.userName === item.userName) || userNames.some((p) => p.userName === item.userName)) {
                return true;
            }
            return false;
        });
        // 获取新增数据
        const addPersons = userNames.filter((item) => {
            if (state.some((p) => p.userName === item.userName)) {
                return false;
            }
            return true;
        });
        // 通过旧的人员选择数据组合得出新数据
        setState([...newState, ...addPersons]);
    };

    return (
        <ComTree
            {...otherProps}
            checkable
            checkedKeys={toArr(state).map((el) => el.userName)}
            onCheck={handleCheck}
            treeData={treeData}
            searchKey={searchKey}
        />
    );
};

export default PersonTreeRole;
