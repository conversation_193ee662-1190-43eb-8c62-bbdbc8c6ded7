import React from "react";
import {createUseStyles} from "react-jss";
import clsx from "clsx";
import {Tag, TagProps} from "antd";


const useStyle = createUseStyles({
    tag: {
        "&.ant-tag": {
            height: 24,
            padding: "0 4px",
            borderRadius: 0,
        }
    },
});

const ComTag = (props: TagProps) => {
    const {...other} = props;
    const cls = useStyle();

    return (
        <Tag
            {...other}
            className={clsx(other.className, cls.tag)}
        />
    );
};

export default ComTag;
