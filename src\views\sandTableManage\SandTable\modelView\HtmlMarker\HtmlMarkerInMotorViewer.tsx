import React, {useCallback, useRef} from "react";

import Motor from "@motor/core";
import {isEmpty} from "lodash-es";
import HtmlMarkerInMotorViewerMgr, {MarkerTipWithIdRender} from "./HtmlMarkerInMotorViewerMgr";
import {PickInfo, useMouseEventListener} from "../../../../../reMotor";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";

interface MarkerTipWithIdPositionRender {
    render: MarkerTipWithIdRender;
    curItemId: string;
    windowPosX: number;
    windowPosY: number;
}

export default function HtmlMarkerInMotorViewer(): JSX.Element {
    const [markerTipWithIdRender, setMarkerTipWithIdRender] = React.useState<MarkerTipWithIdPositionRender | null>(null);
    const showId = useRef<string>("");

    const onMouseMove = useCallback((pickInfo: PickInfo) => {
        const bResult = false;
        const tipRenderList = HtmlMarkerInMotorViewerMgr.getMarkerTipRenderList();
        if (tipRenderList.length === 0) {
            setMarkerTipWithIdRender(null);
            showId.current = "";
            return bResult;
        }

        if (isNotNullOrUndefined(pickInfo.pickObj) && isNotNullOrUndefined(pickInfo.pickObj.model)
        && pickInfo.pickObj?.model.type === Motor.ModelType.CZML) {
            const id = pickInfo.pickObj?.model.id;

            if (showId.current === id) {
                return bResult;
            }
            showId.current = id;
            const currentWindowPosition = pickInfo.windowPosition;
            if (isNotNullOrUndefined(currentWindowPosition)) {
                for (let i = 0; i < tipRenderList.length; ++i) {
                    const elem = tipRenderList[i];
                    const key = isNotNullOrUndefined(elem.markerHandler) ? elem.markerHandler.getKeyByModelId(id) : id;
                    const find = elem.idList.find((elemId) => elemId === key);
                    if (isNotNullOrUndefined(find)) {
                        const newItem: MarkerTipWithIdPositionRender = {
                            render: elem,
                            curItemId: key,
                            windowPosX: currentWindowPosition.x,
                            windowPosY: currentWindowPosition.y,
                        };
                        setMarkerTipWithIdRender(newItem);
                        break;
                    }
                }
            }
        } else {
            setMarkerTipWithIdRender(null);
            showId.current = "";
        }

        return bResult;
    }, []);

    useMouseEventListener(onMouseMove, "mouseMove");

    const onClearMarkerView = React.useCallback(() => {
        setMarkerTipWithIdRender(null);
    }, []);

    React.useEffect(() => {
        HtmlMarkerInMotorViewerMgr.addClearMarkerViewListenerList(onClearMarkerView);

        return () => {
            HtmlMarkerInMotorViewerMgr.removeClearMarkerViewListenerList(onClearMarkerView);
        };
    }, [onClearMarkerView]);


    const MarkerTipRender = (props: { render: MarkerTipWithIdRender; id: string; windowPosX: number; windowPosY: number }) => {
        const {render, id, windowPosX, windowPosY} = props;
        const RenderInstance = render.renderInstance;
        return <><RenderInstance id={id} windowPosX={windowPosX} windowPosY={windowPosY} /></>;
    };


    const renderMarkerTipList = React.useMemo(() => (
        <>
            {markerTipWithIdRender !== null && !isEmpty(markerTipWithIdRender.curItemId)
                ? (
                    <MarkerTipRender
                        render={markerTipWithIdRender.render}
                        id={markerTipWithIdRender.curItemId}
                        windowPosX={markerTipWithIdRender.windowPosX}
                        windowPosY={markerTipWithIdRender.windowPosY}
                    />
                )
                : null}
        </>
    ), [markerTipWithIdRender]);


    return (
        <div id="htmlMarkerInMotorViewer">
            {renderMarkerTipList}
        </div>
    );
}
