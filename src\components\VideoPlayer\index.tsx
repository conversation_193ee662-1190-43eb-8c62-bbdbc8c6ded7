import React, {useEffect, useRef, memo, useState, CSSProperties} from "react";
import videojs, {VideoJsPlayer, VideoJsPlayerOptions} from "video.js";
import "video.js/dist/video-js.min.css";
import ZHCN from "video.js/dist/lang/zh-CN.json";

videojs.addLanguage("zh-CN", ZHCN);

interface VideoPlayerProps {
    videoConfig?: VideoJsPlayerOptions;
    src: string;
    type?: string;
    autoplay?: boolean;
    controls?: boolean;
    style?: CSSProperties;
    className?: string;
}

const VideoPlayer = (props: VideoPlayerProps) => {
    const {
        videoConfig,
        type = "video/mp4",
        autoplay = false,
        controls = true,
        src,
        style,
        className
    } = props;
    const videoRef = useRef<HTMLVideoElement>(null);
    const [player, setPlayer] = useState<VideoJsPlayer>();

    useEffect(() => {
        if (videoRef.current !== null && src !== undefined) {
            setPlayer(videojs(videoRef.current, {
                autoplay,
                controls,
                sources: [{src, type}],
                language: "zh-CN",
                ...videoConfig
            }));
        }
        return () => {
            if (player !== undefined) {
                player.dispose();
            }
        };
    }, [autoplay, controls, player, src, type, videoConfig]);

    return (
        <>
            <div className={className} data-vjs-player style={{height: 500, ...style}}>
                <video ref={videoRef} className="video-js" />
            </div>
        </>
    );
};

export default memo(VideoPlayer);
