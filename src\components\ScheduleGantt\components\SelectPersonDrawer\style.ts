import {createUseStyles} from "react-jss";
import Color from "../../../../assets/css/Color";

const useStyles = createUseStyles({
    root: {
        display: "flex",
        width: "100%",
        height: "100%",
    },
    titleIcon: {
        display: "inline-block",
        width: 3,
        height: 16,
        marginRight: 8,
        background: Color.primary,
    },
    title: {
        display: "flex",
        alignItems: "center",
        fontSize: 14,
        fontWeight: 700,
    },
    subTitle: {
        fontSize: 12,
        color: Color["text-3"],
    },
    selectBoxHeader: {
        padding: "8px 24px",
        borderBottom: "1px solid #E1E2E5"
    },
});

export default useStyles;
