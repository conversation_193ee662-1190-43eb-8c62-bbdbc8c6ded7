## 进度系统


#### 注意点：
1. 多系统选择页配置时要加上 域名路径/#/login； /#/login需要有的，为了统一，都在login页面处理
2. IworksFrame 组件 https://www.yuque.com/docs/share/010d44c0-5533-462b-a2ac-0bb7094f2490?#

#### submodule
代码复用,仓库地址,需要有权限 http://git.luban.fit/builder/common/uikit
使用分支0.0.1

获取submodule
```
git submodule init
git submodule update
```

编译打包
```
yarn release
```
编译如果内存泄漏就先 执行
```
yarn fix-memory-limit
```

生成压缩包
```
yarn zip:beta; 测试服
yarn zip:saas-beta; 内网saas
yarn zip:prod; 正式服
yarn zip:xxx; 定制项目，需自己添加配置
```

上传内网测试
```
yarn upload 执行上传
yarn zip:upload 先生成压缩包，再执行上传
```

编译打包上传内网
```
yarn release && yarn zip:beta && yarn upload
```