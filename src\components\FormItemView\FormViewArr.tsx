import {toArr} from "../../assets/ts/utils";
import {renderText} from "../../../uikit/ts/TableRender";

interface ValueType {
    [key: string]: unknown;
}

export interface FormViewArrProps {
    value?: ValueType[];
    onChange?: (val: ValueType[]) => void;
    nameKey?: string;
}

const FormViewArr = (props: FormViewArrProps) => {
    const {nameKey = "name", value} = props;

    const text = toArr(value)?.map((el) => el[nameKey]).join(",");

    return renderText(text ?? "");
};

export default FormViewArr;
