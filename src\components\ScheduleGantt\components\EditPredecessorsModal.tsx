import React, {useEffect} from "react";
import {Form, Modal} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import {GanttTask} from "../gantt/interface";
import {linksWithPredecessors, linksWithTargetId, taskPredecessors} from "../gantt/taskUtils";
import PredecessorsInput from "./PredecessorsInput";

export interface EditPredecessorsModalProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    editTask: GanttTask | undefined;
}

const EditPredecessorsModal = (props: EditPredecessorsModalProps) => {
    const {visible, setVisible, editTask} = props;
    const [form] = Form.useForm();


    useEffect(() => {
        if (visible && editTask !== undefined) {
            form.resetFields();
            const initValues = {
                predecessors: taskPredecessors(editTask),
            };
            form.setFieldsValue(initValues);
        }
    }, [visible, editTask, form]);

    const handleSave = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
        e.preventDefault();
        form.validateFields()
            .then((values) => {
                if (editTask === undefined) {
                    return;
                }
                const newTask: GanttTask = {
                    ...editTask,
                    predecessors: values.predecessors,
                };
                linksWithTargetId(newTask.id).forEach((item) => {
                    gantt.deleteLink(item.id);
                });
                if (newTask.predecessors !== undefined && newTask.predecessors !== "") {
                    const predecessors = newTask.predecessors.split(",")
                        .filter((linkStr: string) => linkStr.startsWith("undefined") === false)
                        .join(", ");
                    const links = linksWithPredecessors(predecessors, newTask);
                    links.forEach((item) => {
                        gantt.addLink(item);
                    });
                }
                // 需要更新前置任务link之后再更新任务
                gantt.updateTask(newTask.id, newTask);
                // updateProjectActualTimeWithTask(newTask);
                if (setVisible instanceof Function) {
                    setVisible(false);
                }
            });
    };

    const handleCancel = () => {
        if (editTask !== undefined) {
            if (gantt.isTaskExists(editTask.id) === true && editTask.$new === true) {
                gantt.deleteTask(editTask.id);
            }
            if (setVisible instanceof Function) {
                setVisible(false);
            }
        }
    };

    return (
        <Modal
            width={640}
            bodyStyle={{padding: 0}}
            title={<span style={{fontWeight: "bold"}}>前置任务</span>}
            visible={visible}
            onCancel={handleCancel}
            onOk={handleSave}
            destroyOnClose
            maskClosable={false}
            keyboard={false}
        >
            <Form form={form} style={{padding: "20px"}} requiredMark={false}>
                <Form.Item
                    noStyle
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    name="predecessors"
                >
                    <PredecessorsInput
                        editTask={editTask}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};


export default EditPredecessorsModal;
