export interface Person {
    name: string;
    id: string;
    avatar?: string;
}

export interface PersonTreeData {
    key: string;
    person?: Person;
    name: string;
    children: PersonTreeData[];
    parentId?: string;
}

// 用户头像信息
export interface UserPortraitInfo {
    userName?: string;
    realName?: string;
    portraitUuid?: string;
    sourceType?: number;
}

export interface UserItems {
    users?: UserPortraitInfo[];
}

export interface OrgRes {
    dataType: number;
    id: string;
    name: string;
    parentId: string;
    sortOrder: number;
    type: number;
    users: UserPortraitInfo[];
}

export interface RoleRes {
    role: string;

    users: UserPortraitInfo[];
}

export interface PersonState {
    orgData: PersonTreeData[];
    roleData: PersonTreeData[];
    userNames: string[];
    userNameMap: Map<string, string>;
    avatarMap: Map<string, string>;
    personDataLoading: boolean;
}

export interface ApprovalUserInfo {
    isLeave?: boolean;
    portraitUuid?: string;
    realName?: string;
    sourceType?: number;
    userName?: string;
}
