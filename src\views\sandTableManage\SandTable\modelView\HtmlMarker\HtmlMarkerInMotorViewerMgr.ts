import MotorMarkerHandler from "../../../../../assets/ts/graphUtils/MotorMarkerHandler";

export interface MarkerTipWithIdRender {
    idList: string[];
    markerHandler: MotorMarkerHandler | null;
    renderType: string;
    renderInstance: (props: { id: string; windowPosX: number; windowPosY: number }) => JSX.Element;
}

export type ClearHtmlMarkerListener = () => void;

class HtmlMarkerInMotorViewerMgrClass {
    private mapMarkerTipRender: Map<string, MarkerTipWithIdRender> = new Map<string, MarkerTipWithIdRender>();

    private clearMarkerViewListenerList: ClearHtmlMarkerListener[] = [];

    public resetMarkerTipRender(item: MarkerTipWithIdRender) {
        this.mapMarkerTipRender.set(item.renderType, item);
    }

    public removeMarkerTipRender(renderType: string) {
        this.mapMarkerTipRender.delete(renderType);
    }


    public clearMarkerView() {
        this.clearMarkerViewListenerList.forEach((el) => {
            el();
        });
    }

    public getMarkerTipRenderList(): MarkerTipWithIdRender[] {
        const resultList: MarkerTipWithIdRender[] = [];
        this.mapMarkerTipRender.forEach((el) => {
            resultList.push(el);
        });
        return resultList;
    }

    addClearMarkerViewListenerList(listener: ClearHtmlMarkerListener) {
        if (!this.clearMarkerViewListenerList.includes(listener)) {
            this.clearMarkerViewListenerList.push(listener);
        }
    }

    removeClearMarkerViewListenerList(listener: ClearHtmlMarkerListener) {
        this.clearMarkerViewListenerList = this.clearMarkerViewListenerList.filter((el) => el !== listener);
    }
}

const HtmlMarkerInMotorViewerMgr = new HtmlMarkerInMotorViewerMgrClass();
export default HtmlMarkerInMotorViewerMgr;
