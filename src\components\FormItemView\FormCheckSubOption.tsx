import {Typography} from "antd";
import {uniqBy} from "lodash-es";
import React from "react";
import {CheckSubOptionValueType} from "../CheckSubOption/MultipleChoice";

const {Paragraph} = Typography;

export interface FormCheckSubOptionProps {
    value?: CheckSubOptionValueType[];
}

const FormCheckSubOption = (props: FormCheckSubOptionProps) => {
    const {value = []} = props;

    const getValue = () => uniqBy(value, "subOptionId").map((el) => el.subOptionContent).join(";");

    return (
        <Paragraph ellipsis={{tooltip: getValue(), rows: 3}}>
            {getValue()}
        </Paragraph>
    );
};

export default FormCheckSubOption;
