/* eslint-disable max-lines-per-function */
import React, {useCallback, useEffect, useState, useMemo} from "react";
import {useSelector} from "react-redux";
import {Button, Col, Row, Space, Pagination, message, Tabs, Popconfirm, Modal} from "antd";
import {PlusOutlined} from "@ant-design/icons";
import {useForm} from "antd/lib/form/Form";
import clsx from "clsx";
import {RootState} from "../../../store/rootReducer";
import {FormSubmissionType} from "./index.d";
import {QueryFormInit, ComColumnsType, getPreparationColumns, tabList} from "./data";
import ComTable from "../../../components/ComTable";
import {ApprovalStatus, GetPreparationListParams, PlanExportParams, PlanPreparationItemType, PlanPreparationTabNumReturn} from "../../../api/Preparation/type";
import TipModal from "../../../components/TipModal";
import {getPreparationList, deletePlanInfo, putPlanChange, postPlanExport, getPreparationTabNum} from "../../../api/Preparation";
import PlanInfoModal from "../../../components/PlanInfoModal";
import ComTabs from "../../../components/ComTabs";
import ScheduleEdit from "../ScheduleEdit";
import DrawerLaunchApproval from "./components/DrawerLaunchApproval";
import {BackType} from "../../../assets/ts/globalType";
import TableLayout from "../../../components/TableLayout";
import {momentText} from "../../../assets/ts/utils";
import PermissionCode from "../../../components/PermissionCode";
// import TableColumnsControl from "../../../components/TableColumnsControl";
import QueryFormSingle from "../../../components/QueryFormSingle";
import useComStyle from "../../../assets/css/useComStyle";
import ComModalExport from "../../../components/ComModal/ComModalExport";
import renderTableText from "../../../components/renderTableText";
import MyIcon from "../../../components/MyIcon";
import {resourceUnLock} from "../../../components/ScheduleGantt/api/common";
import ComTitle from "../../../components/ComTable/ComTitle";
import useTableStyle from "../../../components/ComTable/useTableStyle";


const layout = {
    // labelCol: {span: 7},
    wrapperCol: {span: 8}
};
const Preparation: React.FC = () => {
    const comCls = useComStyle();
    const tableCls = useTableStyle();
    const {orgInfo, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const [curPage, setCurPage] = useState<"list" | "view" | "edit">("list");
    const [form] = useForm();
    const [approvalType, setApprovalType] = useState(tabList[0].value);
    const [queryItemList] = useState(QueryFormInit);
    const [loading, setLoading] = useState(false);
    const [columns, setColumns] = useState<ComColumnsType[]>([]);
    const [pagination, setPagination] = useState({total: 0, currentPage: 1, pageSize: 10});
    const [selectData, setSelectData] = useState<PlanPreparationItemType[]>([]);
    const [preparationList, setPreparationList] = useState<PlanPreparationItemType[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [approvalVisible, setApprovalVisible] = useState<boolean>(false);
    const [approvalPlan, setApprovalPlan] = useState<PlanPreparationItemType>();
    const [planId, setPlanId] = useState<string>();
    const [curPlanId, setCurPlanId] = useState<string>();
    const [params, setParams] = useState<GetPreparationListParams>({
        pageNum: 1,
        pageSize: 10,
        status: Number(approvalType)
    });
    const [tabNumObj, setTabNumObj] = useState<PlanPreparationTabNumReturn>();
    useEffect(() => {
        // 切换项目部，回到列表页
        setCurPage("list");
    }, [orgInfo.orgId]);

    useEffect(() => {
        form.setFieldsValue({type: -1});
    }, [form]);
    useEffect(() => {
        if (curSectionInfo === undefined || curSectionInfo === null || curSectionInfo.id.length === 0) {
            return;
        }
        getPreparationTabNum({
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
        }).then((res) => {
            setTabNumObj({...res.data, all: Object.values(res.data).reduce((pre, cur) => pre + cur, 0)});
        }).catch((err) => {
            console.log(err);
        });
    }, [curSectionInfo, orgInfo.orgId, preparationList]);
    const getList = useCallback(() => {
        if (curSectionInfo === undefined || curSectionInfo === null || curSectionInfo.id.length === 0) {
            return;
        }
        setLoading(true);
        setPreparationList([]);
        getPreparationList({
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
            ...params
        }).then((res) => {
            if (res.success && res.data !== undefined) {
                setPreparationList(res.data?.items ?? []);
                setPagination((prev) => ({...prev, total: res.data.total ?? 10}));
            }
        }).catch((err) => {
            console.log(err);
        }).finally(() => {
            setLoading(false);
        });
    }, [curSectionInfo, orgInfo.orgId, params]);

    useEffect(() => {
        getList();
    }, [getList]);

    const handleAdd = useCallback(() => {
        setPlanId(undefined);
        setVisible(true);
    }, []);

    const handleExport = useCallback(() => {
        if (curSectionInfo === undefined || curSectionInfo === null || curSectionInfo.id.length === 0) {
            return;
        }
        const exportParams: PlanExportParams = {
            ...params,
            deptId: orgInfo.orgId,
            nodeId: curSectionInfo.isAll !== true ? curSectionInfo.id : undefined,
            nodeType: curSectionInfo.isAll !== true ? curSectionInfo.nodeType : undefined,
            pageNum: 1,
            pageSize: pagination.total,
        };
        if (selectData.length > 0) {
            exportParams.ids = selectData.map((item) => item.id);
        }
        const fileName = `${orgInfo.orgName}-${curSectionInfo.nodeName}-进度计划台账.xlsx`;
        postPlanExport(exportParams, fileName);
    }, [curSectionInfo, orgInfo.orgId, orgInfo.orgName, pagination.total, params, selectData]);

    const handleApprovalTypeChange = useCallback((val) => {
        setApprovalType(val);
        setParams((old) => ({
            ...old,
            pageNum: 1,
            status: val !== "all" ? Number(val) : undefined
        }));
        setPagination((prev) => ({...prev, currentPage: 1}));
        setSelectData([]);
    }, []);

    const handleFormValuesChange = useCallback((values) => {
        // console.log("values", values);
        if (values.type !== undefined) {
            setParams((old) => ({
                ...old,
                pageNum: 1,
                type: values.type < 0 ? undefined : values.type
            }));
            setPagination((prev) => ({...prev, currentPage: 1}));
            setSelectData([]);
        }
    }, []);

    const onformFinished = useCallback((values) => {
        setParams((old) => ({
            ...old,
            ...values,
            pageNum: 1,
            pageSize: old.pageSize,
            type: values.type < 0 ? undefined : values.type
        }));
        setPagination((prev) => ({...prev, currentPage: 1}));
        setSelectData([]);
    }, []);

    const reset = useCallback(() => {
        form.resetFields();
        form.setFieldsValue({type: -1});
        setParams((old) => ({
            pageNum: 1,
            pageSize: old.pageSize,
            status: approvalType !== "all" ? Number(approvalType) : undefined
        }));
        setPagination((prev) => ({...prev, currentPage: 1}));
        setSelectData([]);
    }, [approvalType, form]);

    const paginationChange = useCallback((currentPage: number, pageSize?: number) => {
        if (pageSize === pagination.pageSize) {
            setPagination((oldPage) => ({...oldPage, currentPage}));
        } else {
            setPagination((oldPage) => ({...oldPage, currentPage: 1, pageSize: pageSize ?? 10}));
        }
        setParams((old) => ({...old, pageNum: currentPage, pageSize: pageSize ?? 10}));
        setSelectData([]);
    }, [pagination.pageSize]);

    const onSelectChange = useCallback((_selectedRowKeys: React.Key[], selectedRows: PlanPreparationItemType[]) => {
        setSelectData(selectedRows);
    }, []);

    const planInfoBack = useCallback((_planId: string) => {
        setVisible(false);
        setPlanId(undefined);
        getList();
        setCurPlanId(_planId);
        setCurPage("edit");
    }, [getList]);

    const handleDelete = useCallback(() => {
        // console.log("handleDelete");
        const noDeleteData: PlanPreparationItemType[] = [];
        const deleteData: PlanPreparationItemType[] = [];
        selectData.forEach((item) => {
            if (item.status === 0 || item.status === 1) {
                deleteData.push(item);
            } else {
                noDeleteData.push(item);
            }
        });
        if (noDeleteData.length > 0 && noDeleteData.length === selectData.length) {
            Modal.warning({
                title: "未选中可删除计划！",
                closable: true,
                icon: <MyIcon type="icon-jinggao_mianxing" color="#FAAB0C" style={{fontSize: 18, position: "absolute", left: 20, top: 24}} />,
                content: <div>仅支持删除“未发起”和“无需审批”的计划</div>,
                className: comCls.deleteWarningTip,
                bodyStyle: {
                    paddingTop: 0,
                    backgroundColor: "#fef6e6",
                    borderLeft: "3px solid #FAAB0C"
                }
            });
        } else if (deleteData.length > 0) {
            deletePlanInfo(deleteData.map((item) => item.id)).then((res) => {
                if (res.success) {
                    message.success("删除成功！");
                    setSelectData(noDeleteData);
                    setParams((old) => ({...old, pageNum: 1}));
                    setPagination((prev) => ({...prev, currentPage: 1}));
                }
            }).catch((err) => console.log(err));
        }
    }, [comCls.deleteWarningTip, selectData]);

    const handleView = useCallback((record: PlanPreparationItemType) => {
        // console.log("handleView", record);
        setCurPlanId(record.id);
        setCurPage("view");
    }, []);

    const handleEdit = useCallback((record: PlanPreparationItemType) => {
        // setPlanId(id);
        // setVisible(true);
        setCurPlanId(record.id);
        setCurPage("edit");
    }, []);

    const handleApproval = useCallback((record: PlanPreparationItemType) => {
        setApprovalVisible(true);
        setApprovalPlan(record);
    }, []);

    // 发起审批成功
    // const handleApprovalSuccess = useCallback(() => {
    //     setParams((old) => ({
    //         ...old,
    //         pageNum: 1,
    //         pageSize: old.pageSize
    //     }));
    //     setPagination((prev) => ({...prev, currentPage: 1}));
    //     setSelectData([]);
    //     setApprovalVisible(false);
    //     setApprovalPlan(undefined);
    // }, []);

    const handleStartPlanChange = useCallback(async (record: PlanPreparationItemType) => {
        // console.log("handleStartChange", record);
        try {
            const res = await putPlanChange(record.id);
            if (res.success) {
                setCurPlanId(record.id);
                setCurPage("view");
            }
        } catch (error) {
            // console.log("handleStartChange", error);
        }
    }, []);

    const preparationTableColumns: ComColumnsType[] = useMemo(() => {
        const newColumns = [...getPreparationColumns()];
        if (approvalType === "3" || approvalType === "all") {
            // 审批完成/全部 显示审批人，审批时间
            newColumns.push({
                key: "approvalUserList",
                title: "审批人",
                dataIndex: "approvalUserList",
                align: "left",
                mustShow: false,
                show: true,
                width: 150,
                render: (_approvalUserList, record) => {
                    if (record.approvalUserList !== null && record.approvalUserList !== undefined && record.approvalUserList.length > 0) {
                        return renderTableText(record.approvalUserList.map((user) => user.realName).join(","));
                    }
                    return "--";
                }
            });
            newColumns.push({
                key: "approvalEndDate",
                title: "审批通过时间",
                dataIndex: "approvalEndDate",
                align: "left",
                mustShow: false,
                show: true,
                width: 150,
                render: (text: number) => (text !== null ? renderTableText(momentText(text, "YYYY.MM.DD HH:mm:ss")) : "--")
            });
        }
        newColumns.push({
            key: "operate",
            title: "操作",
            // align: "center",
            align: "right",
            mustShow: true,
            show: true,
            fixed: "right",
            width: 160,
            render: (_id: string, record: PlanPreparationItemType) => {
                const {approvalStatus} = record;
                const buttons = [];
                buttons.push(
                    <PermissionCode
                        authcode="ProjectPlatform-Plan-Progress-Establishment:view"
                    >
                        <Button size="middle" type="link" onClick={() => handleView(record)} style={{paddingRight: 0}}>查看</Button>
                    </PermissionCode>
                );
                if (approvalStatus !== ApprovalStatus.APPROVING && approvalStatus !== ApprovalStatus.APPROVAL_COMPLETED) {
                    // 审批中/审批完成不可编辑
                    buttons.push(
                        <PermissionCode
                            authcode="ProjectPlatform-Plan-Progress-Establishment:edit"
                        >
                            <Button size="middle" type="link" onClick={() => handleEdit(record)} style={{paddingRight: 0}}>编辑</Button>
                        </PermissionCode>
                    );
                }
                if (approvalStatus === ApprovalStatus.APPROVAL_COMPLETED) {
                    // 审批完成可发起变更
                    buttons.push(
                        <PermissionCode
                            authcode="ProjectPlatform-Plan-Progress-Establishment:change"
                        >
                            <Popconfirm
                                title="是否发起变更？"
                                onConfirm={async () => handleStartPlanChange(record)}
                            >
                                <Button size="middle" type="link" style={{paddingRight: 0}}>变更</Button>
                            </Popconfirm>
                        </PermissionCode>
                    );
                }
                // if (status === 1) {
                //     // 未申报可发起审批
                //     buttons.push(<Button size="middle" type="link" onClick={() => handleApproval(record)}>发起审批</Button>);
                // }
                return <>{buttons}</>;
            }
        });
        return newColumns;
    }, [approvalType, handleEdit, handleStartPlanChange, handleView]);

    useEffect(() => {
        setColumns(preparationTableColumns);
    }, [preparationTableColumns]);

    const handleApprovalBack = useCallback(async (type: BackType = "cancel") => {
        if (type === "ok") {
            if (curPlanId !== undefined) {
                await resourceUnLock("PLAN", curPlanId);
            }
            setCurPage("list");
            // 确认 返回
            setParams((old) => ({
                ...old,
                pageNum: 1,
                pageSize: old.pageSize
            }));
            setPagination((prev) => ({...prev, currentPage: 1}));
            setSelectData([]);
            setApprovalPlan(undefined);
            setApprovalVisible(false);
        } else if (type === "cancel") {
            // 取消 返回
            setApprovalPlan(undefined);
            setApprovalVisible(false);
        }
    }, [curPlanId]);

    const approvalDrawer = useMemo(() => {
        if (approvalVisible && approvalPlan !== undefined) {
            return (
                <DrawerLaunchApproval
                    plan={approvalPlan}
                    visible={approvalVisible}
                    setVisible={setApprovalVisible}
                    back={handleApprovalBack}
                />
            );
        }
        return null;
    }, [approvalPlan, approvalVisible, handleApprovalBack]);

    if (curPage !== "list" && curPlanId !== undefined) {
        return (
            <>
                <ScheduleEdit
                    fromType="plan"
                    planId={curPlanId}
                    enterType={curPage}
                    onBack={() => {
                        setParams((prev) => ({...prev}));
                        setCurPage("list");
                    }}
                    tableTitle={<ComTitle text="进度计划任务详情清单" />}
                    onLaunchApproval={(planInfo) => handleApproval(planInfo as PlanPreparationItemType)}
                />
                {approvalDrawer}
            </>
        );
    }

    return (
        <>
            <TableLayout>
                <div className={comCls.rootBox}>
                    <Row justify="start" style={{margin: "0 0 24px 0"}}>
                        <Col>
                            <Space>
                                <PermissionCode
                                    authcode="ProjectPlatform-Plan-Progress-Establishment:add"
                                >
                                    <Button type="primary" onClick={handleAdd} icon={<PlusOutlined />}>新增进度计划</Button>
                                </PermissionCode>
                                <TipModal
                                    onOk={handleDelete}
                                    btnText="批量删除"
                                    buttonConfig={{
                                        disabled: selectData.length < 1 || approvalType === "2" || approvalType === "3",
                                    }}
                                />
                                <ComModalExport
                                    totalNum={pagination.total}
                                    selectedNum={selectData.length}
                                    btnText="导出台账"
                                    onOk={handleExport}
                                    content={(
                                        <div style={{padding: 24}}>
                                            <p>{`已选择${selectData.length !== 0 ? selectData.length : pagination.total}条数据进行批量导出操作!`}</p>
                                            <p>当前导出台账格式为：EXCEL。</p>
                                            <p>如需打印，请修改纸张信息为：A3，横向打印。</p>
                                        </div>
                                    )}
                                />
                            </Space>
                        </Col>
                    </Row>
                    <ComTabs activeKey={approvalType} onChange={handleApprovalTypeChange}>
                        {tabList.map((tab) => <Tabs.TabPane key={tab.value} tab={`${tab.label}(${tabNumObj !== undefined ? tabNumObj[tab.value] : "0"})`} />)}
                    </ComTabs>
                    <Row align="middle" justify="space-between">
                        <Col span={24}>
                            <QueryFormSingle<FormSubmissionType>
                                queryItemList={queryItemList}
                                onFormValuesChange={handleFormValuesChange}
                                onFormFinish={onformFinished}
                                formConfig={{form, ...layout}}
                                onFormClear={reset}
                                labelMaxWidth={115}
                                isOperationBtn={false}
                            />
                        </Col>
                        {/* <Col span={1}>
                            <Row justify="center">
                                <Col>
                                    <TableColumnsControl
                                        key={approvalType}
                                        tableKey={approvalType}
                                        setColumnsList={setColumns}
                                        columnsList={columns}
                                    />
                                </Col>
                            </Row>
                        </Col> */}
                    </Row>
                    <ComTable<PlanPreparationItemType>
                        title={() => <ComTitle text="进度计划台账" />}
                        loading={loading}
                        columns={columns.filter((el: ComColumnsType) => el.show || el.mustShow)}
                        dataSource={preparationList}
                        rowKey={(record) => record.id}
                        className={clsx([
                            tableCls.boldHeaderCell,
                            tableCls.excelTempate,
                            tableCls.customTable,
                            tableCls.standardTable,
                        ])}
                        rowSelection={{
                            type: "checkbox",
                            columnWidth: 60,
                            selectedRowKeys: selectData.map((item) => item.id),
                            onChange: onSelectChange
                        }}
                    />
                    <Row justify="space-between" style={{marginTop: 16}}>
                        <Col>{`已选 ${selectData.length} 项`}</Col>
                        <Col>
                            <Pagination total={pagination.total} showSizeChanger showQuickJumper current={pagination.currentPage} pageSize={pagination.pageSize} onChange={paginationChange} showTotal={(totalCount: number) => `共 ${totalCount} 条`} />
                        </Col>
                    </Row>
                </div>
                {visible && <PlanInfoModal visible={visible} setVisible={setVisible} planId={planId} planInfoBack={planInfoBack as any} />}
                {approvalDrawer}
            </TableLayout>
        </>
    );
};
export default Preparation;
