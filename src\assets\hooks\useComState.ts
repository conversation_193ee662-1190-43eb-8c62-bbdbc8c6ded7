import {FormInstance} from "antd";
import {useForm} from "antd/lib/form/Form";
import {Dispatch, Key, SetStateAction, useMemo, useState} from "react";
import {ComFormItemProps} from "../../components/FormItem";

interface UseComStateProps {
    queryFormInit?: ComFormItemProps[];
}

export interface StateProps {
    queryFormList: ComFormItemProps[];
    total: number;
    curPage: number;
    pageSize: number;
    selectIds: Key[];
    curPageType: string;
    queryForm: FormInstance;
    rowId: string;
    queryFormData: {};
}

export interface SetStateProps {
    setQueryFormList: Dispatch<SetStateAction<StateProps["queryFormList"]>>;
    setTotal: Dispatch<SetStateAction<StateProps["total"]>>;
    setCurPage: Dispatch<SetStateAction<StateProps["curPage"]>>;
    setPageSize: Dispatch<SetStateAction<StateProps["pageSize"]>>;
    setSelectIds: Dispatch<SetStateAction<StateProps["selectIds"]>>;
    setCurPageType: Dispatch<SetStateAction<StateProps["curPageType"]>>;
    setRowId: Dispatch<SetStateAction<StateProps["rowId"]>>;
    setQueryFormData: Dispatch<SetStateAction<StateProps["queryFormData"]>>;
}

const useComState = (props: UseComStateProps): [StateProps, SetStateProps] => {
    const {
        queryFormInit = [],
    } = props;
    const [queryFormList, setQueryFormList] = useState<StateProps["queryFormList"]>(queryFormInit);
    const [total, setTotal] = useState<StateProps["total"]>(0);
    const [curPage, setCurPage] = useState<StateProps["curPage"]>(1);
    const [pageSize, setPageSize] = useState<StateProps["pageSize"]>(10);
    const [selectIds, setSelectIds] = useState<StateProps["selectIds"]>([]);
    const [curPageType, setCurPageType] = useState<StateProps["curPageType"]>("");
    const [rowId, setRowId] = useState<string>("");
    const [queryFormData, setQueryFormData] = useState({});
    const [queryForm] = useForm();
    const state = useMemo(() => (
        {
            queryFormList,
            total,
            curPage,
            pageSize,
            selectIds,
            curPageType,
            queryForm,
            rowId,
            queryFormData,
        }),
    [curPage, curPageType, pageSize, queryForm, queryFormData, queryFormList, rowId, selectIds, total]);

    const setState = useMemo(() => ({
        setQueryFormList,
        setTotal,
        setCurPage,
        setPageSize,
        setSelectIds,
        setCurPageType,
        setRowId,
        setQueryFormData,
    }), []);
    return [state, setState];
};

export default useComState;
