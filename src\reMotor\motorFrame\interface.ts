export interface MsgResponseInfo {
    frameId: string;
    id: string;
    name: string;
    version: number;
    timeStamp: number;
}

export interface FrameMessageType {
    id: string;
    name: string;
    params: unknown;
    version: number;
    timeStamp: number;
    type: "request" | "response";
    responseInfo?: MsgResponseInfo;
}

/** 调用方传递给iframe的消息类型 */
export type FrameCallerEvent =
    "initViewer"
    | "openProject"
    | "resetMainProject"
    | "setColor"
    | "setVisible"
    | "onResetViewPort"
    | "onViewPortChange"
    | "onSandPlay"
    | "onProcessPlay";

/** iframe传递给调用方的消息类型 */
export type FrameEmitEvent = "initViewerResponse" | "openProjectResponse";
