import {isEmpty, isNil} from "lodash-es";
/**
 * Excel模板导出工具
 *
 * 功能特性：
 * 1. 保留模板样式：完全保留Excel模板中的样式、格式、公式等
 * 2. 精确列映射：通过数字列号或字母列名精确控制数据填入位置
 * 3. 多Sheet支持：可以在一个模板文件中填入多个sheet的数据
 * 4. 指定单元格填值：可以在模板的任意单元格填入指定值
 * 5. 错误处理：提供回退机制，确保导出功能的可靠性
 * 6. 样式保持：新填充的数据行与起始行样式保持一致
 */

import ExcelJS, {Column} from "exceljs";
import {message} from "antd";

// ====== 接口定义 ======

/**
 * 基于模板的Sheet信息接口
 */
export interface TemplateSheetInfo {
    /** 可选，如果不指定则使用模板中的第一个sheet */
    sheetName?: string;
    /** 要填入的数据数组 */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: Record<string, any>[];
    /** 数据开始填入的行号，默认为2（第二行，第一行通常是标题） */
    startRow?: number;
    /** 数据开始填入的列号，默认为1 */
    startCol?: number;
    /** 数据字段到Excel列的映射，key为数据字段名，value为Excel列号(如1,2,3)或列名(如A,B,C) */
    columnMapping?: {[key: string]: number | string};
    /** 指定特定单元格的值，key为单元格地址(如'A5', 'B6')，value为要填入的值 */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    cellValues?: {[cellAddress: string]: any};
}

/**
 * 传统Sheet信息接口（保持向后兼容）
 */
export interface SheetInfo {
    name: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: Record<string, any>[];
    columns?: Partial<Column>[];
}

// ====== 私有工具函数 ======

/**
 * 检查值是否为有效值（非undefined、非null、非空字符串）
 * @param value 要检查的值
 * @returns 是否为有效值
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isValidValue = (value: any): boolean => value !== undefined && value !== null && String(value).trim() !== "";

/**
 * 文件下载函数
 */
const saveAs = (obj: Blob, fileName: string) => {
    const tmpa = document.createElement("a");
    tmpa.download = `${fileName}.xlsx`;
    tmpa.href = URL.createObjectURL(obj);
    tmpa.click();

    setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        URL.revokeObjectURL(obj as any);
    }, 100);
};

/**
 * 复制单元格样式从源单元格到目标单元格
 * @param sourceCell 源单元格
 * @param targetCell 目标单元格
 */
const copyCellStyle = (sourceCell: ExcelJS.Cell, targetCell: ExcelJS.Cell) => {
    // 复制字体样式
    if (!isNil(sourceCell.font)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.font = {...sourceCell.font};
    }

    // 复制对齐方式
    if (!isNil(sourceCell.alignment)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.alignment = {...sourceCell.alignment};
    }

    // 复制边框样式
    if (!isNil(sourceCell.border)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.border = {...sourceCell.border};
    }

    // 复制填充样式
    if (!isNil(sourceCell.fill)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.fill = {...sourceCell.fill};
    }

    // 复制数字格式
    if (!isNil(sourceCell.numFmt)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.numFmt = sourceCell.numFmt;
    }

    // 复制保护设置
    if (!isNil(sourceCell.protection)) {
        // eslint-disable-next-line no-param-reassign
        targetCell.protection = {...sourceCell.protection};
    }
};

/**
 * 复制行样式从源行到目标行
 * @param worksheet 工作表
 * @param sourceRowNum 源行号
 * @param targetRowNum 目标行号
 * @param columnMapping 列映射（可选）
 * @param startCol 起始列号（可选，默认为1）
 */
const copyRowStyle = (
    worksheet: ExcelJS.Worksheet,
    sourceRowNum: number,
    targetRowNum: number,
    columnMapping?: {[key: string]: number | string},
    startCol = 1
) => {
    const sourceRow = worksheet.getRow(sourceRowNum);
    const targetRow = worksheet.getRow(targetRowNum);

    // 复制行高
    if (!isNil(sourceRow.height)) {
        targetRow.height = sourceRow.height;
    }

    // 如果有列映射，只复制映射中指定列的样式
    if (!isNil(columnMapping) && Object.keys(columnMapping).length > 0) {
        Object.values(columnMapping).forEach((colPosition) => {
            let sourceColNum: number;
            if (typeof colPosition === "number") {
                sourceColNum = colPosition;
            } else {
                // 字母列名转换为数字，如 'A' -> 1, 'B' -> 2
                sourceColNum = colPosition.charCodeAt(0) - 64;
            }

            const sourceCell = worksheet.getCell(sourceRowNum, sourceColNum);
            const targetCell = worksheet.getCell(targetRowNum, sourceColNum);
            copyCellStyle(sourceCell, targetCell);
        });
    } else {
        // 没有列映射时，复制源行所有有内容或样式的列
        sourceRow.eachCell({includeEmpty: false}, (sourceCell, colNumber) => {
            const targetCell = worksheet.getCell(targetRowNum, colNumber);
            copyCellStyle(sourceCell, targetCell);
        });

        // 如果源行没有内容，至少复制从startCol开始的合理范围的列样式
        if (sourceRow.cellCount === 0) {
            for (let col = startCol; col <= startCol + 20; col++) { // 假设最多20列
                const sourceCell = worksheet.getCell(sourceRowNum, col);
                if (!isNil(sourceCell.font) || !isNil(sourceCell.border) || !isNil(sourceCell.fill) || !isNil(sourceCell.alignment)) {
                    const targetCell = worksheet.getCell(targetRowNum, col);
                    copyCellStyle(sourceCell, targetCell);
                }
            }
        }
    }
};

// ====== 导出函数 ======

/**
 * 基于Excel模板导出数据，保留模板样式
 * @param templatePath 模板文件路径
 * @param sheetInfo 要填入的数据信息
 * @param fileName 导出的文件名
 */
export const exportWithTemplate = async (
    templatePath: string,
    sheetInfo: TemplateSheetInfo,
    fileName: string,
    adjustRowHeight = false,
): Promise<void> => {
    try {
        // 读取模板文件
        const response = await fetch(templatePath);
        if (!response.ok) {
            throw new Error(`Failed to load template: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();

        // 创建工作簿并读取模板
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(arrayBuffer);

        // 获取要操作的工作表
        let findWorksheet: ExcelJS.Worksheet | undefined;
        const sheetName = sheetInfo.sheetName ?? "";
        if (!isEmpty(sheetName)) {
            findWorksheet = workbook.getWorksheet(sheetName);
        } else {
            // 使用数组解构获取第一个工作表
            const [firstWorksheet] = workbook.worksheets;
            findWorksheet = firstWorksheet;
        }

        if (isNil(findWorksheet)) {
            throw new Error(`Worksheet ${sheetInfo.sheetName ?? "first sheet"} not found in template`);
        }

        const worksheet = findWorksheet;
        const startRow = sheetInfo.startRow ?? 2; // 默认从第2行开始（第1行通常是标题）
        const startCol = sheetInfo.startCol ?? 1; // 默认从第1列开始

        // 填入数据
        sheetInfo.data.forEach((rowData, rowIndex) => {
            const currentRow = startRow + rowIndex;

            // 如果不是第一行数据，复制起始行的样式
            if (rowIndex > 0) {
                copyRowStyle(worksheet, startRow, currentRow, sheetInfo.columnMapping, startCol);
            }

            if (!isNil(sheetInfo.columnMapping) && Object.keys(sheetInfo.columnMapping).length > 0) {
                // 使用列映射
                Object.entries(sheetInfo.columnMapping).forEach(([fieldName, colPosition]) => {
                    const value = rowData[fieldName];
                    if (isValidValue(value)) {
                        if (typeof colPosition === "number") {
                            // 数字列号
                            worksheet.getCell(currentRow, colPosition).value = value as ExcelJS.CellValue;
                        } else {
                            // 字母列名，如 'A', 'B', 'C'
                            worksheet.getCell(`${colPosition}${currentRow}`).value = value as ExcelJS.CellValue;
                        }
                    }
                });
            } else {
                // 没有映射时，按照对象属性顺序填入
                const values = Object.values(rowData);
                values.forEach((value, colIndex) => {
                    if (isValidValue(value)) {
                        worksheet.getCell(currentRow, startCol + colIndex).value = value as ExcelJS.CellValue;
                    }
                });
            }

            if (adjustRowHeight) {
                const row = worksheet.getRow(currentRow);
                row.height = null as unknown as number; // 自动高度
                row.commit();
            }
        });

        // 填入指定单元格的值
        if (!isNil(sheetInfo.cellValues) && Object.keys(sheetInfo.cellValues).length > 0) {
            Object.entries(sheetInfo.cellValues).forEach(([cellAddress, value]) => {
                if (isValidValue(value)) {
                    worksheet.getCell(cellAddress).value = value as ExcelJS.CellValue;
                }
            });
        }

        // 导出文件
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], {type: "application/octet-stream"}), fileName);
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Export with template failed:", error);
        message.error("基于模板导出失败，请检查模板文件是否存在");
        throw error;
    }
};

/**
 * 基于Excel模板导出多个sheet的数据
 * @param templatePath 模板文件路径
 * @param sheetsInfo 多个sheet的数据信息
 * @param fileName 导出的文件名
 */
export const exportMultiSheetsWithTemplate = async (
    templatePath: string,
    sheetsInfo: TemplateSheetInfo[],
    fileName: string
): Promise<void> => {
    try {
        // 读取模板文件
        const response = await fetch(templatePath);
        if (!response.ok) {
            throw new Error(`Failed to load template: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();

        // 创建工作簿并读取模板
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(arrayBuffer);

        // 处理每个sheet
        // eslint-disable-next-line no-restricted-syntax
        for (const sheetInfo of sheetsInfo) {
            let worksheet: ExcelJS.Worksheet;
            const sheetName = sheetInfo.sheetName ?? "";
            if (!isEmpty(sheetName)) {
                const existingWorksheet = workbook.getWorksheet(sheetName);
                if (existingWorksheet === undefined || existingWorksheet === null) {
                    // 如果指定的sheet不存在，创建一个新的
                    worksheet = workbook.addWorksheet(sheetInfo.sheetName);
                } else {
                    worksheet = existingWorksheet;
                }
            } else {
                // 如果没有指定sheet名称，使用第一个sheet
                const [firstWorksheet] = workbook.worksheets;
                if (firstWorksheet === undefined || firstWorksheet === null) {
                    throw new Error("No worksheets found in template");
                }
                worksheet = firstWorksheet;
            }

            const startRow = sheetInfo.startRow ?? 2;
            const startCol = sheetInfo.startCol ?? 1;

            // 填入数据
            sheetInfo.data.forEach((rowData, rowIndex) => {
                const currentRow = startRow + rowIndex;

                // 如果不是第一行数据，复制起始行的样式
                if (rowIndex > 0) {
                    copyRowStyle(worksheet, startRow, currentRow, sheetInfo.columnMapping, startCol);
                }

                if (!isNil(sheetInfo.columnMapping) && Object.keys(sheetInfo.columnMapping).length > 0) {
                    // 使用列映射
                    Object.entries(sheetInfo.columnMapping).forEach(([fieldName, colPosition]) => {
                        const value = rowData[fieldName];
                        if (isValidValue(value)) {
                            if (typeof colPosition === "number") {
                                worksheet.getCell(currentRow, colPosition).value = value as ExcelJS.CellValue;
                            } else {
                                worksheet.getCell(`${colPosition}${currentRow}`).value = value as ExcelJS.CellValue;
                            }
                        }
                    });
                } else {
                    // 没有映射时，按照对象属性顺序填入
                    const values = Object.values(rowData);
                    values.forEach((value, colIndex) => {
                        if (isValidValue(value)) {
                            worksheet.getCell(currentRow, startCol + colIndex).value = value as ExcelJS.CellValue;
                        }
                    });
                }
            });

            // 填入指定单元格的值
            if (!isNil(sheetInfo.cellValues) && Object.keys(sheetInfo.cellValues).length > 0) {
                Object.entries(sheetInfo.cellValues).forEach(([cellAddress, value]) => {
                    if (isValidValue(value)) {
                        worksheet.getCell(cellAddress).value = value as ExcelJS.CellValue;
                    }
                });
            }
        }

        // 导出文件
        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], {type: "application/octet-stream"}), fileName);
    } catch (error) {
        // eslint-disable-next-line no-console
        console.error("Export multiple sheets with template failed:", error);
        message.error("基于模板导出失败，请检查模板文件是否存在");
        throw error;
    }
};

// ====== 导出类型 ======
export type ExcelColumnsType = Partial<Column>[];

// ====== 使用示例和文档 ======

/**
 * 使用示例：
 *
 * 1. 基础使用（带列映射和单元格填值）：
 * ```typescript
 * const sheetInfo: TemplateSheetInfo = {
 *     data: processedData,
 *     startRow: 2,
 *     columnMapping: {
 *         '序号': 1,
 *         '姓名': 2,
 *         '时间': 3
 *     },
 *     cellValues: {
 *         A1: "报表标题",
 *         B1: new Date().toLocaleDateString(),
 *         A10: "制表人：_______"
 *     }
 * };
 * await exportWithTemplate('/static/xlsx/template.xlsx', sheetInfo, '导出文件名');
 * ```
 *
 * 2. 多Sheet导出：
 * ```typescript
 * const sheetsInfo: TemplateSheetInfo[] = [
 *     { sheetName: 'Sheet1', data: data1, cellValues: { A1: "标题1" } },
 *     { sheetName: 'Sheet2', data: data2, cellValues: { A1: "标题2" } }
 * ];
 * await exportMultiSheetsWithTemplate('/static/xlsx/template.xlsx', sheetsInfo, '多Sheet文件');
 * ```
 *
 * 3. 指定单元格填值的高级用法：
 * ```typescript
 * cellValues: {
 *     A1: "标题",                           // 文本
 *     B1: new Date().toLocaleDateString(),  // 日期
 *     C1: 123.45,                          // 数字
 *     D1: "=SUM(A2:A10)",                  // 公式
 *     E1: `统计：${data.length}条记录`      // 模板字符串
 * }
 * ```
 *
 * 注意：新版本会自动复制起始行的样式到新填充的数据行，确保字体、对齐、边框、高度等样式保持一致。
 */
