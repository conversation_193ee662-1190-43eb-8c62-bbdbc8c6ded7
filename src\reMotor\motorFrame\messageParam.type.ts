import {CameraSpecAngle} from "../interface";

// InitViewer
export interface InitViewerParam {
    token: string;
    baseUrl: string;
}

// OpenProject
export interface OpenProjectParam {
    id: string;
    isBIM: boolean;
}

export interface ResetMainProjectParam {
    motorId?: string;
}

interface CompIdsWithColor {
    ids: string[];
    /** rgb rgba 16进制 */
    color: string;
}

/** 构件颜色设置传参 */
export interface SetColorParam {
    colorInfo: CompIdsWithColor[];
    motorId?: string;
}

/** 构件显隐设置传参 */
export interface SetVisibilityParam {
    visible?: boolean;
    ids?: string[];
    motorId?: string;
}

export interface SetColorAndVisibilityParam {
    id: string;
    color: string;
    visible: boolean;
}

/** 视口设置传参 */
export interface ViewPortChangeParam {
    angle?: CameraSpecAngle;
    phi?: number;
    theta?: number;
    durationTime?: number;
    callback?: () => void;
}

/** 默认状态播放传参 */
export interface SandPlayParam {
    motor3dId?: string;
    /** 工程设置的颜色 */
    defaultBimColor: string;
    /**
     * 指定的构件列表以及构件所对应要设置的颜色。
     *
     * 构件可以是批量的，比如5个构件设置成红色，6个构件设置成黄色。
     */
    colorInfo: CompIdsWithColor[];

}

/** 默认工序播放传参 将所有构件隐藏，然后将部分构件显示出来 */
export interface ProcessPlayParam {
    motor3dId?: string;
    colorInfo: CompIdsWithColor[];
}
