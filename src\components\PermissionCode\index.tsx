import {ReactElement} from "react";
import {findIndex} from "lodash-es";
import {getState} from "../../store";
import {AuthCodeResultList} from "../../store/common/actionTypes";

interface Props {
    authcode: string; // code 标识符合
    children: ReactElement;
    // 可以跳过权限
    admin?: boolean;
    authCodeList?: AuthCodeResultList[];
}

// 判断 传的数据 是否 有code

// TODO 权限码暂时注释
export const includesCodeStatus = (key: string, autoCodeList?: AuthCodeResultList[]) => {
    const {authCodeList} = getState().commonData;
    // // 开发环境权限失效,测试权限注释掉
    // return true;
    if (autoCodeList !== undefined) {
        return findIndex(autoCodeList, {code: key}) !== -1;
    }
    return (authCodeList ?? []).findIndex((v: unknown) => v === key) !== -1;
};

const PermissionCode = (props: Props) => {
    // 判断是否有权限
    const {authcode, children, admin = false, authCodeList} = props;
    if (includesCodeStatus(authcode, authCodeList) || admin) {
        return children;
    }
    return null;
};
export default PermissionCode;
