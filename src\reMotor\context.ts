import Motor from "@motor/core";
import {CameraSpecAngle, Dir<PERSON>reeWithPath, MouseEventListener, ProjectListener} from "./interface";
import {addDirTreePath, getViewPositionBySpecAngle} from "./utils";

class MotorContext {
    public mainProject: Motor.Project | null = null;

    public subProject: Motor.Model | null = null;

    private viewer: Motor.Viewer | null = null;

    private bimProjectList: Motor.Model[] = [];

    private projectListenerMap: Map<number, ProjectListener[]> = new Map<number, ProjectListener[]>();

    private mouseEventListenerMap: Map<number, MouseEventListener[]> = new Map<number, MouseEventListener[]>();

    // private enableComponentPick = false;

    public getViewer(): Motor.Viewer | null {
        return this.viewer;
    }

    public getCamera(): Motor.Camera | null {
        const viewer = this.getViewer();
        return viewer?.camera ?? null;
    }

    public getProject(): Motor.Project | null {
        return this.mainProject;
    }

    public getCurBIMProject(): Motor.Model | null {
        return this.subProject;
    }

    getInnerBimProject(): Motor.Model | null {
        return this.getCurBIMProject();
    }

    getBIMProject(id: string): Motor.Model | null {
        const find = this.bimProjectList.find((el) => el.id === id);
        return find ?? null;
    }

    getBIMProjectList(): Motor.Model[] {
        return this.bimProjectList;
    }

    async initViewer(viewerId: string | HTMLElement, motorUrl: string, motorToken: string): Promise<boolean> {
        this.viewer = this.createViewer(viewerId, motorUrl, motorToken);
        if (this.viewer === null) {
            return false;
        }
        await this.viewer.Init();
        this.registerAllMouseEvent(this.viewer);
        // 设置右下角方块角度
        this.viewer.enableViewCube(true);
        return true;
    }

    createViewer(viewerId: string | HTMLElement, motorUrl: string, motorToken: string): Motor.Viewer {
        Motor.setBaseUrl(".");
        return new Motor.Viewer({
            container: viewerId,
            token: motorToken,
            baseUrl: `${motorUrl}/editor`
        });
    }

    destroyViewer(): void {
        this.viewer = null;
    }

    resetViewport() {
        const camera = this.getCamera();
        const project = this.mainProject;
        const bimProject = this.subProject;
        if (camera !== null && project !== null && bimProject !== null) {
            if (typeof bimProject.viewPosition !== "undefined") {
                camera.setViewToViewPosition(bimProject.viewPosition);
                return;
            }

            if (typeof project.viewPosition !== "undefined") {
                camera.setViewToViewPosition(project.viewPosition);
            } else {
                camera.setViewToProject(this.mainProject);
            }
        }
    }

    async openProject(guid: string) {
        if (this.viewer === null) {
            return false;
        }
        this.onMainProjectStatusChange(guid, "begin");
        const projectInfo = await this.viewer.openModelProject(guid);
        this.mainProject = projectInfo?.project ?? null;
        this.subProject = projectInfo?.model ?? null;
        this.resetViewport();
        this.onMainProjectStatusChange(guid, "end");
        return true;
    }

    /** 默认设置mainProject的视口 */
    setViewPort(
        angle?: CameraSpecAngle,
        phiParam?: number,
        thetaParam?: number,
        durationTime?: number,
        callback?: () => void
    ) {
        const camera = this.getCamera();
        if (camera === null || this.mainProject === null) {
            return;
        }
        const {phi: phiOld, theta: thetaOld} = camera.getViewPosition();
        if (angle !== undefined) {
            const {phi, theta} = getViewPositionBySpecAngle(angle);
            camera.setViewToProject(this.mainProject, phi ?? phiOld, theta ?? thetaOld, durationTime, callback);
            return;
        }
        camera.setViewToProject(this.mainProject, phiParam ?? phiOld, thetaParam ?? thetaOld, durationTime, callback);
    }

    setVisible(visible: boolean, ids?: string[]) {
        if (this.mainProject === null) {
            return;
        }
        if (Array.isArray(ids) && ids.length > 0) {
            this.mainProject.setVisibility(visible, ids);
        } else {
            this.mainProject.setVisibility(visible);
        }
    }

    registerAllMouseEvent(viewer: Motor.Viewer) {
        const inputMap = new Motor.InputMap(viewer);
        this.registerMouseEvent(viewer, inputMap, Motor.InputType.LEFT_CLICK);
        this.registerMouseEvent(viewer, inputMap, Motor.InputType.RIGHT_CLICK);
        this.registerMouseEvent(viewer, inputMap, Motor.InputType.LEFT_DOWN);
        this.registerMouseEvent(viewer, inputMap, Motor.InputType.LEFT_UP);
        this.registerMouseEvent(viewer, inputMap, Motor.InputType.MOUSE_MOVE);
    }

    registerMouseEvent(viewer: Motor.Viewer, inputMap: Motor.InputMap, inputType: Motor.InputType) {
        inputMap.setInput(inputType, (windowPosition) => {
            if (windowPosition === undefined) {
                return;
            }
            const pickObj = viewer.pick(windowPosition as Motor.Vector2);
            // let breakValues = false;
            viewer.pickPosition(windowPosition as Motor.Vector2).then((position) => {
                const pickInfo = {pickObj, windowPosition: windowPosition as Motor.Vector2, position, inputType};
                this.mouseEventListenerMap.forEach((values) => {
                    for (let i = 0; i < values.length; ++i) {
                        const item = values[i];
                        if (item !== undefined && item.inputType === inputType) {
                            item.notifyMouseEvent(pickInfo);
                        }
                    }
                });
            });
        });
    }

    async getOpenedCompTree(): Promise<DirTreeWithPath[]> {
        if (this.mainProject === null) {
            return [];
        }
        const res: DirTreeWithPath[] = [];
        const modelList = await this.mainProject.queryModel([Motor.ModelType.BIM]);
        for (let i = 0; i < modelList.length; i++) {
            const curBim = modelList[i];
            const curCompTree = await curBim.getBimDirTree();
            if (curCompTree !== undefined) {
                res.push({
                    name: "全部",
                    path: "root",
                    children: addDirTreePath(curCompTree.children ?? [])
                });
            }
        }
        return res;
    }

    /** 模型初始化结束后，查询全部构件，给每个构件建立path => id的映射关系 */
    // async establishCompMapRelation() {
    //     if (this.mainProject === null) {
    //         return;
    //     }
    //     const allCompId = await this.mainProject.queryElement();
    //     const allComP = await this.mainProject.queryElement(allCompId);
    //     const compRelationMap: Map<string, SimpleMotorElement> = new Map();
    //     allComP.forEach((v) => compRelationMap.set((v.dir ?? []).join(pathSeparator), {bimId: v.bimId}));
    // }

    onMainProjectStatusChange(id: string, status: "begin" | "end") {
        let breakValues = false;
        this.projectListenerMap.forEach((values) => {
            if (!breakValues) {
                for (let i = 0; i < values.length; ++i) {
                    const item = values[i];
                    if (item !== undefined && item.notifyProjectChanged({behavior: "open", type: "main", status, id})) {
                        breakValues = true;
                        break;
                    }
                }
            }
        });
    }

    addProjectListener(listener: ProjectListener, priority = 5) {
        const findList = this.projectListenerMap.get(priority);
        if (findList !== undefined) {
            if (!findList.includes(listener)) {
                findList.push(listener);
            }
        } else {
            this.projectListenerMap.set(priority, [listener]);
            const arrayObj = Array.from(this.projectListenerMap);
            arrayObj.sort((val1, val2) => val1[0] - val2[0]);
            this.projectListenerMap = new Map(arrayObj.map((i) => [i[0], i[1]]));
        }
    }

    removeProjectListener(listener: ProjectListener): void {
        this.projectListenerMap.forEach((values, key) => {
            if (values.includes(listener)) {
                const newListener = values.filter((el) => el !== listener);
                this.projectListenerMap.set(key, newListener);
            }
        });
    }

    addMouseEventListener(listener: MouseEventListener, priority = 5) {
        const findList = this.mouseEventListenerMap.get(priority);
        if (findList !== undefined) {
            if (!findList.includes(listener)) {
                findList.push(listener);
            }
        } else {
            this.mouseEventListenerMap.set(priority, [listener]);
            const arrayObj = Array.from(this.mouseEventListenerMap);
            arrayObj.sort((val1, val2) => val1[0] - val2[0]);
            this.mouseEventListenerMap = new Map(arrayObj.map((i) => [i[0], i[1]]));
        }
    }

    removeMouseEventListener(listener: MouseEventListener) {
        this.mouseEventListenerMap.forEach((listenerList, mapKey) => {
            if (listenerList.includes(listener)) {
                const newListenerList = listenerList.filter((el) => el !== listener);
                this.mouseEventListenerMap.set(mapKey, newListenerList);
            }
        });
    }
}

const MotorContextInstance = new MotorContext();

export default MotorContextInstance;
