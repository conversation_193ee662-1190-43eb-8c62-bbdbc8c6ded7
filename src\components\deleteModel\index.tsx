import {Button, ButtonProps, ModalFuncProps, ModalProps} from "antd";
import React, {ReactNode, useState} from "react";
import ComModal from "../ComModal";
import BtnIcon from "../MyIcon/BtnIcon";

interface DeleteModalProps extends ModalProps {
    children?: ReactNode;
    onOk: ModalFuncProps["onOk"];
    content?: ReactNode | string;
    contentTitle?: ReactNode | string;
    btnText?: string;
    buttonConfig?: ButtonProps;
    buttonNode?: ReactNode;
}


const DeleteModel = (props: DeleteModalProps) => {
    const {children, onOk, content, contentTitle = "删除", btnText = "删除", buttonConfig, buttonNode} = props;
    const [visible, setVisible] = useState(false);

    const handleOk = () => {
        setVisible(false);
        if (onOk !== undefined) {
            onOk();
        }
    };

    const handleClick = () => {
        setVisible(true);
    };

    return (
        <>
            <div onClick={handleClick} style={{display: "inline-block"}}>
                {
                    buttonNode !== undefined ? buttonNode : <Button icon={<BtnIcon type="icon-lajitong" />} {...buttonConfig}>{btnText}</Button>
                }
            </div>
            <ComModal
                title={contentTitle}
                visible={visible}
                onCancel={() => setVisible(false)}
                onOk={handleOk}
            >
                <div>
                    {
                        children !== undefined
                            ? children
                            : (
                                <div style={{padding: "0 16px", margin: "20px 0 40px 0"}}>
                                    {content}
                                </div>
                            )
                    }
                </div>
            </ComModal>
        </>
    );
};

export default DeleteModel;
