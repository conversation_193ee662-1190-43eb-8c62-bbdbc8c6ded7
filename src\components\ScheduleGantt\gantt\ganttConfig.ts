/* eslint-disable import/no-cycle */
/* eslint-disable @typescript-eslint/camelcase */
import {gantt} from "@iworks/dhtmlx-gantt";
import moment from "moment";
import {GanttDataset, GanttTask} from "./interface";
import {dateToString} from "../common/function";
import customGantt from "./CustomGantt";
import {initEvent, initExtInlineEditorEvents} from "./eventUtils";
import {isScaleWorkTime} from "./calendarUtils";
import ganttManager from "./ganttManager";
import {getGanttColumnMap, getGanttEditorMap, updateColumnsConfig} from "./columnUtils";

export const globalInit = () => {
    console.log("globalInit");
    // 配置中文
    gantt.i18n.setLocale("cn");
    // 配置甘特图以自动扩展时间范围以适合所有显示的任务
    gantt.config.fit_tasks = true;
    // grid和timeline单独配置水平滚动条
    // gantt.config.layout = {
    //     css: "gantt_container",
    //     rows: [
    //         {
    //             cols: [
    //                 {view: "grid", scrollX: "gridScroll", scrollable: true, scrollY: "scrollVer"},
    //                 // horizontal scrollbar for the grid
    //                 {view: "scrollbar", id: "gridScroll", group: "horizontal"}
    //             ],
    //             gravity: 5
    //         },
    //         {resizer: true, width: 1},
    //         {
    //             cols: [
    //                 {view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
    //                 // horizontal scrollbar for the timeline
    //                 {view: "scrollbar", id: "scrollHor", group: "horizontal"}
    //             ],
    //             gravity: 3
    //         },
    //         {view: "scrollbar", id: "scrollVer"}
    //     ]
    // };

    gantt.config.readonly = true;
    // 配置行高
    gantt.config.task_height = 16;
    gantt.config.row_height = 40;
    gantt.config.scale_height = 40;
    gantt.config.min_column_width = 30;

    // 支持拖动上移下移，升级降级
    // gantt.config.order_branch = "marker";
    // gantt.config.order_branch_free = true;
    // 支持project与task的自动转换
    gantt.config.auto_types = true;
    // 支持工作日历设置
    gantt.config.work_time = true;
    gantt.config.skip_off_time = false;
    gantt.config.correct_work_time = true;

    gantt.plugins({
        auto_scheduling: true,
        tooltip: true,
        critical_path: true
    });
    gantt.config.auto_scheduling = true;
    // auto_scheduling禁用任务使用约束时间
    gantt.config.auto_scheduling_compatibility = true;
    // auto_scheduling任务将始终重新计划为最早的日期，必须配合auto_scheduling_compatibility使用
    gantt.config.auto_scheduling_strict = true;

    gantt.config.drag_timeline = null;

    gantt.config.drag_progress = false;
    gantt.locale.labels.message_ok = "确定";
    gantt.locale.labels.message_cancel = "取消";

    gantt.ext.zoom.init({
        levels: [
            {
                name: "hour",
                min_column_width: 30,
                scales: [
                    {unit: "day", step: 1, format: "%Y年%n月%j日 周%D"},
                    {unit: "hour", step: 1, format: "%G"}
                ]
            },
            {
                name: "day",
                min_column_width: 40,
                scales: [
                    {unit: "month", step: 1, format: "%Y年%n月"},
                    {unit: "day", step: 1, format: "%j%D"}
                ]
            },
            {
                name: "week",
                min_column_width: 40,
                scales: [
                    {unit: "month", step: 1, format: "%Y年%n月"},
                    {
                        unit: "week",
                        step: 1,
                        format(date: Date) {
                            const dateToStr = gantt.date.date_to_str("%n.%d");
                            return `${dateToStr(date)}`;
                        }
                    }
                ]
            },
            {
                name: "month",
                min_column_width: 30,
                scales: [
                    {unit: "year", step: 1, format: "%Y年"},
                    {unit: "month", step: 1, format: "%n月"}
                ]
            },
            {
                name: "year",
                min_column_width: 50,
                scales: [{unit: "year", step: 1, format: "%Y年"}]
            }
        ],
        useKey: "ctrlKey",
        trigger: "wheel",
        element() {
            return document.querySelector(".gantt_task");
        }
    });

    // 设置timeline双休日背景色
    gantt.templates.timeline_cell_class = function timelineCellClass(task: GanttTask, date: Date) {
        const calendar = gantt.getTaskCalendar(task);
        const isWorkTime = calendar.isWorkTime(date) === true || isScaleWorkTime(task, date);
        return isWorkTime ? "" : "weekend";
    };

    gantt.templates.tooltip_text = (_start: Date, _end: Date, task: GanttTask) => {
        const {durationFormatter, linksFormatter} = ganttManager;
        const links = task.$target;
        const labels = [];
        if (links !== null && links !== undefined) {
            for (let i = 0; i < links.length; i++) {
                const link = gantt.getLink(links[i]);
                labels.push(linksFormatter.format(link));
            }
        }
        let result = "";

        const startDateString = dateToString(task.start_date, false);
        const endDateString = dateToString(task.end_date, false);
        const duration = durationFormatter.format(task.duration);

        if (task.type === gantt.config.types.milestone) {
            result = `<b>任务名称:</b> ${task.text}
                <br/><b>里程碑时间:</b> ${startDateString}`;
        } else {
            result = `<b>任务名称:</b> ${task.text}
                <br/><b>计划开始时间:</b> ${startDateString}
                <br/><b>计划完成时间:</b> ${endDateString}
                <br/><b>计划工期:</b> ${duration}`;
        }
        if (ganttManager.fromType !== "actual" && task.type !== gantt.config.types.milestone) {
            const {request_start_date, request_end_date, request_duration} = task;
            if (request_start_date !== undefined && request_end_date !== undefined && request_duration !== undefined) {
                const requestStartDateString = dateToString(request_start_date, false);
                const requestEndDateString = dateToString(request_end_date, false);
                const requestDuration = durationFormatter.format(request_duration);
                result = `${result}
                        <br/><b>要求开始时间:</b> ${requestStartDateString}
                        <br/><b>要求完成时间:</b> ${requestEndDateString}
                        <br/><b>要求工期:</b> ${requestDuration}`;
            }
        }
        return labels.length === 0 ? result : `${result}<br/><b>前置任务:</b>${labels.join(", ")}`;
    };

    // 结束时间格式化
    gantt.templates.task_end_date = (date: Date) => moment(date).format("YYYY年MM月DD日");

    customGantt();

    initEvent();

    const ganttColumnMap = getGanttColumnMap();
    gantt.config.columns = [ganttColumnMap.get("wbs")];

    // 给html head添加link
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = "https://cdn.bootcdn.net/ajax/libs/font-awesome/4.3.0/css/font-awesome.css";
    document.head.appendChild(link);
};

export const updateGanttConfig = () => {
    // console.log("updateGanttConfig ganttManager", ganttManager);
    updateColumnsConfig();
    const {fromType} = ganttManager;
    const editorMap = getGanttEditorMap();
    // 根据当前计划类型（计划/实际）和是否是wbs设置横道图编辑配置
    const columnTaskName = gantt.getGridColumn("text");
    gantt.config.drag_move = fromType === "plan";
    gantt.config.drag_resize = fromType === "plan";
    gantt.config.drag_links = fromType === "plan";
    gantt.config.order_branch = fromType === "plan" ? "marker" : false;
    gantt.config.order_branch_free = fromType === "plan";
    if (columnTaskName !== null && fromType === "plan") {
        columnTaskName.editor = editorMap.get("text");
    }
    if (ganttManager.isWbs === true) {
        // wbs任务不允许上移下移改名
        gantt.config.order_branch = false;
        gantt.config.order_branch_free = false;
        if (columnTaskName !== null) {
            columnTaskName.editor = null;
        }
    }
    gantt.render();
};

export const setGanttCriticalPath = (criticalPath: boolean) => {
    gantt.config.highlight_critical_path = criticalPath;
    gantt.render();
};

// 设置横道图是否可编辑
export const setGanttEditable = (editable: boolean) => {
    gantt.config.readonly = !editable;
};

// 添加实际时间条，由于该方法会在gantt.init()后重置，所以需要每次都在gantt.init()后重新添加
// resetLayout之后也需要调用一次
export const afterGanttInit = () => {
    // console.log("afterGanttInit");
    gantt.addTaskLayer((task: GanttTask) => {
        if ((task.actual_start !== undefined) && (task.actual_end !== undefined)) {
            const sizes = gantt.getTaskPosition(task, task.actual_start, task.actual_end);
            const el = document.createElement("div");
            el.className = "baseline";
            el.style.left = `${sizes.left}px`;
            el.style.width = `${sizes.width}px`;
            el.style.top = `${(sizes.top as number) + (gantt.config.task_height as number) + 13}px`;
            return el;
        }
        return false;
    });
    // gantt.addTaskLayer({
    //     renderer: (task: GanttTask) => {
    //         if ((task.custom_start_date !== undefined) && (task.custom_end_date !== undefined)) {
    //             const sizes = gantt.getTaskPosition(task, task.custom_start_date, task.custom_end_date);
    //             const el = document.createElement("div");
    //             el.className = "custom-gantt-project-baseline";
    //             el.style.left = `${sizes.left}px`;
    //             el.style.width = `${sizes.width}px`;
    //             el.style.top = `${(sizes.top as number) + 10}px`;
    //             return el;
    //         }
    //         return false;
    //     },
    // });

    initExtInlineEditorEvents();
};

export const showGanttTimeLine = () => {
    // console.log("showGanttTimeLine");
    gantt.config.layout = {
        css: "gantt_container",
        cols: [
            {
                rows: [
                    {view: "grid", scrollX: "gridScroll", scrollable: true, scrollY: "scrollVer"},
                    // horizontal scrollbar for the grid
                    {view: "scrollbar", id: "gridScroll", group: "horizontal"}
                ],
                gravity: 5
            },
            {resizer: true, width: 1},
            {
                rows: [
                    {view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
                    // horizontal scrollbar for the timeline
                    {view: "scrollbar", id: "scrollHor", group: "horizontal"}
                ],
                gravity: 3
            },
            {view: "scrollbar", id: "scrollVer"}
        ]
    };
    gantt.resetLayout();
    afterGanttInit();
    gantt.render();
};


export const hideGanttTimeLine = (config: {scrollable: boolean}) => {
    const {scrollable} = config;
    // console.log("hideGanttTimeLine scrollable", scrollable);
    gantt.config.layout = {
        css: "gantt_container_hide_timeline",
        minWidth: 1200,
        cols: [
            {
                minWidth: 1200,
                rows: [
                    {view: "grid", scrollX: "gridScroll", scrollable, scrollY: "scrollVer"},
                    // horizontal scrollbar for the grid
                    {view: "scrollbar", id: "gridScroll", group: "horizontal"},
                ],
                gravity: 5
            },
            {
                width: 1,
                rows: [
                    {view: "timeline", scrollX: "scrollHor", scrollY: "scrollVer"},
                    // horizontal scrollbar for the timeline
                    {view: "scrollbar", id: "scrollHor", group: "horizontal"}
                ],
            },
            {view: "scrollbar", id: "scrollVer"}
        ],
    };
    gantt.resetLayout();
    afterGanttInit();
    gantt.render();
};

export const setShowGanttTimeLine = (show: boolean, _auto = false) => {
    ganttManager.isShowGantt = show;
    // console.log("setShowGanttTimeLine", show, auto);
    if (show !== true) {
        hideGanttTimeLine({scrollable: false});
        // if (auto) {
        //     hideGanttTimeLine({scrollable: false}); // 先设置一下，用于撑开列宽
        //     setTimeout(() => {
        //         hideGanttTimeLine({scrollable: true});
        //     }, 3000);
        // } else {
        //     hideGanttTimeLine({scrollable: true});
        // }
    } else {
        showGanttTimeLine();
    }
};

export const parseGanttData = (ganttData: GanttDataset) => {
    ganttManager.requestTaskCacheMap = new Map(); // 解析时清空上级任务缓存
    ganttManager.updateGanttConfigWithWbs(ganttData);
    gantt.parse(ganttData);
};
