import {PlanChangeStatusType} from "../Preparation/type";

/* 计划类型 */
export const planTypeList = [
    {label: "总计划", value: "MASTER"},
    {label: "年度计划", value: "YEAR"},
    {label: "季度计划", value: "QUARTER"},
    {label: "月度计划", value: "MONTH"},
    {label: "周计划", value: "WEEK"}
];
export const planTypeListRadio = [
    {label: "全部", value: null},
    {label: "总", value: "MASTER"},
    {label: "年", value: "YEAR"},
    {label: "季", value: "QUARTER"},
    {label: "月", value: "MONTH"},
    {label: "周", value: "WEEK"}
];
/* 计划状态 */
export const planApproveStatusList = [
    // {label: "无需审批", value: 0},
    // {label: "未申报", value: 1},
    {label: "审批中", value: 2},
    {label: "审批完成", value: 3}
];

export interface GetPlanApprovalPageParam {
    /* 计划名称模糊搜索 */
    nameKey?: string;
    /* 组织节点ID */
    nodeId?: string;
    /* 组织节点类型 */
    nodeType?: number;
    /* 当前页，从1开始 */
    pageNum: number;
    /* 页大小 */
    pageSize: number;
    /* 审批处理类别：0-全部，1-我发起，2-待处理，3-已处理，4-抄送我的 */
    processType?: number;
    /* 计划状态 */
    status?: number;
    /* 计划类型 */
    type?: string;
    // 计划变更状态：未变更-Unchanged,变更中-Changing,已变更-Changed
    changeStatus?: string;
}

export interface PlanApprovalPageList {
    // 流程状态（进行中：in-progress，撤销：canceled，退回：backed，已通过：passed）
    processStatus: string;
    /* 审批ID */
    approvalId: string;
    /* 计划周期 */
    cycle: string;
    /* 计划完成日期 */
    endDate: string;
    /* 计划ID */
    id: string;
    /* 计划名称 */
    name: string;
    /* 计划开始日期 */
    startDate: string;
    /* 计划状态：0-无需审批，1-未申报，2-已申报，3-审批完成 */
    status: number;
    /* 计划类型:MASTER-总计划，YEAR-年度计划，QUARTER-季度计划， MONTH-月度计划，WEEK-周计划 */
    type: string;
    /* 编制时间 */
    updateAt: string;
    /* 编制人 */
    updateBy: string;
    /* 变更状态：未变更-Unchanged,已变更-Changed */
    changeStatus: PlanChangeStatusType;
    /** 审批状态：1-无审批，2-审批中，3-审批完成，4-审批删除（即审批撤回），5-退回到发起人节点 */
    approvalStatus: number;
}

export interface GetPlanApprovalPageReturn {
    items: PlanApprovalPageList[];
    total: number;
}

/* 具体注释可以参考 GetPlanApprovalPageParam */
export interface PlanApprovalExportParam {
    changeStatus?: PlanChangeStatusType;
    deptId?: string;
    ids?: string[];
    nameKey?: string;
    nodeId?: string;
    nodeType?: number;
    pageNum?: number;
    pageSize?: number;
    /* 审批处理类别：0-全部，1-我发起，2-待处理，3-已处理，4-抄送我的 */
    processType?: number;
    status?: number;
    type?: string;
}
