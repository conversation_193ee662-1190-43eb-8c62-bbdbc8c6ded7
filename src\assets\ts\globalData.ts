import {LabeledValue} from "antd/lib/select";
// import {keyBy, mapValues} from "lodash-es";


// export const getServer = () => {
//     // eslint-disable-next-line no-underscore-dangle
//     const {baseUrl, basePds, shellDownUrl} = window.__IWorksConfig__;
//     const list = [
//         {serverName: "baseUrl", serverURL: baseUrl},
//         {serverName: "basePds", serverURL: basePds},
//         {serverName: "shellDownUrl", serverURL: shellDownUrl}
//     ];
//     // list.push({serverName: "lbmotor", serverURL: `${window.location.origin}/motorview`});
//     list.push({serverName: "lbmotor", serverURL: `${window.location.href.split("#")[0]}motorview`});
//     const serviceList = mapValues(keyBy(list, "serverName"), "serverURL");
//     return serviceList;
// };

// eslint-disable-next-line import/prefer-default-export
export const motorStatusList: LabeledValue[] = [
    {value: -2, label: "未处理"},
    {value: 1, label: "待处理"},
    {value: 2, label: "处理中"},
    {value: 3, label: "处理成功"},
    {value: 4, label: "处理失败"},
    {value: 5, label: "取消处理"},
];

export enum NodeTypeEnum {
    "公司" = 1,
    "项目" = 2,
    "子项目" = 3
}
