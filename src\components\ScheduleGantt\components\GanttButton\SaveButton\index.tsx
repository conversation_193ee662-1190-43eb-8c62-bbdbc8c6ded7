import React, {useCallback, useContext} from "react";
import {Button, message, Modal} from "antd";
import ganttManager from "../../../gantt/ganttManager";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import {getChangeInfluence} from "../../../../../assets/ts/utils";
import {getTotalDuration} from "../../../gantt/taskUtils";
import {ApprovalStatus} from "../../../../../api/Preparation/type";

const SaveButton = () => {
    const cls = useStyles();
    const {
        fromType,
        planInfo,
        parentPlanInfo,
        hasParentPlan,
        loading,
        checkoutStatus,
        saveLoading,
        saveMethodRef
    } = useContext(EditorContext);

    const handleSave = useCallback(async () => {
        if (saveMethodRef.current instanceof Function) {
            const res = await saveMethodRef.current(1);
            if (res === true) {
                message.success({content: "保存成功", key: "savePlan"});
            }
        }
    }, [saveMethodRef]);

    // 变更影响
    const handleConfirmTotalChange = useCallback(() => {
        const totalDuration = getTotalDuration();
        if (planInfo.unchangedTotalDuration !== null && totalDuration !== planInfo.unchangedTotalDuration) {
            Modal.confirm({
                title: "修改的内容会影响总工期，确定保存？",
                content: getChangeInfluence(totalDuration, planInfo.unchangedTotalDuration),
                okText: "确定",
                cancelText: "取消",
                onOk: () => {
                    handleSave();
                }
            });
        } else {
            handleSave();
        }
    }, [planInfo.unchangedTotalDuration, handleSave]);

    // 上下级计划冲突
    const handleConfirmConfict = useCallback(() => {
        if (hasParentPlan) {
            const isConflict = ganttManager.checkTaskRequestDateConfict();
            if (isConflict) {
                Modal.confirm({
                    title: `标红内容与${parentPlanInfo.name}进度计划冲突，确定保存？`,
                    okText: "确定",
                    cancelText: "取消",
                    onOk: () => {
                        handleConfirmTotalChange();
                    }
                });
            } else {
                handleConfirmTotalChange();
            }
        } else {
            handleConfirmTotalChange();
        }
    }, [parentPlanInfo.name, handleConfirmTotalChange, hasParentPlan]);

    const handleClick = useCallback(() => {
        if (loading === true) {
            message.info({content: "正在签出，请稍后再试"});
            return;
        }
        if (fromType === "plan") {
            handleConfirmConfict();
        } else {
            handleSave();
        }
    }, [fromType, handleConfirmConfict, handleSave, loading]);

    if (checkoutStatus === false) {
        return null;
    }

    if (fromType === "plan" && (planInfo.approvalStatus === ApprovalStatus.APPROVING || planInfo.approvalStatus === ApprovalStatus.APPROVAL_COMPLETED)) {
        return null;
    }

    return (
        <Button
            className={cls.button}
            type="primary"
            size="large"
            onClick={handleClick}
            loading={saveLoading}
        >
            保存
        </Button>
    );
};

export default SaveButton;
