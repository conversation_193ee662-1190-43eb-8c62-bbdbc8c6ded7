import React from "react";
import {Drawer} from "antd";
import useStyles from "./style";
import ProcessTmplContent from "./ProcessTmplContent";

interface ProcessTmplManageProps {
    visible?: boolean;
    onClose?: () => void;
}

const ProcessTmplManage = (props: ProcessTmplManageProps) => {
    const cls = useStyles();
    const {visible, onClose} = props;

    return (
        <>
            <Drawer
                title="工序管理"
                visible={visible}
                onClose={onClose}
                width={720}
                bodyStyle={{padding: 16}}
                footer={null}
                className={cls.drawerBox}
                destroyOnClose
                maskClosable={false}
            >
                <ProcessTmplContent onClose={onClose} />
            </Drawer>
        </>
    );
};

export default ProcessTmplManage;
