import {AnyAction} from "redux";
import BimConstructionSandDataInitialize from "../../views/sandTableManage/SandTable/sandBox/handler/BimConstructionSandDataInitialize";
import BimSandDataInitialize from "../../views/sandTableManage/SandTable/sandBox/handler/BimSandDataInitialize";
import ActionTypes from "./actionTypes";

export interface StateType {
    analyzedData: {
        isReady: boolean; // 是否数据准备完成
        handler: BimSandDataInitialize | undefined;// 沙盘播放需要的前置缓存数据处理
    };
    constructionAnalyzedData: {
        isReady: boolean; // 是否数据准备完成
        handler: BimConstructionSandDataInitialize | undefined;// 沙盘播放需要的前置缓存数据处理
    };
}

export const initState: StateType = {
    analyzedData: {
        isReady: false,
        handler: undefined
    },
    constructionAnalyzedData: {
        isReady: false,
        handler: undefined
    }
};

const sandAnalyzedDataReducer = (state = initState, action: AnyAction): StateType => {
    switch (action.type) {
        case ActionTypes.SET_SAND_ANALYZEDDATA:
            return {
                ...state,
                analyzedData: action.payload
            };
        case ActionTypes.SET_CONSTRUCTION_SAND_ANALYZEDDATA:
            return {
                ...state,
                constructionAnalyzedData: action.payload
            };
        default:
            return state;
    }
};

export default sandAnalyzedDataReducer;
