import {PageInfo} from "../common.type";

export interface GetVectorModelListParams extends PageInfo {
    modelName?: string; // 模型名称 ,
    status?: number; // 状态
    deptIds?: string[]; // 节点id
    // unitId?: string; // 节点id
    // unitType?: number; // 节点类型
}
export interface GetVectorModelListResContent {
    deptId: string;
    deptName: string; // 项目部name
    nodeName: string; // 项目部name
    nodeType: number;
    id: string;
    modelName: string; // 模型名称
    ppid: number;
    projGuid: string;
    status: number;
    updateAt: number; // 更新时间【时间戳，毫秒】
    updateBy: string; // 更新人
    pdfKey: string; // 文件uuid
}

export interface GetVectorModelListRes {
    content: GetVectorModelListResContent[];
    total: number;
}

export type GetBimModelListParams = GetVectorModelListParams;

export type GetBimModelListRes = GetVectorModelListRes;

export interface GetGisModelListParams {
    currentPage: number;
    motorSubType: string; // 模型类型
    name: string;
    pageSize: number;
    status: number;
}

export interface GetGisModelListResContent {
    id: string;
    motorSubType: string;
    name: string;
    status: number;
    updateAt: number;
    updateBy: string;
    projGuid: string;
}
export interface GetGisModelListRes {
    content: GetGisModelListResContent[];
    total: number;
}

export interface PostGisModelAddParams {
    linkUrl: string;
    motorSubType: string;
    name: string;
}

export interface DelGisModelParams {
    ids: string[];
}
