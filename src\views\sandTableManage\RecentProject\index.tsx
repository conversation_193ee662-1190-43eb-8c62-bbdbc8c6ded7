import {Tabs} from "antd";
import React, {memo, useEffect, useState} from "react";
import {useSelector} from "react-redux";
import {useHistory} from "react-router-dom";
import {RootState} from "../../../store/rootReducer";
import PageTitle from "../SandTable/pageTitle";
import RecentOpendProject from "./recentOpendProject";
import RecentUploadedProject from "./recentUploadedProject";
import useModelBimStyle from "./style";

const RecentProject = () => {
    const cls = useModelBimStyle();
    const history = useHistory();
    const {lastSelectedMenu} = useSelector((state: RootState) => state.commonData);
    const [currentTab, setCurrentTab] = useState("recentOpendProject");
    const [tabList, setTabList] = useState<{ label: string; key: string }[]>([]);

    useEffect(() => {
        const tempList = [
            {
                key: "recentOpendProject",
                label: "最近打开"
            },
            {
                key: "recentUploadedProject",
                label: "最新上传"
            }
        ];

        setTabList(tempList);
        if (tempList.length > 0) {
            setCurrentTab(tempList[0].key);
        } else {
            setCurrentTab("");
        }
    }, [history, lastSelectedMenu]);

    return (
        <>
            <PageTitle
                title="进度管理"
            />
            <Tabs defaultActiveKey="recentOpendProject" onChange={(activeKey) => setCurrentTab(activeKey)} activeKey={currentTab} className={cls.tabs}>
                {
                    tabList.map((item) => (
                        <Tabs.TabPane
                            className={item.key === currentTab ? `${cls.tabsItem} active` : cls.tabsItem}
                            tab={item.label}
                            key={item.key}
                        />
                    ))
                }
            </Tabs>
            <div style={{backgroundColor: "white", height: "calc( 100vh - 185px )"}}>
                {currentTab === "recentOpendProject"
                    ? <RecentOpendProject />
                    : <RecentUploadedProject />}
            </div>
        </>
    );
};

export default memo(RecentProject);
