import React, {useContext} from "react";
import {But<PERSON>} from "antd";
import {useSelector} from "react-redux";
import {exportToExcel, exportToMSProject} from "../../../gantt/importExportUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";
import Color from "../../../../../assets/css/Color";
import EditorContext from "../../../views/GanttEditor/context";
import PermissionCode from "../../../../PermissionCode";
import {RootState} from "../../../../../store/rootReducer";
import ComModal from "../../../../ComModal";

const ExportButton = () => {
    const cls = useStyles();
    const {planInfo} = useContext(EditorContext);

    const {orgInfo, curSectionInfo} = useSelector((store: RootState) => store.commonData);
    const [visible, setVisible] = React.useState(false);

    const exportExcelBtnClick = React.useCallback(() => {
        setVisible(false);
        // if (authCodeList.find((item) => item === "92008005") !== undefined) {
        exportToExcel({deptName: orgInfo.orgName,
            sectionName: curSectionInfo?.nodeName,
            planName: planInfo.name,
            excelName: `${orgInfo.orgName}-${curSectionInfo?.nodeName}-${planInfo.name}`});
        // } else {
        //     message.info({content: "没有导出excel权限！"});
        // }
        // addLogInfoAsync(dId, deptName, "导出计划Excel");
    }, [curSectionInfo, orgInfo.orgName, planInfo.name]);



    const exportButtonClick = React.useCallback(() => {
        setVisible(true);
    }, []);

    return (
        <>
            <PermissionCode
                authcode="ProjectPlatform-Plan-Progress-Establishment:export"
            >
                <Button
                    className={cls.textButton}
                    type="text"
                    icon={<MyIconFont type="icon-daochu" fontSize={18} />}
                    style={{color: Color["primary-1"]}}
                    onClick={exportButtonClick}
                >
                导出清单
                </Button>
            </PermissionCode>
            <ComModal
                title="提示"
                onOk={exportExcelBtnClick}
                visible={visible}
                onCancel={() => setVisible(false)}
            >
                <div style={{padding: 24}}>
                    <p>当前导出台账格式为：EXCEL。</p>
                    <p>如需打印，请修改纸张信息为：A3，横向打印。</p>
                </div>
            </ComModal>
        </>
    );
};

export default ExportButton;
