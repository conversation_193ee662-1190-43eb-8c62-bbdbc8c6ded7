import {createUseStyles} from "react-jss";
import Color from "../../../../assets/css/Color";

const useStyles = createUseStyles({
    specFormBox: {
        "& .ant-form-item-label": {
            width: 100,
            textAlign: "right"
        }
    },
    desBox: {
        "& .ant-descriptions-item-label": {
            color: "#606266",
            width: "100px",
            display: "block",
            textAlign: "right"
        },
        "& .ant-descriptions-item-content": {
            color: "#303133"
        }
    },
    changeInfoBox: {
        backgroundColor: "#F5F5F6",
        border: "1px solid #E1E2E5",
        padding: "24px",
    },
    infoRowLeft: {
        width: 120,
        marginRight: 20
    },
    infoRowSeparateLine: {
        width: "100%",
        height: 1,
        border: "1px dashed #E1E2E5",
        margin: "24px 0",
    },
    downloadBtn: {
        "&.ant-btn-link": {
            color: "#1F54C5",
            padding: "4px 0"
        },
        "&.ant-btn-link[disabled]": {
            color: "rgba(0, 0, 0, 0.25)",
        }
    },
    visibilityHidden: {
        "& .ant-collapse-content-hidden": {
            display: "block",
            visibility: "hidden",
            height: 0,
        }
    },
    tipIcon: {
        color: Color["yellow-1"],
        marginRight: 8
    },
    tipTitle: {
        color: Color["text-2"],
        fontWeight: "bold",
        fontSize: 16,
    },
    tipContent: {
        color: Color["text-2"],
        fontSize: 14,
    },
});

export default useStyles;
