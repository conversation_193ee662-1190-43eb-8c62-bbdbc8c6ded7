import React from "react";
import ScheduleGantt from "../../../components/ScheduleGantt";
import {ScheduleEditorProps} from "../../../components/ScheduleGantt/views/GanttEditor";

const ScheduleEdit: React.FC<ScheduleEditorProps> = (props) => (
    <div style={{height: "100%", padding: "24px"}}>
        <ScheduleGantt
            {...props}
            headerRightButtons={[
                "import",
                "export",
                "launchApproval",
                "approvalInfo",
                "edit",
                "save",
            ]}
            toolLeftButtons={[
                "columnSetting",
                "planInfo",
                "calendar",
                "relatePlan",
                "insert",
                "delete",
                "moveUp",
                "moveDown",
                "upgrade",
                "degrade",
                "showGantt",
                "criticalPath",
                "timeScale",
            ]}
        />
    </div>
);

export default ScheduleEdit;
