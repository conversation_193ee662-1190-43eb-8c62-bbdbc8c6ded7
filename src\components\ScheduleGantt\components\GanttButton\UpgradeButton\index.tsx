import React, {useContext} from "react";
import {Button} from "antd";
import EditorContext from "../../../views/GanttEditor/context";
import {upgradeTask} from "../../../gantt/taskUtils";
import useStyles from "../styles";
import MyIconFont from "../../../../MyIconFont";

const UpgradeButton = () => {
    const cls = useStyles();
    const {isWbs, checkoutStatus} = useContext(EditorContext);

    if (checkoutStatus === false) {
        return null;
    }

    return (
        <Button
            className={cls.textButton}
            type="text"
            icon={<MyIconFont type="icon-shengji" fontSize={18} />}
            onClick={upgradeTask}
            disabled={isWbs}
        >
            升级
        </Button>
    );
};

export default UpgradeButton;
