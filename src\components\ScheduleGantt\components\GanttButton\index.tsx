import React, {ReactElement} from "react";
import EditButton from "./EditButton";
import SaveButton from "./SaveButton";
import PlanInfoButton from "./PlanInfoButton";
import CalendarButton from "./CalendarButton";
import CriticalPathButton from "./CriticalPathButton";
import DegradeButton from "./DegradeButton";
import UpgradeButton from "./UpgradeButton";
import MoveDownButton from "./MoveDownButton";
import MoveUpButton from "./MoveUpButton";
import InsertButton from "./InsertButton";
import ImportButton from "./ImportButton";
import ExportButton from "./ExportButton";
import RelatePhotoButton from "./RelatePhotoButton";
import ColumnSettingButton from "./ColumnSettingButton";
import DeleteButton from "./DeleteButton";
import RelateBimButton from "./RelateBimButton";
import TimeScaleButton from "./TimeScaleButton";
import ShowGanttButton from "./ShowGanttButton";
import RelatePlanButton from "./RelatePlanButton";
import ToolDivider from "./ToolDivider";
import SyncActualDataButton from "./SyncActualDataButton";
import LaunchApprovalButton from "./LaunchApprovalButton";
import ApprovalInfoButton from "./ApprovalInfoButton";

export type GanttButtonType = "divider" // 分割线
    | "edit" // 编辑
    | "save" // 保存
    | "launchApproval" // 发起审批
    | "approvalInfo" // 审批信息
    | "planInfo" // 计划信息
    | "calendar" // 工作日历
    | "syncActualData" // 同步质量验评
    | "insert" // 插入
    | "delete" // 删除
    | "moveUp" // 上移
    | "moveDown" // 下移
    | "upgrade" // 升级
    | "degrade" // 降级
    | "import" // 导入
    | "export" // 导出
    | "relatePlan" // 关联上级计划
    | "relateBim" // 关联BIM
    | "relatePhoto" // 关联照片
    | "columnSetting" // 列设置
    | "showGantt" // 显示横道图
    | "criticalPath" // 关键路径
    | "timeScale"; // 时间刻度

interface GanttButtonProps {
    button: ReactElement | GanttButtonType;
}

const GanttButton = (props: GanttButtonProps) => {
    const {button} = props;

    switch (button) {
        case "edit":
            return <EditButton />;
        case "save":
            return <SaveButton />;
        case "launchApproval":
            return <LaunchApprovalButton />;
        case "approvalInfo":
            return <ApprovalInfoButton />;
        case "planInfo":
            return <PlanInfoButton />;
        case "calendar":
            return <CalendarButton />;
        case "syncActualData":
            return <SyncActualDataButton />;
        case "insert":
            return <InsertButton />;
        case "moveUp":
            return <MoveUpButton />;
        case "moveDown":
            return <MoveDownButton />;
        case "upgrade":
            return <UpgradeButton />;
        case "degrade":
            return <DegradeButton />;
        case "criticalPath":
            return <CriticalPathButton />;
        case "import":
            return <ImportButton />;
        case "export":
            return <ExportButton />;
        case "relatePlan":
            return <RelatePlanButton />;
        case "relateBim":
            return <RelateBimButton />;
        case "relatePhoto":
            return <RelatePhotoButton />;
        case "columnSetting":
            return <ColumnSettingButton />;
        case "delete":
            return <DeleteButton />;
        case "timeScale":
            return <TimeScaleButton />;
        case "showGantt":
            return <ShowGanttButton />;
        case "divider":
            return <ToolDivider />;
        default:
            return <>{button}</>;
    }
};

export default GanttButton;
