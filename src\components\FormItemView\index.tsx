import {Col, ColProps, FormItemProps, Form} from "antd";
import React, {ReactNode} from "react";
import {useViewFormStyle} from "../../assets/ts/form";
import FileBox, {FileBoxProps} from "../FileBox";
import FormText, {FormTextProps} from "./FormText";
import FormTime, {FormTimeProps} from "./FormTime";
import FormSelect, {FormSelectProps} from "./FormSelect";
import FormPerson, {FormPersonProps} from "./FormPerson";
// import FormSelectPersonAndTeamBox, {FormSelectPersonAndTeamBoxProps} from "../PersonSelect/FormSelectPersonAndTeamBox";
import FormWBS, {FormWBSProps} from "./FormWBS";
import FormCheckSubOption, {FormCheckSubOptionProps} from "./FormCheckSubOption";
import FormRenderFun, {FormRenderFunProps} from "./FormRenderFun";
import FormWbsEbsView from "../FormWbsEbs/FormWbsEbsView";
import {WbsEbsNodeType} from "../FormWbsEbs/data";
import FormViewArr, {FormViewArrProps} from "./FormViewArr";

const {Item} = Form;

export interface FormItemViewProps {
    key?: string;
    type?: "select" | "text" | "time" | "fileBox" | "person" | "personTeam" | "WBS" | "checkSubOption" | "custom" | "fun" | "wbsEbs" | "formArr";
    typeConfig?: unknown;
    itemConfig?: FormItemProps;
    colConfig?: ColProps;
    value?: unknown;
    render?: ReactNode;
}

const defaultDownFileBoxType: FileBoxProps = {
    isEditName: false,
    isDelete: false,
    isUpload: false,
    isDownload: true,
};

const FormItemView = (props: FormItemViewProps) => {
    const cls = useViewFormStyle();
    const {
        type = "text",
        typeConfig = {},
        itemConfig = {},
        colConfig = {},
        value = "",
        render
    } = props;

    const renderText = (): string | ReactNode => {
        if (type === "text") {
            return <FormText value={value as string} {...typeConfig as FormTextProps} />;
        }
        if (type === "time") {
            return <FormTime value={value as number} {...typeConfig as FormTimeProps} />;
        }
        if (type === "select") {
            return <FormSelect value={value as string} {...typeConfig as FormSelectProps} />;
        }
        if (type === "fileBox") {
            return <FileBox value={value as FileBoxProps["value"]} {...defaultDownFileBoxType} {...typeConfig as FileBoxProps} />;
        }
        if (type === "person") {
            return <FormPerson value={value as FormPersonProps["value"]} {...typeConfig as FormPersonProps} />;
        }
        if (type === "checkSubOption") {
            return <FormCheckSubOption value={value as FormCheckSubOptionProps["value"]} {...typeConfig as FormCheckSubOptionProps} />;
        }
        if (type === "personTeam") {
            return <div>wefew</div>;
        }
        if (type === "WBS") {
            return <FormWBS value={value as FormWBSProps["value"]} {...typeConfig as FormWBSProps} />;
        }
        if (type === "custom") {
            return render;
        }
        if (type === "fun") {
            return <FormRenderFun value={value} render={render as unknown as FormRenderFunProps["render"]} />;
        }
        if (type === "formArr") {
            return <FormViewArr value={value as FormViewArrProps["value"]} {...typeConfig as FormViewArrProps} />;
        }
        return "";
    };

    const itemConfigData = () => {
        const fullWidthCol = ["fileBox", "person", "personTeam"];
        if (fullWidthCol.includes(type)) {
            return {wrapperCol: {span: 24}, ...itemConfig};
        }
        return {...itemConfig};
    };

    if (type === "wbsEbs") {
        return (
            <Col {...colConfig}>
                <Item className={cls.item} style={{marginBottom: 0}} {...itemConfigData()} labelCol={{flex: "none"}}>
                    <FormWbsEbsView value={value as WbsEbsNodeType} itemConfig={itemConfig} />
                </Item>
            </Col>
        );
    }

    return (
        <Col {...colConfig}>
            <Item className={cls.item} style={{marginBottom: 0}} {...itemConfigData()} labelCol={{flex: "none"}}>
                {renderText()}
            </Item>
        </Col>
    );
};

export default FormItemView;
