import React, {FC, memo} from "react";
import {useSelector} from "react-redux";
import {RootState} from "../../../store/rootReducer";
import DangerCheckDetail from "../../Inspect/HiddenDangerCheck/view";

interface CombinCheckDetailProps {
    isInRectifyDetail?: boolean; // 是否在整改详情中
}

const CombinCheckDetail: FC<CombinCheckDetailProps> = (props) => {
    const {isInRectifyDetail = false} = props;
    const {relatedCheckInfo} = useSelector((state: RootState) => state.rectificationDetail);

    if (relatedCheckInfo === undefined) {
        return null;
    }

    return (
        <DangerCheckDetail
            isInRectifyDetail={isInRectifyDetail}
            showTitle={false}
            showHandleButton={false}
            id={relatedCheckInfo.id}
            back={() => null}
            edit={() => null}
        />
    );
};

export default memo(CombinCheckDetail);
