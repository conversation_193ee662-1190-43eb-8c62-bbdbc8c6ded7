/* eslint-disable max-len */
import {EditOutlined} from "@ant-design/icons";
import {Button, Col, Row, Space, Collapse, message} from "antd";
import React, {useEffect, useState, useCallback} from "react";
import moment from "moment";
import ComCollapsePanel from "../../../../components/ComCollapsePanel";
import FileBox from "../../../../components/FileBox";
import BackIcon from "../../../../components/MyIcon/BackIcon";
import {delHiddenDangerCheck, getHiddenDangerCheckDetail} from "../../../../api/hiddenDangerCheck";
import {GetHiddenDangerCheckDetailReturn} from "../../../../api/hiddenDangerCheck/type";
import {FileType, DownFileListType} from "../../../../api/common.type";
import {buildTypeList, dangerLevelList, checkTypeList, reformStatusType} from "../data";
import useStyles from "./style";
import FormItemView from "../../../../components/FormItemView";
import {packageDownload} from "../../../../api/common.api";
import TipModal from "../../../../components/TipModal";
import PermissionCode, {includesCodeStatus} from "../../../../components/PermissionCode";
import {handleGetWbsEbs2} from "../../../../components/FormWbsEbs/data";
import PreviewModal from "../../../../components/PreviewModal";

interface ViewProps {
    id: string;
    back: (type?: string) => void;
    edit: (id: string) => void;
    jumpButton?: React.ReactNode;
    isInRectifyDetail?: boolean; // 是否在整改详情中
    showTitle?: boolean;// 是否展示头部标题
    showHandleButton?: boolean;// 是否展示操作按钮
}
// const moduleType = "QUALITY";

const View = (props: ViewProps) => {
    const cls = useStyles();
    // const {orgInfo, authCodeList} = useSelector((state: RootState) => state.commonData);
    // const {curSectionInfo} = useSelector((state: RootState) => state.commonData);
    // const [type, setType] = useState<string>(moduleType);
    const {id, back, edit, jumpButton, isInRectifyDetail = false, showTitle = true, showHandleButton = true} = props;
    const [imageList, setImageList] = useState<FileType[]>([]);
    const [fileList, setFileList] = useState<FileType[]>([]);
    const [detail, setDetail] = useState<GetHiddenDangerCheckDetailReturn>();

    // useEffect(() => {
    //     if (window.location.href.includes("SECURITY")) {
    //         setType("SECURITY");
    //     }
    //     if (window.location.href.includes("QUALITY")) {
    //         setType("QUALITY");
    //     }
    // }, []);

    // const hiddenDangerCheckAutoCodeList = authCodeList.find((el) => el === "ProjectPlatform-SecurityManagement-inspect-Check-Executor");
    // 选中的检查分项Id
    // const [selectOptionList, setSelectOptionList] = useState<string[]>([]);
    const handleDelete = () => {
        delHiddenDangerCheck([id]).then((res) => {
            if (res.success) {
                message.success("删除成功！");
                back("ok");
            }
        });
    };

    const handleEdit = () => {
        if (detail !== undefined) {
            if (detail.reformStatus === reformStatusType.REFORMING) {
                message.error("整改中的隐患排查不允许更改");
                return;
            }
            if (detail.reformStatus === reformStatusType.REFORM_END) {
                message.error("已整改的隐患排查不允许更改");
                return;
            }
            edit(id);
        }
    };

    const getDetailCallback = useCallback(async () => {
        if (typeof id === "string") {
            const res = await getHiddenDangerCheckDetail(id);
            const wbsEbsData = await handleGetWbsEbs2({businessId: id});
            const {data} = res;
            if (data !== undefined && data !== null) {
                const buildTypeListFilter = buildTypeList.filter((item) => item.value === data.buildType);
                data.buildTypeName = (Array.isArray(buildTypeListFilter) && buildTypeListFilter.length > 0 ? buildTypeListFilter[0].label : "") as string;
                const checkTypeListFilter = checkTypeList.filter((item) => item.value === data.checkType);
                data.checkTypeName = (Array.isArray(checkTypeListFilter) && checkTypeListFilter.length > 0 ? checkTypeListFilter[0].label : "") as string;
                // const checkResultListFilter = checkResultList.filter((item) => item.value === data.checkResult);
                // data.checkResultName = (Array.isArray(checkResultListFilter) && checkResultListFilter.length > 0 ? checkResultListFilter[0].label : "") as string;
                const dangerLevelListFilter = dangerLevelList.filter((item) => item.value === data.dangerLevel);
                data.dangerLevelName = (Array.isArray(dangerLevelListFilter) && dangerLevelListFilter.length > 0 ? dangerLevelListFilter[0].label : "无隐患") as string;
                data.wbsEbs = wbsEbsData;
                setDetail(data);
                // setSelectOptionList(type === "SECURITY" ? data.optionList.map((el) => el.checkDescId) : data.optionList.map((el) => el.subOptionId));
                setFileList(data.attachmentFiles);
                setImageList(data.photoFiles);
            }
        }
    }, [id]);

    useEffect(() => {
        getDetailCallback();
    }, [getDetailCallback, id]);

    // 基础信息
    const BaseDetail: React.FC = useCallback(() => (
        <Row justify="start">
            <FormItemView key="orgName" itemConfig={{label: "项目"}} colConfig={{span: 8}} value={detail?.checkNodeName} />
            {/* <FormItemView key="checkNodeName" itemConfig={{label: "被检查标段"}} colConfig={{span: 8}} value={detail?.checkNodeName} /> */}
            <FormItemView key="number" itemConfig={{label: "检查编号"}} colConfig={{span: 8}} value={detail?.number} />
            <FormItemView key="buildTypeName" itemConfig={{label: "检查单位"}} colConfig={{span: 8}} value={detail?.buildTypeName} />
            <FormItemView key="checkUserName" itemConfig={{label: "检查人"}} colConfig={{span: 8}} value={detail?.checkUser.checkUserName} />
            <FormItemView key="projectCategory" itemConfig={{label: "工程类型"}} colConfig={{span: 8}} value={detail?.projectCategory} />
            <FormItemView key="checkTypeName" itemConfig={{label: "检查形式"}} colConfig={{span: 8}} value={detail?.checkTypeName} />
            <FormItemView key="optionList" type="checkSubOption" itemConfig={{label: "检查分项"}} colConfig={{span: 8}} typeConfig={{value: detail?.optionList}} />
            <FormItemView key="position" itemConfig={{label: "检查部位"}} colConfig={{span: 8}} value={detail?.position} />
            <FormItemView key="checkTime" itemConfig={{label: "检查日期"}} colConfig={{span: 8}} value={moment(detail?.checkTime).format("YYYY.MM.DD")} />
            {/* <FormItemView key="checkResultName" itemConfig={{label: "检查结果"}} colConfig={{span: 8}} value={detail?.checkResultName} /> */}
            <FormItemView key="dangerLevelName" itemConfig={{label: "隐患级别"}} colConfig={{span: 8}} value={detail?.dangerLevelName} />
            <FormItemView key="checkDesc" itemConfig={{label: "检查描述"}} colConfig={{span: 8}} value={detail?.checkDesc} />
            {/* <FormItemView
                key="lawBasis"
                itemConfig={{
                    label: (
                        <div style={{position: "relative"}}>
                            法规依据
                            <span style={{position: "absolute", left: -30, top: -4}}><RegulatoryBasisView moduleType={type} ids={selectOptionList} /></span>
                        </div>
                    )
                }}
                colConfig={{span: 8}}
                value={detail?.lawBasis}
            /> */}
            <FormItemView type="wbsEbs" colConfig={{span: 24}} value={detail?.wbsEbs} />
        </Row>
    ), [detail]);
    const [isDownloadFile, setIsDownloadFile] = useState(false);
    const [isDownloadImgs, setIsDownloadImgs] = useState(false);

    // 导出附件
    const handleExportFiles = React.useCallback((e) => {
        setIsDownloadFile(true);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        const files: DownFileListType[] = [
            {
                dir: true,
                name: `${moment(detail?.checkTime).format("YYYYMMDD")}_${detail?.position}_附件`,
                fileList: fileList.map((i) => ({fileUuid: i.fileUuid, name: i.fileName, dir: false})),
            }
        ];
        packageDownload({packDownParam: {fileList: files, packName: `${moment(detail?.checkTime).format("YYYYMMDD")}_${detail?.position}_附件.zip`}, fun: () => setIsDownloadFile(false)});
    }, [detail, fileList]);
    // 导出影像资料
    const handleExportImgs = React.useCallback((e) => {
        setIsDownloadImgs(true);
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
        const files: DownFileListType[] = [
            {
                dir: true,
                name: `${moment(detail?.checkTime).format("YYYYMMDD")}_${detail?.position}_影像资料`,
                fileList: imageList.map((i) => ({fileUuid: i.fileUuid, name: i.fileName, dir: false})),
            }
        ];
        packageDownload({packDownParam: {fileList: files, packName: `${moment(detail?.checkTime).format("YYYYMMDD")}_${detail?.position}_影像资料.zip`}, fun: () => setIsDownloadImgs(false)});
    }, [imageList, detail]);
    const disabledDownload = useCallback(() => fileList.length === 0, [fileList.length]);
    // 基础信息
    const FileDetail: React.FC = useCallback(() => (
        <Row gutter={8}>
            <Col span={12}>
                <Collapse ghost expandIconPosition="right" defaultActiveKey={["files"]}>
                    <ComCollapsePanel
                        required={false}
                        header="附件上传"
                        key="files"
                        operationBtn={
                            includesCodeStatus("Download")
                                ? <Button type="text" onClick={(e) => handleExportFiles(e)} loading={isDownloadFile} disabled={disabledDownload()}>下载全部附件</Button>
                                : ""
                        }
                    >
                        <FileBox isUpload={false} value={fileList} isDelete={false} isEditName={false} />
                    </ComCollapsePanel>
                </Collapse>
            </Col>
            <Col span={12}>
                <Collapse ghost expandIconPosition="right" defaultActiveKey={["imgs"]}>
                    <ComCollapsePanel
                        required={false}
                        header="影像资料"
                        key="imgs"
                        operationBtn={
                            includesCodeStatus("Download")
                                ? <Button type="text" onClick={(e) => handleExportImgs(e)} loading={isDownloadImgs} disabled={imageList.length === 0}>下载全部附件</Button>
                                : ""
                        }
                    >
                        <FileBox isUpload={false} value={imageList} isDelete={false} isEditName={false} />
                    </ComCollapsePanel>
                </Collapse>
            </Col>
        </Row>
    ), [disabledDownload, fileList, handleExportFiles, handleExportImgs, imageList, isDownloadFile, isDownloadImgs]);

    if (isInRectifyDetail) {
        return (
            <Row>
                <Col span={24}>
                    <BaseDetail />
                </Col>
                <Col span={24}>
                    <FileDetail />
                </Col>
            </Row>
        );
    }

    return (
        <div className={`${cls.box} contentBox`}>
            <Row justify="space-between">
                <Col style={{display: showTitle ? "" : "none"}}>
                    <Space>
                        <BackIcon onClick={() => back()} />
                        <div className="title">进度检查详情</div>
                    </Space>
                </Col>
                <Col style={{display: showHandleButton ? "" : "none"}}>
                    <Space>
                        <PermissionCode
                            authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:detele"
                            // autoCodeList={hiddenDangerCheckAutoCodeList?.authCodeResultList}
                        >
                            <TipModal onOk={handleDelete} />
                        </PermissionCode>
                        <PermissionCode
                            authcode="ProjectPlatform-Plan-SandTable-Schedule-Management-Abnormal-Event-Check:edit"
                            // autoCodeList={hiddenDangerCheckAutoCodeList?.authCodeResultList}
                        >
                            <Button onClick={() => handleEdit()} type="primary" icon={<EditOutlined />}>编辑资料</Button>
                        </PermissionCode>
                    </Space>
                </Col>
            </Row>
            <Row style={{marginTop: 24}}>
                <Col span={24}>
                    <Collapse ghost expandIconPosition="right" defaultActiveKey={["des"]}>
                        <ComCollapsePanel
                            required={false}
                            header="基础信息"
                            key="des"
                        >
                            <BaseDetail />
                        </ComCollapsePanel>
                    </Collapse>
                </Col>
                <Col span={24}>
                    <FileDetail />
                </Col>
            </Row>
            <div>
                {jumpButton}
            </div>
            <PreviewModal />
        </div>
    );
};

export default View;
