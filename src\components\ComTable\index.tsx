import {Table, TableProps} from "antd";
import React, {useEffect, useRef, useState} from "react";
import useComStyle from "../../assets/css/useComStyle";

const ComTable = <T extends {}>(props: TableProps<T>) => {
    const {
        style,
        ...other
    } = props;
    const comCls = useComStyle();
    const ref = useRef<HTMLDivElement>(null);
    const [maxHeight, setMaxHeight] = useState<number | string | undefined>();

    useEffect(() => {
        if (ref.current !== null) {
            setMaxHeight(ref.current.offsetHeight);
        }
    }, []);

    if (maxHeight === undefined) {
        return (
            <div ref={ref} style={{flexGrow: 1, ...style}} />
        );
    }

    return (
        <Table<T>
            style={{maxHeight: Number(maxHeight) >= 50 ? maxHeight : undefined, ...style}}
            className={comCls.table}
            rowClassName={(_record, index) => (index % 2 === 0 ? "" : "darkRow")}
            scroll={{x: "100%", y: Number(maxHeight) >= 50 ? Number(maxHeight) - 47 : undefined}}
            pagination={false}
            rowKey="id"
            {...other}
        />
    );
};

export default ComTable;
