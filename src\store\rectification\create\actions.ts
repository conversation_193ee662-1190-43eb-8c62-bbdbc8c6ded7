import {createSFAPayloadAction} from "../../utils";
import {ConditionFlowChartData, FlowType} from "../../../components/Rectification/models/flow-chart";

export const setFlowChartData = createSFAPayloadAction("SET_FLOW_CHART_DATA" as const, (payload: ConditionFlowChartData) => payload);
export const setChangingFlowType = createSFAPayloadAction("SET_CHANGING_FLOW_TYPE" as const, (payload: boolean) => payload);
export const setFilteredFlowType = createSFAPayloadAction("SET_FILTERED_FLOW_TYPE" as const, (payload: FlowType[]) => payload);
export const setRectVisible = createSFAPayloadAction("SET_REACT_VISIBLE" as const, (payload: boolean) => payload);
export const setFlowTemplateVisible = createSFAPayloadAction("SET_FLOW_TEMPLATE_VISIBLE" as const, (payload: boolean) => payload);

type SetFlowChartData = ReturnType<typeof setFlowChartData>;
type SetChangingFlowType = ReturnType<typeof setChangingFlowType>;
type SetFilteredFlowType = ReturnType<typeof setFilteredFlowType>;
type SetFlowTemplateVisible = ReturnType<typeof setFlowTemplateVisible>;

type SetRectVisible = ReturnType<typeof setRectVisible>;
type Actions = SetFlowChartData | SetChangingFlowType | SetFilteredFlowType | SetRectVisible | SetFlowTemplateVisible;
export default Actions;
