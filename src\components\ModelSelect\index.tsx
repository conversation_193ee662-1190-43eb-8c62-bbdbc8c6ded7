import React, {FC, useEffect, useState, useCallback, useRef} from "react";
import ReactDOM from "react-dom";
import {useSelector, useDispatch} from "react-redux";
import {useParent} from "@luban/react-open-tab";
import {message, Spin} from "antd";
import {RootState} from "../../store/rootReducer";
import {projTypetoStr} from "../Rectification/rectification-helpers";
import {CheckBind} from "../Rectification/models/custom-field";
import {getMotorInfoByPpid, getProjectDetailInfo, getMotorToken2} from "../../api/common.api";
import {pathSeparator} from "../../assets/ts/utils";
import {setShowModelSpin, setShowModelSelect} from "../../store/no-persist/action";

// eslint-disable-next-line no-underscore-dangle
const {motorViewUrl, baseUrl} = window.__IWorksConfig__;

/** 关联的bim数据 */
export interface BindMsgVo {
    /** 构件handle */
    handle?: string;
    /** handle的数组 */
    handleList?: string[];
    /** 构件路径 */
    path?: string;
    /** 模型id */
    ppid: number;
}

export interface BimInfo {
    bimGuid: string;
    floor?: string;
    treePaths?: string[];
}
export enum BindType {
    binbdProj = 1,
    bindComp = 2,
    bindClass = 3,
    bindGroup = 4
}

export interface ModelSelectProps {
    extra?: unknown;
    onSelect?: (result: BindMsgVo[], extra?: unknown) => void;
    onCancel?: () => void;
    selected?: string[];
    selectInfo?: CheckBind[];
}

interface ProjInfo {
    ppid: number;
    projName: string;
    projType: string;
    projTypeInt: number;
}

interface MotorHandleItem {
    bimGuid: string;
    floor: string;
    main_type: string;
    sub_type?: string;
    treePaths?: string[];
}

interface MotorHandleInfo {
    selected: MotorHandleItem[];
}

const ModelSelect: FC<ModelSelectProps> = (props) => {
    const dispatch = useDispatch();
    const {extra, onSelect, onCancel} = props;
    const {showModelSelect, bimProjInfo, bindInfo, showModelSpin} = useSelector((state: RootState) => state.noRegister);
    const {token: commonToken, orgInfo, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const motorId = useRef<string>();
    const token = useRef<string>();
    const childName = useRef<string>("");
    const childLoaded = useRef(false);
    const notifyChild = useRef<((name: string, type: string, data?: {}) => void)>();
    const projInfo = useRef<ProjInfo>();
    const [showSpinFlag, setShowSpinFlag] = useState<boolean>(false);

    const sendMessage = useCallback((messageIn: string, data?: {}) => {
        if (notifyChild.current !== undefined && childName.current !== "") {
            notifyChild.current(childName.current, messageIn, data);
        }
    }, []);

    const setProject = useCallback(() => {
        if (motorId.current !== undefined) {
            sendMessage("ChangeProject", {
                id: motorId.current,
                token: token.current,
                changePpid: bimProjInfo.nPPid,
                openApiUrl: `${baseUrl}/openApi`,
                changeTitle: bimProjInfo.projName,
                projectType: projInfo.current?.projTypeInt,
                commonToken,
                sModel: "multiply"
            });
            if (showModelSpin) {
                sendMessage("SelectComponent");
            }
            if (Array.isArray(bindInfo)) {
                const info: BimInfo[] = [];
                if (bimProjInfo.bindType === BindType.bindComp) {
                    bindInfo.forEach((val) => {
                        info.push({
                            bimGuid: val.handle ?? "",
                            treePaths: val.treePaths,
                            // eslint-disable-next-line no-nested-ternary
                            floor: Boolean(val.floor) === true
                                ? val.floor
                                : Array.isArray(val.treePaths) && val.treePaths.length > 0
                                    ? val.treePaths[0]
                                    : undefined,
                        });
                    });
                }
                sendMessage("SetSelectComponent", {ids: info});
                const safetyQualityData = {
                    deptId: orgInfo.orgId,
                    deptName: orgInfo.orgName,
                    sectionId: curSectionInfo?.nodeId,
                };
                sendMessage("SetSafetyQuality", safetyQualityData);
            }
        }
    }, [
        sendMessage,
        bimProjInfo.nPPid,
        bimProjInfo.projName,
        bimProjInfo.bindType,
        commonToken,
        showModelSpin,
        bindInfo,
        orgInfo.orgId,
        orgInfo.orgName,
        curSectionInfo
    ]);

    const onChildMount = useCallback(() => {
        if (childLoaded.current !== true) {
            childLoaded.current = true;
            setProject();
        }
    }, [setProject]);

    const onMessage = useCallback((name: string, data: unknown) => {
        switch (name) {
            case "Selected":
                dispatch(setShowModelSelect(false));
                dispatch(setShowModelSpin(false));
                sendMessage("StopSelect");
                if (onSelect !== undefined) {
                    const {selected} = data as MotorHandleInfo;
                    let newSelected: MotorHandleItem[] = selected.filter((v) => Array.isArray(v.treePaths));
                    if (Boolean(bimProjInfo.bindBws) === false) {
                        newSelected = newSelected.map((v) => ({
                            ...v,
                            treePaths: v.treePaths!.filter((_v, idx) => idx !== 1)
                        }));
                    }
                    if (projInfo.current !== undefined) {
                        onSelect(
                            newSelected.map((val) => ({
                                handle: val.bimGuid,
                                path: val.treePaths!.filter((v) => typeof v === "string").join(pathSeparator),
                                ppid: bimProjInfo.nPPid
                            })),
                            extra
                        );
                    }
                }
                break;
            case "SelectCancel":
                sendMessage("StopSelect");
                dispatch(setShowModelSelect(false));
                dispatch(setShowModelSpin(false));
                if (onCancel !== undefined) {
                    onCancel();
                }
                break;
            default:
                console.log(name, data);
        }
    }, [extra, onSelect, onCancel, sendMessage, bimProjInfo, dispatch]);
    const onChildClose = useCallback(() => {
        dispatch(setShowModelSelect(false));
        dispatch(setShowModelSpin(false));
        childLoaded.current = false;
        if (onCancel !== undefined) {
            onCancel();
        }
    }, [onCancel, dispatch]);
    const parent = useParent(
        `${motorViewUrl}`,
        {onChildMount, onMessage, onChildClose, oneChild: true, crossOrigin: true},
        "iworks-table-name"
    );

    const {openChild} = parent;
    notifyChild.current = parent.notifyChild;

    const init = useCallback(
        async () => {
            if (showModelSelect === true) {
                const {data} = await getProjectDetailInfo(bimProjInfo.nPPid);
                if (data !== null) {
                    projInfo.current = {
                        ppid: data.ppid!,
                        projName: data.projName!,
                        projType: projTypetoStr(data.projTypeInt!),
                        projTypeInt: data.projTypeInt!
                    };
                }
                const tokenRes = await getMotorToken2();
                token.current = tokenRes.data ?? "";
                const tempMotorInfo = await getMotorInfoByPpid(bimProjInfo.nPPid);
                const tempMotorId = tempMotorInfo.result.motor3dRelationResult?.motor3dId;
                if (typeof tempMotorId === "string" && tempMotorId.length !== 0) {
                    setShowSpinFlag(showModelSelect);
                    motorId.current = tempMotorId;
                    childName.current = openChild();
                    setProject();
                } else {
                    message.error("工程轻量化未处理或抽取失败");
                    if (onCancel !== undefined) {
                        onCancel();
                    }
                }
            }
        },
        [bimProjInfo, showModelSelect, openChild, setProject, onCancel]
    );

    useEffect(() => {
        init();
    }, [init]); // TODO: 重复请求接口
    return (
        <Spin spinning={showSpinFlag && showModelSelect}>
            {showSpinFlag && showModelSpin && ReactDOM.createPortal(
                <div
                    style={{
                        position: "fixed",
                        top: 0,
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: 10000,
                        backgroundColor: "gray",
                        opacity: 0.8,
                        pointerEvents: "unset",
                        fontSize: 48,
                        textAlign: "center"
                    }}
                >
                    请在弹出窗口中选择构件
                </div>,
                document.body
            )}
        </Spin>
    );
};

export default ModelSelect;
