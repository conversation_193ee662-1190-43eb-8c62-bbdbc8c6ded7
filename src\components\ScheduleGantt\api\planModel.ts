export interface PlanCalendarForPlanSetParam {
    //  自定义日历名称（日历类型为自定义类型时必传）
    restDayName: string;
    //  非工作日集合（这里只记录休息日期，不记录休息日期设置规则）（日历类型为自定义类型时选传）
    restDays: number[] | null;
    //  工作日（日历类型为自定义类型时选传）
    workDays: number[] | null;
    //  日历类型 * 0：24小时日历（默认）* 1：标准日历（周六周日休息）* 2:自定义日历（复制24小时）* 3:自定义日历（复制标准），必传
    calendarType: number;
    //  日历有效时间（开始）日历类型为自定义类型时必传）
    startDate: number | null;
    //  日历有时间（结束）日历类型为自定义类型时必传）
    endDate: number | null;
}

export interface ProjNameForPlan {
    //  代理工程id
    ppid: number;
    //  工程名称，用于校验工程
    projName: string;
    //  工程类型
    projType?: number;
    //  工程模型（施工或预算）
    projModel?: string;
}

export interface ProjNameVo {
    //  代理工程id
    ppid: number;
    //  工程名称，用于校验工程
    projName: string;
    //  工程类型
    projType: number;
    //  工程模型（施工或预算）
    projModel: string;
}

export interface ScheduleWindowType extends Window {
    embedPlatform: boolean;
    _PLATFORM_: string;
    __IWorksConfig__: {
        baseUrl: string;
        basePds: string;
        token: string;
        productId: string;
        motorEditor: string;
        motorViewUrl: string;
        devBaseUrl: string;
        shellDownUrl: string;
    };
}
