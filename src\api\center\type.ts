import {Key} from "react";
import {WebRes} from "../common.type";

export interface SectionInfoType {
    id: string;
    type: number;
    deptId: string;
    deptName: string;
    parentId: string;
    name: string;
    classification: number;
    label?: string;
    value?: string;
}

export interface GetSectionListRes {
    code: number;
    msg: string;
    success: boolean;
    result: {
        [k: string]: unknown;
        content: SectionInfoType[];
    };
}

export interface WorkAreaType {
    areaId: string;
    areaName: string;
}
export interface TreeType {
    id: string;
    type: number;
    title: string;
    value: string;
    parentId: string;
    children: TreeType[];
    centerUserName: string | null;
}
export interface PersonTreeType {
    children: TreeType[];
    type: string;
}

export interface JobOrPostType {
    jobOrPostId: string;
    jobOrPostName: string;
    craftType: number;
}
export interface JobOrPostTypeWithSectionId extends JobOrPostType {
    sectionId: string;
}

export interface DepartmentType {
    orgId: string;
    orgName: string;
    orgType: string;
}

export interface DepartmentTreeType {
    [key: string]: unknown;
    orgId: string;
    orgName: string;
    orgType: string | number;
    parentId: string;
}
export interface DepartmentTypeWithPath extends DepartmentType {
    parentId: string;
    parentType: string;
}
export interface DepartmentAndJobListType {
    orgVos: DepartmentTypeWithPath[];

    postVos: JobOrPostTypeWithSectionId[];
}
export interface InfraSectionListParams {
    deptIds: string[];
    classification?: number; // 0 施工 1监理 2 检测 3 建设方
    keyword?: string;
    nodeCode?: string;
    nodeIds?: string[];
    nodeType?: number;
}
export interface AttachmentResultsItem {
    fileName: string;
    fileSize: number;
    uuid: string;
}
export interface InfraSectionListReturn {
    attachmentResults: AttachmentResultsItem[];
    builderOrg: string;
    classification: number;
    constructionOrg: string;
    contractCode: string;
    contractInvertment: number;
    deptId: string;
    deptName: string;
    designOrg: string;
    endCoordinate: string;
    endDate: string; // (date-time)
    endStation: string;
    id: string;
    meterRuleId: string;
    name: string;
    nodeCode: string;
    parentId: string;
    sortOrder: number;
    startCoordinate: string;
    startDate: string;
    startStation: string;
    supervisorOrg: string;
    type: number;
}

export interface DangerSourceType {
    checkMd5: string;
    // 可能导致的事故
    content: string;
    id: string;
    itemOptionId: string;
    // 工序,行为,设备
    itemOptionName: string;
    // 风险名称
    name: string;
    optionId: string;
    // 过程区域
    optionName: string;
}

export interface GetDangerSourceListRes extends WebRes {
    result: {
        content: DangerSourceType[];
        totalElements: number;
    };
}

export interface RiskEvaluationType {
    id: string;
    level: 1 | 2 | 3 | 4 | 5;
    // < 或 <=,在右边
    postConditionSymbolStr: string;
    postConditionVal: number;
    // < 或 <=,在左边
    preConditionSymbolStr: string;
    preConditionVal: number;
    isSerious: boolean;

}
export interface GetRiskEvaluationRes extends WebRes {
    result: RiskEvaluationType[];
}

export interface PersonOriginalType {
    buildType: number;
    deptId: string;
    orgId: string;
    orgName: string;
    orgType: number;
    partId: string;
    partName: string;
    personId: string;
    personName: string;
    personType: number;
}

export interface WBSNodeType {
    id: string;
    parentId: string;
    name: string;
    sort: number;
    // 这两项是为了tree,需要自己加上
    key: string;
    title: string;
    businessType: number;
    children?: WBSNodeType[];
}

export interface GetCheckListProjectCategoryType {
    id: string;
    name: string;
    status: null;
}

export interface HiddenDangerPointsType {
    parentName: string;
    parentId: string;
    lastNode: boolean;
    title: string;
    key: string;
    children: ChooseTypechildren[];
    id: string;
    content: string;
    reformDemand: string;
}
export interface CheckSubentriesType {
    customDisabled?: boolean; // 自定义 禁用
    parentName: string;
    parentId: string;
    disabled?: boolean;
    title: string;
    key: string;
    children: ChooseTypechildren[];
    id: string;
    content: string;
    hiddenDangerPoints: HiddenDangerPointsType[];
}
export interface ChooseTypechildren {
    children: ChooseTypechildren[];
    title: string;
    key: string;
}
export interface GetCheckSecurityListChooseType {
    customDisabled?: boolean; // 自定义 禁用
    disabled?: boolean;
    selectable?: boolean;
    title: string;
    key: string;
    children: ChooseTypechildren[];
    id: string;
    content: string;
    checkSubentries: CheckSubentriesType[];
}

export interface GetCheckQualityListChooseType {
    disabled?: boolean;
    selectable?: boolean;
    parentName: string;
    parentId: string;
    lastNode: boolean;
    title: string;
    key: string;
    children: ChooseTypechildren[];
    id: string;
    content: string;
    checkContents: GetCheckQualityListChooseType[];
}

export interface ParamsIdsType {
    ids: Key[] | string[];
}

export interface ReferenceSatutesType {
    id: string;
    content: string;
    detail: string;
}
export interface CheckListSatuteGistType {
    id: string;
    content: string;
    referenceSatutes: ReferenceSatutesType[];
}

export interface WBSListType {
    wbsNodeId: string;
    wbsNodeName: string;
}

export interface DangerOptionType {
    id: string;
    name: string;
}

export interface EnterpriseRes {
    authCodes: string;
    deployType: string;
    epid: number;
    logoUrl: string | null;
    nameCn: string;
    nameEn: string;
    platformName: string | null;
    systemCurrentTime: number;
    welcomeBackgroundUrl: string | null;
    user: UserInfoRes;
}

export interface UserInfoRes {
    admin: boolean;
    realName: string;
    roleId: string;
    roleName: string;
    username: string;
}
export interface GetOrgNodeDetailReturn {
    attachmentResults: [];
    builderOrg: string;
    classification: number;
    constructionOrg: string;
    contractCode: string;
    contractInvertment: number;
    deptDataType: unknown;
    deptId: string;
    deptName: string;
    designOrg: string;
    endCoordinate: string;
    endDate: unknown;
    endStation: string;
    id: string;
    meterRuleId: unknown;
    name: string;
    nodeCode: string;
    parentId: string;
    sortOrder: number;
    startCoordinate: string;
    startDate: unknown;
    startStation: string;
    supervisorOrg: string;
    type: number;
}
export interface FindUsersParams {
    deptId?: string; // 项目部id,默认为空
    deptSearchType: number; // 项目部查询方式,1:有项目授权的人 2:在项目部下的人
    dictOrder?: boolean; // 是否要按字典序排序
    direction: number; // 排序方式 0 ASC 1 DESC
    ignoreCase?: boolean;
    orgId?: string; // 当前组织节点及其子节点的id
    pageNum?: number;
    pageSize?: number;
    property: string; // 要排序的字段名 排序的字段：realName(姓名),roleName(角色名称),updateDate(更新时间),weekActive(周活跃度）
    roleId?: string; // 角色id
    searchStr?: string;
    searchAll?: boolean; // 查询所有用户，不分页，不排序，不模糊查询
    sourceType?: number; // 查询用户类型 0:默认内部人员 1:外部人员 2:全部
}
export interface FindUsersItem {
    email: string;
    idCard: string;
    mobile: string;
    orgId: string;
    orgName: string;
    postNameList: number;
    realName: number;
    remarks: string;
    roleId: string;
    roleName: string;
    showAdminTag: boolean;
    signId: string;
    signStampIds: string[];
    systemId: string;
    updateTime: number;
    updateUser: string;
    userId: string;
    userName: string;
    weekActive: string;
}
export interface FindUsersReturn {
    pageInfo: {
        pageSize: number;
        current: number;
        totalPage: number;
        beginNumber: number;
    };
    result: FindUsersItem[];
}
export interface GetUserSignImageReturn {
    height: number;
    signName: string;
    url: string;
    width: number;
}
export interface FindUserType {
    checkCard: boolean;
    idCard: string;
    signId: string;
    signStampIds: string[];
    username: string;
}

export interface WBSBusinessType {
    key: number;
    value: string;
}
