/* eslint-disable max-len */
/* eslint-disable max-nested-callbacks */
import {List, Space, Tooltip} from "antd";
import clsx from "clsx";
import React, {memo, useCallback, useState} from "react";
import {useSelector} from "react-redux";
// import {useHistory} from "react-router-dom";
import Icon from "@ant-design/icons";
import {WebRes} from "../../../../api/common.type";
import {FileDownloadInfo, RecentProjInfo} from "../../../../api/graphicModel";
import {EXTRACT_STATUS_OPTION_ITEMS, projTypeIconMap, projTypeNameMap} from "../../../../api/projListPageDataAndType";
import {getDownloadUrls, getRecentOpenedSandTableProjList, recordRecentOpenedSandTableProj} from "../../../../api/recentProject/recentProjectApi";
import compStyles from "../../../../assets/css/graghicStyles";
import {showMessage} from "../../../../assets/ts/utils";
import LbImage from "../../../../components/LbImage";
import {RootState} from "../../../../store/rootReducer";

import DefaultCover from "../../../../assets/images/default_cover_large.png";
import ComDrawer from "../../../../components/ComDrawer";
import DeptTree from "./DeptTree";
import {BIMSandTableInfo} from "../../../../store/status/types";
import {setCurSandTable} from "../../../../store/status/action";
import {dispatch} from "../../../../store";
import {SceneEngineeringIcon} from "../../../../assets/icons";

const RecentOpendProject = () => {
    const {
        recentProjOpenDiv,
        recentProjDiv,
        listBox,
        flexCenter,
        textEllipsisEnd,
        flexRowCenter,
        imgBox,
        recentProjStaDiv,
        choiceProject
    } = compStyles();

    const {orgInfo} = useSelector((state: RootState) => state.commonData);

    const [recentData, setRecentData] = useState<RecentProjInfo[]>([]);
    const [openProjectVisible, setOpenProjectVisible] = useState<boolean>(false);

    const dataSource = [{} as unknown as RecentProjInfo].concat(recentData);

    const recentOpenedListCallBack = useCallback(
        (data: RecentProjInfo[]) => {
            const dataList = data.map((el) => ({...el, key: el.ppid}));
            setRecentData(dataList);
        },
        []
    );

    const fetchRecentProjImgDownloadUrl = useCallback((items: RecentProjInfo[]) => {
        if (items === null || items.length === 0) {
            recentOpenedListCallBack([]);
            return;
        }
        const uuidArr: string[] = items.map((value) => value.projPicUuid);
        const downloadUrlParam = {
            fileUUIDList: uuidArr
        };
        getDownloadUrls(downloadUrlParam).then((wRes: FileDownloadInfo[]) => {
            if (wRes !== null && wRes.length !== 0) {
                // eslint-disable-next-line array-callback-return
                items.map((value) => {
                    wRes.forEach((item: FileDownloadInfo) => {
                        if (value.projPicUuid === item.fileUUID) {
                            if (item.downloadUrls !== null && item.downloadUrls.length > 0) {
                                const [url] = item.downloadUrls;
                                // eslint-disable-next-line no-param-reassign
                                value.projPicDownloadUrl = url;
                            }
                        }
                    });
                });
            }
            recentOpenedListCallBack(items);
        });
    }, [recentOpenedListCallBack]);

    const fetchRecentListData = useCallback(() => {
        getRecentOpenedSandTableProjList(orgInfo.orgId, 100).then((res: WebRes<RecentProjInfo[]>) => {
            if (res.success) {
                const {data} = res;
                if (data !== null && data.length !== 0) {
                    fetchRecentProjImgDownloadUrl(data);
                } else {
                    setRecentData([]);
                }
            } else {
                showMessage(res.msg);
            }
        });
    }, [fetchRecentProjImgDownloadUrl, orgInfo.orgId]);

    const fetchRecordRecentOpened = useCallback((ppid: number) => {
        recordRecentOpenedSandTableProj(orgInfo.orgId, ppid).then((res: WebRes<boolean>) => {
            if (res.success) {
                fetchRecentListData();
            }
        });
    }, [fetchRecentListData, orgInfo.orgId]);

    React.useEffect(() => {
        if ((orgInfo?.orgId ?? "").length > 0) {
            fetchRecentListData();
        }
    }, [fetchRecentListData, orgInfo]);

    const clickRecentListModel = useCallback((info: RecentProjInfo) => {
        if (info.hashDeleted) {
            showMessage("工程已删除！");
            return;
        }
        if (!info.hashAuth) {
            showMessage("工程未授权！");
            return;
        }
        switch (info.extractStatus) {
            case EXTRACT_STATUS_OPTION_ITEMS[1]:
            case EXTRACT_STATUS_OPTION_ITEMS[2]:
            case EXTRACT_STATUS_OPTION_ITEMS[3]:
                showMessage("模型未抽取，请先进行轻量化抽取！");
                return;
            case EXTRACT_STATUS_OPTION_ITEMS[5]:
                showMessage("模型正在进行轻量化抽取，无法打开！");
                return;
            default:
                break;
        }
        switch (info.pdsStatus) {
            case -1:
                showMessage("pds模型未抽取，请先进行pds模型抽取！");
                return;
            case 0:
            case 2:
            case 3:
                showMessage("模型正在进行pds抽取，无法打开！");
                return;
            default:
                break;
        }
        fetchRecordRecentOpened(info.ppid);

        const bimSandTableInfo = info as BIMSandTableInfo;
        dispatch(setCurSandTable(bimSandTableInfo));
    }, [fetchRecordRecentOpened]);

    const clickOpenProject = useCallback(() => {
        setOpenProjectVisible(true);
    }, []);

    const getRecentListItem = useCallback(
        (item: RecentProjInfo) => {
            let stateStr = "";
            if (item.hashDeleted) {
                stateStr = "已删除";
            } else if (!item.hashAuth) {
                stateStr = "未授权";
            } else if (item.extractStatus === EXTRACT_STATUS_OPTION_ITEMS[1]) {
                stateStr = "未处理";
            } else if (item.extractStatus === EXTRACT_STATUS_OPTION_ITEMS[2]) {
                stateStr = "待处理";
            } else if (item.extractStatus === EXTRACT_STATUS_OPTION_ITEMS[5] || item.pdsStatus === 0 || item.pdsStatus === 2 || item.pdsStatus === 3) {
                stateStr = "处理中";
            } else if (item.extractStatus === EXTRACT_STATUS_OPTION_ITEMS[3] || item.pdsStatus === -1) {
                stateStr = "处理失败";
            } else if (item.hashUpdated && item.extractStatus === EXTRACT_STATUS_OPTION_ITEMS[4]) {
                stateStr = "已更新";
            }
            let statusDiv = null;
            if (stateStr !== "") {
                statusDiv = (
                    <div className={clsx(flexCenter, recentProjStaDiv)}>
                        {stateStr}
                    </div>
                );
            }
            let projTypeIcon: string | undefined = projTypeIconMap.get(item.projType);
            if (projTypeIcon === undefined) {
                projTypeIcon = "unmatch";
            }
            let projTypeName: string | undefined = projTypeNameMap.get(item.projType);
            if (projTypeName === undefined) {
                projTypeName = "未知工程类型";
            }
            return (
                <List.Item>
                    <div
                        className={clsx(recentProjDiv)}
                        onClick={() => {
                            clickRecentListModel(item);
                        }}
                    >
                        <div style={{width: "100%", height: "100%"}}>
                            <LbImage
                                src={item.projPicDownloadUrl}
                                style={{width: "100%", height: "100%"}}
                                defaultImg={DefaultCover}
                            />
                            {statusDiv}
                        </div>
                        <div className={clsx(flexRowCenter)} style={{marginLeft: 16}}>
                            <div className={clsx(imgBox)} title={projTypeName}>
                                <div className={["smallicon", projTypeIcon].join(" ")} />
                            </div>
                            <Tooltip placement="topLeft" title={item.projName}>
                                <div className={clsx(textEllipsisEnd)} style={{marginLeft: 8, fontSize: 14, lineHeight: "44px"}}>{item.projName}</div>
                            </Tooltip>
                        </div>
                    </div>
                </List.Item>
            );
        },
        [clickRecentListModel, flexCenter, flexRowCenter, imgBox, recentProjDiv, recentProjStaDiv, textEllipsisEnd]
    );

    const getOpenProjectItem = useCallback(
        () => (
            <List.Item>
                <div
                    className={clsx(recentProjOpenDiv)}
                    onClick={() => {
                        clickOpenProject();
                    }}
                >
                    <Space align="center" direction="vertical" style={{width: "100%", height: "100%", paddingTop: 80, color: "#1F54C5", background: "#F5F5F6"}}>
                        <Icon component={SceneEngineeringIcon} style={{fontSize: 32, color: "#1F54C5"}} />
                        打开工程
                    </Space>
                </div>
            </List.Item>
        ),
        [clickOpenProject, recentProjOpenDiv]
    );

    const renderRecentListItem = useCallback(
        (item: RecentProjInfo) => {
            if (Object.keys(item).length > 0) {
                return getRecentListItem(item);
            }
            return getOpenProjectItem();
        },
        [getOpenProjectItem, getRecentListItem]
    );

    const renderComponents = useCallback(() => (
        <div className={clsx(listBox)} style={{flex: "0 0 auto", height: "100%"}}>
            <List
                itemLayout="vertical"
                grid={{gutter: 10, column: 6}}
                dataSource={dataSource}
                split={false}
                renderItem={(item) => renderRecentListItem(item)}
            />
        </div>
    ), [listBox, dataSource, renderRecentListItem]);

    const handleCancel = React.useCallback(() => {
        setOpenProjectVisible(false);
    }, []);

    const renderOpenProj = useCallback(() => (
        <ComDrawer
            title="选择工程"
            width={722}
            visible={openProjectVisible}
            destroyOnClose
            footer={null}
            onCancel={handleCancel}
            className={clsx(choiceProject)}
        >
            <div style={{display: "flex", height: "100%", flexDirection: "column", overflowY: "scroll"}}>
                <div style={{flexGrow: 1}}>
                    <DeptTree />
                </div>
            </div>
        </ComDrawer>
    ), [choiceProject, handleCancel, openProjectVisible]);

    return (
        <>
            {renderComponents()}
            {renderOpenProj()}
        </>
    );
};

export default memo(RecentOpendProject);
