import {Input, TreeProps} from "antd";
import {DataNode} from "antd/lib/tree";
import {cloneDeep} from "lodash-es";
import React, {useEffect, useState, Key} from "react";
import {createUseStyles} from "react-jss";
import {useDispatch, useSelector} from "react-redux";
import {filterTreeNew, generateList, toArr} from "../../assets/ts/utils";
import {RootState} from "../../store/rootReducer";
import ComTree from "../ComTree";
import {setTreeData, setCheckedKeys, setQueryCheckedNode, setWbsBindEbs} from "./store/action";
import {WBSNodeType} from "../../api/center/type";
import {getWbsBindEbs} from "../../api/wbsEbs";
import {SectionListType} from "../../store/common/actionTypes";
import {NodeTypeEnum} from "../../assets/ts/globalData";

const useStyle = createUseStyles({
    tree: {
        "& .ant-tree-list": {
            height: "100%",
            overflow: "hidden",
            "& .ant-tree-treenode": {
                width: "100%",
                "& .ant-tree-node-content-wrapper": {
                    flexGrow: 1,
                    width: 0,
                    whiteSpace: "nowrap"
                },
                // 可性能优化 目前来看渲染还可以 先注释
                // "& .ant-tree-checkbox .ant-tree-checkbox-inner:after": {
                //     transition: "none",
                // },
                // "& .ant-tree-checkbox-checked::after": {
                //     animation: "none",
                // }
            },
            "& .ant-tree-list-holder > div": {
                overflow: "auto !important"
            }
        },
        "& .ant-tree-list-scrollbar-thumb": {
            background: "#f2f3f5 !important"
        }
    }
});

export interface WBSTreeProps extends TreeProps {
    isShowSearch?: boolean;
    // 根据页节点实现过滤,不传
    leafIds?: string[];
    // 是不是搜索模式,搜索模式会初始化check
    query?: true;
    nodeInfo?: SectionListType;
}

const getParentIds = (leafIds: string[], originData: WBSNodeType[]) => {
    const ids = [...leafIds];
    const circleGetId = (id: string) => {
        const leafData = Array.isArray(originData) ? originData.filter((el) => el.id === id) : [];
        if (leafData.length > 0) {
            const {parentId} = leafData[0];
            if (ids.includes(parentId)) {
                return;
            }
            ids.push(parentId);
            circleGetId(parentId);
        }
    };

    leafIds.forEach((el) => circleGetId(el));
    return ids;
};

// 根据要展示的叶节点,过滤树
const filterTreeByLeafIds = (treeData: DataNode, leafIds: string[] | undefined, originData: WBSNodeType[]): DataNode | undefined => {
    if (leafIds === undefined) {
        return treeData;
    }
    if (leafIds.length === 0) {
        return undefined;
    }
    const tempTreeData = cloneDeep(treeData);
    const parentIds = getParentIds(leafIds, originData);
    const circleTree = (node: DataNode) => {
        node.children = node.children?.filter((el) => {
            if (parentIds.includes(`${el.key}`)) {
                circleTree(el);
                return true;
            }
            return false;
        });
    };
    circleTree(tempTreeData);
    if (tempTreeData.children?.length === 0) {
        return undefined;
    }
    return tempTreeData;
};

export const getLeafNodeIds = (nodeData: DataNode) => {
    const leafKeys: Key[] = [];
    const traverTree = (val: DataNode) => {
        if (Array.isArray(val.children) && val.children.length > 0) {
            val.children.forEach((item) => traverTree(item));
        } else {
            leafKeys.push(val.key);
        }
    };
    traverTree(nodeData);
    return leafKeys;
};

// treeData由redux处理,也可以自己传入
const WBSTree = (props: WBSTreeProps) => {
    const cls = useStyle();
    const {isShowSearch = true, leafIds, query, nodeInfo} = props;
    const dispatch = useDispatch();
    const {orgInfo, sectionList, curSectionInfo} = useSelector((state: RootState) => state.commonData);
    const {treeDataObj, treeData, originData, ebsTreeData, wbsBindEbs} = useSelector((state: RootState) => state.wbsTree);
    const [copyTreeData, setCopyTreeData] = useState<DataNode[]>([]); // 备份一份数据
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(["all", "noRelevance", "relevance", "wbs", "ebs"]);
    const [autoExpandParent, setAutoExpandParent] = useState(false);
    const [curNodeInfo, setCurNodeInfo] = useState(curSectionInfo);

    useEffect(() => {
        if (nodeInfo !== undefined) {
            setCurNodeInfo(nodeInfo);
        }
    }, [nodeInfo]);

    useEffect(() => {
        setCopyTreeData(treeData);
        if (query === true) {
            dispatch(setCheckedKeys([
                ...getLeafNodeIds({
                    title: "全部",
                    key: "all",
                    children: [
                        {title: "未关联", key: "noRelevance"},
                        {title: "已关联",
                            key: "relevance",
                            children: [
                                {title: "WBS分部分项", key: "wbs", children: copyTreeData},
                                {title: "BIM模型", key: "ebs", children: ebsTreeData}
                            ]},
                    ]
                }),
                "all"
            ]));
            dispatch(setQueryCheckedNode([{title: "全部", key: "all"}]));
        }
    }, [copyTreeData, dispatch, ebsTreeData, query, treeData]);

    useEffect(
        () => {
            let tempArr: DataNode[] = [];
            // 基建项目
            if (orgInfo.deptDataType === 2) {
                // 项目方
                if (curNodeInfo?.nodeType === NodeTypeEnum.项目) {
                    Object.values(treeDataObj).forEach((el) => {
                        const tempTreeData = el[0];
                        const filteredTree = filterTreeByLeafIds(tempTreeData, leafIds, originData[tempTreeData.key]);
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            tempArr = tempArr.concat([filteredTree]);
                        }
                    });
                }
                // 施工方查看自己的
                if (curNodeInfo?.classification === 0) {
                    const curSectionTreeData = treeDataObj[curNodeInfo?.id ?? ""] ?? [];
                    if (curSectionTreeData.length > 0) {
                        const tempTreeData = curSectionTreeData[0];
                        const filteredTree = filterTreeByLeafIds(tempTreeData, leafIds, originData[tempTreeData.key]);
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            tempArr = [filteredTree];
                        }
                    }
                }
                // 监理方查看,自己的+下属施工方的
                if (curNodeInfo?.classification === 1) {
                    const belongSectionId = sectionList.filter((el) => el.parentId === curNodeInfo?.id);
                    belongSectionId.forEach((el) => {
                        const tempTreeData = treeDataObj[el.nodeId][0];
                        const filteredTree = filterTreeByLeafIds(tempTreeData, leafIds, originData[tempTreeData.key]);
                        if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                            tempArr = tempArr.concat([filteredTree]);
                        }
                    });
                }
            }
            // 房建项目
            if (orgInfo.deptDataType === 1) {
                const tempTreeData = treeDataObj[orgInfo.orgId] ?? [];
                if (tempTreeData.length > 0) {
                    const filteredTree = filterTreeByLeafIds(tempTreeData[0], leafIds, originData[orgInfo.orgId]);
                    if (filteredTree !== undefined && filteredTree.children?.length !== 0) {
                        tempArr = [filteredTree];
                    }
                }
            }
            dispatch(setTreeData(tempArr));
        },
        [curNodeInfo, dispatch, leafIds, orgInfo.deptDataType, orgInfo.orgId, originData, sectionList, treeDataObj],
    );

    // const renderNode: TreeProps["titleRender"] = (nodeData) => <Text style={{width: "100%"}} ellipsis>{nodeData.title}</Text>;

    // TODO: 这下面的搜索是做实验

    const defaultMatcher = (filterText: string, node: DataNode) => {
        if (typeof node?.title === "string") {
            return node?.title?.includes(filterText);
        }
        return false;
    };

    const onSearch = (value: string) => {
        const newTreeData = cloneDeep(treeData);
        if (value === "") {
            setAutoExpandParent(false);
            setExpandedKeys([]);
            setCopyTreeData(treeData);
            return;
        }
        const filtered = newTreeData.map((item) => filterTreeNew(item, value, defaultMatcher));
        if (filtered.some((item) => Array.isArray(item.children) && item.children.length > 0)) {
            setCopyTreeData(filtered.filter((item) => Array.isArray(item.children) && item.children.length > 0));
        } else {
            setCopyTreeData(filtered.filter((item) => defaultMatcher(value, item)));
        }
        const newExpandedKeys = generateList(filtered, [])
            .map((item: DataNode) => item.key);
        setAutoExpandParent(true);
        setExpandedKeys(newExpandedKeys);
    };
    const onExpand: TreeProps["onExpand"] = (newExpandedKeys: React.Key[], expandInfo) => {
        setAutoExpandParent(false);
        setExpandedKeys(newExpandedKeys);
        if (expandInfo.expanded === true && toArr(expandInfo.node.children ?? []).length > 0 && curNodeInfo !== null) {
            const childIds = toArr(expandInfo.node.children ?? []).map((el) => el.key);
            const params = {
                deptId: orgInfo.orgId,
                nodeId: curNodeInfo.nodeId,
                ids: childIds,
            };
            getWbsBindEbs(params)
                .then((res) => {
                    const tempData: RootState["wbsTree"]["wbsBindEbs"] = {...wbsBindEbs};
                    toArr(res.data).forEach((el) => {
                        tempData[el.id] = el;
                    });
                    dispatch(setWbsBindEbs(tempData));
                });
        }
    };

    return (
        <div style={{height: "100%", display: "flex", flexDirection: "column"}}>
            { isShowSearch !== undefined && isShowSearch === true ? <div style={{marginBottom: "12px"}}><Input.Search onSearch={onSearch} /></div> : ""}
            { (copyTreeData.length !== 0 || ebsTreeData.length !== 0) && (
                <ComTree
                    flex
                    className={cls.tree}
                    onExpand={onExpand}
                    autoExpandParent={autoExpandParent}
                    treeData={query === true
                        ? [
                            {
                                title: "全部",
                                key: "all",
                                children: [
                                    {title: "未关联", key: "noRelevance"},
                                    {title: "已关联",
                                        key: "relevance",
                                        children: [
                                            {title: "WBS分部分项", key: "wbs", children: copyTreeData},
                                            {title: "BIM模型", key: "ebs", children: ebsTreeData}
                                        ]},
                                ]
                            }
                        ]
                        : copyTreeData}
                    // titleRender={renderNode}
                    expandedKeys={expandedKeys}
                    {...props}
                />
            )}
        </div>
    );
};

export default WBSTree;
