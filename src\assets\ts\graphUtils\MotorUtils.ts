import Motor from "@motor/core";
import {MotorContext} from "../../../reMotor";
import {BIMCompInfo} from "../../../reMotor/interface";
import {isNotNullOrUndefined} from "../utils";

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
class MotorUtils {
    static getBIMCompInfo(comp: Motor.PickObject): BIMCompInfo {
        const result: BIMCompInfo = {
            guid: "",
            name: "",
            path: "",
            pathWithoutMajor: "",
            bimGuid: "",
            floor: "",
            major: "",
            mainType: "",
            subType: "",
        };

        result.guid = comp.id ?? "";
        result.bimGuid = comp.bimId ?? "";
        const dirs = comp.dir;
        if (Array.isArray(dirs) && dirs.length > 0) {
            result.floor = dirs.length > 0 ? dirs[0] : "";
            result.major = dirs.length > 1 ? dirs[1] : "";
            result.mainType = dirs.length > 2 ? dirs[2] : "";
            result.subType = dirs.length > 3 ? dirs[3] : "";
            result.name = dirs.length > 4 ? dirs[4] : "";
            result.path = dirs.join("☎");

            const dirsClone = dirs.slice();
            dirsClone.splice(1, 1);
            result.pathWithoutMajor = dirsClone.join("☎");
        }


        return result;
    }

    static getCurrentCameraInfo(): Motor.MotorCore.ViewPosition {
        const camera = MotorContext.getCamera();
        if (isNotNullOrUndefined(camera)) {
            return camera.getViewPosition();
        }

        return {
            pos: [0, 0, 0],
            phi: 0,
            theta: 0,
        };
    }

    static hasInvalidPosition(positionList: Motor.Vector3[]): boolean {
        const zeroPosition = new Motor.Vector3(0, 0, 0);
        const find = positionList.find((el) => (el.x === zeroPosition.x && el.y === zeroPosition.y && el.z === zeroPosition.z)
            || typeof el.x !== "number" || typeof el.y !== "number" || typeof el.z !== "number");
        return typeof find !== "undefined";
    }

    static flyTo(viewPosition: Motor.MotorCore.ViewPosition): void {
        const camera = MotorContext.getCamera();
        if (isNotNullOrUndefined(camera)) {
            camera.setViewToViewPosition(viewPosition);
        }
    }

    static flyToGeo(longitude: number, latitude: number, height: number,
        phi?: number, theta?: number, durationTime?: number, completeFunc?: () => void): void {
        const camera = MotorContext.getCamera();
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(camera) && isNotNullOrUndefined(project)) {
            const ptWorld = project.geoToWorld(longitude, latitude, height);
            const pt = Motor.GeoAlgorithm.generateBoxByPt(ptWorld, 50, 50, 50);
            camera.setViewToBox(pt, phi, theta, durationTime, completeFunc);
        }
    }

    static flyToModel(model: Motor.Model): void {
        const camera = MotorContext.getCamera();
        if (isNotNullOrUndefined(camera)) {
            if (isNotNullOrUndefined(model.viewPosition)) {
                camera.setViewToViewPosition(model.viewPosition);
            } else {
                camera.setViewToBox(model.mod.getWorldBox());
            }
        }
    }

    static async flyToCompoment(compList: Motor.Element[]): Promise<void> {
        const camera = MotorContext.getCamera();
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(camera) && isNotNullOrUndefined(project) && compList.length > 0) {
            if (compList.length === 1) {
                const {id} = compList[0];
                const box = isNotNullOrUndefined(id) ? await project.getElmentBoundingBox(id) : undefined;
                if (isNotNullOrUndefined(box)) {
                    camera.setViewToBox(box);
                }
            } else {
                let boxAllComp: Motor.MotorCore.Box | undefined;
                for (let i = 0; i < compList.length; ++i) {
                    const {id} = compList[i];
                    const box = isNotNullOrUndefined(id) ? await project.getElmentBoundingBox(id) : undefined;
                    if (isNotNullOrUndefined(box)) {
                        if (isNotNullOrUndefined(boxAllComp)) {
                            boxAllComp.addBox(box);
                        } else {
                            boxAllComp = box;
                        }
                    }
                }

                if (isNotNullOrUndefined(boxAllComp)) {
                    camera.setViewToBox(boxAllComp);
                }
            }
        }
    }

    static flyToCompomentByModelAndId(model: Motor.Model, idList: string[], select = true): void {
        model.queryElement(idList).then((elementObjList) => {
            if (elementObjList.length > 0) {
                if (select) {
                    model.select(elementObjList);
                }
                MotorUtils.flyToCompoment(elementObjList);
            }
        });
    }

    static flyToCompomentById(idList: string[], select = true): void {
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(project)) {
            project.queryElement(idList).then((elementObjList) => {
                if (elementObjList.length > 0) {
                    if (select) {
                        project.select(elementObjList);
                    }
                    MotorUtils.flyToCompoment(elementObjList);
                }
            });
        }
    }

    static isolateComponent(idList: string[]): void {
        const camera = MotorContext.getCamera();
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(camera) && isNotNullOrUndefined(project)) {
            project.clearIsolate();
            project.isolate(idList);
            MotorUtils.flyToCompomentById(idList);
        }
    }

    static isolateCompomentByModelAndPath(model: Motor.Model, pathList: string[][]): void {
        const camera = MotorContext.getCamera();
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(camera) && isNotNullOrUndefined(project)) {
            project.clearIsolate();
            model.queryElement(pathList).then((elementObjList) => {
                if (elementObjList.length > 0) {
                    project.isolate(elementObjList);
                    MotorUtils.flyToCompoment(elementObjList);
                }
            });
        }
    }

    // 世界坐标系坐标点转屏幕点
    static getWorldScreenPt(ptParam: Motor.Vector3): Motor.Vector2| undefined {
        const camera = MotorContext.getCamera();
        if (isNotNullOrUndefined(camera)) {
            return camera.getWorldScreenPt(ptParam);
        }
        return undefined;
    }

    // 当前世界坐标系点转投影坐标系绝对位置点
    static worldToProjection(position: Motor.Vector3): Motor.Vector3 | undefined {
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(project)) {
            return project.worldToProjection(position);
        }
        return undefined;
    }

    // 将世界坐标系点转成投影（工程）坐标系的绝对位置点
    static projectionToWorld(position: Motor.Vector3): Motor.Vector3 | undefined {
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(project)) {
            return project.projectionToWorld(position);
        }
        return undefined;
    }

    // 当前世界坐标系点转wgs84（gps）经纬度
    static worldToGeo(position: Motor.Vector3): Motor.Vector3 | undefined {
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(project)) {
            return project.worldToGeo(position);
        }
        return undefined;
    }

    // wgs84 (gps) 经纬度坐标转当前世界坐标系点
    static geoToWorld(longitude: number, latitude: number, height: number): Motor.Vector3 | undefined {
        const project = MotorContext.getProject();
        if (isNotNullOrUndefined(project)) {
            return project.geoToWorld(longitude, latitude, height);
        }
        return undefined;
    }

    static resestBIMProject(bimProject: Motor.Model): void {
        bimProject.setVisibility(true);
        bimProject.resetColor();
        bimProject.deselect();
    }
}

export default MotorUtils;
