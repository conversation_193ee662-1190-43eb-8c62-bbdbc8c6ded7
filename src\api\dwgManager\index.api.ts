/* eslint-disable no-underscore-dangle */

import Fetch from "../../service/Fetch";
import {WebRes} from "../common.type";
import {AddDwgPDFDrawingInfoData, PostTriggerMotorModelTransformData} from "./index.type";


// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = window.__IWorksConfig__ as any;

export const addDwgPDFDrawingInfo = async (data: AddDwgPDFDrawingInfoData[]): Promise<WebRes<string>> => Fetch({
    url: `${baseUrl}/lbbe/rs/dwg/addPDFDrawingInfo`,
    methods: "post",
    data,
});

// 触发motor模型抽取
export const postTriggerMotorModelTransform = async (data: PostTriggerMotorModelTransformData): Promise<WebRes<string>> => Fetch({
    url: `${baseUrl}/pdscommon/rs/motor/v2/model/trigger-motor-model-transform`,
    methods: "post",
    data
});

// 删除
export const delDwgInfo = async (params: {ids: string[]}): Promise<WebRes<string>> => Fetch({
    methods: "post",
    url: `${baseUrl}/lbbe/rs/dwg/deletePDFDrawingInfo`,
    data: params,
});
