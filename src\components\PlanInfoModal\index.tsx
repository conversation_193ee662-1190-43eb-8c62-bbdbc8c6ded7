/* eslint-disable max-lines-per-function */
import React, {useCallback, useEffect, useMemo, useState} from "react";
import {Form, Input, Select, DatePicker, Modal, Spin, Row, Col} from "antd";
import moment, {Moment} from "moment";
import {useSelector} from "react-redux";
import {LabeledValue} from "antd/lib/select";
import {sortBy} from "lodash-es";
import confirmLeave from "../../assets/ts/confirmLeave";
import {planTypeList} from "../../views/schedule/Preparation/data";
import useEditedStatus from "../../hooks/useEditedStatus";
import {RootState} from "../../store/rootReducer";
import {getPlanInfoDetail, getCalendarList, addPlanInfo, updatePlanInfo, getCalendarInfo} from "../../api/Preparation";
import {AddPlanInfoType, ApprovalStatus, CalendarListItem, PlanCycleType, PlanInfoDetailType, UpdatePlanInfoType} from "../../api/Preparation/type";
import {PlanInfoProps, AddOrEditFormType} from "./index.d";
import {getDeptDetail, getDeptHouseDetail, getProcessTemplates} from "../../api/common.api";
import {getCycleMomentText, isDefined, momentText} from "../../assets/ts/utils";
import PlanCycleDatePicker from "../PlanCycleDatePicker";
import {getEndDate} from "../PlanCycleDatePicker/utils";
import {buildCalenderInfo, checkHasParentPlan} from "../ScheduleGantt/tool/planUtils";
import {WebRes} from "../../api/common.type";
import {getDeptOrgList} from "../../api/orginationStructure";
import ComModal from "../ComModal";
import updateWorkGuideTask from "../CompleteWorkGuideTask";

const layout = {
    labelCol: {span: 6},
    wrapperCol: {span: 17}
};

interface ProjectNode {
    id: string;
    name: string;
    type: number;
}

const defaultMinData = new Date(2000, 0, 1);

const defaultMaxData = new Date(2100, 11, 31);

const PlanInfoModal: React.FC<PlanInfoProps> = (props) => {
    const {planId, visible, setVisible, planInfoBack, readonly} = props;
    const [form] = Form.useForm();
    const {orgInfo, curSectionInfo, leafMenuId} = useSelector((state: RootState) => state.commonData);
    const [loading, setLoading] = useState(false);
    const [planInfo, setPlanInfo] = useState<PlanInfoDetailType>();
    const [projectList, setProjectList] = useState<ProjectNode[]>([]);
    const [isDataChange, setIsDataChange] = useState(false);
    const [projectOptions, setProjectOptions] = useState<LabeledValue[]>([]);
    const [cycleType, setCycleType] = useState<PlanCycleType>("MASTER");
    const [calendarOptions, setCalendarOptions] = useState<LabeledValue[]>([]);
    const [approvalOptions, setApprovalOptions] = useState<LabeledValue[]>([]);
    const [planMinDate, setPlanMinDate] = useState<Moment>();
    const [planMaxDate, setPlanMaxDate] = useState<Moment>();
    const hasParentPlan = useMemo(() => checkHasParentPlan(planInfo), [planInfo]);

    const getDetail = useCallback(async () => {
        if (planId === undefined) {
            return;
        }
        const res = await getPlanInfoDetail(planId.toString());
        setCycleType(res.data.type);
        form.setFieldsValue({
            project: res.data.nodeId,
            planName: res.data.name,
            planType: res.data.type,
            planStartDate: moment(res.data.startDate),
            planEndDate: moment(res.data.endDate),
            planCycle: moment(Number(res.data.cycle)),
            planCalendar: res.data.calendar,
            approvalTemplateId: res.data.approvalTemplateId.length > 0 ? res.data.approvalTemplateId : "0",
        });
        setPlanInfo({
            ...res.data,
            approvalTemplateId: res.data.approvalTemplateId.length > 0 ? res.data.approvalTemplateId : "0",
        });
    }, [form, planId]);

    const back = useCallback(() => {
        setVisible(false);
        form.resetFields();
        setIsDataChange(false);
    }, [form, setVisible]);

    useEditedStatus(isDataChange, back);

    const fetchCalendarList = useCallback(async () => {
        const res: WebRes<CalendarListItem[]> = await getCalendarList();
        if (res.success) {
            if (res.data !== null) {
                const options = res.data.map((i) => ({label: i.ctName, value: i.ctid}));
                setCalendarOptions(options);
            }
        }
    }, []);

    const fetchProjectList = useCallback(async () => {
        const orgNode = {
            id: orgInfo.orgId,
            name: orgInfo.orgName,
            type: 2,
        };
        let list: ProjectNode[] = [];
        if (orgInfo.deptDataType === 1) {
            list = [orgNode];
        }
        if (orgInfo.deptDataType === 2) {
            const res = await getDeptOrgList(orgInfo.orgId);
            if (res.success) {
                console.log("getOrgListProjNodeByDeptId", res);
                const orgRes = await getDeptOrgList(orgInfo.orgId);
                const resSectionList = (orgRes.data ?? [])
                    .filter((el) => el.authFlag)
                    .map((el) => ({...el, name: el.nodeName, id: el.nodeId, nodeType: 3}));
                const tempSectionList1 = resSectionList.map((el) => ({...el, classification1: -el.classification}));
                const tempSectionList2 = sortBy(tempSectionList1, ["classification1", "nodeName"]);
                if (resSectionList.length === orgRes.data.length) {
                    list.push(orgNode);
                }
                tempSectionList2.forEach((section) => list.push({
                    id: section.nodeId,
                    name: section.nodeName,
                    type: 3,
                }));
            }
        }
        setProjectList(list);
        setProjectOptions(list.map((proj) => ({value: proj.id, label: proj.name, type: proj.type})));

        if (planId === undefined) {
            form.setFieldsValue({
                project: curSectionInfo?.id,
            });
        }
    }, [curSectionInfo, form, orgInfo.deptDataType, orgInfo.orgId, orgInfo.orgName, planId]);

    const getProjectDetail = useCallback(async () => {
        if (orgInfo.deptDataType === 2) {
            const res = await getDeptDetail(orgInfo.orgId);
            const {result} = res;
            if (result.startDate === null || result.endDate === null) {
                // message.warning("未读取到数据，请配置开工日期、竣工日期！");
                setPlanMinDate(moment(defaultMinData));
                setPlanMaxDate(moment(defaultMaxData));
                return;
            }
            setPlanMinDate(moment(result.startDate).startOf("year").subtract(1, "year"));
            setPlanMaxDate(moment(result.endDate).endOf("year").add(1, "year"));
        }
        if (orgInfo.deptDataType === 1) {
            const res = await getDeptHouseDetail(orgInfo.orgId);
            const {result} = res;
            if (!isDefined(result.org) || result.org.startDate === null || result.org.endDate === null) {
                // message.warning("未读取到数据，请配置开工日期、竣工日期！");
                setPlanMinDate(moment(defaultMinData));
                setPlanMaxDate(moment(defaultMaxData));
                return;
            }
            setPlanMinDate(moment(result.org.startDate).startOf("year").subtract(1, "year"));
            setPlanMaxDate(moment(result.org.endDate).endOf("year").add(1, "year"));
        }
    }, [orgInfo.deptDataType, orgInfo.orgId]);

    const getApprovalTemplateList = useCallback(async () => {
        setApprovalOptions([]);
        const res = await getProcessTemplates({
            appModule: -1,
            busiModule: "PLAN_APPROVAL",
            nodeId: orgInfo.orgId,
            page: 1,
            size: 999999,
        });
        if (res.success) {
            if (Array.isArray(res.result.content)) {
                const approvalList = [
                    {
                        id: "0",
                        typeName: "无需审批"
                    },
                    ...res.result.content.filter((item) => item.switchStatus === 1),
                ];
                setApprovalOptions(approvalList.map((item) => ({
                    value: item.id,
                    label: item.typeName,
                })));
            }
        }
    }, [orgInfo.orgId]);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            await fetchCalendarList();
            await fetchProjectList();
            await getProjectDetail();
            await getApprovalTemplateList();
            if (planId !== undefined) {
                await getDetail();
            }
        } catch (error) {
            console.log("error", error);
        } finally {
            setLoading(false);
        }
    }, [fetchCalendarList, fetchProjectList, getApprovalTemplateList, getDetail, getProjectDetail, planId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const onValuesChange = useCallback((changedValues: {[key: string]: any}, values: AddOrEditFormType) => {
        // console.log("onValuesChange", changedValues);
        setIsDataChange(true);
        // if (changedValues.project !== undefined) {
        //     getApprovalTemplateList(changedValues.project);
        //     form.setFieldsValue({
        //         approvalTemplateId: undefined,
        //     });
        // }
        if (changedValues.planType !== undefined) {
            const value = changedValues.planType as PlanCycleType;
            setCycleType(value);
            form.setFieldsValue({
                planCycle: undefined,
                planStartDate: undefined,
                planEndDate: undefined
            });
        }
        if (changedValues.planCycle !== undefined) {
            const {planCycle} = changedValues;
            // console.log("planCycle", planCycle.format("YYYY.MM.DD"));
            form.setFieldsValue({
                planStartDate: planCycle,
                planEndDate: getEndDate(planCycle, values.planType),
            });
        }
    }, [form]);

    const onClose = useCallback(() => {
        if (isDataChange) {
            confirmLeave(back);
        } else {
            setVisible(false);
        }
    }, [back, isDataChange, setVisible]);

    const doSaveOrEditPlanInfo = useCallback(async (values) => {
        const projectNode = projectList.find((item) => item.id === values.project);
        console.log("projectNode", projectNode);
        const {data: calendar} = await getCalendarInfo(values.planCalendar);
        //  calendarOption.find((i) => i.value === values.planCalendar.value);
        if (projectNode === undefined || calendar === undefined) {
            return;
        }
        const params: AddPlanInfoType = {
            deptId: orgInfo.orgId,
            nodeId: projectNode.id,
            nodeType: projectNode.type,
            name: values.planName,
            type: values.planType,
            startDate: values.planStartDate.startOf("day").valueOf(),
            endDate: values.planEndDate.startOf("day").add(1, "day").subtract(1, "second").valueOf(),
            cycle: values.planCycle !== undefined ? moment(values.planCycle).valueOf() : null,
            calendar: values.planCalendar,
            approvalTemplateId: values.approvalTemplateId !== "0" ? values.approvalTemplateId : "",
        };
        // console.log("params", params);
        // console.log("planInfo", planInfo);
        try {
            if (planId === undefined) {
                const res = await addPlanInfo(params);
                console.log("addPlanInfo", res);
                if (res.success) {
                    planInfoBack(res.data, buildCalenderInfo(calendar));
                    // 新增成功，调用updateWorkGuideTask更新任务完成状态
                    if (orgInfo?.orgId !== "" && curSectionInfo?.id !== undefined && leafMenuId !== "") {
                        updateWorkGuideTask(orgInfo?.orgId, curSectionInfo.id, leafMenuId, "ADD");
                    }
                }
            } else {
                const updateParams: UpdatePlanInfoType = {
                    ...params,
                    id: planId,
                    parentId: planInfo?.parentId,
                };
                Object.keys(updateParams).forEach((key) => {
                    if (key !== "id" && (planInfo as any)?.[key] === (updateParams as any)[key]) {
                        delete (updateParams as any)[key];
                    }
                });
                const res = await updatePlanInfo(updateParams);
                console.log("updatePlanInfo", res);
                if (res.success) {
                    planInfoBack(res.data, buildCalenderInfo(calendar));
                }
            }
        } catch (error) {
            console.log("error", error);
        }
    }, [curSectionInfo, leafMenuId, orgInfo, planId, planInfo, planInfoBack, projectList]);

    const onFinish = useCallback((values) => {
        console.log("onFinish", values);
        // console.log("planInfo", planInfo);
        const {project, planType, planCycle} = values;
        const cycle = planCycle !== undefined ? moment(planCycle).valueOf() : 0;

        const changeProject = project !== planInfo?.nodeId;
        const changeType = planType !== planInfo?.type;
        const changeCycle = planType !== "MASTER" && cycle !== planInfo?.cycle;
        const isChanged = changeProject || changeType || changeCycle;

        // console.log("changeCycle", planType !== "MASTER");
        // console.log("cycle !== planInfo?.cycle", cycle !== planInfo?.cycle);

        // console.log("changeProject", changeProject);
        // console.log("changeType", changeType);
        // console.log("changeCycle", changeCycle);
        if (planInfo !== undefined && (planInfo.parentId.length > 0 || planInfo.hasChildren) && isChanged) {
            let changeTitle = "计划周期";
            if (changeCycle) {
                changeTitle = "计划周期";
            } else if (changeType) {
                changeTitle = "计划类型";
            } else {
                changeTitle = "项目";
            }
            Modal.confirm({
                title: `修改“${changeTitle}”将清空原上级计划关联数据，是否确定修改？`,
                okText: "确定",
                cancelText: "取消",
                onOk: async () => doSaveOrEditPlanInfo(values),
            });
        } else {
            doSaveOrEditPlanInfo(values);
        }
    }, [doSaveOrEditPlanInfo, planInfo]);

    const renderRowBox = useCallback((leftText?: string, rightText?: string | React.ReactNode) => (
        <Row style={{lineHeight: "20px", marginBottom: 16}}>
            <Col style={{width: 112, color: "#717784", marginRight: 20}}>{leftText ?? ""}</Col>
            <Col style={{color: "#061127"}}>{rightText ?? ""}</Col>
        </Row>
    ), []);

    if (readonly === true) {
        // console.log("projectOptions", projectOptions);
        // console.log("projectOptions", projectOptions?.find((item) => item.value === form.getFieldValue("project")));
        return (
            <ComModal
                title="计划信息"
                onCancel={onClose}
                visible={visible}
                width={480}
                onOk={onClose}
                destroyOnClose
                renderFooterStatus={false}
            >
                <Spin spinning={loading}>
                    <div style={{padding: "16px 16px 32px"}}>
                        {renderRowBox("所属项目", projectOptions?.find((item) => item.value === planInfo?.nodeId)?.label ?? "")}
                        {renderRowBox("计划名称", planInfo?.name ?? "")}
                        {renderRowBox("计划类型", planTypeList?.find((item) => item.value === planInfo?.type)?.label ?? "")}
                        {
                            cycleType !== "MASTER" && (
                                <>
                                    {
                                        renderRowBox(
                                            "计划周期",
                                            planInfo?.cycle !== undefined ? getCycleMomentText(Number(planInfo.cycle), cycleType) : ""
                                        )
                                    }
                                </>
                            )
                        }
                        {renderRowBox("计划开始日期", planInfo?.startDate !== undefined ? momentText(planInfo?.startDate) : "")}
                        {renderRowBox("计划完成日期", planInfo?.endDate !== undefined ? momentText(planInfo?.endDate) : "")}
                        {renderRowBox("工作日历", calendarOptions?.find((item) => item.value === planInfo?.calendar)?.label ?? "")}
                        {renderRowBox("审批流程", approvalOptions?.find((item) => item.value === planInfo?.approvalTemplateId)?.label ?? "")}
                    </div>
                </Spin>
            </ComModal>
        );
    }

    return (
        <ComModal
            title={`${planId === undefined ? "新增" : "编辑"}计划信息`}
            onCancel={onClose}
            visible={visible}
            width={480}
            onOk={() => form.submit()}
            destroyOnClose
        >
            <Spin spinning={loading}>
                <Form {...layout} form={form} onValuesChange={onValuesChange} style={{padding: 24}} onFinish={onFinish}>
                    <Form.Item label="项目" name="project" rules={[{required: true}]}>
                        <Select options={projectOptions} placeholder="请选择项目" />
                    </Form.Item>
                    <Form.Item label="计划名称" name="planName" rules={[{required: true}]}>
                        <Input placeholder="请输入计划名称" maxLength={100} />
                    </Form.Item>
                    <Form.Item label="计划类型" name="planType" rules={[{required: true, message: "请选择计划类型"}]}>
                        <Select options={planTypeList} placeholder="请选择计划类型" />
                    </Form.Item>
                    {
                        cycleType !== "MASTER" && (
                            <Form.Item label="计划周期" name="planCycle" rules={[{required: true, message: "请选择计划周期"}]}>
                                <PlanCycleDatePicker
                                    cycleType={cycleType}
                                    minDate={planMinDate}
                                    maxDate={planMaxDate}
                                />
                            </Form.Item>
                        )
                    }
                    <Form.Item
                        label="计划开始日期"
                        name="planStartDate"
                        rules={[{required: true, message: "请选择计划开始日期"}]}
                    >
                        <DatePicker
                            format="YYYY.MM.DD"
                            style={{width: "100%"}}
                            placeholder="请选择计划开始日期"
                            disabledDate={(cur: Moment) => {
                                if (planMinDate === undefined || planMaxDate === undefined || cur < planMinDate || cur > planMaxDate) {
                                    return true;
                                }
                                const planEndDate = form.getFieldValue(["planEndDate"]);
                                if (Boolean(planEndDate) === true) {
                                    return cur > planEndDate;
                                }
                                return false;
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label="计划完成日期"
                        name="planEndDate"
                        rules={[{required: true, message: "请选择计划完成日期"}]}
                    >
                        <DatePicker
                            format="YYYY.MM.DD"
                            style={{width: "100%"}}
                            placeholder="请选择计划完成日期"
                            disabledDate={(cur: Moment) => {
                                if (planMinDate === undefined || planMaxDate === undefined || cur < planMinDate || cur > planMaxDate) {
                                    return true;
                                }
                                const planStartDate = form.getFieldValue(["planStartDate"]);
                                if (Boolean(planStartDate) === true) {
                                    return cur < planStartDate;
                                }
                                return false;
                            }}
                        />
                    </Form.Item>
                    <Form.Item label="工作日历" name="planCalendar" rules={[{required: true, message: "请选择工作日历"}]}>
                        <Select
                            disabled={hasParentPlan} // 存在上级计划，日历不可修改
                            options={calendarOptions}
                            placeholder="请选择工作日历"
                        />
                    </Form.Item>
                    <Form.Item
                        label="审批流程"
                        name="approvalTemplateId"
                        rules={[{required: planInfo?.approvalStatus !== ApprovalStatus.RETURN, message: "请选择审批流程"}]}
                    >
                        <Select disabled={planInfo?.approvalStatus === ApprovalStatus.RETURN} options={approvalOptions} placeholder="请选择审批流程" />
                    </Form.Item>
                </Form>
            </Spin>
        </ComModal>
    );
};
export default PlanInfoModal;
