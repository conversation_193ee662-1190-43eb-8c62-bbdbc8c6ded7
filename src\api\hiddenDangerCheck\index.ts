import {Key} from "react";
import {WebRes, WebResResult} from "../common.type";
import Fetch from "../../service/Fetch";
import {GetHiddenDangerCheckDetailReturn, GetHiddenDangerCheckListReturn, QuerySecurityListSatuteGistReturn, QueryQualityListReferenceReturn} from "./type";
import FileFetch from "../../service/FileFetch";

/** 进度检查列表 */
export const getHiddenDangerCheckList = async (params: {}): Promise<WebRes<GetHiddenDangerCheckListReturn>> => Fetch({
    url: "/sphere/plan/check/page",
    methods: "post",
    data: params
});

/** 获取详情 */
export const getHiddenDangerCheckDetail = async (id: string): Promise<WebRes<GetHiddenDangerCheckDetailReturn>> => Fetch({
    url: `/sphere/plan/check/id/${id}`,
    methods: "get",
});

export const updateHiddenDangerCheck = async (param: {}): Promise<WebRes> => Fetch({
    url: "/sphere/plan/check/update",
    methods: "put",
    data: param
});

export const addHiddenDangerCheck = async (param: {}): Promise<WebRes<string>> => Fetch({
    url: "/sphere/plan/check/add",
    methods: "post",
    data: param
});

export const delHiddenDangerCheck = async (param: Key[]): Promise<WebRes> => Fetch({
    url: "/sphere/plan/check/delete",
    methods: "delete",
    data: param
});

// 法规依据列表（安全）
export const querySecurityListSatuteGist = async (ids: string[]): Promise<WebResResult<QuerySecurityListSatuteGistReturn[]>> => Fetch({
    url: "/businessdata/rs/check/security/listSatuteGist",
    methods: "post",
    data: {ids}
});
// 法规依据列表（质量）
export const queryQualityListReference = async (ids: string[]): Promise<WebResResult<QueryQualityListReferenceReturn[]>> => Fetch({
    url: "/businessdata/rs/check/quality/listReference",
    methods: "post",
    data: {ids}
});
export const exportExcel = (params: {}, fileName: string) => FileFetch({
    url: "/sphere/plan/check/export-page",
    methods: "post",
    data: params,
    fileName
});
