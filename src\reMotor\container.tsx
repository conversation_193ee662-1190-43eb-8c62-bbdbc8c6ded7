import React, {CSSProperties, FC, useRef} from "react";
// import MotorContext from "./context";

interface ReactMotorProps {
    /** motor baseUrl，必传 */
    baseUrl: string;
    /** motor token，必传 */
    token: string;
    /** 当前打开模型的motor3dId，必传 */
    motor3dId: string;
    /** 容器的宽度 */
    width: string | number;
    /** 容器的高度 */
    height: string | number;
    className?: string;
    style?: CSSProperties;
}

/** 对motor做基于react hooks写法的开箱即用的封装 */
const ReactMotor: FC<ReactMotorProps> = (props) => {
    const {
        width = "100%",
        height = "100%",
        // baseUrl,
        // token,
        // motor3dId,
        className,
        style,
        children
    } = props;
    const motorDom = useRef<HTMLDivElement>(null);

    // const initMotor = useCallback(
    //     () => {
    //         if (motorDom.current === null) {
    //             throw new Error("can not get motor dom");
    //         }
    //         MotorContext.initViewer(motorDom.current, baseUrl, token, motor3dId);
    //     },
    //     [baseUrl, motor3dId, token]
    // );

    // useEffect(
    //     () => {
    //         initMotor();
    //     },
    //     [initMotor]
    // );

    // useEffect(
    //     () => () => {
    //         MotorContext.destroyViewer();
    //     },
    //     []
    // );

    return (
        <div
            className={className}
            style={{width, height, position: "relative", ...style}}
            ref={motorDom}
        >
            {children}
        </div>
    );
};

export default ReactMotor;
