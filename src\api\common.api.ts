/* eslint-disable no-underscore-dangle */

import Fetch from "../service/Fetch";
import FileFetch from "../service/FileFetch";
import {AuthCodeList} from "../store/common/actionTypes";
import {GetAppAllocationPackageInfoReturn, GetDeptOrgListReturn, GetDownloadUrlRes1, GetOrgListProjNodeByDeptIdReturn, LoginInfoDataType, MenuListProps, OrgNode, TokenUserInfo, UserAuthPackageType, WebRes, WebResResult, GetDeptDetailResult, HouseDeptRes, PackageDownloadParams, GetFileUrlRes, GetDocListParams, GetDocListRes, GetDocTreeParams, GetDocTreeRes, GetProcessTemplatesParams, GetProcessTemplatesRes, OrgProjNodeVo, ProjectTreeVO, Motor3dStatusResult, ProjectDetailInfoVo} from "./common.type";
import {ProRes} from "./rectification/models/process";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const {baseUrl} = window.__IWorksConfig__ as any;

// /** 获取motor token */
// export const getMotorToken = async () => Fetch<string>({
//     url: `${baseUrl}/pds/rs/cim/motor/client_token`
// });

export const getOrgInfo = async (deptId: string): Promise<WebRes<OrgNode>> => Fetch({url: `${baseUrl}/pdscommon/rs/org/detailByDeptId/${deptId}`, methods: "get"});
export const getOrgList = async (): Promise<WebRes<OrgNode[]>> => Fetch({url: `${baseUrl}/pdscommon/rs/org/list`, methods: "get"});
export const putAuthEnterprise = async (epid: number): Promise<WebRes<LoginInfoDataType>> => Fetch({
    url: `${baseUrl}/auth-server/auth/enterprise`,
    methods: "put",
    data: {
        epid
    },
});
// 根据已有子系统的token，获取另一个子系统的token
export const getInnerSystemToken = async (token: string, loginType: string): Promise<WebRes<string>> => Fetch({
    url: `${baseUrl}/auth-server/auth/innerSystemToken/${loginType}`,
    methods: "get",
    token
});
// 根据已有子系统的token，获取另一个子系统的token
export const getLoginTypeInnerSystemToken = async (loginType: string): Promise<WebRes<string>> => Fetch({
    url: `${baseUrl}/auth-server/auth/innerSystemToken/${loginType}`,
    methods: "get",
});

// 获取权限
export const getAuthCode = async (productId: number): Promise<WebRes<AuthCodeList[]>> => Fetch({
    url: `${baseUrl}/auth-server/auth/authgroup/${productId}`,
    methods: "get",
});

/**
 * 根据token获取用户信息
 */
export const getUserInfoByToken = async (): Promise<WebRes<TokenUserInfo>> => Fetch({
    url: `${baseUrl}/auth-server/user/get-user-detail`,
    methods: "get",
});

/**
 * 根据token获取用户信息
 */
export const getMenuList = async (parentId: number): Promise<WebRes<MenuListProps[]>> => Fetch({
    url: `${baseUrl}/auth-server/auth/menu/parentId/${parentId}`,
    methods: "post",
});

/**
 * 根据项目部id获取下面的标段
 */
export const getDeptOrgList = async (deptId: string): Promise<WebRes<GetDeptOrgListReturn[]>> => Fetch({
    url: `${baseUrl}/sphere/rest/deptOrg/loadOrgList/${deptId}`,
    methods: "get",
});

// 根据项目部id获取下面所有的节点
export const getOrgListProjNodeByDeptId = async (deptId: string): Promise<GetOrgListProjNodeByDeptIdReturn[]> => Fetch({
    url: `${baseUrl}/pdscommon/rs/org/listProjNodeByDeptId/${deptId}`,
    methods: "get",
    isDeal: false
});

/**
 * 获取全部客户端
 */
export const getAppAllocationPackageInfo = async (): Promise<WebResResult<GetAppAllocationPackageInfoReturn[]>> => Fetch({
    url: `${baseUrl}/builder/appallocation/getAppAllocationPackageInfo`,
    methods: "get",
});

/**
 * 获取全部授权客户端
 */
export const getUserAuthPackage = async (userName: string): Promise<WebResResult<UserAuthPackageType[]>> => Fetch({
    url: `${baseUrl}/builder/appallocation/getUserAuthPackage/userName/${userName}/packageType/31`,
    methods: "get",
});

/**
 * 获取基建项目详情
 */
export const getDeptDetail = async (deptId: string): Promise<WebResResult<GetDeptDetailResult>> => Fetch({
    url: `${baseUrl}/builder/infra/dept/detail/deptId/${deptId}`,
    methods: "get",
});

/**
 * 获取房建项目详情
 */
export const getDeptHouseDetail = async (deptId: string): Promise<WebResResult<HouseDeptRes>> => Fetch({
    url: `${baseUrl}/builder/org/${deptId}`,
    methods: "get",
});

// 附件管理

/**
 * 获取附件上传地址
 * @param params
 * @returns
 */
export const getAttachmentUploadUrl = async (params: unknown[]): Promise<GetDownloadUrlRes1[]> => Fetch({
    url: `${baseUrl}/pdscommon/rs/fileaddress/applyUploadUrlEx`,
    methods: "post",
    data: params,
    isDeal: false
});

/**
 * 上传文件
 * @param baseUrl 文件上传你地址
 * @param file 文件
 * @returns uuid,string
 */
export const uploadAttachment = async (uploadUrl: string, file: unknown) => {
    const form = new FormData();
    form.append("file", file as Blob);
    return Fetch<string>({
        baseUrl: uploadUrl,
        url: "",
        methods: "post",
        formData: form,
    });
};

export const getDocUrl = async (fileUuid: string, fileName: string, depId: string): Promise<WebResResult<string>> => {
    const requestParams = {
        nodeType: 1,
        nodeId: depId,
        uuid: fileUuid,
        filename: fileName,
        type: 1,
        viewMode: 1
    };
    const param = {
        url: `${baseUrl}/pdsdoc/rs/doc/preview`,
        methods: "post" as const,
        data: requestParams
    };
    return Fetch(param);
};

// kv获取接口
export const getKV = async (k: string) => Fetch<WebRes<string>>({
    url: `/sphere/common/kv/getKV/${k}`,
});
// kv存储接口
export const setKV = async (data: {}) => Fetch<WebRes<string>>({
    methods: "post",
    url: "/sphere/common/kv/setKV",
    data
});

// 批量下载成压缩包
export const packageDownload = (params: {packDownParam: PackageDownloadParams; fun: () => void}) => FileFetch({
    methods: "post",
    url: "/sphere/files/zip",
    data: params.packDownParam,
    fileName: params.packDownParam.packName,
    finallyFun: params.fun
});

/**
 * 获取文件的下载地址,预览地址,据说永久有效
 * @param ids uuid数组
 * @returns 未知
 */
export const getFileUrl = async (ids: string[]) => Fetch<GetFileUrlRes[]>({
    methods: "post",
    url: "/pdscommon/rs/fileaddress/longLineDownloadURLs",
    data: {fileUUIDList: ids}
});

// 获取文档目录树结构
export const getDocList = async (params: GetDocListParams) => Fetch<ProRes<GetDocListRes>>({
    methods: "post",
    url: "/pdsdoc/rs/doc/list_v2",
    data: params,
});

// 获取文档目录树结构
export const getDocPathTree = async (params: GetDocTreeParams) => Fetch<ProRes<GetDocTreeRes>>({
    methods: "post",
    url: "/pdsdoc/rs/path/tree",
    data: params,
});

/**
 * 获取文件的预览地址
 * @param params
 * @returns
 */
export const getPreviewUrl = async (params: {}) => Fetch<string>({
    methods: "post",
    url: "/pdscommon/rs/fileaddress/viewUrl",
    data: params,
    responseType: "text"
});

/** 获取审批模板列表 */
export const getProcessTemplates = async (params: GetProcessTemplatesParams): Promise<WebResResult<GetProcessTemplatesRes>> => Fetch({
    url: "/process/process/templates",
    methods: "post",
    data: params,
});

/** 获取项目部下的工程组织树列表（标段，单项工程，单位工程，工程）（iworks&iworksApp） */
export const getListProjNode = async (deptId: string) => Fetch<OrgProjNodeVo[]>({
    url: `/pdscommon/rs/org/listProjNodeByDeptId/${deptId}`,
    methods: "get",
});

/** 查询模型的构件列表 */
export const getProjectDetail = async (ppid: string) => Fetch<WebRes<ProjectTreeVO>>({
    url: `/luban-infrastructure-center/wbs-bind-ebs/load-project-detail/${ppid}`
});

/** 获取motor3d模型转换信息 */
export const getMotorInfoByPpid = async (ppid: number) => Fetch<WebResResult<Motor3dStatusResult>>({
    url: `/pdscommon/rs/project/extract/motor3d/status/${ppid}`
});

/** 获取工程详情 */
export const getProjectDetailInfo = async (ppid: number | string) => Fetch<WebRes<ProjectDetailInfoVo>>({
    url: `/pdscommon/rs/proj/detail/ppid/${ppid}`
});

export const getMotorToken2 = async () => Fetch<WebRes<string>>({
    url: "/auth-server/auth/motor/client_token"
});
