import React, {forwardRef, useCallback, useEffect, useState} from "react";
import {Input, InputProps} from "antd";
import {SearchOutlined} from "@ant-design/icons";


interface MyInputSearchProps extends InputProps {
    onSearch?: (value: string) => void;
}

/**
 * 搜索输入框
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const MyInputSearch = (props: MyInputSearchProps, ref: any) => {
    const {className, onSearch, value: _value, onChange, ...otherProps} = props;
    const [value, setValue] = useState<string>("");

    useEffect(() => {
        setValue(String(_value ?? ""));
    }, [_value]);

    const handleChange: InputProps["onChange"] = useCallback((e) => {
        setValue(e.target.value);
        if (onChange instanceof Function) {
            onChange(e);
        }
    }, [onChange]);

    const handlePressEnter = useCallback(() => {
        if (onSearch instanceof Function) {
            onSearch(value);
        }
    }, [value, onSearch]);

    return (
        <Input
            {...otherProps}
            ref={ref}
            className={className}
            prefix={<SearchOutlined />}
            onPressEnter={handlePressEnter}
            value={value}
            onChange={handleChange}
        />
    );
};

export default forwardRef(MyInputSearch);
