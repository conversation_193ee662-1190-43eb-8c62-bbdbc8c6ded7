/* eslint-disable @typescript-eslint/camelcase */
import React, {useContext, useEffect, useState} from "react";
import {DatePicker, Form, Input, Modal} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import {Store} from "antd/lib/form/interface";
import moment from "moment";
import {GanttTask} from "../gantt/interface";
import {linksWithPredecessors, linksWithTargetId, taskPredecessors} from "../gantt/taskUtils";
import PredecessorsInput from "./PredecessorsInput";
// import ActualWorkItems from "./ActualWorkItems";
import {notNullOrUndefined} from "../common/function";
import {isStandardCalendar} from "../common/calendar";
import {TaskStatus} from "../common/constant";
import ganttManager from "../gantt/ganttManager";
import SelectPersonItem from "./SelectPersonDrawer/SelectPersonItem";
import EditorContext from "../views/GanttEditor/context";
import DurationInputItems from "./DurationInputItems";

export interface EditTaskModalProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    editTask: GanttTask | undefined;
}

const EditTaskModal = (props: EditTaskModalProps) => {
    const {visible, setVisible, editTask} = props;
    const [form] = Form.useForm();
    const {planInfo, hasParentPlan} = useContext(EditorContext);
    // const [staffList, setStaffList] = useState<TaskDutyPerson[]>([]);
    const [type, setType] = useState<string | undefined>(editTask?.type);

    useEffect(() => {
        if (visible) {
            form.resetFields();
        }
    }, [form, visible]);
    useEffect(() => {
        if (visible && editTask !== undefined) {
            form.resetFields();
            const initValues = {
                name: editTask.text,
                type: editTask.type,
                dutyUnit: editTask.dutyUnit,
                dutyPerson: editTask.dutyPerson,
                predecessors: taskPredecessors(editTask),
                milestone_time: moment(editTask.start_date)
            };
            form.setFieldsValue(initValues);
            setType(editTask.type);
        }
    }, [visible, editTask, form]);

    // 解析子任务实际时间
    const parseTaskActualDate = (task: GanttTask, values: Store): GanttTask => {
        const newTask = task;
        if (values.taskStatus === TaskStatus.NOT_START) {
            newTask.actual_duration = -1;
            newTask.actual_start = undefined;
            newTask.actual_end = undefined;
        } else {
            if (notNullOrUndefined(values.actual_start_date)) {
                const newActualStart = values.actual_start_date.toDate();
                if (newTask.actual_start !== newActualStart) {
                    newTask.actual_start = newActualStart;
                }
            } else {
                newTask.actual_start = undefined;
            }
            if (values.taskStatus === TaskStatus.IN_PROGRESS) {
                // 进行中，计算预计时间
                newTask.actual_duration = -1;
                newTask.actual_end = undefined;
            } else if (notNullOrUndefined(newTask.actual_start) && notNullOrUndefined(values.actual_end_date)) {
                // 实际工期
                newTask.actual_end = gantt.date.add(gantt.date.day_start(values.actual_end_date.toDate()), isStandardCalendar(ganttManager.currentCalendarInfo) ? 17 : 0, "hour");
                const duration = gantt.calculateDuration({
                    start_date: newTask.actual_start,
                    end_date: newTask.actual_end,
                    task: editTask
                });
                newTask.actual_duration = duration;
            }
        }
        return newTask;
    };

    // 解析父任务实际时间
    const parseProjectActualDate = (task: GanttTask, values: Store): GanttTask => {
        const newTask = task;
        if (notNullOrUndefined(values.actual_duration)
            && notNullOrUndefined(values.actual_start_date)
            && notNullOrUndefined(values.actual_end_date)) {
            newTask.actual_duration = ganttManager.durationFormatter.parse(`${values.actual_duration}`);
            newTask.actual_start = values.actual_start_date?.toDate();
            newTask.actual_end = values.actual_end_date?.toDate();
        } else {
            newTask.actual_duration = -1;
            newTask.actual_start = undefined;
            newTask.actual_end = undefined;
        }
        return newTask;
    };

    // 解析任务要求时间
    const parseRequestDate = (task: GanttTask, values: Store): GanttTask => {
        const newTask = task;
        if (notNullOrUndefined(values.request_duration)
            && notNullOrUndefined(values.request_start_date)
            && notNullOrUndefined(values.request_end_date)) {
            newTask.request_duration = ganttManager.durationFormatter.parse(`${values.request_duration}`);
            newTask.request_start_date = values.request_start_date?.toDate();
            newTask.request_end_date = values.request_end_date?.toDate();
        } else {
            newTask.request_duration = -1;
            newTask.request_start_date = undefined;
            newTask.request_end_date = undefined;
        }
        return newTask;
    };

    const handleSave = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
        e.preventDefault();
        form.validateFields()
            .then((values) => {
                console.log("handleSave", values, type);
                if (editTask === undefined) {
                    return;
                }
                let newTask: GanttTask = {
                    ...editTask,
                    text: values.name,
                    type,
                    dutyUnit: values.dutyUnit,
                    dutyPerson: values.dutyPerson,
                    predecessors: values.predecessors,
                };
                if (type === gantt.config.types.milestone) {
                    newTask.actual_end = undefined;
                    newTask.actual_start = undefined;
                    newTask.actual_duration = -1;
                    newTask.taskStatus = undefined;
                    if (values.milestone_time !== undefined) {
                        newTask.start_date = values.milestone_time.toDate();
                    }
                } else {
                    newTask.duration = ganttManager.durationFormatter.parse(`${values.plan_duration}`);
                    newTask.start_date = values.plan_start_date?.toDate();
                    newTask.end_date = values.plan_end_date?.toDate();
                    newTask = {
                        ...newTask,
                        ...parseRequestDate(newTask, values)
                    };
                    // 进度执行状态
                    if (notNullOrUndefined(values.taskStatus)) {
                        newTask.taskStatus = values.taskStatus;
                    }
                    if (newTask.type === gantt.config.types.task) {
                        newTask = {
                            ...newTask,
                            ...parseTaskActualDate(newTask, values),
                        };
                    } else {
                        newTask = {
                            ...newTask,
                            ...parseProjectActualDate(newTask, values),
                        };
                    }
                }

                if (newTask.$new === true) {
                    // 新建后，删除$new，和gantt组件保持一致
                    delete newTask.$new;
                    gantt.addTask(newTask, newTask?.parent, gantt.getTaskIndex(newTask?.id));
                } else {
                    linksWithTargetId(newTask.id).forEach((item) => {
                        gantt.deleteLink(item.id);
                    });
                    if (newTask.predecessors !== undefined && newTask.predecessors !== "") {
                        const predecessors = newTask.predecessors.split(",")
                            .filter((linkStr: string) => linkStr.startsWith("undefined") === false)
                            .join(", ");
                        const links = linksWithPredecessors(predecessors, newTask);
                        links.forEach((item) => {
                            gantt.addLink(item);
                        });
                    }
                    // 需要更新前置任务link之后再更新任务
                    gantt.updateTask(newTask.id, newTask);
                    // updateProjectActualTimeWithTask(newTask);
                }
                if (setVisible instanceof Function) {
                    setVisible(false);
                }
            });
    };

    const handleCancel = () => {
        if (editTask !== undefined) {
            if (gantt.isTaskExists(editTask.id) === true && editTask.$new === true) {
                gantt.deleteTask(editTask.id);
            }
            if (setVisible instanceof Function) {
                setVisible(false);
            }
        }
    };

    return (
        <Modal
            width={640}
            bodyStyle={{padding: 0}}
            title={<span style={{fontWeight: "bold"}}>{editTask?.$new === true ? "新建任务" : "编辑任务"}</span>}
            visible={visible}
            onCancel={handleCancel}
            onOk={handleSave}
            destroyOnClose
            maskClosable={false}
            keyboard={false}
        >
            <Form form={form} style={{padding: "20px"}} requiredMark={false}>
                <Form.Item
                    label="任务名称"
                    name="name"
                    labelCol={{span: 4}}
                    wrapperCol={{span: 20}}
                    rules={[
                        {
                            required: true,
                            message: "请输入任务名称"
                        }
                    ]}
                >
                    <Input placeholder="请输入任务名称" />
                </Form.Item>
                <Form.Item
                    label="责任单位"
                    name="dutyUnit"
                    labelCol={{span: 4}}
                    wrapperCol={{span: 20}}
                >
                    <Input placeholder="请输入责任单位" />
                </Form.Item>
                <Form.Item
                    label="责任人"
                    name="dutyPerson"
                    labelCol={{span: 4}}
                    wrapperCol={{span: 20}}
                >
                    <SelectPersonItem
                        placeholder="请选择责任人"
                        nodeId={planInfo.nodeId}
                        nodeType={planInfo.nodeType}
                    />
                </Form.Item>
                {/* <Form.Item
                    label="任务类型"
                    name="type"
                    labelCol={{span: 4}}
                    wrapperCol={{span: 20}}
                >
                    <Select onSelect={(value: string) => setType(value)} placeholder="请选择任务类型">
                        {
                            Object.keys(gantt.config.types)
                                .filter((key) => key !== "placeholder")
                                .map((key) => (
                                    <Select.Option key={key} value={key}>
                                        {
                                            (gantt.locale.labels as any)?.[`type_${key}`] // eslint-disable-line
                                        }
                                    </Select.Option>
                                ))
                        }
                    </Select>
                </Form.Item> */}
                {
                    type !== gantt.config.types.milestone && (
                        <DurationInputItems
                            form={form}
                            editTask={editTask}
                            editType="request"
                            taskType={type}
                            disabled={hasParentPlan}
                            defaultvalue={{
                                duration: Number(editTask?.request_duration) ?? undefined,
                                startDate: editTask?.request_start_date ?? undefined,
                                endDate: editTask?.request_end_date ?? undefined
                            }}
                        />
                    )
                }
                {
                    type !== gantt.config.types.milestone && (
                        <DurationInputItems
                            form={form}
                            editTask={editTask}
                            taskType={type}
                            disabled={type === gantt.config.types.project}
                            defaultvalue={{
                                duration: Number(editTask?.duration) ?? 1,
                                startDate: editTask?.start_date ?? undefined,
                                endDate: editTask?.end_date ?? undefined
                            }}
                        />
                    )
                }
                {
                    type === gantt.config.types.milestone && (
                        <Form.Item
                            label="里程碑时间"
                            name="milestone_time"
                            labelCol={{span: 4}}
                            wrapperCol={{span: 8}}
                        >
                            <DatePicker style={{width: "100%"}} placeholder="请选择里程碑时间" />
                        </Form.Item>
                    )
                }
                <Form.Item
                    noStyle
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    name="predecessors"
                >
                    <PredecessorsInput
                        label="前置任务"
                        editTask={editTask}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};


export default EditTaskModal;
