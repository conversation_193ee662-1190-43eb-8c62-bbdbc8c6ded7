import {FormInstance, Input, InputProps, message} from "antd";
import {uniqBy} from "lodash-es";
import React, {useState} from "react";
import {createUseStyles} from "react-jss";
import MultipleChoice, {CheckSubOptionValueType} from "./MultipleChoice";

const useStyle = createUseStyles({
    box: {},
});

interface FormCheckSubOptionProps extends Omit<InputProps, "value" | "onChange" | "form"> {
    value?: CheckSubOptionValueType[];
    onChange?: (value: CheckSubOptionValueType[]) => void;
    form: FormInstance;
    // 工程类别key
    projectCategoryKey: string;
    // 模块类型
    moduleType: string;
}

const FormCheckSubOption = (props: FormCheckSubOptionProps) => {
    const {value = [], onChange, form, projectCategoryKey, moduleType, ...other} = props;
    const cls = useStyle();
    const [visible, setVisible] = useState<boolean>(false);
    const [projectCategoryId, setProjectCategoryId] = useState<string>("");

    const handleClose = () => {
        setVisible(false);
    };

    const handleClick = () => {
        const tempProjectCategoryId = form.getFieldValue(projectCategoryKey);
        if (tempProjectCategoryId === undefined) {
            message.info("请先选择工程类型");
        } else {
            setProjectCategoryId(tempProjectCategoryId);
            setVisible(true);
        }
    };

    const getValue = () => uniqBy(value, "subOptionId").map((el) => el.subOptionContent).join(",");

    return (
        <div className={cls.box}>
            <Input autoComplete="off" {...other} onClick={handleClick} value={getValue()} />
            <MultipleChoice
                moduleType={moduleType}
                isVisible={visible}
                projectCategoryId={projectCategoryId}
                onClose={handleClose}
                value={value ?? []}
                onChange={onChange}
            />
        </div>
    );
};

export default FormCheckSubOption;
