import React, {useCallback, useEffect, useState} from "react";
import {Button, Col, Row, Typography} from "antd";
import {getTaskChangedProject} from "../ScheduleGantt/api/plan";
import {GetTaskChangedProjectType} from "../ScheduleGantt/api/plan/type";
import ModelSelect, {BindType, CheckBind, PlanData} from "../ScheduleGantt/ModelSelect";
import {postTaskEbsNodeWithTimeList} from "../../api/wbsEbs";
import {ProjNameVo} from "../../api/common.type";
import MyIconFont from "../MyIconFont";

interface PlanChangeProjectProps {
    planId?: string;
    /* 不同页面展示不同，planView: 进度审批展示页要展示的内容 */
    viewType?: string;
}
const {Text} = Typography;
const PlanChangeProject: React.FC<PlanChangeProjectProps> = (props) => {
    const {planId, viewType} = props;
    const [taskChangedProjectList, setTaskChangedProjectList] = useState<GetTaskChangedProjectType[]>([]); // 计划变更相关模型详情
    const [projInfo, setProjInfo] = useState<Partial<ProjNameVo>>();
    const [modelViewVisible, setModelViewVisible] = useState<boolean>(false);
    const [modelViewInfo, setModelViewInfo] = useState<CheckBind[]>([]);
    const [planData, setPlanData] = useState<PlanData[]>([]);

    const fetchPlanInfoDetail = useCallback(async () => {
        if (planId !== undefined) {
            const res = await getTaskChangedProject(planId);
            if (res.success) {
                setTaskChangedProjectList(res.data);
            }
        }
    }, [planId]);

    useEffect(() => {
        fetchPlanInfoDetail();
    }, [fetchPlanInfoDetail]);

    const handleClickModel = useCallback(async (changeProject) => {
        const {ppid, projName, changedTaskIdList} = changeProject;
        const listWbsEbsRes = await postTaskEbsNodeWithTimeList(changedTaskIdList);
        if (listWbsEbsRes.success) {
            const modelCompList: CheckBind[] = [];
            const tempPlanData: PlanData[] = [];
            listWbsEbsRes.data.forEach((node) => {
                const {ebsNodes} = node;
                ebsNodes.forEach((ebsNode) => {
                    modelCompList.push({
                        ppid: ebsNode.ppid,
                        floor: ebsNode.paths[0],
                        handle: ebsNode.handle,
                    });
                    tempPlanData.push({
                        bimGuid: ebsNode.handle ?? "",
                        startDate: ebsNode.startDate,
                        endDate: ebsNode.endDate,
                        preStartDate: ebsNode.preStartDate,
                        preEndDate: ebsNode.preEndDate
                    });
                });
            });
            setProjInfo({ppid, projName});
            setModelViewVisible(true);
            setModelViewInfo(modelCompList);
            setPlanData(tempPlanData);
        }
    }, []);

    const renderModelBottom = useCallback((changeProject, index: number) => {
        const {projName} = changeProject;
        if (viewType === "planView") {
            return (
                <Col>
                    <Row align="middle" wrap>
                        <Col>
                            <Button
                                type="link"
                                icon={<MyIconFont type="icon-moxing" />}
                                onClick={async () => handleClickModel(changeProject)}
                                style={{width: "100%", padding: "4px 0"}}
                            >
                                <Text
                                    ellipsis={{tooltip: projName}}
                                    style={{width: "120px", color: "#1f54c5", textAlign: "left"}}
                                >
                                    {projName}
                                </Text>
                            </Button>
                        </Col>
                        {(taskChangedProjectList.length !== index + 1) && (
                            <Col>
                                <div
                                    style={{width: 1, height: 16, margin: "0 16px", background: "#C8CBCF"}}
                                />
                            </Col>
                        )}
                    </Row>
                </Col>
            );
        }
        return (
            <Col span={10}>
                <Button
                    type="link"
                    icon={<MyIconFont type="icon-moxing" />}
                    onClick={async () => handleClickModel(changeProject)}
                    style={{width: "100%", paddingLeft: 0}}
                >
                    <Text
                        ellipsis={{tooltip: projName}}
                        style={{width: "100%", color: "#1f54c5", textAlign: "left"}}
                    >
                        {projName}
                    </Text>
                </Button>
            </Col>
        );
    }, [handleClickModel, taskChangedProjectList.length, viewType]);

    if (taskChangedProjectList.length === 0) {
        return <>暂无关联模型</>;
    }

    return (
        <>
            <Row>
                {
                    taskChangedProjectList.map((changeProject, index: number) => renderModelBottom(changeProject, index))
                }
            </Row>
            {/* 仅用于查看模型 */}
            <ModelSelect
                ppid={projInfo?.ppid}
                projName={projInfo?.projName}
                show={modelViewVisible}
                selectInfo={modelViewInfo}
                showSpin={false}
                selectType={BindType.bindComp}
                planData={planData}
            />
        </>
    );
};

export default PlanChangeProject;
