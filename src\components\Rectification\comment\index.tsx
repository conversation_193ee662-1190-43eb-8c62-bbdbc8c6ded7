import React from "react";
import {Avatar, List, Space, Typography} from "antd";
import moment from "moment";
import {RectificationOperationType, RectifyTypeList} from "../models/rectification";
import {ApprovalCommentVo} from "../../../api/rectification/models/process";
import useStyle from "./style";
import Color from "../../../assets/css/Color";
import FileBox from "../../FileBox";
import {transferAttachmentVoToFileType} from "../rectification-helpers";

const {Paragraph} = Typography;

interface CommentProps {
    commentList?: ApprovalCommentVo[];
    className?: string;
}

const renderTagColor = (type?: RectificationOperationType) => {
    if (type === RectificationOperationType.Initiate) {
        return {
            color: Color["green-1"],
            bgColor: Color["green-2"]
        };
    }
    return {
        color: Color["yellow-1"],
        bgColor: Color["yellow-2"]
    };
};

const CommentComp = (props: CommentProps) => {
    const cls = useStyle();
    const {commentList = [], className} = props;

    if (commentList === undefined || commentList.length === 0) {
        return null;
    }
    return (
        <List
            className={className}
            dataSource={commentList}
            itemLayout="vertical"
            renderItem={(v, i) => (
                <List.Item className={cls.itemBox} key={i.toString()}>
                    <div className={cls.topBox}>
                        <Avatar size={40} src={v.commentator?.userName}>{v.commentator?.realName}</Avatar>
                        <div className={cls.boxWithoutAvatar}>
                            <Space>
                                <span className={cls.itemName}>
                                    {v.operationType !== RectificationOperationType.AutoCopyTo ? v.commentator?.realName : "系统"}
                                </span>
                                {
                                    v.operationType !== RectificationOperationType.AutoCopyTo
                                        ? (
                                            <span
                                                className={cls.itemTag}
                                                style={{
                                                    color: renderTagColor(v.operationType).color,
                                                    background: renderTagColor(v.operationType).bgColor
                                                }}
                                            >
                                                {v.flowNodeName}
                                            </span>
                                        )
                                        : null
                                }
                            </Space>
                            <Space>
                                <span style={{fontSize: 12}}>{moment(v.commentTime).format("YYYY.MM.DD HH:mm:ss")}</span>
                                <span className={cls.itemAction}>{RectifyTypeList.find((r) => r.key === v.operationType)?.label}</span>
                            </Space>
                        </div>
                    </div>
                    {
                        Boolean(v.commentMsg) === true
                            ? (
                                <div className={cls.commentMsgBox}>
                                    <Paragraph ellipsis={{rows: 4, expandable: true, symbol: "展开"}}>
                                        {v.commentMsg ?? ""}
                                    </Paragraph>
                                </div>
                            )
                            : null
                    }
                    {
                        (v.attachments ?? []).length > 0
                            ? (
                                <FileBox
                                    isDownload
                                    value={transferAttachmentVoToFileType(v.attachments)}
                                    isEditName={false}
                                    isUpload={false}
                                    isDelete={false}
                                    style={{width: "100%"}}
                                />
                            )
                            : null
                    }
                </List.Item>
            )}
        />
    );
};

export default CommentComp;
