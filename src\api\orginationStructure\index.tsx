import Fetch from "../../service/Fetch";
import {GetDeptOrgListReturn, WebRes} from "../common.type";
import {GetGetOrgListWithPersonNumReturn, GetQueryorgSectionListParams, GetQueryorgSectionListReturn} from "./type";

// 获取组织机构列表【不加载人数】
export const getDeptOrgList = async (deptId?: string): Promise<WebRes<GetDeptOrgListReturn[]>> => Fetch({
    url: `/sphere/rest/deptOrg/loadOrgList/${deptId}`,
    methods: "get",
    data: {
        deptId
    }
});
// 获取组织机构列表【返回建设方及人数】
export const getOrgListWithPersonNum = async (deptId?: string): Promise<WebRes<GetGetOrgListWithPersonNumReturn[]>> => Fetch({
    url: `/sphere/rest/deptOrg/loadOrgListWithPersonNum/${deptId}`,
    methods: "get",
    data: {
        deptId
    }
});
// 根据标段id获取标段下面对应的部门
export const getQueryorgSectionList = async (
    params: GetQueryorgSectionListParams
): Promise<WebRes<GetQueryorgSectionListReturn[]>> => Fetch({
    url: `/sphere/rest/deptOrg/queryorgSectionList/${params.nodeId}/${params.buildType}`,
    methods: "get",
    data: params
});

// 获取项目下各标段的总人数
export const getQueryNodePersonNum = async (projectId: string): Promise<WebRes<{0: number; 1: number; 3: number}>> => Fetch({
    url: `/sphere/personmgr/person/loadNodePersonNum/${projectId}`,
    methods: "get",
    data: {projectId}
});
