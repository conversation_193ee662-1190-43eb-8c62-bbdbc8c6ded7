import React from "react";
import moment from "moment";

export interface FormTimeProps {
    value?: number | null | string;
    format?: string;
}

const FormTime = (props: FormTimeProps) => {
    const {
        value,
        format = "YYYY.MM.DD"
    } = props;

    return (
        <div>
            {moment(value).isValid()
                ? moment(value).format(format)
                : ""}
        </div>
    );
};

export default FormTime;
