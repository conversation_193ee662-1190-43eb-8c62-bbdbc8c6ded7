import {Col, Row, Spin} from "antd";
import React, {useEffect, useState, useCallback} from "react";
import {useSelector} from "react-redux";
import {isArray} from "lodash-es";
import {MotorContext} from "../../../../reMotor";
import {RootState} from "../../../../store/rootReducer";
import MotorFrame from "./MotorFrame";
import BimBtnControl from "../bim/BtnControl";
import {isNotNullOrUndefined} from "../../../../assets/ts/utils";
import BimSandDataInitialize from "../sandBox/handler/BimSandDataInitialize";
import {dispatch} from "../../../../store";
import {setAnalyzedData, setConstructionAnalyzedData} from "../../../../store/sandAnalyzedData/action";
import {CheckedInfoType} from "../sandBox/index.d";
import SandBoxBtnControl from "../sandBox/BtnControl";
import HtmlMarkerInMotorViewer from "./HtmlMarker/HtmlMarkerInMotorViewer";
import useStyle from "../style";
import BimConstructionSandDataInitialize from "../sandBox/handler/BimConstructionSandDataInitialize";

const MotorViewerId = "motorViewer";
// eslint-disable-next-line no-underscore-dangle
const {baseUrl} = window.__IWorksConfig__ as unknown as {baseUrl: string};
interface ModelViewProps {
    curTab: string;
    changeTab?: string;
    checkedInfo?: CheckedInfoType;
}

const ModelView = (props: ModelViewProps) => {
    const cls = useStyle();
    const {token} = useSelector((state: RootState) => state.commonData);
    const {curSandTable} = useSelector((state: RootState) => state.statusData);
    const {playStatus, playPattern} = useSelector((state: RootState) => state.sandManageData);
    const {analyzedData} = useSelector((state: RootState) => state.sandAnalyzedData);

    const [isRenderContrast, setRenderContrast] = useState(false);
    const {curTab, checkedInfo} = props;
    useEffect(
        () => {
            if (curSandTable !== null) {
                MotorContext.initViewer(MotorViewerId, baseUrl, token).then(() => {
                    MotorContext.openProject(curSandTable?.motor3dId ?? "").then(() => {
                        const newHanlder = new BimSandDataInitialize(curSandTable);
                        // eslint-disable-next-line max-nested-callbacks
                        newHanlder.refresh().then(() => {
                            dispatch(setAnalyzedData({
                                isReady: true,
                                handler: newHanlder
                            }));
                        });

                        const wbsHandler = new BimConstructionSandDataInitialize(curSandTable);
                        // eslint-disable-next-line max-nested-callbacks
                        wbsHandler.refresh().then(() => {
                            dispatch(setConstructionAnalyzedData({
                                isReady: true,
                                handler: wbsHandler
                            }));
                        });
                    });
                });
            }
        },
        [curSandTable, token]
    );

    const getSandBoxFormValues = useCallback((values) => {
        if (isNotNullOrUndefined(values) && isArray(values.playbackMode)) {
            setRenderContrast(values.playbackMode.length === 2);
        }
    }, []);

    const renderBimSign = useCallback(
        () => {
            if (curTab !== "sandBox") {
                return null;
            }
            const name = playPattern.length === 1 && playPattern[0] === "plan" ? "计划" : "实际";
            return (
                <span className={cls.bimSignBox}>
                    {name}
                </span>
            );
        },
        [cls.bimSignBox, curTab, playPattern]
    );

    return (
        <Spin
            tip="Loading..."
            spinning={playStatus === "playing" && (analyzedData.isReady !== true)}
            style={{width: "100%", height: "80vh", maxHeight: "80vh"}}
            wrapperClassName={cls.modelViewSpin}
        >
            <Row style={{width: "100%", height: "100%"}}>
                <Col style={{width: isRenderContrast && curTab === "sandBox" ? "50%" : "100%", height: curTab === "sandBox" ? "calc(100% - 46px)" : "100%"}} id={MotorViewerId}>
                    {curTab === "sandBox" ? <SandBoxBtnControl getFormValues={getSandBoxFormValues} isRenderContrast={isRenderContrast} checkedInfo={checkedInfo} /> : <BimBtnControl />}
                    <HtmlMarkerInMotorViewer />
                    {renderBimSign()}
                </Col>
                {isRenderContrast && curTab === "sandBox" && (
                    <Col className={cls.motorFrameBox}>
                        <MotorFrame />
                        <span className={cls.bimSignBox}>计划</span>
                    </Col>
                )}
            </Row>
        </Spin>
    );
};

export default ModelView;
