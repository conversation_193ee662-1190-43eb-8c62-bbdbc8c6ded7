import {IGET, IPOST} from "../api/commonFetch";
/* eslint-disable */
import {getServicesUrl} from "../api/common";

export function projTypetoStr(iprojType: number) {
    // 设置BIM
    let projType = "";
    switch (iprojType) {
        case 1:
            projType = "土建";
            break;
        case 2:
            projType = "安装";
            break;
        case 3:
            projType = "钢筋";
            break;
        case 4:
            projType = "Revit";
            break;
        case 5:
            projType = "Tekla";
            break;
        case 8:
            projType = "班筑家装";
            break;
        case 9:
            projType = "场布";
            break;
        case 10:
            projType = "Civil3D";
            break;
        case 11:
            projType = "Bentley";
            break;
        case 12:
            projType = "Rhino";
            break;
        case 13:
            projType = "IFC";
            break;
        case 14:
            projType = "CATIA";
            break;
        default:
            throw new TypeError("unexpected type");
    }

    return projType;
}

export interface TreeNode {
    name: string;
    rel: string;
    ext: string;
    level: number;
    treeNodeList: TreeNode[]

};

export interface ProjInfo {
    ppid: number;
    projName: string;
    projType: string;
    projTypeInt: number;
}

const requestProjectTree = (ppid: number) =>
    IGET(`${getServicesUrl("pdscommon")}/rs/project/tree/projecttree/ppid/${ppid}`)

export const getTree = async (ppid: number): Promise<TreeNode[]> => requestProjectTree(ppid).then((data) =>
    (data as {
        treeNodeList: TreeNode[],
    }).treeNodeList
);

export const getMotorId = async (ppid: number) => IGET(`${getServicesUrl("pdscommon")}/rs/project/extract/motor3d/status/${ppid}`)
    .then((data) => data.result.motor3dRelationResult.motor3dId as string);

export interface ProjectComponent {
    epid: number,
    ppid: number,
    pathList: string[],
    compNamesList: {
        name: string,
        hlnum: number,
        handles: string[],
    }[]
}

const getComponentsByPathPage = async (path: string[], ppid: number): Promise<ProjectComponent[]> => IPOST(
    `${getServicesUrl("pdscommon")}/rs/project/tree/compHandle/ppid/${ppid}`, path)

export const getComponentsByPath = async (path: string[], ppid: number) => {
    const data: ProjectComponent[] = [];
    do {
        const res = await getComponentsByPathPage(path, ppid);
        data.push(...res);
        if (res.length < 100) {
            break;
        }
    } while (true);
    return data;
}

// export const getMotorToken = async () => IGET(
//     `${getServicesUrl("pds")}/auth-server/auth/motor/client_token`)

export const getProjInfo = async (ppid: number) => IGET(
    `${getServicesUrl("pdscommon")}/rs/proj/detail/ppid/${ppid}`)

export const getComponentPathByHandle = async (ppid: number, handles: string[]) => IPOST(
    `${getServicesUrl("pdscommon")}/rs/project/tree/component_detail/ppid/${ppid}`, handles)
