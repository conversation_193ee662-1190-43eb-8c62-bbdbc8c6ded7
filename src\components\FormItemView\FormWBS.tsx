import React from "react";
import {Typography} from "antd";
import {WBSType} from "../../api/common.type";

const {Paragraph} = Typography;

export interface FormWBSProps {
    value?: WBSType[];
}

const FormWBS = (props: FormWBSProps) => {
    const {value = []} = props;

    const text = value === null ? "" : value.map((el) => el.wbsNodeName).join(",");

    return (
        <Paragraph ellipsis={{tooltip: text, rows: 3}}>
            {text}
        </Paragraph>
    );
};

export default FormWBS;
