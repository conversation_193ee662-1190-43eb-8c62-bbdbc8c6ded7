import React, {ReactNode} from "react";
import {PlanPreparationItemType, ProjNameForUpdatePlan} from "../../api/Preparation/type";
import {CalendarInfo} from "../ScheduleGantt/api/plan/type";

export interface PlanInfoProps {
    planId?: string;
    visible: boolean;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    taskStartDate?: Date;
    taskEndDate?: Date;
    record?: PlanPreparationItemType;
    planInfoBack: (planId: string, calendar?: CalendarInfo) => void;
    readonly?: boolean;
}
export interface AddOrEditFormType {
    project: string;
    planName: string;
    planType: LabeledValue;
    // planCycle: Moment;
    planCycle: string;
    planStartDate: Moment;
    planEndDate: Moment;
    planCalendar: PlanCalendarForPlanSetParam;
    associatedEngineering?: ProjNameForUpdatePlan;
}
export interface TreeDataProps<T> {
    title?: string | ReactNode;
    value?: string | number;
    key?: string | number;
    children?: TreeDataProps[];
    origin: T;
}
