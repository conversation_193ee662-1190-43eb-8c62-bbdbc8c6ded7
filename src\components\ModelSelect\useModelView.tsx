import {useState, useCallback, useRef} from "react";
import {useParent} from "@luban/react-open-tab";

// eslint-disable-next-line no-underscore-dangle
const {motorViewUrl} = window.__IWorksConfig__;

export interface ProjectType {
    ppid: number;
    projectName: string;
    selectedGuids?: string[];
}

const useModelView = () => {
    const [loaded, setLoaded] = useState<boolean>(false);
    const childName = useRef<string>("");
    const onMountRef = useRef<() => void>();
    const onCloseRef = useRef<() => void>();

    const onChildMount = useCallback(() => {
        setLoaded(true);
        if (onMountRef.current instanceof Function) {
            onMountRef.current();
        }
    }, []);

    const onChildClose = useCallback(() => {
        setLoaded(false);
        if (onCloseRef.current instanceof Function) {
            onCloseRef.current();
        }
    }, []);

    const parent = useRef(useParent(
        `${motorViewUrl}?selectionClear=false`,
        {
            onChildMount,
            onChildClose,
            oneChild: false,
            crossOrigin: true
        },
        "plan-motor-view"
    ));

    const sendMessage = useCallback((type: string, data?: {}) => {
        if (parent.current?.notifyChild !== undefined && childName.current !== "") {
            parent.current.notifyChild(childName.current, type, data);
        }
    }, []);

    const openChildTab = useCallback((onMount?: () => void, onClose?: () => void) => {
        childName.current = parent.current.openChild();
        onMountRef.current = onMount;
        onCloseRef.current = onClose;
    }, []);

    return {
        openChildTab,
        sendMessage,
        loaded,
    };
};

export default useModelView;
