// import Motor from "@motor/core";
import {ParentAPI} from "postmate";
// import MotorUtils from "../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
// import {MotorContext} from "../../../../reMotor";
import {frameMsgCreator} from "../../modelView/MotorFrame";
import {SandBoxHandlerForMotorFrame, SandDataInitializeBase} from "./dataOrInterface";

export default class ProcessPlanSandBoxHandlerForMotorFrame implements SandBoxHandlerForMotorFrame {
    // private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    private motorFrame: ParentAPI | null = null;

    constructor(dataContainer: SandDataInitializeBase) {
        // this.bimProject = MotorContext.getCurBIMProject();
        this.dataContainer = dataContainer;
    }

    reset() {
        this.resetModel();
    }

    setChildApi(motorFrame: ParentAPI | null) {
        this.motorFrame = motorFrame;
    }

    showSandBoxByDate(curTime: Date): void {
        const {motorFrame, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(motorFrame)) {
            const processConfig = dataContainer.getFilterInfo("process");
            if (!processConfig.isCheckAll && processConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryCompSandBoxListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                if (processConfig.isCheckAll
                    || processConfig.setCheckedKeys.has(compItem.timePeriodInfo.stateKey)) {
                    const colorString = compItem.timePeriodInfo.isInterval || compItem.timePeriodInfo.isVirtualTail
                        ? `rgba(${compItem.timePeriodInfo.stateColor},0.3)`
                        : `rgb(${compItem.timePeriodInfo.stateColor})`;
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            const colorList = Array.from(mapColorComp);
            const colorInfo: {color: string; ids: string[]}[] = [];
            for (let i = 0; i < colorList.length; ++i) {
                const item = colorList[i];
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    colorInfo.push({
                        color: item[0],
                        ids: comps.map((el) => el.id ?? "")
                    });
                }
            }
            const param = frameMsgCreator({
                name: "onProcessPlay",
                params: {colorInfo}
            });
            motorFrame.call(param.name, param);
        }
    }

    jumpToDate(curTime: Date) {
        const {motorFrame, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(motorFrame)) {
            const processConfig = dataContainer.getFilterInfo("process");
            if (!processConfig.isCheckAll && processConfig.setCheckedKeys.size === 0) {
                return;
            }
            const compItems = dataContainer.queryAccumulatedCompSandBoxListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                if (processConfig.isCheckAll
                    || processConfig.setCheckedKeys.has(compItem.timePeriodInfo.stateKey)) {
                    const colorString = compItem.timePeriodInfo.isInterval || compItem.timePeriodInfo.isVirtualTail
                        ? `rgba(${compItem.timePeriodInfo.stateColor},0.3)`
                        : `rgb(${compItem.timePeriodInfo.stateColor})`;
                    const {compKey} = compItem;
                    const find = mapColorComp.get(colorString);
                    if (typeof find !== "undefined") {
                        find.push(compKey);
                    } else {
                        mapColorComp.set(colorString, [compKey]);
                    }
                }
            });

            this.resetModel();
            const colorList = Array.from(mapColorComp);
            const colorInfo: {color: string; ids: string[]}[] = [];
            for (let i = 0; i < colorList.length; ++i) {
                const item = colorList[i];
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    colorInfo.push({
                        color: item[0],
                        ids: comps.map((el) => el.id ?? "")
                    });
                }
            }
            const param = frameMsgCreator({
                name: "onProcessPlay",
                params: {colorInfo}
            });
            motorFrame.call(param.name, param);
        }
    }

    private resetModel() {
        const {motorFrame} = this;
        if (isNotNullOrUndefined(motorFrame)) {
            const param = frameMsgCreator({
                name: "resetProject",
                params: {visible: false}
            });
            motorFrame.call(param.name, param);
        }
    }
}
