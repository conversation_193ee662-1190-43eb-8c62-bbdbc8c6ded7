import {createUseStyles} from "react-jss";
import anzhuang from "../images/proj_type/project_type_anzhuang_ic.png";
import bentely from "../images/proj_type/project_type_bentely_ic.png";
import c3d from "../images/proj_type/project_type_c3d_ic.png";
import catia from "../images/proj_type/project_type_catia_ic.png";
import gangjin from "../images/proj_type/project_type_gangjin_ic.png";
import ifc from "../images/proj_type/project_type_ifc_ic.png";
import jzx from "../images/proj_type/project_type_jzx_ic.png";
import revit from "../images/proj_type/project_type_revit_ic.png";
import rhino from "../images/proj_type/project_type_rhino_ic.png";
import site from "../images/proj_type/project_type_site_ic.png";
import tekla from "../images/proj_type/project_type_tekla_ic.png";
import tujian from "../images/proj_type/project_type_tujian_ic.png";
import zaojia from "../images/proj_type/project_type_zaojia_ic.png";
import pkpm from "../images/proj_type/project_type_pkpm_ic.png";
import unmatch from "../images/proj_type/project_type_unmatch_ic.png";

const compStyles = createUseStyles({
    box: {
        height: "100%",
        display: "flex",
        flexDirection: "column",
        backgroundColor: "#F7F7FC",
    },
    tableWrapper: {
        // height: "calc(47vh)",
        flexGrow: 1,
        overflow: "auto",
        "& thead": {
            "& tr": {
                background: "#F2F3F7",
                "& th": {
                    fontSize: "14px",
                    padding: "7px 15px 7px 15px !important"
                }
            },
        },
        "& tbody": {
            "& tr": {
                cursor: "pointer",
                "& td": {
                    fontSize: "14px",
                    fontWeight: 400,
                    color: "#666666",
                    padding: "7px 15px 7px 15px !important"
                }
            },
        }
    },
    listBox: {
        padding: "20px 20px 0",
        backgroundColor: "#f5f5f6",
        flexWrap: "wrap",
        overflowY: "scroll"
    },
    flexRow: {
        display: "flex",
        flexDirection: "row",
    },
    flex: {
        display: "flex",
    },
    flexTop: {
        flexDirection: "column",
    },
    flexItem: {
        flex: 1
    },
    flexRowCenter: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center"
    },
    flexColumnCenter: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center"
    },
    flexCenter: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    rowItemCenter: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center"
    },
    RowItemRight: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end"
    },
    textEllipsisEnd: {
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap",
    },
    textDiv: {
        fontSize: "14px",
        "@global": {
            ".black": {
                color: "#606060"
            },
            ".orange": {
                color: "#FAAD14"
            },
            ".green": {
                color: "#52C41A"
            },
            ".blue": {
                color: "#1890FF"
            },
            ".red": {
                color: "#FF4D4F"
            },
        }
    },
    recentProjStaDiv: {
        width: "48px",
        height: "20px",
        position: "absolute",
        top: "117px",
        background: "#000000",
        opacity: "0.6",
        color: "#ffffff",
        fontSize: "12px",
        borderRadius: "0px 10px 0px 0px"
    },
    recentProjOpenDiv: {
        width: "100%",
        height: "243px",
        cursor: "pointer",
        padding: "12px",
        marginRight: "15px",
        marginBottom: "16px",
        display: "flex",
        flexDirection: "column",
        background: "#FFFFFF",
        border: "1px dashed #061127",
        borderRadius: "4px",
        "&:hover": {
            border: "2px solid #1F54C5",
        },
    },
    recentProjDiv: {
        width: "100%",
        height: "243px",
        cursor: "pointer",
        marginRight: "15px",
        marginBottom: "16px",
        display: "flex",
        flexDirection: "column",
        background: "#FFFFFF",
        border: "1px solid #F0F0F5",
        borderRadius: "4px",
    },
    imgBox: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        "@global": {
            ".smallicon": {
                width: "16px",
                height: "16px",
                margin: "0 0 0 0",
                backgroundSize: "100% 100% !important"
            },
            ".icon": {
                width: "32px",
                height: "32px",
                margin: "0 0 0 0",
                backgroundSize: "100% 100% !important"
            },
            ".anzhuang": {
                background: `url(${anzhuang}) no-repeat 0 0`
            },
            ".bentely": {
                background: `url(${bentely}) no-repeat 0 0`
            },
            ".c3d": {
                background: `url(${c3d}) no-repeat 0 0`
            },
            ".catia": {
                background: `url(${catia}) no-repeat 0 0`
            },
            ".gangjin": {
                background: `url(${gangjin}) no-repeat 0 0`
            },
            ".ifc": {
                background: `url(${ifc}) no-repeat 0 0`
            },
            ".jzx": {
                background: `url(${jzx}) no-repeat 0 0`
            },
            ".revit": {
                background: `url(${revit}) no-repeat 0 0`
            },
            ".rhino": {
                background: `url(${rhino}) no-repeat 0 0`
            },
            ".site": {
                background: `url(${site}) no-repeat 0 0`
            },
            ".tekla": {
                background: `url(${tekla}) no-repeat 0 0`
            },
            ".tujian": {
                background: `url(${tujian}) no-repeat 0 0`
            },
            ".zaojia": {
                background: `url(${zaojia}) no-repeat 0 0`
            },
            ".pkpm": {
                background: `url(${pkpm}) no-repeat 0 0`
            },
            ".unmatch": {
                background: `url(${unmatch}) no-repeat 0 0`
            }
        }
    },
    paginationWrapper: {
        display: "flex",
        flex: "0 0 auto",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "flex-end",
        padding: "12px 0"
    },
    choiceProject: {
        "& .ant-drawer-content div.ant-drawer-body": {
            padding: 24,
            paddingRight: 0,
            overflow: "hidden"
        }
    }
});

export default compStyles;
