import {BIM_FULL_SCREEN, CUR_PROJ_SETTING_INFO, CUR_SANDTABLE, SET_MOTOR_FRAME, SET_MOTOR_FRAME_INIT_STATUS, StatusActionTypes, StatusStateType} from "./types";

const InitStatusData: StatusStateType = {
    curSandTable: null,
    curProjSettingInfo: null,
    motorFrame: null,
    motorFrameInitStatus: false,
};

const statusReducer = (state = InitStatusData, action: StatusActionTypes): StatusStateType => {
    switch (action.type) {
        case CUR_SANDTABLE:
            return {
                ...state,
                curSandTable: action.payload
            };
        case CUR_PROJ_SETTING_INFO:
            return {
                ...state,
                curProjSettingInfo: action.payload
            };
        case BIM_FULL_SCREEN:
            return {
                ...state,
                isBimFullscreen: action.payload
            };
        case SET_MOTOR_FRAME:
            return {
                ...state,
                motorFrame: action.payload
            };
        case SET_MOTOR_FRAME_INIT_STATUS:
            return {
                ...state,
                motorFrameInitStatus: action.payload
            };
        default:
            return state;
    }
};

export default statusReducer;
