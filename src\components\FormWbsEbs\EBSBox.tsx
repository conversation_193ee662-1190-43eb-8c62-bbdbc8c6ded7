import React, {useState, useCallback, useEffect} from "react";
import {createUseStyles} from "react-jss";
import {Tabs} from "antd";
import {defaultTabs, ProjectTreeNodeData, EBSNodeType, RelateTabType, getWBSEBSType} from "./data";
import {useAppSelector} from "../../store";
import {getListProjNode} from "../../api/common.api";
import {toArr} from "../../assets/ts/utils";
import RelatedProject from "./RelatedProject";
import RelatedComp from "./RelatedComp";
import RelatedCompCategory from "./RelatedCompCategory";
import {ProjNameVo} from "../../api/common.type";
import {SectionListType} from "../../store/common/actionTypes";

const useStyle = createUseStyles({
    box: {},
});
export interface EBSBoxProps {
    value?: EBSNodeType[];
    onChange?: (val: EBSNodeType[]) => void;
    nodeInfo?: SectionListType;
}


// const toTree = (list: OrgProjNodeVo[]): ProjectTreeNodeData[] => {
//     const {
//         keyField = "key",
//         childField = "children",
//         parentField = "parentId"
//     } = {};

//     const tree = [];
//     const record: {[key: string]: ProjectTreeNodeData[]} = {};

//     for (let i = 0, len = list.length; i < len; i++) {
//         const curList = list[i];
//         const item: ProjectTreeNodeData = {
//             key: curList.id,
//             title: curList.name,
//             originData: curList,
//         };
//         const id = item[keyField];

//         if (record[id] !== undefined) {
//             item[childField] = record[id];
//         } else {
//             record[id] = [];
//             item[childField] = record[id];
//         }

//         if (curList.type === 0) {
//             tree.push(item);
//         } else {
//             const parentId = curList[parentField];
//             if (record[parentId] === undefined) {
//                 record[parentId] = [];
//             }
//             record[parentId].push(item);
//         }
//     }

//     return tree;
// };

// const dealOrgNodeTree = (val: ProjectTreeNodeData) => {
//     const deal = (node: ProjectTreeNodeData) => {
//         const originData = node.originData as unknown as OrgProjNodeVo;
//         toArr(originData.projects ?? []).forEach((el) => {
//             toArr(node.children ?? []).push({
//                 key: el.ppid ?? "",
//                 title: el.projName,
//                 originData: el
//             });
//         });
//         toArr(node.children ?? []).forEach((el) => deal(el));
//     };
//     deal(val);
// };


const EBSBox = (props: EBSBoxProps) => {
    const {value, onChange, nodeInfo} = props;
    const cls = useStyle();
    const [tabActive, setTabActive] = useState<string>();
    const [projectTreeData, setProjectTreeData] = useState<ProjectTreeNodeData[]>([]);
    const {orgInfo} = useAppSelector((state) => state.commonData);
    const {curSectionInfo} = useAppSelector((state) => state.commonData);
    const [curNodeInfo, setCurNodeInfo] = useState(curSectionInfo);

    useEffect(() => {
        if (nodeInfo !== undefined) {
            setCurNodeInfo(nodeInfo);
        }
    }, [nodeInfo]);

    const valueType = getWBSEBSType(value);

    const getProjectTreeData = useCallback(() => {
        if (orgInfo.orgId === "") {
            return;
        }
        getListProjNode(orgInfo.orgId)
            .then((res) => {
                const projectNode = toArr(res).find((el) => el.id === orgInfo.orgId);
                if (projectNode === undefined || curNodeInfo === null) {
                    setProjectTreeData([]);
                    return;
                }
                const treeTopNode: ProjectTreeNodeData = {
                    title: projectNode?.name ?? "",
                    key: projectNode?.id ?? "",
                    originData: projectNode,
                };
                // 房建项目
                if (orgInfo.deptDataType === 1) {
                    // 会把单位工程显示出来,太复杂了,有点问题
                    // const tempTree = toTree(res);
                    // dealOrgNodeTree(tempTree[0]);
                    // [treeTopNode] = tempTree;
                    let projectList: ProjNameVo[] = [];
                    toArr(res).forEach((el) => {
                        projectList = projectList.concat(el.projects ?? []);
                    });
                    treeTopNode.children = projectList.map((el) => ({
                        key: el.ppid ?? "",
                        title: el.projName,
                        originData: el
                    }));
                }
                const curSectionNodeData = toArr(res).find((el) => el.id === curNodeInfo.nodeId);
                // 基建,节点为项目节点
                if (orgInfo.deptDataType === 2 && curSectionNodeData?.type === 0) {
                    treeTopNode.children = res.filter((el) => el.projects !== null)
                        .map((el) => ({
                            key: el.id ?? "",
                            title: el.name,
                            originData: el,
                            // eslint-disable-next-line max-nested-callbacks
                            children: toArr(el.projects ?? []).map((e) => ({key: e.ppid ?? "", title: e.projName, originData: e}))
                        }));
                }
                // 基建项目,节点为标段
                if (orgInfo.deptDataType === 2 && curSectionNodeData?.type === 1) {
                    treeTopNode.children = [
                        {
                            key: curSectionNodeData.id ?? "",
                            title: curSectionNodeData.name,
                            originData: curSectionNodeData,
                            children: toArr(curSectionNodeData.projects ?? []).map((el) => ({
                                key: el.ppid ?? "",
                                title: el.projName,
                                originData: {...el}
                            }))

                        }
                    ];
                }
                setProjectTreeData([treeTopNode]);
            });
    }, [curNodeInfo, orgInfo.deptDataType, orgInfo.orgId]);

    useEffect(() => {
        getProjectTreeData();
    }, [getProjectTreeData]);

    const handleTabChange = (val: string) => {
        setTabActive(val);
    };

    const renderEbsContent = (type: RelateTabType) => {
        if (type === RelateTabType.RelateProj) {
            return <RelatedProject value={valueType === 1 ? value : undefined} onChange={onChange} projectTreeData={projectTreeData} />;
        }
        if (type === RelateTabType.RelateComp) {
            return <RelatedComp value={valueType === 2 ? value : undefined} onChange={onChange} projectTreeData={projectTreeData} />;
        }
        if (type === RelateTabType.RelateCompCategory) {
            return (
                <RelatedCompCategory
                    value={valueType === 3 ? value : undefined}
                    onChange={onChange}
                    projectTreeData={projectTreeData}
                />
            );
        }
        return null;
    };

    return (
        <div className={`${cls.box} box`}>
            <Tabs activeKey={tabActive} onChange={handleTabChange} className="com-tabs" destroyInactiveTabPane>
                {
                    defaultTabs.map((el) => (
                        <Tabs.TabPane tab={el.name} key={el.type}>
                            {renderEbsContent(el.type)}
                        </Tabs.TabPane>
                    ))
                }
            </Tabs>
        </div>
    );
};

export default EBSBox;
