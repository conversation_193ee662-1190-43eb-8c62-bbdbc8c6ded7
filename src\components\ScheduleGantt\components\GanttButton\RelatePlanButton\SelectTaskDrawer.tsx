import React, {use<PERSON><PERSON>back, useContext, useEffect, useLayoutEffect, useMemo, useRef, useState} from "react";
import {Form, message, Modal, Select} from "antd";
import {gantt} from "@iworks/dhtmlx-gantt";
import moment from "moment";
import {TableRowSelection} from "antd/lib/table/interface";
import ComDrawer from "../../../../ComDrawer";
import {getPlanTaskList} from "../../../api/plan";
import ComTable from "../../../../ComTable";
import {updatePlanInfo} from "../../../../../api/Preparation";
import {PlanPreparationItemType} from "../../../../../api/Preparation/type";
import {TaskItem} from "../../../api/plan/type";
import EditorContext from "../../../views/GanttEditor/context";
import useStyles from "../styles";
import {getParentPlanTasks} from "../../../gantt/taskUtils";

interface TaskDataType extends TaskItem {
    children?: TaskDataType[];
}

interface SelectTaskDrawerProps {
    planId: string;
    planList: PlanPreparationItemType[];
    visible: boolean;
    setVisible: (visible: boolean) => void;
    onOk: (parentPlan?: PlanPreparationItemType) => void;
}

const SelectTaskDrawer = (props: SelectTaskDrawerProps) => {
    const cls = useStyles();
    const {planId, planList, visible, setVisible, onOk} = props;
    const {planInfo, setPlanInfo, hasParentPlan} = useContext(EditorContext);
    const [parentPlanId, setParentPlanId] = useState<string>();
    const [loading, setLoading] = useState(false);
    const [taskData, setTaskData] = useState<TaskDataType[]>([]);
    const [taskSelectedRowKeys, setTaskSelectedRowKeys] = useState<string[]>([]);
    const [parentPlanTaskIds, setParentPlanTaskIds] = useState<string[]>([]); // 需要关联的任务id，包括半选节点
    const taskMapRef = useRef<Map<string, TaskDataType>>(new Map());
    const boxRef = useRef<HTMLDivElement>(null);
    const [tableHeight, setTableHeight] = useState(500);

    useLayoutEffect(() => {
        const handleResize = () => {
            if (visible) {
                if (boxRef.current !== null) {
                    setTableHeight(boxRef.current.offsetHeight - 150);
                }
            }
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [visible]);

    const getTaskListData = useCallback(async (id?: string) => {
        if (id === undefined) {
            setTaskData([]);
            return;
        }
        setLoading(true);
        try {
            const res = await getPlanTaskList(id);
            if (res.data !== undefined) {
                const rootTasks: TaskDataType[] = [];
                taskMapRef.current = new Map<string, TaskDataType>(res.data.taskList.map((el) => [el.id, el]));
                res.data.taskList.forEach((el) => {
                    if (el.parentTaskId.length > 0) {
                        const parentTask = taskMapRef.current.get(el.parentTaskId);
                        if (parentTask !== undefined) {
                            if (parentTask.children === undefined) {
                                parentTask.children = [];
                            }
                            parentTask.children.push(el);
                        }
                    } else {
                        const task = taskMapRef.current.get(el.id);
                        if (task !== undefined) {
                            rootTasks.push(task);
                        }
                    }
                });
                const sortTasks = (list: TaskDataType[]) => {
                    list.sort((a, b) => a.sort - b.sort);
                    list.forEach((el) => {
                        if (el.children !== undefined && el.children.length > 0) {
                            sortTasks(el.children);
                        } else {
                            el.children = undefined;
                        }
                    });
                };
                sortTasks(rootTasks);
                setTaskData(rootTasks);
            }
        } finally {
            setLoading(false);
        }
    }, []);

    const handlePlanChange = useCallback((value) => {
        setParentPlanId(value);
        getTaskListData(value);
        setTaskSelectedRowKeys([]);
    }, [getTaskListData]);

    useEffect(() => {
        if (visible) {
            setParentPlanId(undefined);
            setTaskData([]);
            const {parentPlanTasks, leafTasks} = getParentPlanTasks();
            setTaskSelectedRowKeys(leafTasks.map((el) => `${el.id}`));
            if (hasParentPlan) {
                setParentPlanTaskIds(parentPlanTasks.map((el) => `${el.id}`));
                setParentPlanId(planInfo.parentId);
                getTaskListData(planInfo.parentId);
            }
        }
    }, [visible, planInfo.parentId, getTaskListData, hasParentPlan]);

    const unRelatedParentPlan = useCallback(async () => {
        const res = await updatePlanInfo({
            id: planId,
            parentId: undefined,
            parentPlanTaskIds: []
        });
        if (res.success) {
            setPlanInfo((prev) => ({...prev, parentId: undefined}));
            message.success("取消关联成功");
            if (onOk instanceof Function) {
                onOk();
            }
        } else {
            message.error("取消关联失败");
        }
    }, [onOk, planId, setPlanInfo]);

    const relateParentPlan = useCallback(async () => {
        if (planId !== undefined) {
            try {
                const res = await updatePlanInfo({
                    id: planId,
                    parentId: parentPlanId,
                    parentPlanTaskIds
                });
                if (res.success) {
                    setPlanInfo((prev) => ({...prev, parentId: parentPlanId}));
                    message.success("关联成功");
                    if (onOk instanceof Function) {
                        onOk(planList.find((plan) => plan.id === parentPlanId));
                    }
                } else {
                    message.error("关联失败");
                }
            } catch (error) {
                console.log("error", error);
                message.error("关联失败");
            }
        }
    }, [onOk, parentPlanId, planId, planList, setPlanInfo, parentPlanTaskIds]);

    const handleOk = async () => {
        const taskCount = gantt.getTaskCount();
        if (parentPlanId === undefined) {
            if (taskCount > 0) {
                Modal.confirm({
                    title: "提示",
                    content: "确定则清空任务，取消则取消当前操作",
                    onOk: async () => {
                        await unRelatedParentPlan();
                    }
                });
            } else {
                await unRelatedParentPlan();
            }
        } else {
            if (parentPlanTaskIds.length === 0) {
                message.warning("请选择关联任务");
                return;
            }
            if (taskCount > 0) {
                Modal.confirm({
                    title: "提示",
                    content: "确定则清空任务，取消则取消当前操作",
                    onOk: async () => {
                        await relateParentPlan();
                    }
                });
            } else {
                await relateParentPlan();
            }
        }
    };

    const handleCancel = () => {
        setVisible(false);
    };

    const rowSelection: TableRowSelection<any> = useMemo(() => ({
        selectedRowKeys: taskSelectedRowKeys,
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            // console.log("selectedRowKeys", selectedRowKeys);
            // console.log("selectedRows", selectedRows);
            const selectedSet = new Set<string>(selectedRows.map((el: any) => el.id));
            const findParentTask = (task: TaskDataType) => {
                if (task.parentTaskId.length > 0) {
                    selectedSet.add(task.parentTaskId);
                    const parentTask = taskMapRef.current.get(task.parentTaskId);
                    if (parentTask !== undefined) {
                        findParentTask(parentTask);
                    }
                }
            };
            selectedRows.forEach((el: any) => {
                findParentTask(el);
            });
            const ids = [...selectedSet];
            // console.log("setTaskCheckedIds", ids);
            setTaskSelectedRowKeys(selectedRowKeys);
            setParentPlanTaskIds(ids);
        },
    }), [taskSelectedRowKeys]);

    return (
        <ComDrawer
            title="关联上级任务"
            visible={visible}
            width={700}
            onOk={handleOk}
            onCancel={handleCancel}
        >
            <div ref={boxRef} style={{padding: "24px", height: "100%"}}>
                <Form.Item label="上级计划">
                    <Select
                        style={{width: 200, marginLeft: 8}}
                        onChange={handlePlanChange}
                        placeholder="请选择上级计划"
                        value={parentPlanId}
                        allowClear
                    >
                        {planList.map((plan) => (
                            <Select.Option className={cls.dropDown} key={plan.id} value={plan.id}>
                                {plan.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <ComTable
                    loading={loading}
                    columns={[
                        {title: "序号", dataIndex: "wbsNo", ellipsis: true},
                        {title: "任务名称", dataIndex: "name", ellipsis: true},
                        {title: "计划开始", dataIndex: "planStartDate", ellipsis: true, width: 130, render: (value) => moment(value).format("YYYY.MM.DD")},
                        {title: "计划完成", dataIndex: "planEndDate", ellipsis: true, width: 130, render: (value) => moment(value).format("YYYY.MM.DD")},
                    ]}
                    rowSelection={{...rowSelection, checkStrictly: false, columnWidth: 50, fixed: true}}
                    indentSize={8}
                    dataSource={taskData}
                    scroll={{y: tableHeight}}
                />
            </div>
        </ComDrawer>
    );
};

export default SelectTaskDrawer;
