import {CalendarInfo} from "../api/plan/type";


// // 日历类型:0：24小时日历（默认）、1：标准日历（周六周日休息）、2：自定义日历（复制24小时）、3：自定义日历（复制标准）
export const CalendarType = {
    TYPE_24_7: 0,
    TYPE_STANDARD: 1,
    TYPE_CUSTOM_24_7: 2,
    TYPE_CUSTOM_STANDARD: 3,
};

export const is24HourCalendar = (cal: CalendarInfo) => {
    if (cal.calendarType === CalendarType.TYPE_24_7 || cal.calendarType === CalendarType.TYPE_CUSTOM_24_7) {
        return true;
    }
    return false;
};

export const isStandardCalendar = (cal: CalendarInfo) => {
    if (cal.calendarType === CalendarType.TYPE_STANDARD || cal.calendarType === CalendarType.TYPE_CUSTOM_STANDARD) {
        return true;
    }
    return false;
};

export const isCustomCalendar = (cal: CalendarInfo) => {
    if ((cal.calendarType === CalendarType.TYPE_CUSTOM_24_7 || cal.calendarType === CalendarType.TYPE_CUSTOM_STANDARD)
        && !cal.isTemplate) {
        return true;
    }
    return false;
};

/**
 * 判断两个日历是否是同种类型
 */
export const isSimilarCalendarType = (cal1: CalendarInfo, cal2: CalendarInfo) => {
    if (isStandardCalendar(cal1)) {
        return isStandardCalendar(cal2);
    }
    if (is24HourCalendar(cal1)) {
        return is24HourCalendar(cal2);
    }
    return false;
};
