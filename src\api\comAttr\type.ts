/** 构件属性查询条件 */
export interface CompAtrrQueryParam {
    /** 代理工程id */
    ppid?: number;
    /** 楼层 */
    floor?: string;
    /** 大类 */
    compClass?: string;
    /** 小类 */
    subClass?: string;
    /** 构件handle */
    handle?: string;
}

/** 构件属性信息 */
export interface CompAttrResult {
    /** 属性组列表 */
    attrGroups?: CompAttrGroupResult[];
    /** 自定义构件属性 */
    attr?: AttrNode;
    /** 属性模板值列表 */
    attrTmplList?: BVAttrTmplInfo[];
    /** 产品ID */
    productId?: number;
    /** 楼层别名 */
    floorAlias?: string;
    /** 大类别名 */
    classAlias?: string;
    /** 构件图片uuid */
    uuid?: string;
}

/** 构件属性组 */
export interface CompAttrGroupResult {
    /** 组名称 */
    groupName?: string;
    /** 属性信息列表 */
    attrItems?: CompAttrItemResult[];
    /** 组key（判断安装信息组件与基本信息的标识，安装可能返回多个组件信息，土建/钢筋只有一个） */
    groupKey?: string;
}

/** 构件属性信息 */
export interface CompAttrItemResult {
    /** 属性名称 */
    name?: string;
    /** 属性值 */
    value?: string;
}

export interface AttrNode {
    ik?: string;
    iv?: string;
    it?: number;
    pv?: string;
    cpk?: string;
    subList?: AttrNode[];
    docInfo?: DocInfo;
}

export interface DocInfo {
    docId?: string;
    ppid?: number;
    enterpriseId?: number;
    fileuuid: string;
    filename: string;
    extension?: string;
    filesize?: number;
    fileMD5?: string;
    fileType?: number;
    docType?: number;
    createUser?: string;
    createTime?: string;
    modifyUser?: string;
    modifyTime?: string;
    relType?: number;
    tags?: DocTag[];
    weaveTime?: string;
    isPreview?: boolean;
    thumbnailUuid?: string;
    /** 资料修改时间戳（mylubanApp5.10.0新增） */
    modifyTimeStamp?: number;
    /** 工程名称（iworksApp1.0.0新增） */
    projName?: string;
    /** 工程模型（iworksApp1.0.0新增） */
    projModel?: number;
}

export interface DocTag {
    tagId?: string;
    tagName?: string;
}

export interface BVAttrTmplInfo {
    attrGroupName?: string;
    subAttrInfos?: BVSubAttrTemplateInfo[];
}

export interface BVSubAttrTemplateInfo {
    attrName?: string;
    attrValues?: string[];
}

/** 工程属性控制查询参数 */
export interface ProjectAttrControlQueryParam {
    /** 代理工程Id列表 */
    ppids: number[];
    /** 工作集id(查工作集时必传，查询单工程时不传) */
    wsid?: number;
    /** 工程类型 */
    projType: number;
}

/** 用户工程属性设置信息 */
export interface RevitUserAttrRemote {
    /** 用户是否设置了revit属性显示控制(true:已设置，false：未设置) */
    isUserSetAttr?: boolean;
    /** 用户未设置的属性ID */
    noSelectIds?: string[];
    /** 是否显示空白属性(true:显示，false：不显示) */
    showBlank?: boolean;
    /** 属性信息列表 */
    attrList?: RevitGroupAttrInfo[];
}

/** 分组属性列表 */
export interface RevitGroupAttrInfo {
    /** 属性类型名称 */
    attrTypeName?: string;
    /** 属性列表 */
    revitAttrs?: RevitAttr[];
}

/** 属性信息 */
export interface RevitAttr {
    /** 属性ID */
    id?: string;
    /** 属性名称 */
    name?: string;
}

/** 工程属性控制设置传参 */
export interface ProjectAttrControlSetParam {
    /** 代理工程ID */
    ppid: number;
    /** 工作集id */
    wsid?: number;
    /** 是否显示空白属性(true:显示，false：不显示) */
    showBlank: boolean;
    /** 用户不勾选的属性ID,全勾选则不传 */
    ids?: string[];
}
