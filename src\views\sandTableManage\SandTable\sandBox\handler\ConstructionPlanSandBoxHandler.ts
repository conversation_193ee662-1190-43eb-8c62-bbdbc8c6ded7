import Motor from "@motor/core";
import MotorUtils from "../../../../../assets/ts/graphUtils/MotorUtils";
import {isNotNullOrUndefined} from "../../../../../assets/ts/utils";
import {MotorContext} from "../../../../../reMotor";
import StatusColor, {SandBoxHandler, SandDataInitializeBase} from "./dataOrInterface";

export default class ConstructionPlanSandBoxHandler implements SandBoxHandler {
    private bimProject: Motor.Model | null = null;

    private dataContainer: SandDataInitializeBase | undefined = undefined;

    constructor(dataContainer: SandDataInitializeBase) {
        this.bimProject = MotorContext.getCurBIMProject();
        this.dataContainer = dataContainer;
    }

    reset() {
        this.resetModel();
    }

    showSandBoxByDate(curTime: Date): void {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const compItems = dataContainer.queryCompStatusListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = compItem.status === "complete" ? StatusColor.completeColor : StatusColor.ongoingColor;
                const {compKey} = compItem;
                const find = mapColorComp.get(colorString);
                if (typeof find !== "undefined") {
                    find.push(compKey);
                } else {
                    mapColorComp.set(colorString, [compKey]);
                }
            });

            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setVisibility(true, comps);
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    jumpToDate(curTime: Date) {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            const compItems = dataContainer.queryAccumulatedCompStatusListByPlanTime(curTime);
            const mapColorComp: Map<string, string[]> = new Map();
            compItems.forEach((compItem) => {
                const colorString = compItem.status === "complete" ? StatusColor.completeColor : StatusColor.ongoingColor;
                const {compKey} = compItem;
                const find = mapColorComp.get(colorString);
                if (typeof find !== "undefined") {
                    find.push(compKey);
                } else {
                    mapColorComp.set(colorString, [compKey]);
                }
            });

            this.resetModel();
            const colorList = Array.from(mapColorComp);
            for (let i = 0; i < colorList.length; ++i) {
                const mcolor = Motor.MotorCore.Color.fromCssColorString(colorList[i][0]);
                const comps = dataContainer.queryComponentsByCompKeyFromCache(colorList[i][1]);
                if (comps.length > 0) {
                    bimProject.setVisibility(true, comps);
                    bimProject.setColor(mcolor, comps.map((el) => el.id ?? ""));
                }
            }
        }
    }

    private resetModel() {
        const {bimProject, dataContainer} = this;
        if (isNotNullOrUndefined(dataContainer) && isNotNullOrUndefined(bimProject)) {
            MotorUtils.resestBIMProject(bimProject);
            bimProject.setVisibility(false);
        }
    }
}
