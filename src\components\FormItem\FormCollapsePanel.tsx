import {Row} from "antd";
import React from "react";
import ComFormItem, {ComFormItemProps} from ".";
import ComCollapsePanel, {ComCollapsePanelProps} from "../ComCollapsePanel";

export interface FormCardProps extends Omit<ComCollapsePanelProps, "children"> {
    formItemList: ComFormItemProps[];
}

const FormCollapsePanel = (props: FormCardProps) => {
    const {formItemList, ...other} = props;

    return (
        <ComCollapsePanel {...other}>
            <Row>
                {formItemList.map((item) => <ComFormItem {...item} />)}
            </Row>
        </ComCollapsePanel>
    );
};

export default FormCollapsePanel;
