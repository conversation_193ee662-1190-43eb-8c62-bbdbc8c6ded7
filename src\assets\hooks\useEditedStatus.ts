import {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {setIframeEditStatus} from "../../store/no-persist/action";
import {RootState} from "../../store/rootReducer";

const useEditedStatus = (isEdited: boolean, back: () => void) => {
    const dispatch = useDispatch();
    const {iframeEditStatus} = useSelector((state: RootState) => state.noRegister);
    const {orgInfo} = useSelector((state: RootState) => state.commonData);
    const [initOrgId] = useState(orgInfo.orgId);

    // 当在编辑界面切换了项目的话,返回列表页面.
    useEffect(() => {
        if (orgInfo.orgId !== initOrgId) {
            back();
        }
    }, [back, initOrgId, orgInfo.orgId]);

    useEffect(() => {
        if (isEdited !== iframeEditStatus) {
            dispatch(setIframeEditStatus(isEdited));
        }
    }, [dispatch, iframeEditStatus, isEdited]);

    useEffect(() => () => {
        dispatch(setIframeEditStatus(false));
    }, [dispatch]);
};

export default useEditedStatus;
