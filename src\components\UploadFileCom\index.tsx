import React, {FC, ReactNode} from "react";
import {Upload, But<PERSON>, message} from "antd";
import {RcFile, UploadProps} from "antd/lib/upload/interface";
import Compressor from "compressorjs";
import {customAlphabet} from "nanoid";
import {createFileMd5, isImg, isVideo} from "../../assets/ts/utils";
import {getAttachmentUploadUrl, uploadAttachment} from "../../api/common.api";
import {GetDownloadUrlRes1, FileType} from "../../api/common.type";
import UploadFetch from "../../service/UploadFetch";

// 大家可以把其他用到的类型的限制类型在这里加下
// audio/* 任何音频文件
// video/* 任何视频文件
// image/* 任何图像文件
// .doc,.docx,.xml,.zip,.rar,.pdf 文档文件格式

interface UploadFileType {
    fileName: string;
    uuid: string;
    thumbUuid: string;
    size: number;
    timeStamp?: number;
    percent?: number;
    failed?: boolean;
}
export interface UploadFileProps {
    accept?: string;
    maxFileSize?: number;
    usedTotalFileSize?: number;
    children?: ReactNode;
    fileUploadSuccess: (props: UploadFileType) => void;
    BeforeUpload?: (file: RcFile, FileList: RcFile[]) => void;
    fileUploadFail?: () => void;
    onFileSelected?: (val: FileType, valList: FileType[]) => void;
}

let fileUploadNum = 0;

const UploadFileCom: FC<UploadFileProps> = (props: UploadFileProps) => {
    const {
        accept,
        fileUploadSuccess,
        children,
        BeforeUpload,
        fileUploadFail,
        onFileSelected,
        usedTotalFileSize = 0,
        maxFileSize = 512,
    } = props;

    const uploadOther = (timeStamp: number, fileMd5: string, file: File) => {
        getAttachmentUploadUrl([
            {
                fileMd5,
                fileSize: file.size,
                fileName: file.name,
                suportModes: [0],
                isBPUpload: false,
                isCheckFastUpload: true
            }
        ])
            .then((res: GetDownloadUrlRes1[]) => {
                if (Boolean(res.length) === true) {
                    if (Boolean(res[0].finished) === false) {
                        UploadFetch(
                            res[0].uploadUrls[0],
                            file,
                            (percent: number) => {
                                fileUploadSuccess({fileName: file.name, uuid: "", thumbUuid: "", size: file.size ?? 0, timeStamp, percent});
                            }
                        ).then((e) => {
                            // message.success("文件上传成功!");
                            fileUploadSuccess({fileName: file.name, uuid: e, thumbUuid: "", size: file.size ?? 0, timeStamp});
                        }).catch(() => {
                            fileUploadSuccess({fileName: file.name, uuid: "", thumbUuid: "", size: file.size ?? 0, timeStamp, failed: true});
                        });
                        // uploadAttachment(res[0].uploadUrls[0], file).then((resUuid: string) => {
                        //     message.success("文件上传成功!");
                        //     fileUploadSuccess({fileName: file.name, uuid: resUuid, thumbUuid: "", size: file.size ?? 0, timeStamp});
                        // });
                    } else {
                        // message.success("文件上传成功!");
                        fileUploadSuccess({fileName: file.name, uuid: res[0].fileUUID, thumbUuid: "", size: file.size ?? 0, timeStamp});
                    }
                }
            })
            .catch((err) => {
                message.error(err.message);
            });
    };

    const uploadImg = (timeStamp: number, fileMd5: string, file: File, thumbFileMd5: string, thumbFile: File) => {
        getAttachmentUploadUrl([
            {
                fileMd5,
                fileSize: file.size,
                fileName: file.name,
                suportModes: [0],
                isBPUpload: false,
                isCheckFastUpload: true
            },
            {
                fileMd5: thumbFileMd5,
                fileSize: thumbFile.size,
                fileName: thumbFile.name,
                suportModes: [0],
                isBPUpload: false,
                isCheckFastUpload: true
            },
        ])
            .then(async (res: GetDownloadUrlRes1[]) => {
                const fileData = {fileName: file.name, uuid: "", thumbUuid: "", size: file.size ?? 0, timeStamp};
                if (Boolean(res.length) === true) {
                    for (let index = 0; index < res.length; index++) {
                        const item = res[index];
                        if (item.fileMd5 === fileMd5) {
                            // 原图
                            if (item.finished === false) {
                                const resUuid = await UploadFetch(
                                    item.uploadUrls[0],
                                    file,
                                    (percent: number) => {
                                        fileUploadSuccess({fileName: file.name, uuid: "", thumbUuid: "", size: file.size ?? 0, timeStamp, percent});
                                    }
                                );
                                fileData.uuid = resUuid;
                            } else {
                                fileData.uuid = item.fileUUID;
                            }
                        } else if (item.fileMd5 === thumbFileMd5) {
                            // 压缩图
                            if (item.finished === false) {
                                const resUuid = await uploadAttachment(item.uploadUrls[0], thumbFile);
                                fileData.thumbUuid = resUuid;
                            } else {
                                fileData.thumbUuid = item.fileUUID;
                            }
                        }
                    }
                    // message.success("文件上传成功!");
                    fileUploadSuccess(fileData);
                    // if (Boolean(res[0].finished) === false) {
                    //     uploadAttachment(res[0].uploadUrls[0], file).then((resUuid: string) => {
                    //         console.log(resUuid, "123");
                    //         message.success("文件上传成功!");
                    //         fileUploadSuccess({fileName: file.name, uuid: resUuid, size: file.size ?? 0, timeStamp});
                    //     });
                    // } else {
                    //     message.success("文件上传成功!");
                    //     fileUploadSuccess({fileName: file.name, uuid: res[0].fileUUID, size: file.size ?? 0, timeStamp});
                    // }
                }
            })
            .catch((err) => {
                message.error(err.message);
            });
    };

    const fileChange: UploadProps["onChange"] = async (val) => {
        //         try {
        //             const timeStamp = Number(customAlphabet("123456789", 6)());
        //             const file = (val.file as unknown) as File;
        //             // 单个文件大小限制
        //             const maxSingleSize = isVideo(file.name) ? 1000 : 100;
        //             // 当前文件大小
        //             const curFileSize = Math.round((file.size / 1024 / 1024) * 100) / 100;
        //             // 剩余可用
        //             const remainderFileSize = Math.round((maxFileSize - usedTotalFileSize) * 100) / 100;
        //
        //             if (usedTotalFileSize + curFileSize > maxFileSize) {
        //                 message.warning(`上传失败！当前已上传${usedTotalFileSize}M，还可以上传${remainderFileSize}M（总大小不超过${maxFileSize}M）`);
        //                 return;
        //             }
        //             if (curFileSize > maxSingleSize) {
        //                 message.warning(isVideo(file.name) ? "上传失败！请确保视频文件大小不超过1000M" : "上传失败！请确保文件大小不超过100M");
        //                 return;
        //             }
        //             const fileMd5 = await createFileMd5(file);
        //
        //             if (onFileSelected !== undefined) {
        //                 onFileSelected({fileName: file.name, fileSize: file.size ?? 0, fileUuid: "", timeStamp, percent: 0}, []);
        //             }
        //             // 是图片就压缩
        //             if (isImg(file.name)) {
        //                 // eslint-disable-next-line no-new
        //                 new Compressor(file, {
        //                     quality: 0.2,
        //                     checkOrientation: false,
        //                     async success(result: File) {
        //                         // const newResult = new window.File([result], result.name, {type: result.type})
        //                         const thumbFileMd5 = await createFileMd5((result as unknown) as File);
        //                         const thumbFile = result;
        //                         // 提交到服务器
        //                         uploadImg(timeStamp, fileMd5, file, thumbFileMd5, thumbFile);
        //                     },
        //                     error(err: {message: unknown}) {
        //                         console.log(err.message);
        //                     }
        //                 });
        //                 // 如何是图片 就终止
        //                 return;
        //             }
        //             uploadOther(timeStamp, fileMd5, file);
        //         } catch (error) {
        //             console.log("文件转换失败");
        //             if (fileUploadFail !== undefined) {
        //                 fileUploadFail();
        //             }
        //         }

        const {fileList} = val;
        fileUploadNum += 1;
        if (fileUploadNum === fileList.length) {
            fileUploadNum = 0;
        }
        if (fileList.length > 1 && fileUploadNum !== 1) {
            return;
        }
        const tempFileList: {timeStamp: number; file: File; fileMd5: string}[] = [];
        for (let i = 0; i < fileList.length; i++) {
            try {
                const randomNum = customAlphabet("123456789", 6);
                const timeStamp = Number(randomNum());
                const file = fileList[i].originFileObj as unknown as File;
                // 单个文件大小限制
                const maxSingleSize = isVideo(file.name) ? 500 : 100;
                // 当前文件大小
                const curFileSize = Math.round((file.size / 1024 / 1024) * 100) / 100;
                // 文件总大小
                const maxTotalFileSize = isVideo(file.name) ? 5120 : maxFileSize;
                // 剩余可用
                const remainderFileSize = Math.round((maxTotalFileSize - usedTotalFileSize) * 100) / 100;

                if (usedTotalFileSize + curFileSize > maxTotalFileSize) {
                    message.warning(`上传失败！当前已上传${usedTotalFileSize}M，还可以上传${remainderFileSize}M（总大小不超过${maxTotalFileSize}M）`);
                    return;
                }
                if (curFileSize > maxSingleSize) {
                    message.warning(isVideo(file.name) ? "上传失败！请确保视频文件大小不超过500M" : "上传失败！请确保文件大小不超过100M");
                    return;
                }
                const fileMd5 = await createFileMd5(file);
                tempFileList.push({timeStamp, file, fileMd5});
            } catch (error) {
                message.warning("文件转换失败");
                if (fileUploadFail !== undefined) {
                    fileUploadFail();
                }
            }
        }

        if (onFileSelected !== undefined) {
            const selectedFile = tempFileList.map((el) => ({fileName: el.file.name, fileSize: el.file.size, fileUuid: "", timeStamp: el.timeStamp, percent: 0}));
            onFileSelected(selectedFile[selectedFile.length - 1], selectedFile);
        }

        for (let i = 0; i < tempFileList.length; i++) {
            const {file, fileMd5, timeStamp} = tempFileList[i];
            // 是图片就压缩
            if (isImg(file.name)) {
                // eslint-disable-next-line no-new
                new Compressor(file, {
                    quality: 0.2,
                    checkOrientation: false,
                    async success(result: File) {
                        // const newResult = new window.File([result], result.name, {type: result.type})
                        const thumbFileMd5 = await createFileMd5((result as unknown) as File);
                        const thumbFile = result;
                        // 提交到服务器
                        uploadImg(timeStamp, fileMd5, file, thumbFileMd5, thumbFile);
                    },
                    error(err: {message: unknown}) {
                        console.log(err.message);
                    }
                });
            } else {
                uploadOther(timeStamp, fileMd5, file);
            }
        }
    };

    const onBeforeUpload: UploadProps["beforeUpload"] = (file, fileList) => {
        if (BeforeUpload !== undefined) {
            BeforeUpload(file, fileList);
        }
        return false;
    };

    return (
        <Upload
            multiple
            accept={accept}
            onChange={fileChange}
            showUploadList={false}
            beforeUpload={onBeforeUpload}
            fileList={[]}
        >
            {children ?? <Button type="primary">上传</Button>}
        </Upload>
    );
};

export default UploadFileCom;
